'use client';

import { useState, useEffect } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { But<PERSON> } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import { miniAppSchema } from '../_lib/miniapp.schema';
import { createMiniApp } from '../_lib/server/miniapp.actions';
import { useMiniAppLimitDialog } from '../_lib/hooks/use-miniapp-limit-dialog';
import { ResourceLimitDialog } from '../../_components/resource-limit-dialog';

interface CreateMiniAppModalProps {
  children: React.ReactNode;
}

export default function CreateMiniAppModal({
  children,
}: CreateMiniAppModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { account } = useTeamAccountWorkspace();
  const {
    isOpen: isLimitDialogOpen,
    openDialog: openLimitDialog,
    closeDialog: closeLimitDialog,
    checkAndOpenDialog,
    accountSlug,
    reason,
    current,
    limit,
    canCreate,
    isLoading: isLimitCheckLoading,
  } = useMiniAppLimitDialog();

  const form = useForm({
    resolver: zodResolver(miniAppSchema),
    defaultValues: {
      mini_app_id: '',
      oa_id: '',
      status: 'active',
    },
  });

  // Check limits before opening the modal
  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      // If trying to open the modal, check limits first
      if (!isLimitCheckLoading && !canCreate) {
        openLimitDialog();
        return;
      }
    }
    setOpen(newOpen);
  };

  const onSubmit = form.handleSubmit(async (data) => {
    try {
      // Double-check limits before submitting
      if (checkAndOpenDialog()) {
        return;
      }

      setLoading(true);
      const result = await createMiniApp({
        ...data,
        accountId: account.id,
      });

      if (!result.success) {
        if (result.reason === 'limit_reached' || result.reason === 'no_active_subscription') {
          openLimitDialog();
          setLoading(false);
          return;
        }
        throw new Error(result.error || 'Unknown error');
      }

      setOpen(false);
      form.reset();
      toast.success(
        <Trans i18nKey="miniapp:create:success">
          MiniApp created successfully
        </Trans>,
      );
    } catch (error) {
      toast.error(
        <Trans i18nKey="miniapp:create:error">Failed to create MiniApp</Trans>,
      );
    } finally {
      setLoading(false);
    }
  });

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="miniapp:create:title">Create New MiniApp</Trans>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="mini_app_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="miniapp:create:miniAppId">MiniApp ID</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="oa_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="miniapp:create:oaId">OA ID</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                type="button"
              >
                <Trans i18nKey="common:cancel">Cancel</Trans>
              </Button>
              <Button type="submit" loading={loading}>
                <Trans i18nKey="miniapp:create:submit">Create MiniApp</Trans>
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
      </Dialog>

      {/* Resource Limit Dialog */}
      <ResourceLimitDialog
        isOpen={isLimitDialogOpen}
        onClose={closeLimitDialog}
        reason={reason}
        resourceType="miniapp"
        accountSlug={accountSlug}
        current={current}
        limit={limit}
      />
    </>
  );
}
