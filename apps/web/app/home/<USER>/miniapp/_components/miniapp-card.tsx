'use client';

import { useState } from 'react';

import Link from 'next/link';

import {
  CreditCard,
  Eye,
  QrCode,
  Settings,
  TrendingUp,
  Users,
  X,
} from 'lucide-react';

import { formatCurrencyVND } from '@kit/shared/utils';
import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader } from '@kit/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Switch } from '@kit/ui/switch';
import { Trans } from '@kit/ui/trans';

import type { MiniAppWithStats } from '../_lib/types';
import { updateMiniAppStatus } from '../setup/customization/_lib/server/status.actions';

interface MiniAppCardProps {
  miniApp: MiniAppWithStats;
  accountSlug: string;
}

export function MiniAppCard({ miniApp, accountSlug }: MiniAppCardProps) {
  const [isQrDialogOpen, setIsQrDialogOpen] = useState(false);
  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(miniApp.shortUrl)}`;

  return (
    <>
      <Card className="h-full transition-all duration-200 hover:shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-4">
          <div className="flex items-center space-x-4">
            <div className="from-primary/10 to-primary/30 relative h-10 w-10 overflow-hidden rounded-xl bg-gradient-to-br md:h-12 md:w-12">
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-primary text-lg font-bold">
                  {miniApp.mini_app_id.slice(0, 2).toUpperCase()}
                </span>
              </div>
            </div>
            <div>
              <h3 className="font-semibold tracking-tight">
                {miniApp.mini_app_id}
              </h3>
              <p className="text-muted-foreground text-sm">
                {miniApp.theme?.name || (
                  <Trans i18nKey="miniapp:themeInfo">Theme Information</Trans>
                )}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground hidden text-xs sm:inline">
              <Trans i18nKey="miniapp:card.status">Status</Trans>
            </span>
            <Switch
              checked={miniApp.is_active}
              onCheckedChange={async (checked) => {
                try {
                  await updateMiniAppStatus(miniApp.id, checked, accountSlug);
                } catch (error) {
                  console.error('Failed to update status:', error);
                }
              }}
              className="ml-auto"
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex justify-between gap-3 md:mb-6 md:gap-6">
            <div className="flex h-full w-[48%] flex-col rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-3 shadow-sm">
              <div className="text-muted-foreground flex items-center justify-start mb-1">
                <div className="bg-blue-100 dark:bg-blue-900/30 rounded-full p-1.5 mr-2">
                  <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <span className="text-sm font-medium">
                  <Trans i18nKey="miniapp:card.users">Users</Trans>
                </span>
              </div>
              <p className="flex-grow text-left text-xl font-bold tracking-tight md:text-2xl my-1">
                {miniApp.users.toLocaleString()}
              </p>
              <div className="flex items-center justify-start text-emerald-600 mt-1">
                <div className="bg-emerald-100 dark:bg-emerald-900/30 rounded-full p-0.5 mr-1.5">
                  <TrendingUp className="h-3 w-3" />
                </div>
                <span className="text-xs font-medium">
                  +{miniApp.newUsersToday}{' '}
                  <Trans i18nKey="common:today">today</Trans>
                </span>
              </div>
            </div>
            <div className="flex h-full w-[48%] flex-col rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-3 shadow-sm">
              <div className="text-muted-foreground flex items-center justify-end mb-1">
                <span className="text-sm font-medium">
                  <Trans i18nKey="miniapp:card.revenue">Revenue</Trans>
                </span>
                <div className="bg-purple-100 dark:bg-purple-900/30 rounded-full p-1.5 ml-2">
                  <CreditCard className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <p className="flex-grow text-right text-xl font-bold tracking-tight md:text-2xl my-1">
                {formatCurrencyVND(miniApp.revenues || 0)}
              </p>
              <div className="text-muted-foreground flex items-center justify-end mt-1">
                <span className="text-xs font-medium">
                  {miniApp.orders.toLocaleString()}{' '}
                  <Trans i18nKey="miniapp:card.orders">orders</Trans>
                </span>
                <div className="bg-slate-100 dark:bg-slate-800 rounded-full p-0.5 ml-1.5">
                  <CreditCard className="h-3 w-3" />
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap justify-between gap-2 sm:flex-nowrap sm:gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsQrDialogOpen(true)}
              className="hover:bg-primary hover:text-primary-foreground min-w-[80px] flex-1 transition-colors"
            >
              <QrCode className="mr-2 h-4 w-4" />
              <Trans i18nKey="miniapp:card.viewQR">QR Code</Trans>
            </Button>
            <Button
              variant="outline"
              size="sm"
              asChild
              className="hover:bg-primary hover:text-primary-foreground min-w-[80px] flex-1 transition-colors"
            >
              <Link
                href={`/home/<USER>/miniapp/setup/customization?editThemeId=${miniApp.id}`}
              >
                <Settings className="mr-2 h-4 w-4" />
                <Trans i18nKey="miniapp:card.settings">Settings</Trans>
              </Link>
            </Button>
            {miniApp.status === 'active' && (
              <Button
                variant="outline"
                size="sm"
                asChild
                className="hover:bg-primary hover:text-primary-foreground min-w-[80px] flex-1 transition-colors"
              >
                <Link
                  href={`/home/<USER>/miniapp/${miniApp.id}/preview`}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  <Trans i18nKey="miniapp:card.preview">Preview</Trans>
                </Link>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog open={isQrDialogOpen} onOpenChange={setIsQrDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              <Trans i18nKey="miniapp:qrCode.title">MiniApp QR Code</Trans>
            </DialogTitle>
            <DialogClose className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4">
            <img
              src={qrCodeUrl}
              alt="Preview QR Code"
              className="h-40 w-40 md:h-48 md:w-48"
            />
            <p className="text-muted-foreground text-center text-sm">
              <Trans i18nKey="miniapp:qrCode.description">
                Scan this QR code to open your MiniApp
              </Trans>
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
