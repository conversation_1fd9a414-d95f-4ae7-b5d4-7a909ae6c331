'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Plus } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { ResourceLimitDialog } from '../../_components/resource-limit-dialog';

interface MiniAppActionsProps {
  accountSlug: string;
  canCreate: boolean;
  reason?: string;
  resourceCurrent?: number;
  resourceLimit?: number;
}

export function MiniAppActions({
  accountSlug,
  canCreate,
  reason,
  resourceCurrent,
  resourceLimit,
}: MiniAppActionsProps) {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleNewMiniAppClick = async () => {
    try {
      if (canCreate) {
        router.push(`/home/<USER>/miniapp/setup?step=1`);
      } else {
        setIsDialogOpen(true);
      }
    } catch (error) {
      console.error('Error navigating to new miniapp page:', error);
      // Fallback to opening dialog
      setIsDialogOpen(true);
    }
  };

  return (
    <>
      <Button
        data-testid={canCreate ? 'create-miniapp-button' : 'upgrade-plan-button'}
        onClick={handleNewMiniAppClick}
        variant={!canCreate ? 'outline' : 'default'}
        className={
          !canCreate
            ? 'bg-amber-50 text-amber-600 hover:bg-amber-100 hover:text-amber-700'
            : canCreate
            ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-sm hover:shadow-md transition-all duration-200'
            : ''
        }
      >
        <Plus className="mr-2 h-4 w-4" />
        {!canCreate && reason === 'limit_reached' ? (
          <Trans
            i18nKey="resources:limitReached"
            values={{
              current: resourceCurrent,
              limit: resourceLimit,
            }}
          />
        ) : !canCreate && reason === 'no_active_subscription' ? (
          <Trans i18nKey="resources:subscriptionRequired" />
        ) : !canCreate ? (
          <Trans i18nKey="resources:upgradeRequired" />
        ) : (
          <Trans i18nKey="miniapp:createNew" />
        )}
      </Button>

      <ResourceLimitDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        reason={reason || 'no_permission'}
        resourceType="miniapp"
        accountSlug={accountSlug}
        current={resourceCurrent}
        limit={resourceLimit}
      />
    </>
  );
}
