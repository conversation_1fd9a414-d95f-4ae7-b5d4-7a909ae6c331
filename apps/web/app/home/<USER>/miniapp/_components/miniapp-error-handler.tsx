'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { ResourceLimitDialog } from '../../_components/resource-limit-dialog';

interface MiniAppErrorHandlerProps {
  accountSlug: string;
}

export function MiniAppErrorHandler({ accountSlug }: MiniAppErrorHandlerProps) {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  const [isDialogOpen, setIsDialogOpen] = useState(!!error);

  useEffect(() => {
    if (error) {
      setIsDialogOpen(true);
    }
  }, [error]);

  const handleClose = () => {
    setIsDialogOpen(false);
  };

  if (!error) {
    return null;
  }

  return (
    <ResourceLimitDialog
      isOpen={isDialogOpen}
      onClose={handleClose}
      reason={error}
      resourceType="miniapp"
      accountSlug={accountSlug}
    />
  );
}
