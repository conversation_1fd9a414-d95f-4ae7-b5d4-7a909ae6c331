import { Suspense } from 'react';

import { redirect } from 'next/navigation';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '../../../_components/team-account-layout-page-header';
import { WizardSteps } from '../_components/wizard-steps';
import { MiniAppPreview } from './_components/miniapp-preview';
import { PreviewActions } from './_components/preview-actions';

interface PreviewPageProps {
  params: Promise<{ account: string }>;
  searchParams: Promise<{ themeId?: string }>;
}

export default async function PreviewPage({
  params,
  searchParams,
}: PreviewPageProps) {
  const [{ account }, { themeId }] = await Promise.all([params, searchParams]);

  if (!themeId) {
    redirect(`/home/<USER>/miniapp/`);
  }

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account}
        title={
          <Trans i18nKey="miniapp:setup:preview">Preview Your MiniApp</Trans>
        }
        description={<AppBreadcrumbs />}
      />
      <PageBody>
        <div className="container py-6">
          <div className="mx-auto max-w-4xl">
            <WizardSteps currentStep="3" />

            <div className="mt-8 space-y-8">
              <Suspense fallback={<div>Loading preview...</div>}>
                <MiniAppPreview themeId={themeId} />
              </Suspense>

              <PreviewActions account={account} themeId={themeId} />
            </div>
          </div>
        </div>
      </PageBody>
    </>
  );
}
