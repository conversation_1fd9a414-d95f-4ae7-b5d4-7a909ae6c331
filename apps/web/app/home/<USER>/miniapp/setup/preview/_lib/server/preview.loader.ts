'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export type PreviewData = {
  miniAppId: string;
  templateId: string;
  themeId: string;
  shortLink?: string;
  qrCodeUrl?: string;
  logoUrl?: string;
  brandColor: string;
  accessibility: {
    modifierKeys: boolean;
    highContrast: boolean;
    autoplayVideos: 'system' | 'on' | 'off';
    openLinksInDesktop: boolean;
  };
};

export async function loadPreview(themeId: string): Promise<PreviewData> {
  const supabase = getSupabaseServerClient();

  // First get the account theme with left join
  const { data: accountTheme, error: themeError } = await supabase
    .from('account_themes')
    .select(
      `
      id,
      mini_app_id,
      template_id,
      config,
      template:themes(
        id,
        name,
        thumbnail_url,
        preview_url
      )
    `,
    )
    .eq('id', themeId)
    .maybeSingle();

  if (themeError) {
    console.error('Theme load error:', themeError);
    throw new Error('Failed to load theme');
  }

  if (!accountTheme) {
    throw new Error('Theme not found');
  }

  // Separately get the short link
  const { data: shortLink } = await supabase
    .from('short_links')
    .select('url')
    .eq('reference_id', themeId)
    .eq('reference_table', 'account_themes')
    .maybeSingle();

  const config = accountTheme.config || {};
  const url = shortLink?.url;
  const qrCodeUrl = url
    ? `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(url)}`
    : undefined;

  return {
    miniAppId: accountTheme.mini_app_id,
    templateId: accountTheme.template_id,
    themeId: accountTheme.id,
    qrCodeUrl,
    shortLink: url,
    logoUrl: config.logoUrl,
    brandColor: config.brandColor || '#000000',
    accessibility: {
      modifierKeys: config.accessibility?.modifierKeys ?? true,
      highContrast: config.accessibility?.highContrast ?? false,
      autoplayVideos: config.accessibility?.autoplayVideos ?? 'system',
      openLinksInDesktop: config.accessibility?.openLinksInDesktop ?? false,
    },
  };
}
