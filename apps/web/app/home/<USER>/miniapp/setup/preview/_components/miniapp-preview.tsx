'use client';

import { useEffect, useState } from 'react';

import { Card } from '@kit/ui/card';

import type { PreviewData } from '../_lib/server/preview.loader';
import { loadPreview } from '../_lib/server/preview.loader';

interface MiniAppPreviewProps {
  themeId: string;
}

export function MiniAppPreview({ themeId }: MiniAppPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPreviewData = async () => {
      try {
        const data = await loadPreview(themeId);
        setPreviewData(data);

        if (data.qrCodeUrl) {
          setQrCodeUrl(data.qrCodeUrl);
        } else if (data.shortLink) {
          const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(data.shortLink)}`;
          setQrCodeUrl(qrUrl);
        }
      } catch (err) {
        setError('Failed to load preview data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    loadPreviewData();
  }, [themeId]);

  if (isLoading) {
    return (
      <div className="flex h-[600px] items-center justify-center">
        <div className="text-sm text-gray-500">Loading preview...</div>
      </div>
    );
  }

  if (error || !previewData) {
    return (
      <div className="flex h-[600px] items-center justify-center">
        <div className="text-sm text-red-500">
          {error || 'Failed to load preview'}
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-sm space-y-4">
      <Card className="overflow-hidden bg-white p-6 shadow-md">
        <div className="flex flex-col items-center space-y-4">
          <div className="relative aspect-[9/16] w-[280px] rounded-xl border border-gray-200 bg-gray-50 p-4">
            {qrCodeUrl && (
              <div className="absolute inset-0 flex items-center justify-center">
                <img
                  src={qrCodeUrl}
                  alt="Preview QR Code"
                  className="h-40 w-40"
                  style={{ imageRendering: 'pixelated' }}
                />
              </div>
            )}
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500">
              Scan QR code to preview your MiniApp
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}
