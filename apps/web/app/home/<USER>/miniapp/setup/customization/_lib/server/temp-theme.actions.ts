'use server';

import { nanoid } from 'nanoid';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

interface ThemeConfig {
  name: string;
  brandColor: string;
  author_id?: string;
  template_id?: string;
  template?: any; // Template object from the database
  colors: {
    primary: { main: string; light: string; dark: string };
    secondary: { main: string; light: string; dark: string };
    accent: { main: string; light: string; dark: string };
    background: { default: string; paper: string };
    text: { primary: string; secondary: string };
  };
  typography: {
    fontFamily: string;
    headings: { fontFamily: string; fontWeight: string };
    body: { fontFamily: string; fontWeight: string };
  };
  layout: {
    maxWidth: string;
    containerPadding: string;
    header: { height: string; sticky: boolean };
    footer: { columns: number };
  };
  components: {
    button: { borderRadius: string; paddingY: string; paddingX: string };
    card: { borderRadius: string; shadow: string };
    input: { borderRadius: string; borderColor: string };
  };
  accessibility: {
    modifierKeys: boolean;
    highContrast: boolean;
    autoplayVideos: string;
    openLinksInDesktop: boolean;
  };
}

const DEFAULT_CONFIG: ThemeConfig = {
  name: '',
  brandColor: '#BA35A0',
  colors: {
    primary: { main: '#2563eb', light: '#3b82f6', dark: '#1d4ed8' },
    secondary: { main: '#4f46e5', light: '#6366f1', dark: '#4338ca' },
    accent: { main: '#f59e0b', light: '#fbbf24', dark: '#d97706' },
    background: { default: '#ffffff', paper: '#f3f4f6' },
    text: { primary: '#111827', secondary: '#4b5563' },
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    headings: { fontFamily: 'Inter, system-ui, sans-serif', fontWeight: '600' },
    body: { fontFamily: 'Inter, system-ui, sans-serif', fontWeight: '400' },
  },
  layout: {
    maxWidth: '1280px',
    containerPadding: '1rem',
    header: { height: '80px', sticky: true },
    footer: { columns: 4 },
  },
  components: {
    button: { borderRadius: '0.5rem', paddingY: '0.5rem', paddingX: '1rem' },
    card: { borderRadius: '0.75rem', shadow: 'sm' },
    input: { borderRadius: '0.5rem', borderColor: '#e5e7eb' },
  },
  accessibility: {
    modifierKeys: false,
    highContrast: false,
    autoplayVideos: 'system',
    openLinksInDesktop: false,
  },
};

async function getExistingTempTheme(
  accountId: string,
  themeId?: string,
  editThemeId?: string,
) {
  const supabase = getSupabaseServerClient();

  try {
    // Build query conditions
    const query = supabase
      .from('temp_themes')
      .select('*')
      .eq('account_id', accountId);

    if (editThemeId) {
      query.eq('account_theme_id', editThemeId);
    } else {
      query.is('account_theme_id', null);
    }

    if (themeId) {
      query.eq('theme_id', themeId);
    } else {
      query.is('theme_id', null);
    }

    // Execute query
    const { data: existingTempThemes, error: searchError } = await query
      .order('created_at', { ascending: false })
      .limit(1);

    if (searchError) {
      throw searchError;
    }

    return existingTempThemes?.[0];
  } catch (error) {
    console.error('Error searching for existing temp theme:', error);
    return null;
  }
}

export async function getInitialTheme(
  accountId: string,
  themeId?: string,
  editThemeId?: string,
): Promise<ThemeConfig> {
  try {
    const supabase = getSupabaseServerClient();

    if (editThemeId) {
      // Get from account_themes if editing
      const { data: accountTheme, error: accountError } = await supabase
        .from('account_themes')
        .select('*, theme:template_id(*)')
        .eq('id', editThemeId)
        .single();

      if (!accountError && accountTheme) {
        // Merge with DEFAULT_CONFIG to ensure all fields exist
        return {
          ...DEFAULT_CONFIG,
          name: accountTheme.name || DEFAULT_CONFIG.name,
          author_id: accountTheme.theme.author_id,
          ...accountTheme.config,
          colors: {
            ...DEFAULT_CONFIG.colors,
            ...(accountTheme.config?.colors || {}),
          },
          typography: {
            ...DEFAULT_CONFIG.typography,
            ...(accountTheme.config?.typography || {}),
          },
          layout: {
            ...DEFAULT_CONFIG.layout,
            ...(accountTheme.config?.layout || {}),
          },
          components: {
            ...DEFAULT_CONFIG.components,
            ...(accountTheme.config?.components || {}),
          },
          accessibility: {
            ...DEFAULT_CONFIG.accessibility,
            ...(accountTheme.config?.accessibility || {}),
          },
        };
      }
    }

    if (themeId) {
      // Get from themes table for new themes
      const { data: theme, error: themeError } = await supabase
        .from('themes')
        .select('*')
        .eq('id', themeId)
        .single();

      if (!themeError && theme) {
        return {
          ...DEFAULT_CONFIG,
          name: `${theme.name} (copy)`,
          author_id: theme.author_id,
          ...theme.config,
          colors: {
            ...DEFAULT_CONFIG.colors,
            ...(theme.config?.colors || {}),
          },
          typography: {
            ...DEFAULT_CONFIG.typography,
            ...(theme.config?.typography || {}),
          },
          layout: {
            ...DEFAULT_CONFIG.layout,
            ...(theme.config?.layout || {}),
          },
          components: {
            ...DEFAULT_CONFIG.components,
            ...(theme.config?.components || {}),
          },
          accessibility: {
            ...DEFAULT_CONFIG.accessibility,
            ...(theme.config?.accessibility || {}),
          },
        };
      }
    }

    // Return DEFAULT_CONFIG if no theme found
    return { ...DEFAULT_CONFIG };
  } catch (error) {
    console.error('Error getting theme from source:', error);
    return { ...DEFAULT_CONFIG }; // Fallback to default config
  }
}

export async function createOrUpdateTempTheme(
  accountId: string,
  config: ThemeConfig,
  themeId?: string,
  editThemeId?: string,
) {
  const supabase = getSupabaseServerClient();
  const previewToken = nanoid();
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);

  try {
    // Check for existing temp theme
    const existingTempTheme = await getExistingTempTheme(
      accountId,
      themeId,
      editThemeId,
    );

    // If we found an existing temp theme, update it
    if (existingTempTheme) {
      const { data: updatedTempTheme, error: updateError } = await supabase
        .from('temp_themes')
        .update({
          config,
          preview_token: previewToken,
          expires_at: expiresAt,
          updated_at: new Date(),
        })
        .eq('id', existingTempTheme.id)
        .select()
        .single();

      if (updateError) throw updateError;
      return updatedTempTheme;
    }

    // Create new temp theme
    const { data: tempTheme, error: insertError } = await supabase
      .from('temp_themes')
      .insert({
        account_id: accountId,
        theme_id: themeId || null,
        account_theme_id: editThemeId || null,
        config,
        preview_token: previewToken,
        expires_at: expiresAt,
      })
      .select()
      .single();

    if (insertError) throw insertError;
    return tempTheme;
  } catch (error) {
    console.error('Error in createOrUpdateTempTheme:', error);
    throw error;
  }
}
