'use server';

import { nanoid } from 'nanoid';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function saveFinalTheme(tempThemeId: string) {
  const supabase = getSupabaseServerClient();

  try {
    // Get temp theme data
    const { data: tempTheme, error: tempThemeError } = await supabase
      .from('temp_themes')
      .select('*, theme:themes(*, oa_config_id)')
      .eq('id', tempThemeId)
      .single();

    if (tempThemeError) throw tempThemeError;
    if (!tempTheme) throw new Error('Temp theme not found');

    // Kiểm tra quyền miniapps.manage

    // Kiểm tra gói subscription nếu theme là custom
    // const oaConfigId = tempTheme.theme.oa_config_id;
    /*    if (tempTheme.theme.type === 'custom') {
          const accountsApi = createAccountsApi(supabase);
          const subscription = await accountsApi.getSubscription(
            tempTheme.account_id,
          );
          if (!subscription) throw new Error('No active subscription found');

          const { product } = getProductPlanPair(
            billingConfig,
            subscription.plan_id,
          );
          if (product.id === 'free') {
            throw new Error('Custom themes not supported in Free plan');
          }

          // // Kiểm tra quyền oa.manage
          // if (
          //   !rolePermissions.data.some((perm) => perm.permission === 'oa.manage')
          // ) {
          //   throw new Error('Permission denied for private OA');
          // }

          // Tạo OA riêng nếu chưa có
          // if (!oaConfigId) {
          //   const { data: newOaConfig } = await supabase
          //     .from('oa_configurations')
          //     .insert({
          //       account_id: tempTheme.account_id,
          //       oa_type: 'private',
          //       oa_id: 'new_oa_id', // Giả lập
          //       app_id: 'new_app_id',
          //       secret_key: 'new_secret_key',
          //     })
          //     .select()
          //     .single();
          //
          //   oaConfigId = newOaConfig.id;
          // }
        }*/

    let savedTheme;

    if (tempTheme.account_theme_id) {
      // Update existing theme
      const { data, error: updateError } = await supabase
        .from('account_themes')
        .update({
          name: tempTheme.config.name,
          config: tempTheme.config,
          updated_at: new Date(),
        })
        .eq('id', tempTheme.account_theme_id)
        .select()
        .single();

      if (updateError) throw updateError;
      savedTheme = data;
    } else {
      // Create new theme
      const { data, error: insertError } = await supabase
        .from('account_themes')
        .insert({
          account_id: tempTheme.account_id,
          template_id: tempTheme.theme_id,
          name: tempTheme.config.name,
          config: tempTheme.config,
          is_active: true,
          oa_config_id: tempTheme.theme.oa_config_id,
          mini_app_id: tempTheme.theme?.mini_app_id,
        })
        .select()
        .single();

      if (insertError) throw insertError;
      savedTheme = data;
    }

    // Create URL for both cases
    const miniAppId = savedTheme.mini_app_id || tempTheme.theme?.mini_app_id;
    if (!miniAppId) throw new Error('MiniApp ID not found');
    const url = `https://zalo.me/s/${miniAppId}/?theme_id=${savedTheme.id}`;

    // Check if short link exists
    const { data: existingShortLink } = await supabase
      .from('short_links')
      .select()
      .eq('reference_id', savedTheme.id)
      .eq('reference_table', 'account_themes')
      .single();

    if (existingShortLink) {
      // Update existing short link
      const { error: updateError } = await supabase
        .from('short_links')
        .update({
          url,
          title: `Theme: ${savedTheme.name}`,
          description: `Preview link for theme ${savedTheme.name}`,
          updated_at: new Date(),
          metadata: {
            themeVersion: '1.0',
            miniAppId,
            updatedAt: new Date().toISOString(),
          },
        })
        .eq('id', existingShortLink.id);

      if (updateError) throw updateError;
    } else {
      // Create new short link
      const shortId = nanoid(8);
      const { error: shortLinkError } = await supabase
        .from('short_links')
        .insert({
          account_id: tempTheme.account_id,
          type: 'theme',
          reference_id: savedTheme.id,
          reference_table: 'account_themes',
          url,
          short_id: shortId,
          title: `Theme: ${savedTheme.name}`,
          description: `Preview link for theme ${savedTheme.name}`,
          metadata: {
            themeVersion: '1.0',
            miniAppId,
            createdAt: new Date().toISOString(),
          },
        });

      if (shortLinkError) throw shortLinkError;
    }

    // Clean up temp theme
    await supabase.from('temp_themes').delete().eq('id', tempThemeId);

    return {
      themeId: savedTheme.id,
      qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(url)}`,
      shortLink: url,
    };
  } catch (error) {
    console.error('SaveFinalTheme error:', error);
    throw error;
  }
}
