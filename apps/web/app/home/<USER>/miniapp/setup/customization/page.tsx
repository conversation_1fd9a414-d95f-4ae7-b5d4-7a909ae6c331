'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { debounce, isEqual } from 'lodash';
import { ArrowLeft, ChevronDown, ChevronUp, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@kit/ui/collapsible';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { toast } from '@kit/ui/sonner';
import { Switch } from '@kit/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { ThemeOaConnect } from './_components/theme-oa-connect';
import { saveFinalTheme } from './_lib/server/customization.actions';
import {
  createOrUpdateTempTheme,
  getInitialTheme,
} from './_lib/server/temp-theme.actions';
import { uploadLogo } from './_lib/server/upload-logo';

interface CustomizationPageProps {
  params: Promise<{
    account: string;
  }>;
  searchParams: Promise<{
    themeId?: string; // Template ID for creating new theme
    editThemeId?: string; // Account theme ID for editing
  }>;
}

// Thêm schema validation
const themeFormSchema = z.object({
  name: z
    .string()
    .min(3, 'Tên theme phải có ít nhất 3 ký tự')
    .max(50, 'Tên theme không được vượt quá 50 ký tự')
    .regex(
      /^[\p{L}\p{N}\s()-]+$/u,
      'Chỉ cho phép chữ cái (có dấu), số, dấu gạch ngang, dấu ngoặc đơn và khoảng trắng',
    )
    .transform((val) => val.trim()),
});

type ThemeFormValues = z.infer<typeof themeFormSchema>;

export default function CustomizationPage({
  params,
  searchParams,
}: CustomizationPageProps) {
  const router = useRouter();
  const { account, user } = useTeamAccountWorkspace();
  const supabase = useSupabase();
  const previousConfigRef = useRef<any>(null);
  const [isSaving, setIsSaving] = useState(false);

  const [pageParams, setPageParams] = useState<{
    themeId?: string;
    editThemeId?: string;
  }>({
    themeId: undefined,
    editThemeId: undefined,
  });

  // Add a ref to track initialization
  const isInitializedRef = useRef(false);

  // Load params
  useEffect(() => {
    const loadParams = async () => {
      const [resolvedParams, resolvedSearchParams] = await Promise.all([
        params,
        searchParams,
      ]);

      // Check if we have either themeId (for new theme) or editThemeId (for editing)
      if (!resolvedSearchParams.themeId && !resolvedSearchParams.editThemeId) {
        router.push(`/home/<USER>/miniapp/setup?step=1`);
        return;
      }

      setPageParams({
        themeId: resolvedSearchParams.themeId || '', // Template ID for new theme
        editThemeId: resolvedSearchParams.editThemeId, // Account theme ID for editing
      });
    };

    loadParams();
  }, [params, searchParams, router]);

  // State cho các trường
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [brandColor, setBrandColor] = useState('#BA35A0'); // Giá trị mặc định từ Figma
  const [modifierKeys, setModifierKeys] = useState(false);
  const [highContrast, setHighContrast] = useState(false);
  const [autoplayVideos, setAutoplayVideos] = useState('system');
  const [openLinksInDesktop, setOpenLinksInDesktop] = useState(false);

  // State cho các field trong config
  const [colors, setColors] = useState({
    primary: { main: '#2563eb', light: '#3b82f6', dark: '#1d4ed8' },
    secondary: { main: '#4f46e5', light: '#6366f1', dark: '#4338ca' },
    accent: { main: '#f59e0b', light: '#fbbf24', dark: '#d97706' },
    background: { default: '#ffffff', paper: '#f3f4f6' },
    text: { primary: '#111827', secondary: '#4b5563' },
  });
  const [typography, setTypography] = useState({
    fontFamily: 'Inter, system-ui, sans-serif',
    headings: { fontFamily: 'Inter, system-ui, sans-serif', fontWeight: '600' },
    body: { fontFamily: 'Inter, system-ui, sans-serif', fontWeight: '400' },
  });
  const [layout, setLayout] = useState({
    maxWidth: '1280px',
    containerPadding: '1rem',
    header: { height: '80px', sticky: true },
    footer: { columns: 4 },
  });
  const [components, setComponents] = useState({
    button: { borderRadius: '0.5rem', paddingY: '0.5rem', paddingX: '1rem' },
    card: { borderRadius: '0.75rem', shadow: 'sm' },
    input: { borderRadius: '0.5rem', borderColor: '#e5e7eb' },
  });

  // State cho collapsible sections
  const [isColorsOpen, setIsColorsOpen] = useState(false);
  const [isTypographyOpen, setIsTypographyOpen] = useState(false);
  const [isLayoutOpen, setIsLayoutOpen] = useState(false);
  const [isComponentsOpen, setIsComponentsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // State for QR codes
  const [previewQrCode, setPreviewQrCode] = useState<string | null>(null);
  const [devQrCode, setDevQrCode] = useState<string | null>(null);
  const [devUrl, setDevUrl] = useState<string>('');
  const [isThemeAuthor, setIsThemeAuthor] = useState(false);

  // Add tempTheme state
  const [tempTheme, setTempTheme] = useState<any>(null);

  // State for QR code preview dialog
  const [isQrDialogOpen, setIsQrDialogOpen] = useState(false);
  const [activeQrTab, setActiveQrTab] = useState('preview');

  // Form initialization
  const form = useForm<ThemeFormValues>({
    resolver: zodResolver(themeFormSchema),
    defaultValues: {
      name: '',
    },
    mode: 'onChange',
  });

  // Combine all config values into one object
  const configValues = {
    name: form.watch('name'),
    template: pageParams.themeId,
    brandColor,
    colors,
    typography,
    layout,
    components,
    accessibility: {
      modifierKeys,
      highContrast,
      autoplayVideos,
      openLinksInDesktop,
    },
    logoUrl: logoPreview, // Add logoUrl to config
  };

  // Tạo memoized save function với useCallback và lodash debounce
  const saveConfig = useCallback(
    debounce(async (config: typeof configValues) => {
      if (!account?.id || isSaving) return;

      if (
        previousConfigRef.current &&
        isEqual(previousConfigRef.current, config)
      ) {
        return;
      }

      setIsSaving(true);
      try {
        const tempTheme = await createOrUpdateTempTheme(
          account.id,
          config,
          pageParams.themeId,
          pageParams.editThemeId,
        );
        setTempTheme(tempTheme);
        previousConfigRef.current = config;

        const previewUrl = `https://zalo.me/s/${pageParams.themeId}/?tempThemeId=${tempTheme.id}`;
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(previewUrl)}`;
        setPreviewQrCode(qrCodeUrl);
      } catch (error) {
        console.error('Error saving temp theme:', error);
      } finally {
        setIsSaving(false);
      }
    }, 1000),
    [account?.id, pageParams, isSaving],
  );

  // Effect để theo dõi thay đổi của config
  useEffect(() => {
    // Skip the first render
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      return;
    }

    saveConfig(configValues);

    return () => {
      saveConfig.cancel();
    };
  }, [configValues, saveConfig]);

  // Function to generate developer QR code
  const generateDevQrCode = useCallback(() => {
    if (!devUrl || !tempTheme?.id) return;

    // Check if the URL already has query parameters
    const separator = devUrl.includes('?') ? '&' : '?';
    const fullUrl = `${devUrl}${separator}tempThemeId=${tempTheme.id}`;
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(fullUrl)}`;
    setDevQrCode(qrCodeUrl);
  }, [devUrl, tempTheme?.id]);

  // Handle form submission
  const onSubmit = async (data: ThemeFormValues) => {
    if (!account?.id) {
      toast.error('Không tìm thấy thông tin tài khoản');
      return;
    }

    if (!tempTheme?.id) {
      toast.error('Vui lòng đợi theme được lưu');
      return;
    }

    setLoading(true);
    try {
      const result = await saveFinalTheme(tempTheme.id);

      router.push(
        `/home/<USER>/miniapp/setup/preview?themeId=${result.themeId}`,
      );
      toast.success('Theme đã được lưu thành công');
    } catch (error) {
      console.error('Error saving theme:', error);
      toast.error(
        error instanceof Error ? error.message : 'Không thể lưu theme',
      );
    } finally {
      setLoading(false);
    }
  };

  // Cập nhật điều kiện disabled của nút Submit
  const isSubmitDisabled =
    !form.formState.isValid || loading || !tempTheme?.id || isSaving;

  // Thêm handler cho logo upload
  const handleLogoChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Kiểm tra file type
    if (!['image/svg+xml', 'image/png', 'image/jpeg'].includes(file.type)) {
      toast.error('Please upload SVG, PNG or JPG file');
      return;
    }

    // Kiểm tra file size (e.g., max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File size should be less than 2MB');
      return;
    }

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setLogoPreview(previewUrl);
    setLogoFile(file);

    // Upload logo to server
    try {
      const logoToast = toast.loading('Uploading logo...');

      // Upload logo and get the URL
      const logoUrl = await uploadLogo(account.id, file);

      // Update config with logo URL
      const updatedConfig = {
        ...configValues,
        logoUrl: logoUrl,
      };

      // Save the updated config
      saveConfig(updatedConfig);

      // Update the logo preview with the actual URL
      setLogoPreview(logoUrl);

      toast.success('Logo uploaded successfully', { id: logoToast });
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to upload logo',
      );

      // Keep the preview but mark that upload failed
      setLogoFile(null);
    }

    // Cleanup preview URL when component unmounts
    return () => URL.revokeObjectURL(previewUrl);
  };

  useEffect(() => {
    const initializeTheme = async () => {
      if (!account?.id || (!pageParams.themeId && !pageParams.editThemeId))
        return;

      try {
        const themeData = await getInitialTheme(
          account.id,
          pageParams.themeId,
          pageParams.editThemeId,
        );
        // Set form name và trigger validation
        form.setValue('name', themeData.name, {
          shouldValidate: true, // Trigger validation
          shouldTouch: true, // Đánh dấu field là đã touched
          shouldDirty: true, // Đánh dấu field là đã thay đổi
        });

        // Set all states in one batch to reduce renders
        setBrandColor(themeData.brandColor);
        setColors(themeData.colors);
        setTypography(themeData.typography);
        setLayout(themeData.layout);
        setComponents(themeData.components);

        // Set accessibility options
        setModifierKeys(themeData.accessibility.modifierKeys);
        setHighContrast(themeData.accessibility.highContrast);
        setAutoplayVideos(themeData.accessibility.autoplayVideos);
        setOpenLinksInDesktop(themeData.accessibility.openLinksInDesktop);

        // Set logo URL if available
        if (themeData.logoUrl) {
          setLogoPreview(themeData.logoUrl);
        }

        // Create initial temp theme
        const tempTheme = await createOrUpdateTempTheme(
          account.id,
          themeData,
          pageParams.themeId,
          pageParams.editThemeId,
        );

        setTempTheme(tempTheme);
        previousConfigRef.current = themeData;

        // Set QR code
        const previewUrl = `https://zalo.me/s/${pageParams.editThemeId || pageParams.themeId}/?tempThemeId=${tempTheme.id}`;
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(previewUrl)}`;
        setPreviewQrCode(qrCodeUrl);

        // Check if user is theme author
        if (user && themeData.author_id === user.id) {
          setIsThemeAuthor(true);
        }

        // Mark as initialized
        isInitializedRef.current = true;
      } catch (error) {
        console.error('Error initializing theme:', error);
        toast.error('Không thể tải thông tin theme');
      }
    };

    initializeTheme();
  }, [pageParams.themeId, pageParams.editThemeId, account?.id, supabase.auth]);

  return (
    <>
      {pageParams.editThemeId ? (
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="common:routes:miniapps" />}
          description={<AppBreadcrumbs />}
          account={account.slug}
        />
      ) : null}

      <PageBody>
        <div
          className={cn(
            'mx-auto max-w-3xl',
            pageParams.editThemeId && 'bg-card rounded-lg p-6 shadow-sm',
          )}
        >
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Zalo OA Connection Section */}
              {pageParams.themeId && (
                <ThemeOaConnect
                  themeId={pageParams.themeId}
                  accountId={account.slug}
                />
              )}

              {/* Theme Name Section */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">
                  <Trans i18nKey="miniapp:themeInfo">Thông tin Theme</Trans>
                </h2>

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <Trans i18nKey="miniapp:themeName">Tên Theme</Trans>
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Nhập tên theme của bạn"
                          className={cn(
                            'w-full',
                            form.formState.errors.name && 'border-red-500',
                          )}
                          onChange={(e) => {
                            field.onChange(e);
                            // Tự động lưu khi thay đổi
                            saveConfig({
                              ...configValues,
                              name: e.target.value,
                            });
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        <Trans i18nKey="miniapp:themeNameDesc">
                          Đặt một tên dễ nhớ và độc đáo cho theme của bạn
                        </Trans>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Logo */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <Trans i18nKey="miniapp:logo">Logo</Trans>
                </label>
                <p className="mb-2 text-sm text-gray-500">
                  <Trans i18nKey="miniapp:logoDesc">
                    Emblematic Corporate Identity Symbol
                  </Trans>
                </p>
                <div className="flex items-center space-x-4">
                  <div className="flex h-24 w-24 items-center justify-center rounded border border-dashed border-gray-300">
                    {logoPreview ? (
                      <img
                        src={logoPreview}
                        alt="Logo Preview"
                        className="h-full w-full object-contain"
                      />
                    ) : configValues.logoUrl ? (
                      <img
                        src={configValues.logoUrl}
                        alt="Logo Preview"
                        className="h-full w-full object-contain"
                      />
                    ) : (
                      <img
                        src="/images/mcp-logo.webp"
                        alt="Default Logo"
                        className="h-16 w-16"
                      />
                    )}
                  </div>
                  <div>
                    <Input
                      type="file"
                      accept="image/svg+xml,image/png,image/jpeg"
                      onChange={handleLogoChange}
                      className="hidden"
                      id="logo-upload"
                    />
                    <label
                      htmlFor="logo-upload"
                      className="cursor-pointer text-sm text-blue-600"
                    >
                      <Trans i18nKey="miniapp:uploadLogo">
                        Click or Drag & Drop
                      </Trans>
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      <Trans i18nKey="miniapp:logoFormat">
                        SVG, PNG, JPG (max. 800x400)
                      </Trans>
                    </p>
                  </div>
                </div>
              </div>

              {/* Brand Color */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <Trans i18nKey="miniapp:brandColor">Brand Color</Trans>
                </label>
                <p className="mb-2 text-sm text-gray-500">
                  <Trans i18nKey="miniapp:brandColorDesc">
                    Signature Palette Branding Element
                  </Trans>
                </p>
                <div className="flex items-center space-x-2">
                  <div
                    className="h-8 w-8 rounded"
                    style={{ backgroundColor: brandColor }}
                  />
                  <Input
                    type="color"
                    value={brandColor}
                    onChange={(e) => setBrandColor(e.target.value)}
                    className="h-12 w-12 border-none p-0"
                  />
                  <Input
                    value={brandColor}
                    onChange={(e) => setBrandColor(e.target.value)}
                    className="w-24"
                  />
                </div>
              </div>

              {/* Colors Section (Collapsible) */}
              <Collapsible open={isColorsOpen} onOpenChange={setIsColorsOpen}>
                <CollapsibleTrigger className="flex w-full items-center justify-between py-2">
                  <h3 className="text-lg font-medium">
                    <Trans i18nKey="miniapp:colors">Colors</Trans>
                  </h3>
                  {isColorsOpen ? (
                    <ChevronUp className="h-5 w-5" />
                  ) : (
                    <ChevronDown className="h-5 w-5" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4">
                  {/* Primary Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Primary Color
                    </label>
                    <div className="mt-2 flex space-x-2">
                      <Input
                        type="color"
                        value={colors.primary.main}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            primary: {
                              ...colors.primary,
                              main: e.target.value,
                            },
                          })
                        }
                        className="h-12 w-12 border-none p-0"
                      />
                      <Input
                        value={colors.primary.main}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            primary: {
                              ...colors.primary,
                              main: e.target.value,
                            },
                          })
                        }
                        className="w-24"
                      />
                    </div>
                  </div>
                  {/* Secondary Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Secondary Color
                    </label>
                    <div className="mt-2 flex space-x-2">
                      <Input
                        type="color"
                        value={colors.secondary.main}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            secondary: {
                              ...colors.secondary,
                              main: e.target.value,
                            },
                          })
                        }
                        className="h-12 w-12 border-none p-0"
                      />
                      <Input
                        value={colors.secondary.main}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            secondary: {
                              ...colors.secondary,
                              main: e.target.value,
                            },
                          })
                        }
                        className="w-24"
                      />
                    </div>
                  </div>
                  {/* Accent Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Accent Color
                    </label>
                    <div className="mt-2 flex space-x-2">
                      <Input
                        type="color"
                        value={colors.accent.main}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            accent: { ...colors.accent, main: e.target.value },
                          })
                        }
                        className="h-12 w-12 border-none p-0"
                      />
                      <Input
                        value={colors.accent.main}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            accent: { ...colors.accent, main: e.target.value },
                          })
                        }
                        className="w-24"
                      />
                    </div>
                  </div>
                  {/* Background Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Background Color
                    </label>
                    <div className="mt-2 flex space-x-2">
                      <Input
                        type="color"
                        value={colors.background.default}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            background: {
                              ...colors.background,
                              default: e.target.value,
                            },
                          })
                        }
                        className="h-12 w-12 border-none p-0"
                      />
                      <Input
                        value={colors.background.default}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            background: {
                              ...colors.background,
                              default: e.target.value,
                            },
                          })
                        }
                        className="w-24"
                      />
                    </div>
                  </div>
                  {/* Text Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Text Color
                    </label>
                    <div className="mt-2 flex space-x-2">
                      <Input
                        type="color"
                        value={colors.text.primary}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            text: { ...colors.text, primary: e.target.value },
                          })
                        }
                        className="h-12 w-12 border-none p-0"
                      />
                      <Input
                        value={colors.text.primary}
                        onChange={(e) =>
                          setColors({
                            ...colors,
                            text: { ...colors.text, primary: e.target.value },
                          })
                        }
                        className="w-24"
                      />
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* Typography Section (Collapsible) */}
              <Collapsible
                open={isTypographyOpen}
                onOpenChange={setIsTypographyOpen}
              >
                <CollapsibleTrigger className="flex w-full items-center justify-between py-2">
                  <h3 className="text-lg font-medium">
                    <Trans i18nKey="miniapp:typography">Typography</Trans>
                  </h3>
                  {isTypographyOpen ? (
                    <ChevronUp className="h-5 w-5" />
                  ) : (
                    <ChevronDown className="h-5 w-5" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Font Family
                    </label>
                    <Input
                      value={typography.fontFamily}
                      onChange={(e) =>
                        setTypography({
                          ...typography,
                          fontFamily: e.target.value,
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Headings Font Weight
                    </label>
                    <Input
                      type="number"
                      value={typography.headings.fontWeight}
                      onChange={(e) =>
                        setTypography({
                          ...typography,
                          headings: {
                            ...typography.headings,
                            fontWeight: e.target.value,
                          },
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Body Font Weight
                    </label>
                    <Input
                      type="number"
                      value={typography.body.fontWeight}
                      onChange={(e) =>
                        setTypography({
                          ...typography,
                          body: {
                            ...typography.body,
                            fontWeight: e.target.value,
                          },
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* Layout Section (Collapsible) */}
              <Collapsible open={isLayoutOpen} onOpenChange={setIsLayoutOpen}>
                <CollapsibleTrigger className="flex w-full items-center justify-between py-2">
                  <h3 className="text-lg font-medium">
                    <Trans i18nKey="miniapp:layout">Layout</Trans>
                  </h3>
                  {isLayoutOpen ? (
                    <ChevronUp className="h-5 w-5" />
                  ) : (
                    <ChevronDown className="h-5 w-5" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Max Width
                    </label>
                    <Input
                      value={layout.maxWidth}
                      onChange={(e) =>
                        setLayout({ ...layout, maxWidth: e.target.value })
                      }
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Container Padding
                    </label>
                    <Input
                      value={layout.containerPadding}
                      onChange={(e) =>
                        setLayout({
                          ...layout,
                          containerPadding: e.target.value,
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Header Height
                    </label>
                    <Input
                      value={layout.header.height}
                      onChange={(e) =>
                        setLayout({
                          ...layout,
                          header: { ...layout.header, height: e.target.value },
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Footer Columns
                    </label>
                    <Input
                      type="number"
                      value={layout.footer.columns}
                      onChange={(e) =>
                        setLayout({
                          ...layout,
                          footer: {
                            ...layout.footer,
                            columns: Number(e.target.value),
                          },
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* Components Section (Collapsible) */}
              <Collapsible
                open={isComponentsOpen}
                onOpenChange={setIsComponentsOpen}
              >
                <CollapsibleTrigger className="flex w-full items-center justify-between py-2">
                  <h3 className="text-lg font-medium">
                    <Trans i18nKey="miniapp:components">Components</Trans>
                  </h3>
                  {isComponentsOpen ? (
                    <ChevronUp className="h-5 w-5" />
                  ) : (
                    <ChevronDown className="h-5 w-5" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Button Border Radius
                    </label>
                    <Input
                      value={components.button.borderRadius}
                      onChange={(e) =>
                        setComponents({
                          ...components,
                          button: {
                            ...components.button,
                            borderRadius: e.target.value,
                          },
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Card Border Radius
                    </label>
                    <Input
                      value={components.card.borderRadius}
                      onChange={(e) =>
                        setComponents({
                          ...components,
                          card: {
                            ...components.card,
                            borderRadius: e.target.value,
                          },
                        })
                      }
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Input Border Color
                    </label>
                    <div className="mt-2 flex space-x-2">
                      <Input
                        type="color"
                        value={components.input.borderColor}
                        onChange={(e) =>
                          setComponents({
                            ...components,
                            input: {
                              ...components.input,
                              borderColor: e.target.value,
                            },
                          })
                        }
                        className="h-12 w-12 border-none p-0"
                      />
                      <Input
                        value={components.input.borderColor}
                        onChange={(e) =>
                          setComponents({
                            ...components,
                            input: {
                              ...components.input,
                              borderColor: e.target.value,
                            },
                          })
                        }
                        className="w-24"
                      />
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </form>

            {/* Accessibility Section */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">
                <Trans i18nKey="miniapp:accessibility">Accessibility</Trans>
              </h2>

              {/* Shortcuts Require Modifier */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    <Trans i18nKey="miniapp:modifierKeys">
                      Shortcuts require modifier
                    </Trans>
                  </label>
                  <p className="text-sm text-gray-500">
                    <Trans i18nKey="miniapp:modifierKeysDesc">
                      Enable modifier keys for quick keyboard shortcuts.
                    </Trans>
                  </p>
                </div>
                <Switch
                  checked={modifierKeys}
                  onCheckedChange={setModifierKeys}
                />
              </div>

              {/* High Color Contrast */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    <Trans i18nKey="miniapp:highContrast">
                      High color contrast
                    </Trans>
                  </label>
                  <p className="text-sm text-gray-500">
                    <Trans i18nKey="miniapp:highContrastDesc">
                      Improve readability with high-contrast interface colors.
                    </Trans>
                  </p>
                </div>
                <Switch
                  checked={highContrast}
                  onCheckedChange={setHighContrast}
                />
              </div>

              {/* Autoplay Videos */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    <Trans i18nKey="miniapp:autoplayVideos">
                      Autoplay videos
                    </Trans>
                  </label>
                  <p className="text-sm text-gray-500">
                    <Trans i18nKey="miniapp:autoplayVideosDesc">
                      Choose preferences for automatic video playback.
                    </Trans>
                  </p>
                </div>
                <Select
                  value={autoplayVideos}
                  onValueChange={setAutoplayVideos}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="system">
                      <Trans i18nKey="miniapp:autoplayVideosSystem">
                        System preferences
                      </Trans>
                    </SelectItem>
                    <SelectItem value="always">
                      <Trans i18nKey="miniapp:autoplayVideosAlways">
                        Always
                      </Trans>
                    </SelectItem>
                    <SelectItem value="never">
                      <Trans i18nKey="miniapp:autoplayVideosNever">Never</Trans>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Open Links in Desktop */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    <Trans i18nKey="miniapp:openLinksInDesktop">
                      Open links in Desktop
                    </Trans>
                  </label>
                  <p className="text-sm text-gray-500">
                    <Trans i18nKey="miniapp:openLinksInDesktopDesc">
                      Links in the desktop app for convenience.
                    </Trans>
                  </p>
                </div>
                <Switch
                  checked={openLinksInDesktop}
                  onCheckedChange={setOpenLinksInDesktop}
                />
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  router.push(
                    pageParams.editThemeId
                      ? `/home/<USER>/miniapp`
                      : `/home/<USER>/miniapp/setup?step=1`,
                  )
                }
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <Trans i18nKey="common:back">Quay lại</Trans>
              </Button>

              {/* Sửa lại button submit */}
              <Button
                type="submit"
                disabled={isSubmitDisabled}
                onClick={(e) => {
                  e.preventDefault();
                  form.handleSubmit(onSubmit)();
                }}
                className={cn(
                  'relative',
                  (loading || isSaving) && 'cursor-not-allowed opacity-70',
                )}
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <span className="animate-spin">⏳</span>
                    <Trans i18nKey="common:saving">Đang lưu...</Trans>
                  </div>
                ) : (
                  <Trans i18nKey="common:next">Tiếp tục</Trans>
                )}
              </Button>
            </div>
          </Form>

          {/* Auto-save Indicator */}
          {isSaving && (
            <div className="fixed right-4 bottom-4 flex items-center space-x-2 rounded-md bg-black/80 px-4 py-2 text-white">
              <span className="animate-spin">⏳</span>
              <span>
                <Trans i18nKey="common:autoSaving">Đang tự động lưu...</Trans>
              </span>
            </div>
          )}

          {/* QR Code Preview Dialog */}
          {previewQrCode && (
            <Dialog open={isQrDialogOpen} onOpenChange={setIsQrDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="fixed right-4 bottom-4 flex items-center gap-2"
                >
                  <img src={previewQrCode} alt="QR" className="h-6 w-6" />
                  <span>
                    <Trans i18nKey="miniapp:previewQR">Preview QR Live</Trans>
                  </span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>
                    <Trans i18nKey="miniapp:scanToPreview">
                      Scan to Preview
                    </Trans>
                  </DialogTitle>
                  <DialogClose className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none">
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close</span>
                  </DialogClose>
                </DialogHeader>

                <Tabs
                  value={activeQrTab}
                  onValueChange={setActiveQrTab}
                  className="w-full"
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="preview">
                      <Trans i18nKey="miniapp:previewTab">Preview</Trans>
                    </TabsTrigger>
                    {isThemeAuthor && (
                      <TabsTrigger value="developer">
                        <Trans i18nKey="miniapp:developerTab">Developer</Trans>
                      </TabsTrigger>
                    )}
                  </TabsList>

                  <TabsContent value="preview" className="mt-4">
                    <div className="flex flex-col items-center gap-4">
                      <img
                        src={previewQrCode}
                        alt="Preview QR Code"
                        className="h-48 w-48"
                      />
                      <p className="text-center text-sm text-gray-500">
                        <Trans i18nKey="miniapp:scanInstructions">
                          Scan this QR code with your mobile device to preview
                          the customized mini app
                        </Trans>
                      </p>
                    </div>
                  </TabsContent>

                  {isThemeAuthor && (
                    <TabsContent value="developer" className="mt-4">
                      <div className="flex flex-col gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">
                            <Trans i18nKey="miniapp:devUrlLabel">
                              Development URL
                            </Trans>
                          </label>
                          <div className="flex gap-2">
                            <Input
                              value={devUrl}
                              onChange={(e) => setDevUrl(e.target.value)}
                              placeholder="https://zalo.me/app/link/zapps/..."
                              className="flex-1"
                            />
                            <Button
                              onClick={generateDevQrCode}
                              disabled={!devUrl || !tempTheme?.id}
                              type="button"
                            >
                              <Trans i18nKey="miniapp:generateQR">
                                Generate
                              </Trans>
                            </Button>
                          </div>
                          <p className="text-xs text-gray-500">
                            <Trans i18nKey="miniapp:devUrlDesc">
                              Enter your development URL to generate a QR code
                              with the tempThemeId parameter
                            </Trans>
                          </p>
                        </div>

                        {devQrCode && (
                          <div className="mt-4 flex flex-col items-center gap-4">
                            <img
                              src={devQrCode}
                              alt="Developer QR Code"
                              className="h-48 w-48"
                            />
                            <p className="text-center text-sm text-gray-500">
                              <Trans i18nKey="miniapp:devScanInstructions">
                                Scan this QR code to test your theme in the
                                development environment
                              </Trans>
                            </p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  )}
                </Tabs>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </PageBody>
    </>
  );
}
