'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { toast } from 'sonner';

import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { publishTheme } from '../_lib/server/publish.actions';

interface PreviewStepProps {
  account: string;
  themeId: string;
}

export function PreviewStep({ account, themeId }: PreviewStepProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handlePublish = async () => {
    setLoading(true);
    try {
      await publishTheme(themeId, account);
      toast.success('Theme published successfully');
      router.push(`/home/<USER>/miniapp`);
    } catch (error) {
      toast.error('Failed to publish theme');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="mx-auto max-w-md">
      <CardHeader>
        <CardTitle className="text-center">
          <Trans i18nKey="miniapp:previewTitle">
            Mini App is now ready to publish
          </Trans>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center">
        <div className="mb-6 h-48 w-48">
          <img
            src={`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(
              `https://zalo.me/s/${themeId}`,
            )}`}
            alt="QR Code"
            className="h-full w-full"
          />
        </div>
        <Button onClick={handlePublish} disabled={loading}>
          {loading ? (
            <Trans i18nKey="miniapp:publishing">Publishing...</Trans>
          ) : (
            <Trans i18nKey="miniapp:publish">Publish</Trans>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
