'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function publishTheme(themeId: string, accountSlug: string) {
  const supabase = getSupabaseServerClient();

  try {
    const { error } = await supabase
      .from('account_themes')
      .update({
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', themeId);

    if (error) throw error;

    // Revalidate related paths
    revalidatePath(`/home/<USER>/miniapp`);
    revalidatePath(`/home/<USER>/miniapp/setup`);
  } catch (error) {
    console.error('Error publishing theme:', error);
    throw new Error('Failed to publish theme');
  }
}
