import { getSupabaseServerClient } from '@kit/supabase/server-client';

export interface Theme {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  type: string;
  category: string;
  config: Record<string, any>;
}

export async function loadThemes() {
  const supabase = getSupabaseServerClient();

  const { data: themes, error } = await supabase
    .from('themes')
    .select('*')
    .eq('type', 'default')
    .order('created_at', { ascending: true });

  if (error) throw error;

  return themes as Theme[];
}
