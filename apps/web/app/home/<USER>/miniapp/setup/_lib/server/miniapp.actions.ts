'use server';

import { revalidatePath } from 'next/cache';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { checkCanCreateResource } from '~/home/<USER>/_lib/server/resource-access';
import { incrementMiniAppCounter } from '~/home/<USER>/miniapp/_lib/server/resource-access';

export async function createMiniApp({
  account,
  themeId,
}: {
  account: string;
  themeId: string;
}) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // Get account ID from slug
    const { data: accountData } = await supabase
      .from('accounts')
      .select('id')
      .eq('slug', account)
      .single();

    if (!accountData) {
      return { success: false, error: 'Account not found' };
    }

    // Kiểm tra quyền tạo mini app
    const { canCreate, reason } = await checkCanCreateResource(accountData.id, 'miniapp');

    if (!canCreate) {
      logger.info(
        { accountId: accountData.id, reason },
        'Cannot create mini app due to resource limits'
      );
      return { success: false, reason };
    }

  // Get default theme info
  const { data: theme } = await supabase
    .from('themes')
    .select('*')
    .eq('id', themeId)
    .eq('type', 'default')
    .single();

  if (!theme) {
    return { success: false, error: 'Theme not found' };
  }

  // Create account theme from default theme
  const { data: accountTheme } = await supabase
    .from('account_themes')
    .insert({
      account_id: accountData.id,
      template_id: themeId,
      name: theme.name,
      config: theme.config,
      zalo_oa_id: theme.zalo_oa_id,
      zalo_app_id: theme.zalo_app_id,
      zalo_secret_key: theme.zalo_secret_key,
      mini_app_id: theme.mini_app_id,
      is_active: true,
    })
    .select()
    .single();

  if (!accountTheme) {
    return { success: false, error: 'Failed to create account theme' };
  }

  // Tăng counter mini app
  await incrementMiniAppCounter(accountData.id);

  // Create dynamic URL with parameters
  const url = `https://zalo.me/s/${theme.mini_app_id}/?team_id=${accountData.id}&theme_id=${accountTheme.id}`;

  // Create short link
  const { data: shortLink } = await supabase
    .from('short_links')
    .insert({
      account_id: accountData.id,
      theme_id: accountTheme.id,
      url,
    })
    .select()
    .single();

  if (!shortLink) {
    return { success: false, error: 'Failed to create short link' };
  }

  revalidatePath(`/home/<USER>/miniapp`);

  return {
    success: true,
    qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(url)}`
  };
  } catch (error) {
    logger.error(
      { error, account },
      'Error in createMiniApp'
    );
    return { success: false, error: 'unknown_error' };
  }
}
