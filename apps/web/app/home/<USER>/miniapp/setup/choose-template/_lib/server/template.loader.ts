'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export interface Template {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  preview_url: string;
  type: 'free' | 'paid' | 'custom' | 'default';
  category: string;
  config: {
    template: string;
    brandColor: string;
    accessibility: {
      modifierKeys: boolean;
      highContrast: boolean;
      autoplayVideos: string;
      openLinksInDesktop: boolean;
    };
  };
  is_premium: boolean;
  version: string;
}

export async function loadTemplates() {
  const supabase = getSupabaseServerClient();

  const { data: templates, error } = await supabase.from('themes').select('*');

  if (error) throw error;

  return templates.map((template) => ({
    ...template,
    previewImage: template.thumbnail_url, // Map để tương thích với interface cũ
  })) as Template[];
}
