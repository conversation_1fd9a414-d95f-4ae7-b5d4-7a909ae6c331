import { useState } from 'react';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

interface Template {
  id: string;
  name: string;
  previewImage: string;
  description: string;
}
const DEFAULT_TEMPLATES: Template[] = [
  {
    id: 'online-market',
    name: 'Online Market',
    description:
      'Perfect for e-commerce stores with product listings and shopping cart',
    previewImage: '/images/templates/online-market.png',
  },
  {
    id: 'online-menu',
    name: 'Online Menu & Ordering',
    description: 'Ideal for restaurants and food delivery services',
    previewImage: '/images/templates/online-menu.png',
  },
  {
    id: 'fashion',
    name: 'Fashion Store',
    description: 'Designed for fashion and apparel businesses',
    previewImage: '/images/templates/fashion.png',
  },
];

export function useTemplates() {
  const [templates] = useState<Template[]>(DEFAULT_TEMPLATES);

  const selectTemplate = async (
    miniAppId: string,
    accountId: string,
    templateId: string,
  ) => {
    const supabase = useSupabase();
    // Create theme based on template
    const { data: theme, error: themeError } = await supabase
      .from('account_themes')
      .insert({
        account_id: accountId,
        template_id: templateId,
        config: {
          template: templateId,
          brandColor: '#000000',
          accessibility: {
            modifierKeys: true,
            highContrast: false,
            autoplayVideos: 'system',
            openLinksInDesktop: false,
          },
        },
      })
      .select()
      .single();

    if (themeError) throw themeError;

    // Update miniapp with theme
    const { error: miniappError } = await supabase
      .from('custom_apps')
      .update({ theme_id: theme.id })
      .eq('mini_app_id', miniAppId)
      .eq('account_id', accountId);

    if (miniappError) throw miniappError;
  };

  return {
    templates,
    selectTemplate,
  };
}
