'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { toast } from 'sonner';

import { useMiniAppLimitDialog } from '~/home/<USER>/miniapp/_lib/hooks/use-miniapp-limit-dialog';
import { ResourceLimitDialog } from '~/home/<USER>/_components/resource-limit-dialog';
import { createMiniApp } from '../../_lib/server/miniapp.actions';

interface NavigationButtonsProps {
  account: string;
  currentStep: string;
  themeId?: string;
  miniAppId?: string;
  isDisabled?: boolean;
}

export function NavigationButtons({
  account,
  currentStep,
  themeId,
  miniAppId,
  isDisabled = false,
}: NavigationButtonsProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const {
    isOpen: isLimitDialogOpen,
    openDialog: openLimitDialog,
    closeDialog: closeLimitDialog,
    checkAndOpenDialog,
    accountSlug,
    reason,
    current,
    limit,
  } = useMiniAppLimitDialog();

  const handleNext = async () => {
    // If we're on step 1 and moving to step 2, we need to check if the user can create a mini app
    if (currentStep === '1' && themeId) {
      // Check if the user has reached their limit
      if (checkAndOpenDialog()) {
        return;
      }

      try {
        setLoading(true);
        const result = await createMiniApp({
          account,
          themeId,
        });

        if (!result.success) {
          if (result.reason === 'limit_reached' || result.reason === 'no_active_subscription') {
            openLimitDialog();
            setLoading(false);
            return;
          }
          throw new Error(result.error || 'Unknown error');
        }

        // Navigate to the next step
        const nextStep = parseInt(currentStep) + 1;
        if (nextStep > 3) return;

        const baseUrl = `/home/<USER>/miniapp/setup`;
        let nextUrl = `${baseUrl}?step=${nextStep}`;

        if (themeId) nextUrl += `&themeId=${themeId}`;
        if (miniAppId) nextUrl += `&miniAppId=${miniAppId}`;

        router.push(nextUrl);
      } catch (error) {
        toast.error(
          <Trans i18nKey="miniapp:setup:error">Failed to create mini app</Trans>
        );
      } finally {
        setLoading(false);
      }
    } else {
      // For other steps, just navigate
      const nextStep = parseInt(currentStep) + 1;
      if (nextStep > 3) return;

      const baseUrl = `/home/<USER>/miniapp/setup`;
      let nextUrl = `${baseUrl}?step=${nextStep}`;

      if (themeId) nextUrl += `&themeId=${themeId}`;
      if (miniAppId) nextUrl += `&miniAppId=${miniAppId}`;

      router.push(nextUrl);
    }
  };

  const isLastStep = currentStep === '3';

  return (
    <>
      <div className="mt-6 flex justify-end">
        <Button
          onClick={handleNext}
          disabled={isDisabled || isLastStep || loading}
          loading={loading}
          className="bg-blue-600 px-8 text-white hover:bg-blue-700"
        >
          {isLastStep ? (
            <Trans i18nKey="common:finish">Finish</Trans>
          ) : (
            <Trans i18nKey="common:next">Next</Trans>
          )}
        </Button>
      </div>

      {/* Resource Limit Dialog */}
      <ResourceLimitDialog
        isOpen={isLimitDialogOpen}
        onClose={closeLimitDialog}
        reason={reason}
        resourceType="miniapp"
        accountSlug={accountSlug}
        current={current}
        limit={limit}
      />
    </>
  );
}
