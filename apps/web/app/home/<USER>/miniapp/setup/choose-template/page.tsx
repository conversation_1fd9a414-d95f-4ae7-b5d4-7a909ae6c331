import { TemplateSelection } from './_components/template-selection';
import { loadTemplates } from './_lib/server/template.loader';

interface ChooseTemplatePageProps {
  params: {
    account: string;
  };
  searchParams: {
    miniAppId: string;
    step?: string;
  };
}

export default async function ChooseTemplatePage({
  params,
  searchParams,
}: ChooseTemplatePageProps) {
  const { account } = params;
  const { miniAppId, step } = searchParams;
  const currentStep = step || '1';
  const templates = await loadTemplates();

  return (
    <TemplateSelection
      account={account}
      templates={templates}
      miniAppId={miniAppId}
      currentStep={currentStep}
    />
  );
}
