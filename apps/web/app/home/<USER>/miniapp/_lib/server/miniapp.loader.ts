'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export type MiniApp = {
  id: string;
  mini_app_id: string | null;
  account_id: string;
  name: string;
  type: string;
  config: any;
  zalo_oa_id: string | null;
  users?: number;
  orders?: number;
  revenues?: number;
  newUsersToday?: number;
};

export async function loadMiniApps(
  accountId: string,
  search?: string,
  statusFilter?: string,
) {
  const supabase = getSupabaseServerClient();

  let query = supabase
    .from('account_themes')
    .select(
      `
      id,
      name,
      account_id,
      config,
      is_active,
      mini_app_id,
      oa_config_id,
      oa_config:oa_configurations!oa_config_id (
        oa_id
      ),
      template:themes!inner (
        id,
        name,
        type,
        config
      )
    `,
    )
    .eq('account_id', accountId);

  if (search) {
    query = query.ilike('name', `%${search}%`);
  }
  if (statusFilter) {
    query = query.eq('status', statusFilter);
  }

  const { data: miniApps, error } = await query;

  if (error) throw error;
  // Transform the data to match the MiniApp type
  const miniAppsWithStats = miniApps.map((app) => ({
    id: app.id,
    mini_app_id: app.mini_app_id,
    account_id: app.account_id,
    name: app.name,
    is_active: app.is_active,
    config: app.config,
    zalo_oa_id: app.oa_config?.oa_id ?? null,
    shortUrl: `https://zalo.me/s/${app.mini_app_id}/?theme_id=${app.id}`,
    theme: {
      id: app.template.id,
      name: app.template.name,
      config: app.template.config,
    },
    users: 0,
    orders: 0,
    revenues: 0,
    newUsersToday: 0,
  }));
  return miniAppsWithStats;
}
