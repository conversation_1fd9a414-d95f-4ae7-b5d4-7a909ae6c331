'use server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

import { checkCanCreateResource } from '~/home/<USER>/_lib/server/resource-access';

/**
 * Kiểm tra quyền tạo mini app mới
 * @param accountId ID của tài khoản
 */
export async function checkCanCreateMiniApp(accountId: string) {
  return checkCanCreateResource(accountId, 'miniapp');
}

/**
 * Lấy thông tin giới hạn và sử dụng mini app
 * @param accountId ID của tài khoản
 */
export async function getMiniAppUsage(accountId: string) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // Lấy thông tin sử dụng hiện tại
    const { data: stats, error: statsError } = await supabase
      .from('account_usage_stats')
      .select('counters')
      .eq('account_id', accountId)
      .single();

    if (statsError) {
      logger.error(
        { error: statsError, accountId },
        'Error fetching mini app usage stats'
      );
      return {
        current: 0,
        limit: 0,
        error: 'stats_error',
      };
    }

    // Lấy thông tin subscription
    const teamAccountsApi = createTeamAccountsApi(supabase);
    const subscription = await teamAccountsApi.getSubscription(accountId);

    const limit = subscription?.metadata?.miniapp_limit ?? 0;
    const current = stats?.counters?.miniapp || 0;

    return {
      current,
      limit,
      hasSubscription: !!subscription,
      canCreate: limit === -1 || current < limit,
    };
  } catch (error) {
    logger.error({ error, accountId }, 'Error getting mini app usage');
    return {
      current: 0,
      limit: 0,
      error: 'unknown_error',
    };
  }
}

/**
 * Tăng counter mini app
 * @param accountId ID của tài khoản
 */
export async function incrementMiniAppCounter(accountId: string) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    const { error } = await supabase.rpc('increment_account_counter', {
      p_account_id: accountId,
      p_counter_name: 'miniapp',
      p_increment: 1,
    });

    if (error) {
      logger.error(
        { error, accountId },
        'Error incrementing mini app counter'
      );
      return false;
    }

    return true;
  } catch (error) {
    logger.error({ error, accountId }, 'Error incrementing mini app counter');
    return false;
  }
}

/**
 * Giảm counter mini app
 * @param accountId ID của tài khoản
 */
export async function decrementMiniAppCounter(accountId: string) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    const { error } = await supabase.rpc('decrement_account_counter', {
      p_account_id: accountId,
      p_counter_name: 'miniapp',
      p_increment: 1,
    });

    if (error) {
      logger.error(
        { error, accountId },
        'Error decrementing mini app counter'
      );
      return false;
    }

    return true;
  } catch (error) {
    logger.error({ error, accountId }, 'Error decrementing mini app counter');
    return false;
  }
}
