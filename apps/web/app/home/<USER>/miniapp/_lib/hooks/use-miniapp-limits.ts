'use client';

import { useParams } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';

import { checkCanCreateMiniApp, getMiniAppUsage } from '../server/resource-access';

/**
 * Hook để kiểm tra giới hạn mini app
 */
export function useMiniAppLimits() {
  const { account } = useParams();
  const accountId = account as string;

  const { data: canCreateData, isLoading: isLoadingCanCreate } = useQuery({
    queryKey: ['can-create-miniapp', accountId],
    queryFn: () => checkCanCreateMiniApp(accountId),
  });

  const { data: usageData, isLoading: isLoadingUsage } = useQuery({
    queryKey: ['miniapp-usage', accountId],
    queryFn: () => getMiniAppUsage(accountId),
  });

  return {
    canCreate: canCreateData?.canCreate || false,
    isLoading: isLoadingCanCreate || isLoadingUsage,
    limits: {
      current: usageData?.current || 0,
      limit: usageData?.limit || 0,
      hasSubscription: usageData?.hasSubscription || false,
      reason: canCreateData?.reason || (usageData?.hasSubscription ? 'limit_reached' : 'no_active_subscription'),
    },
  };
}
