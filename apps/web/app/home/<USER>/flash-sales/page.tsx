'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';
import { useParams } from 'next/navigation';

import { format, formatDistanceToNow } from 'date-fns';
import { Plus, Zap } from 'lucide-react';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { SearchListInput } from '@kit/ui/search-list-input';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';

import { FlashSale } from '~/lib/types/flash-sale';

import { TeamAccountLayoutPageHeader } from '../_components/team-account-layout-page-header';
import { createFlashSaleApi } from './_lib/server/flash-sale.api';

export default function FlashSalesPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const supabase = useSupabase();
  const { t } = useTranslation();
  const [flashSales, setFlashSales] = useState<FlashSale[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFlashSales = async () => {
      setLoading(true);
      try {
        const api = createFlashSaleApi(supabase);

        // Get team account data using the API
        const teamAccountsApi = createTeamAccountsApi(supabase);
        const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

        if (!teamAccount || !teamAccount.id) {
          throw new Error('Failed to fetch account');
        }

        const data = await api.getFlashSales(teamAccount.id);
        setFlashSales(data);
      } catch (error) {
        console.error('Error fetching flash sales:', error);
        toast({
          title: 'Error',
          description: 'Failed to load flash sales',
        });
      } finally {
        setLoading(false);
      }
    };
    fetchFlashSales();
  }, [accountSlug, supabase]);

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="common:routes.flash_sales" />}
        description={<AppBreadcrumbs />}
        account={accountSlug}
      />

      <PageBody>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle>
            <SearchListInput
              defaultValue=""
              placeholder={t('flash_sales:search.placeholder')}
              data-testid="flash-sales-page-search"
            />
          </CardTitle>

          <Button asChild data-testid="flash-sales-page-add-button">
            <Link href={`/home/<USER>/flash-sales/new`}>
              <Plus className="mr-2 h-4 w-4" />
              <Trans i18nKey="flash_sales:create_new">Create New</Trans>
            </Link>
          </Button>
        </CardHeader>

        <CardContent>
          {loading ? (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-5 w-16 rounded-full" />
                    </div>
                    <Skeleton className="mt-1 h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="mb-2 h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : flashSales.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="bg-muted rounded-full p-3">
                <Zap className="text-muted-foreground h-10 w-10" />
              </div>
              <h3 className="mt-4 text-lg font-semibold">
                <Trans i18nKey="flash_sales:no_flash_sales">
                  No Flash Sales
                </Trans>
              </h3>
              <p className="text-muted-foreground mt-2 max-w-md text-sm">
                <Trans i18nKey="flash_sales:no_flash_sales_description">
                  You haven&apos;t created any flash sales yet. Create your
                  first flash sale to offer time-limited discounts to your
                  customers.
                </Trans>
              </p>
              <Button className="mt-4" asChild>
                <Link href={`/home/<USER>/flash-sales/new`}>
                  <Plus className="mr-2 h-4 w-4" />
                  <Trans i18nKey="flash_sales:create_first">
                    Create Your First Flash Sale
                  </Trans>
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {flashSales.map((flashSale) => (
                <Link
                  key={flashSale.id}
                  href={`/home/<USER>/flash-sales/${flashSale.id}`}
                  className="block"
                >
                  <Card className="hover:border-primary/50 h-full overflow-hidden transition-all hover:shadow-md">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center text-base">
                          <Zap className="mr-2 h-5 w-5 text-red-500" />
                          {flashSale.name}
                        </CardTitle>
                        <div
                          className={`rounded-full px-2 py-1 text-xs font-medium ${
                            flashSale.status === 'active'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : flashSale.status === 'ended'
                                ? 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                                : flashSale.status === 'draft'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          }`}
                        >
                          <Trans i18nKey={`flash_sales:status.${flashSale.status}`}>
                            {flashSale.status.charAt(0).toUpperCase() + flashSale.status.slice(1)}
                          </Trans>
                        </div>
                      </div>
                      <CardDescription className="mt-1">
                        {format(new Date(flashSale.start_time), 'dd/MM/yyyy HH:mm')} -{' '}
                        {format(new Date(flashSale.end_time), 'dd/MM/yyyy HH:mm')}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground line-clamp-2 text-sm">
                        {flashSale.description || (
                          <span className="text-muted-foreground/70 italic">
                            No description
                          </span>
                        )}
                      </p>

                      {/* Countdown timer or progress indicator could go here */}
                      {flashSale.status === 'active' && (
                        <div className="text-muted-foreground mt-3 flex items-center text-xs">
                          <span className="flex items-center text-green-600 dark:text-green-400">
                            <span className="mr-1 h-2 w-2 animate-pulse rounded-full bg-green-500"></span>
                            Active
                          </span>
                          <span className="mx-2">•</span>
                          <span>
                            <Trans i18nKey="flash_sales:ends_in">Ends in</Trans>{' '}
                            {formatDistanceToNow(new Date(flashSale.end_time))}
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}
        </CardContent>
      </PageBody>
    </>
  );
}
