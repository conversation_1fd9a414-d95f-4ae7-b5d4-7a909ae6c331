'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Plus, Search, Trash2, X, Zap } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Badge } from '@kit/ui/badge';
import { But<PERSON> } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { DateTimePicker } from '@kit/ui/date-time-picker';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Separator } from '@kit/ui/separator';
import { Skeleton } from '@kit/ui/skeleton';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { FlashSale } from '~/lib/types/flash-sale';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import {
  deleteFlashSaleAction,
  updateFlashSaleAction,
} from '../_lib/server/flash-sale.actions';
import { createFlashSaleApi } from '../_lib/server/flash-sale.api';

const flashSaleFormSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().optional(),
  start_time: z.date({
    required_error: 'Start date is required',
  }),
  end_time: z.date({
    required_error: 'End date is required',
  }),
  status: z.enum(['draft', 'active', 'ended', 'cancelled']),
  products: z
    .array(
      z.object({
        product_id: z.string().uuid(),
        discount_percentage: z.number().min(1).max(100),
        quantity_limit: z.number().optional(),
      }),
    )
    .min(1, { message: 'At least one product is required' }),
});

type FlashSaleFormValues = z.infer<typeof flashSaleFormSchema>;

export default function FlashSaleDetailPage() {
  const { account: accountSlug, id } = useParams<{
    account: string;
    id: string;
  }>();
  const router = useRouter();
  const supabase = useSupabase();
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);

  // Products state
  const [products, setProducts] = useState<
    { id: string; name: string; price: number; image_url?: string }[]
  >([]);
  const [filteredProducts, setFilteredProducts] = useState<
    { id: string; name: string; price: number; image_url?: string }[]
  >([]);

  // Selected products for the flash sale
  const [selectedProducts, setSelectedProducts] = useState<
    {
      product_id: string;
      name: string;
      discount_percentage: number;
      quantity_limit?: number;
    }[]
  >([]);

  // Temporary state for product selection dialog
  const [tempSelectedProducts, setTempSelectedProducts] = useState<
    {
      product_id: string;
      name: string;
      price: number;
      discount_percentage: number;
      quantity_limit?: number;
    }[]
  >([]);

  const [flashSale, setFlashSale] = useState<FlashSale | null>(null);

  const form = useForm<FlashSaleFormValues>({
    resolver: zodResolver(flashSaleFormSchema),
    defaultValues: {
      name: '',
      description: '',
      start_time: new Date(),
      end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      status: 'draft',
      products: [],
    },
  });

  // Fetch flash sale and products when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const teamAccountsApi = createTeamAccountsApi(supabase);
        const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

        if (!teamAccount || !teamAccount.id) {
          throw new Error('Failed to fetch account');
        }

        // Fetch flash sale
        const api = createFlashSaleApi(supabase);
        const flashSaleData = await api.getFlashSaleById(id);

        if (!flashSaleData) {
          throw new Error('Flash sale not found');
        }

        setFlashSale(flashSaleData);

        // Set form values
        form.reset({
          name: flashSaleData.name,
          description: flashSaleData.description || '',
          start_time: new Date(flashSaleData.start_time),
          end_time: new Date(flashSaleData.end_time),
          status: flashSaleData.status as any,
          products: flashSaleData.products.map((p) => ({
            product_id: p.product_id,
            discount_percentage: p.discount_percentage,
            quantity_limit: p.quantity_limit,
          })),
        });

        // Set selected products
        setSelectedProducts(
          flashSaleData.products.map((p) => ({
            product_id: p.product_id,
            name: p.product.name,
            discount_percentage: p.discount_percentage,
            quantity_limit: p.quantity_limit,
          })),
        );

        // Fetch all products
        const { data: productsData, error } = await supabase
          .from('products')
          .select('id, name, price, image_url')
          .eq('account_id', teamAccount.id)
          .eq('status', 'active');

        if (error) throw error;

        setProducts(productsData || []);
        setFilteredProducts(productsData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description:
            error instanceof Error ? error.message : 'Failed to load data',
        });
        router.push(`/home/<USER>/flash-sales`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [accountSlug, id, supabase, router, form]);

  // Filter products when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter((product) =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredProducts(filtered);
    }
  }, [searchTerm, products]);

  // Handle opening the product selection dialog
  const handleOpenProductDialog = () => {
    // Initialize temp selected products with current selections
    const initialTempSelected = selectedProducts.map((p) => ({
      product_id: p.product_id,
      name: p.name,
      price: products.find((prod) => prod.id === p.product_id)?.price || 0,
      discount_percentage: p.discount_percentage,
      quantity_limit: p.quantity_limit,
    }));

    setTempSelectedProducts(initialTempSelected);
    setIsProductDialogOpen(true);
    // Reset search when opening dialog
    setSearchTerm('');
  };

  // Handle selecting/deselecting a product in the dialog
  const handleProductSelection = (product: {
    id: string;
    name: string;
    price: number;
  }) => {
    setTempSelectedProducts((prev) => {
      // Check if product is already selected
      const existingIndex = prev.findIndex((p) => p.product_id === product.id);

      if (existingIndex >= 0) {
        // Remove product if already selected
        return prev.filter((p) => p.product_id !== product.id);
      } else {
        // Add product with default values
        return [
          ...prev,
          {
            product_id: product.id,
            name: product.name,
            price: product.price,
            discount_percentage: 20,
            quantity_limit: 50,
          },
        ];
      }
    });
  };

  // Handle updating discount percentage for a product
  const handleDiscountChange = (productId: string, value: number) => {
    setTempSelectedProducts((prev) =>
      prev.map((p) =>
        p.product_id === productId
          ? { ...p, discount_percentage: Math.min(100, Math.max(1, value)) }
          : p,
      ),
    );
  };

  // Handle updating quantity limit for a product
  const handleQuantityLimitChange = (productId: string, value: string) => {
    setTempSelectedProducts((prev) =>
      prev.map((p) =>
        p.product_id === productId
          ? { ...p, quantity_limit: value ? parseInt(value, 10) : undefined }
          : p,
      ),
    );
  };

  // Handle confirming product selections
  const handleConfirmProductSelection = () => {
    // Update the main selected products state
    setSelectedProducts(
      tempSelectedProducts.map((p) => ({
        product_id: p.product_id,
        name: p.name,
        discount_percentage: p.discount_percentage,
        quantity_limit: p.quantity_limit,
      })),
    );

    // Update the form value
    form.setValue(
      'products',
      tempSelectedProducts.map((p) => ({
        product_id: p.product_id,
        discount_percentage: p.discount_percentage,
        quantity_limit: p.quantity_limit,
      })),
    );

    // Close the dialog
    setIsProductDialogOpen(false);
    // Clear temporary state
    setSearchTerm('');
  };

  // Handle closing the product dialog without saving
  const handleCloseProductDialog = () => {
    setIsProductDialogOpen(false);
    setSearchTerm('');
    // Discard temporary changes
    setTempSelectedProducts([]);
  };

  const onSubmit = async (values: FlashSaleFormValues) => {
    if (!flashSale) return;

    setIsSubmitting(true);

    try {
      const teamAccountsApi = createTeamAccountsApi(supabase);
      const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

      if (!teamAccount || !teamAccount.id) {
        throw new Error('Failed to fetch account');
      }

      const result = await updateFlashSaleAction({
        id: flashSale.id,
        accountId: teamAccount.id,
        accountSlug,
        name: values.name,
        description: values.description,
        start_time: values.start_time.toISOString(),
        end_time: values.end_time.toISOString(),
        status: values.status,
        products: values.products,
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to update flash sale');
      }

      toast.success('Flash sale updated successfully');

      // Refresh the page to show updated data
      router.refresh();
    } catch (error) {
      console.error('Error updating flash sale:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update flash sale',
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!flashSale) return;

    try {
      const teamAccountsApi = createTeamAccountsApi(supabase);
      const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

      if (!teamAccount || !teamAccount.id) {
        throw new Error('Failed to fetch account');
      }

      const result = await deleteFlashSaleAction({
        id: flashSale.id,
        accountId: teamAccount.id,
        accountSlug,
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete flash sale');
      }

      toast.success('Flash sale deleted successfully');

      // Redirect to flash sales list
      router.push(`/home/<USER>/flash-sales`);
    } catch (error) {
      console.error('Error deleting flash sale:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to delete flash sale',
      );
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'ended':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'draft':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  if (isLoading) {
    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="flash_sales:flash_sale_details" />}
          description={<AppBreadcrumbs />}
          account={accountSlug}
        />

        <PageBody>
          <div className="mx-auto w-full max-w-3xl">
            <div className="mb-6 flex items-center">
              <Skeleton className="h-10 w-20" />
            </div>

            <Card>
              <CardHeader>
                <Skeleton className="h-8 w-1/3" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent className="space-y-6">
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          </div>
        </PageBody>
      </>
    );
  }

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="flash_sales:flash_sale_details" />}
        description={<AppBreadcrumbs />}
        account={accountSlug}
      />

      <PageBody>
        <div className="mx-auto max-w-3xl">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-2">
              <Link href={`/home/<USER>/flash-sales`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                <Trans i18nKey="common:back">Back</Trans>
              </Link>
            </Button>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="rounded-full bg-red-100 p-2 dark:bg-red-900/20">
                    <Zap className="h-5 w-5 text-red-600 dark:text-red-500" />
                  </div>
                  <div>
                    <CardTitle>{flashSale?.name}</CardTitle>
                    <CardDescription>
                      {flashSale?.status && (
                        <span
                          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusBadgeClass(flashSale.status)}`}
                        >
                          <Trans
                            i18nKey={`flash_sales:status.${flashSale.status}`}
                          >
                            {flashSale.status.charAt(0).toUpperCase() +
                              flashSale.status.slice(1)}
                          </Trans>
                        </span>
                      )}
                    </CardDescription>
                  </div>
                </div>
              </div>
            </CardHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="flash_sales:name">Name</Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t('flash_sales:name_placeholder')}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="flash_sales:status.label">
                                Status
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <select
                                className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                {...field}
                              >
                                <option value="draft">
                                  {t('flash_sales:status.draft', 'Draft')}
                                </option>
                                <option value="active">
                                  {t('flash_sales:status.active', 'Active')}
                                </option>
                                <option value="ended">
                                  {t('flash_sales:status.ended', 'Ended')}
                                </option>
                                <option value="cancelled">
                                  {t(
                                    'flash_sales:status.cancelled',
                                    'Cancelled',
                                  )}
                                </option>
                              </select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="flash_sales:description">
                              Description
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={t(
                                'flash_sales:description_placeholder',
                              )}
                              className="resize-none"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="start_time"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="flash_sales:start_time">
                                Start Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="end_time"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="flash_sales:end_time">
                                End Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Products Section */}
                  <div>
                    <h3 className="mb-4 text-base font-medium">
                      <Trans i18nKey="flash_sales:products_on_sale">
                        Products on Sale
                      </Trans>
                    </h3>

                    {selectedProducts.length > 0 ? (
                      <div className="space-y-4">
                        <div className="rounded-md border">
                          <table className="w-full">
                            <thead className="bg-muted/50">
                              <tr className="border-b">
                                <th className="px-4 py-2 text-left">
                                  <Trans i18nKey="flash_sales:product_name">
                                    Product Name
                                  </Trans>
                                </th>
                                <th className="px-4 py-2 text-center">
                                  <Trans i18nKey="flash_sales:discount">
                                    Discount
                                  </Trans>
                                </th>
                                <th className="px-4 py-2 text-center">
                                  <Trans i18nKey="flash_sales:quantity_limit">
                                    Quantity Limit
                                  </Trans>
                                </th>
                                <th className="w-16 px-4 py-2"></th>
                              </tr>
                            </thead>
                            <tbody>
                              {selectedProducts.map((product, index) => {
                                const productInfo = products.find(
                                  (p) => p.id === product.product_id,
                                );
                                return (
                                  <tr
                                    key={product.product_id}
                                    className="border-b"
                                  >
                                    <td className="px-4 py-3">
                                      <div className="flex items-center space-x-2">
                                        {productInfo?.image_url ? (
                                          <img
                                            src={productInfo.image_url}
                                            alt={product.name}
                                            className="h-8 w-8 rounded-md object-cover"
                                          />
                                        ) : (
                                          <div className="bg-muted flex h-8 w-8 items-center justify-center rounded-md">
                                            <Zap className="text-muted-foreground h-4 w-4" />
                                          </div>
                                        )}
                                        <div>
                                          <div className="font-medium">
                                            {product.name}
                                          </div>
                                          {productInfo && (
                                            <div className="text-muted-foreground text-xs">
                                              {productInfo.price.toLocaleString()}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </td>
                                    <td className="px-4 py-3">
                                      <div className="flex items-center justify-center space-x-1">
                                        <Input
                                          type="number"
                                          min="1"
                                          max="100"
                                          className="w-16 text-center"
                                          value={product.discount_percentage}
                                          onChange={(e) => {
                                            const newProducts = [
                                              ...selectedProducts,
                                            ];
                                            newProducts[
                                              index
                                            ].discount_percentage = parseInt(
                                              e.target.value,
                                              10,
                                            );
                                            setSelectedProducts(newProducts);
                                            form.setValue(
                                              'products',
                                              newProducts.map((p) => ({
                                                product_id: p.product_id,
                                                discount_percentage:
                                                  p.discount_percentage,
                                                quantity_limit:
                                                  p.quantity_limit,
                                              })),
                                            );
                                          }}
                                        />
                                        <span className="text-muted-foreground">
                                          %
                                        </span>
                                      </div>
                                      {productInfo && (
                                        <div className="text-muted-foreground mt-1 text-center text-xs">
                                          <span className="line-through">
                                            {productInfo.price.toLocaleString()}
                                          </span>
                                          {' → '}
                                          <span className="font-medium text-green-600">
                                            {Math.round(
                                              productInfo.price *
                                                (1 -
                                                  product.discount_percentage /
                                                    100),
                                            ).toLocaleString()}
                                          </span>
                                        </div>
                                      )}
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <Input
                                        type="number"
                                        min="1"
                                        className="mx-auto w-20 text-center"
                                        placeholder="∞"
                                        value={product.quantity_limit || ''}
                                        onChange={(e) => {
                                          const newProducts = [
                                            ...selectedProducts,
                                          ];
                                          newProducts[index].quantity_limit = e
                                            .target.value
                                            ? parseInt(e.target.value, 10)
                                            : undefined;
                                          setSelectedProducts(newProducts);
                                          form.setValue(
                                            'products',
                                            newProducts.map((p) => ({
                                              product_id: p.product_id,
                                              discount_percentage:
                                                p.discount_percentage,
                                              quantity_limit: p.quantity_limit,
                                            })),
                                          );
                                        }}
                                      />
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                          const newProducts =
                                            selectedProducts.filter(
                                              (_, i) => i !== index,
                                            );
                                          setSelectedProducts(newProducts);
                                          form.setValue(
                                            'products',
                                            newProducts.map((p) => ({
                                              product_id: p.product_id,
                                              discount_percentage:
                                                p.discount_percentage,
                                              quantity_limit: p.quantity_limit,
                                            })),
                                          );
                                        }}
                                      >
                                        <Trash2 className="text-muted-foreground hover:text-destructive h-4 w-4" />
                                      </Button>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : (
                      <div className="rounded-md border border-dashed p-6 text-center">
                        <p className="text-muted-foreground">
                          <Trans i18nKey="flash_sales:no_products_selected">
                            No products selected. Add products to include in
                            this flash sale.
                          </Trans>
                        </p>
                      </div>
                    )}

                    <Button
                      type="button"
                      variant="outline"
                      className="mt-4"
                      onClick={handleOpenProductDialog}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      <Trans i18nKey="flash_sales:add_product">
                        Add Product
                      </Trans>
                    </Button>

                    {form.formState.errors.products && (
                      <p className="mt-2 text-sm text-red-500">
                        {form.formState.errors.products.message}
                      </p>
                    )}
                  </div>
                </CardContent>

                <CardFooter className="bg-muted/10 flex justify-between border-t px-6 py-4">
                  <div className="flex space-x-2">
                    <Button variant="outline" type="button" asChild>
                      <Link href={`/home/<USER>/flash-sales`}>
                        <Trans i18nKey="common:cancel">Cancel</Trans>
                      </Link>
                    </Button>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" type="button">
                          <Trans i18nKey="flash_sales:delete_flash_sale">
                            Delete Flash Sale
                          </Trans>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            <Trans i18nKey="flash_sales:confirm_delete">
                              Are you sure you want to delete this flash sale?
                            </Trans>
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            <Trans i18nKey="flash_sales:confirm_delete_description">
                              This action cannot be undone. This will
                              permanently delete the flash sale.
                            </Trans>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>
                            <Trans i18nKey="common:cancel">Cancel</Trans>
                          </AlertDialogCancel>
                          <AlertDialogAction onClick={handleDelete}>
                            <Trans i18nKey="common:delete">Delete</Trans>
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>

                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <Trans i18nKey="common:saving">Saving...</Trans>
                    ) : (
                      <Trans i18nKey="flash_sales:update_flash_sale">
                        Update Flash Sale
                      </Trans>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </div>
      </PageBody>

      {/* Product Selection Dialog */}
      <Dialog open={isProductDialogOpen} onOpenChange={setIsProductDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              <Trans i18nKey="flash_sales:select_products">Select Products</Trans>
            </DialogTitle>
            <DialogDescription>
              <Trans i18nKey="flash_sales:select_products_description">
                Select products to include in this flash sale and set their discount percentages.
              </Trans>
            </DialogDescription>
          </DialogHeader>

          <div className="mb-4 flex items-center border rounded-md px-3 py-2">
            <Search className="mr-2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('flash_sales:search_products', 'Search products...')}
              className="border-0 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5"
                onClick={() => setSearchTerm('')}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>

          <div className="flex space-x-4">
            <div className="w-1/2 border-r pr-4">
              <h4 className="mb-2 text-sm font-medium">
                <Trans i18nKey="flash_sales:available_products">Available Products</Trans>
              </h4>
              <ScrollArea className="h-[300px]">
                <div className="space-y-2">
                  {filteredProducts.length > 0 ? (
                    filteredProducts.map((product) => {
                      const isSelected = tempSelectedProducts.some(
                        (p) => p.product_id === product.id
                      );
                      return (
                        <div
                          key={product.id}
                          className={`flex cursor-pointer items-center justify-between rounded-md border p-2 ${isSelected ? 'border-primary bg-primary/5' : ''}`}
                          onClick={() => handleProductSelection(product)}
                        >
                          <div className="flex items-center space-x-2">
                            {product.image_url ? (
                              <img
                                src={product.image_url}
                                alt={product.name}
                                className="h-8 w-8 rounded-md object-cover"
                              />
                            ) : (
                              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                                <Zap className="h-4 w-4 text-muted-foreground" />
                              </div>
                            )}
                            <div>
                              <div className="font-medium">{product.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {product.price.toLocaleString()}
                              </div>
                            </div>
                          </div>
                          {isSelected && (
                            <Badge variant="outline" className="bg-primary/10">
                              <Trans i18nKey="flash_sales:selected">Selected</Trans>
                            </Badge>
                          )}
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center text-muted-foreground py-4">
                      <Trans i18nKey="flash_sales:no_products_found">
                        No products found
                      </Trans>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            <div className="w-1/2">
              <h4 className="mb-2 text-sm font-medium">
                <Trans i18nKey="flash_sales:selected_products">Selected Products</Trans>
                {tempSelectedProducts.length > 0 && (
                  <span className="ml-1 text-xs text-muted-foreground">
                    ({tempSelectedProducts.length})
                  </span>
                )}
              </h4>
              <ScrollArea className="h-[300px]">
                <div className="space-y-3">
                  {tempSelectedProducts.length > 0 ? (
                    tempSelectedProducts.map((product) => (
                      <div key={product.product_id} className="rounded-md border p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium">{product.name}</div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => handleProductSelection({ id: product.product_id, name: product.name, price: product.price })}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="space-y-2">
                          <div>
                            <label className="text-xs font-medium">
                              <Trans i18nKey="flash_sales:discount_percentage">Discount %</Trans>
                            </label>
                            <div className="flex items-center space-x-1">
                              <Input
                                type="number"
                                min="1"
                                max="100"
                                className="w-16"
                                value={product.discount_percentage}
                                onChange={(e) => handleDiscountChange(product.product_id, parseInt(e.target.value, 10))}
                              />
                              <span className="text-muted-foreground">%</span>
                            </div>
                          </div>
                          <div>
                            <label className="text-xs font-medium">
                              <Trans i18nKey="flash_sales:quantity_limit">Quantity Limit</Trans>
                            </label>
                            <Input
                              type="number"
                              min="1"
                              className="w-full"
                              placeholder="∞"
                              value={product.quantity_limit || ''}
                              onChange={(e) => handleQuantityLimitChange(product.product_id, e.target.value)}
                            />
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-4">
                      <Trans i18nKey="flash_sales:no_products_selected_dialog">
                        No products selected
                      </Trans>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCloseProductDialog}>
              <Trans i18nKey="common:cancel">Cancel</Trans>
            </Button>
            <Button onClick={handleConfirmProductSelection}>
              <Trans i18nKey="common:confirm">Confirm</Trans>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
