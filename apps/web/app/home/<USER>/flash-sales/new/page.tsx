'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  ArrowLeft,
  Check,
  Plus,
  Search,
  Trash2,
  Zap,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import { DateTimePicker } from '@kit/ui/date-time-picker';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Separator } from '@kit/ui/separator';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';
import { cn, formatDateTimeFromObject } from '@kit/ui/utils';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { createFlashSaleAction } from '../_lib/server/flash-sale.actions';

const flashSaleFormSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().optional(),
  start_time: z.date({
    required_error: 'Start date is required',
  }),
  end_time: z.date({
    required_error: 'End date is required',
  }),
  status: z.enum(['draft', 'active', 'ended', 'cancelled']),
  products: z
    .array(
      z.object({
        product_id: z.string().uuid(),
        discount_percentage: z.number().min(1).max(100),
        quantity_limit: z.number().optional(),
      }),
    )
    .min(1, { message: 'At least one product is required' }),
});

type FlashSaleFormValues = z.infer<typeof flashSaleFormSchema>;

export default function NewFlashSalePage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const router = useRouter();
  const supabase = useSupabase();
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<
    { id: string; name: string; price: number; image_url?: string }[]
  >([]);
  const [filteredProducts, setFilteredProducts] = useState<
    { id: string; name: string; price: number; image_url?: string }[]
  >([]);
  const [selectedProducts, setSelectedProducts] = useState<
    {
      product_id: string;
      name: string;
      discount_percentage: number;
      quantity_limit?: number;
    }[]
  >([]);
  const [tempSelectedProducts, setTempSelectedProducts] = useState<
    {
      product_id: string;
      name: string;
      price: number;
      discount_percentage: number;
      quantity_limit?: number;
    }[]
  >([]);

  const form = useForm<FlashSaleFormValues>({
    resolver: zodResolver(flashSaleFormSchema),
    defaultValues: {
      name: '',
      description: '',
      start_time: new Date(),
      end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      status: 'draft',
      products: [],
    },
  });

  // Fetch products when component mounts
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const teamAccountsApi = createTeamAccountsApi(supabase);
        const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

        if (!teamAccount || !teamAccount.id) {
          throw new Error('Failed to fetch account');
        }

        const { data, error } = await supabase
          .from('products')
          .select('id, name, price, image_url')
          .eq('account_id', teamAccount.id)
          .eq('status', 'active');

        if (error) throw error;

        setProducts(data || []);
        setFilteredProducts(data || []);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast({
          title: 'Error',
          description: 'Failed to load products',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Filter products when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter((product) =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredProducts(filtered);
    }
  }, [searchTerm, products]);

  // Handle opening the product selection dialog
  const handleOpenProductDialog = () => {
    // Initialize temp selected products with current selections
    const initialTempSelected = selectedProducts.map((p) => ({
      product_id: p.product_id,
      name: p.name,
      price: products.find((prod) => prod.id === p.product_id)?.price || 0,
      discount_percentage: p.discount_percentage,
      quantity_limit: p.quantity_limit,
    }));

    setTempSelectedProducts(initialTempSelected);
    setIsProductDialogOpen(true);
  };

  // Handle selecting/deselecting a product in the dialog
  const handleProductSelection = (product: {
    id: string;
    name: string;
    price: number;
  }) => {
    setTempSelectedProducts((prev) => {
      // Check if product is already selected
      const existingIndex = prev.findIndex((p) => p.product_id === product.id);

      if (existingIndex >= 0) {
        // Remove product if already selected
        return prev.filter((p) => p.product_id !== product.id);
      } else {
        // Add product with default values
        return [
          ...prev,
          {
            product_id: product.id,
            name: product.name,
            price: product.price,
            discount_percentage: 20,
            quantity_limit: 50,
          },
        ];
      }
    });
  };

  // Handle updating discount percentage for a product
  const handleDiscountChange = (productId: string, value: number) => {
    setTempSelectedProducts((prev) =>
      prev.map((p) =>
        p.product_id === productId
          ? { ...p, discount_percentage: Math.min(100, Math.max(1, value)) }
          : p,
      ),
    );
  };

  // Handle updating quantity limit for a product
  const handleQuantityLimitChange = (productId: string, value: string) => {
    setTempSelectedProducts((prev) =>
      prev.map((p) =>
        p.product_id === productId
          ? { ...p, quantity_limit: value ? parseInt(value, 10) : undefined }
          : p,
      ),
    );
  };

  // Handle confirming product selections
  const handleConfirmProductSelection = () => {
    // Update the main selected products state
    setSelectedProducts(
      tempSelectedProducts.map((p) => ({
        product_id: p.product_id,
        name: p.name,
        discount_percentage: p.discount_percentage,
        quantity_limit: p.quantity_limit,
      })),
    );

    // Update the form value
    form.setValue(
      'products',
      tempSelectedProducts.map((p) => ({
        product_id: p.product_id,
        discount_percentage: p.discount_percentage,
        quantity_limit: p.quantity_limit,
      })),
    );

    // Close the dialog
    setIsProductDialogOpen(false);
  };

  const onSubmit = async (values: FlashSaleFormValues) => {
    setIsSubmitting(true);

    try {
      const teamAccountsApi = createTeamAccountsApi(supabase);
      const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

      if (!teamAccount || !teamAccount.id) {
        throw new Error('Failed to fetch account');
      }

      const result = await createFlashSaleAction({
        accountId: teamAccount.id,
        accountSlug,
        name: values.name,
        description: values.description,
        start_time: values.start_time.toISOString(),
        end_time: values.end_time.toISOString(),
        status: values.status,
        products: values.products,
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to create flash sale');
      }

      toast.success('Flash sale created successfully');

      // Redirect to the flash sale detail page
      router.push(`/home/<USER>/flash-sales/${result.id}`);
    } catch (error) {
      console.error('Error creating flash sale:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to create flash sale'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="flash_sales:create_flash_sale" />}
        description={<AppBreadcrumbs />}
        account={accountSlug}
      />

      <PageBody>
        <div className="mx-auto max-w-3xl">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-2">
              <Link href={`/home/<USER>/flash-sales`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                <Trans i18nKey="common:back">Back</Trans>
              </Link>
            </Button>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <div className="rounded-full bg-red-100 p-2 dark:bg-red-900/20">
                  <Zap className="h-5 w-5 text-red-600 dark:text-red-500" />
                </div>
                <div>
                  <CardTitle>
                    <Trans i18nKey="flash_sales:create_flash_sale">
                      Create Flash Sale
                    </Trans>
                  </CardTitle>
                  <CardDescription>
                    <Trans i18nKey="flash_sales:create_flash_sale_description">
                      Create a new flash sale to offer time-limited discounts to
                      your customers.
                    </Trans>
                  </CardDescription>
                </div>
              </div>
            </CardHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="flash_sales:name">Name</Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'flash_sales:name_placeholder',
                                  'Summer Flash Sale',
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="flash_sales:status">Status</Trans>
                            </FormLabel>
                            <FormControl>
                              <div className="flex h-10 w-full items-center rounded-md border border-input bg-background px-3 py-2 text-sm">
                                <span className={`inline-flex rounded-full px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400`}>
                                  <Trans i18nKey="flash_sales:draft">Draft</Trans>
                                </span>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="flash_sales:description">
                              Description
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={t(
                                'flash_sales:description_placeholder',
                                'Special discounts for summer products...',
                              )}
                              className="resize-none"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="start_time"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="flash_sales:start_time">
                                Start Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="end_time"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="flash_sales:end_time">
                                End Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Products Section */}
                  <div>
                    <h3 className="mb-4 text-base font-medium">
                      <Trans i18nKey="flash_sales:products_on_sale">
                        Products on Sale
                      </Trans>
                    </h3>

                    {selectedProducts.length > 0 ? (
                      <div className="space-y-4">
                        <div className="rounded-md border">
                          <table className="w-full">
                            <thead className="bg-muted/50">
                              <tr className="border-b">
                                <th className="px-4 py-2 text-left">
                                  <Trans i18nKey="flash_sales:product_name">
                                    Product Name
                                  </Trans>
                                </th>
                                <th className="px-4 py-2 text-center">
                                  <Trans i18nKey="flash_sales:discount">
                                    Discount
                                  </Trans>
                                </th>
                                <th className="px-4 py-2 text-center">
                                  <Trans i18nKey="flash_sales:quantity_limit">
                                    Quantity Limit
                                  </Trans>
                                </th>
                                <th className="w-16 px-4 py-2"></th>
                              </tr>
                            </thead>
                            <tbody>
                              {selectedProducts.map((product, index) => {
                                const productInfo = products.find(
                                  (p) => p.id === product.product_id,
                                );
                                return (
                                  <tr
                                    key={product.product_id}
                                    className="border-b"
                                  >
                                    <td className="px-4 py-3">
                                      <div className="flex items-center space-x-2">
                                        {productInfo?.image_url ? (
                                          <img
                                            src={productInfo.image_url}
                                            alt={product.name}
                                            className="h-8 w-8 rounded-md object-cover"
                                          />
                                        ) : (
                                          <div className="bg-muted flex h-8 w-8 items-center justify-center rounded-md">
                                            <Zap className="text-muted-foreground h-4 w-4" />
                                          </div>
                                        )}
                                        <div>
                                          <div className="font-medium">
                                            {product.name}
                                          </div>
                                          {productInfo && (
                                            <div className="text-muted-foreground text-xs">
                                              {productInfo.price.toLocaleString()}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </td>
                                    <td className="px-4 py-3">
                                      <div className="flex items-center justify-center space-x-1">
                                        <Input
                                          type="number"
                                          min="1"
                                          max="100"
                                          className="w-16 text-center"
                                          value={product.discount_percentage}
                                          onChange={(e) => {
                                            const newProducts = [
                                              ...selectedProducts,
                                            ];
                                            newProducts[
                                              index
                                            ].discount_percentage = parseInt(
                                              e.target.value,
                                              10,
                                            );
                                            setSelectedProducts(newProducts);
                                            form.setValue(
                                              'products',
                                              newProducts.map((p) => ({
                                                product_id: p.product_id,
                                                discount_percentage:
                                                  p.discount_percentage,
                                                quantity_limit:
                                                  p.quantity_limit,
                                              })),
                                            );
                                          }}
                                        />
                                        <span className="text-muted-foreground">
                                          %
                                        </span>
                                      </div>
                                      {productInfo && (
                                        <div className="text-muted-foreground mt-1 text-center text-xs">
                                          <span className="line-through">
                                            {productInfo.price.toLocaleString()}
                                          </span>
                                          {' → '}
                                          <span className="font-medium text-green-600">
                                            {Math.round(
                                              productInfo.price *
                                                (1 -
                                                  product.discount_percentage /
                                                    100),
                                            ).toLocaleString()}
                                          </span>
                                        </div>
                                      )}
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <Input
                                        type="number"
                                        min="1"
                                        className="mx-auto w-20 text-center"
                                        placeholder="∞"
                                        value={product.quantity_limit || ''}
                                        onChange={(e) => {
                                          const newProducts = [
                                            ...selectedProducts,
                                          ];
                                          newProducts[index].quantity_limit = e
                                            .target.value
                                            ? parseInt(e.target.value, 10)
                                            : undefined;
                                          setSelectedProducts(newProducts);
                                          form.setValue(
                                            'products',
                                            newProducts.map((p) => ({
                                              product_id: p.product_id,
                                              discount_percentage:
                                                p.discount_percentage,
                                              quantity_limit: p.quantity_limit,
                                            })),
                                          );
                                        }}
                                      />
                                    </td>
                                    <td className="px-4 py-3 text-center">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                          const newProducts =
                                            selectedProducts.filter(
                                              (_, i) => i !== index,
                                            );
                                          setSelectedProducts(newProducts);
                                          form.setValue(
                                            'products',
                                            newProducts.map((p) => ({
                                              product_id: p.product_id,
                                              discount_percentage:
                                                p.discount_percentage,
                                              quantity_limit: p.quantity_limit,
                                            })),
                                          );
                                        }}
                                      >
                                        <Trash2 className="text-muted-foreground hover:text-destructive h-4 w-4" />
                                      </Button>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : (
                      <div className="rounded-md border border-dashed p-6 text-center">
                        <p className="text-muted-foreground">
                          <Trans i18nKey="flash_sales:no_products_selected">
                            No products selected. Add products to include in
                            this flash sale.
                          </Trans>
                        </p>
                      </div>
                    )}

                    <Button
                      type="button"
                      variant="outline"
                      className="mt-4"
                      onClick={handleOpenProductDialog}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      <Trans i18nKey="flash_sales:add_product">
                        Add Product
                      </Trans>
                    </Button>

                    {/* Product Selection Dialog */}
                    <Dialog
                      open={isProductDialogOpen}
                      onOpenChange={setIsProductDialogOpen}
                    >
                      <DialogContent className="sm:max-w-[800px]">
                        <DialogHeader>
                          <DialogTitle>
                            <Trans i18nKey="flash_sales:add_product_modal_title">
                              Add Products to Flash Sale
                            </Trans>
                          </DialogTitle>
                          <DialogDescription>
                            <Trans i18nKey="flash_sales:add_product_modal_description">
                              Select products to include in your flash sale and
                              set discount percentages.
                            </Trans>
                          </DialogDescription>
                        </DialogHeader>

                        <div className="space-y-4">
                          {/* Search input */}
                          <div className="relative">
                            <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                            <Input
                              type="search"
                              placeholder={t('flash_sales:search_products')}
                              className="pl-8"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                            />
                          </div>

                          {isLoading ? (
                            <div className="flex h-40 items-center justify-center">
                              <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
                            </div>
                          ) : filteredProducts.length === 0 ? (
                            <div className="flex h-40 flex-col items-center justify-center space-y-2 text-center">
                              <div className="bg-muted rounded-full p-3">
                                <Search className="text-muted-foreground h-6 w-6" />
                              </div>
                              <p className="text-muted-foreground text-sm">
                                <Trans i18nKey="flash_sales:no_products_available">
                                  No products available. Please add products to
                                  your store first.
                                </Trans>
                              </p>
                            </div>
                          ) : (
                            <div className="max-h-[400px] overflow-auto rounded-md border">
                              <table className="w-full">
                                <thead className="bg-muted/50 sticky top-0">
                                  <tr className="border-b">
                                    <th className="w-12 px-4 py-2"></th>
                                    <th className="px-4 py-2 text-left">
                                      <Trans i18nKey="flash_sales:product_name">
                                        Product Name
                                      </Trans>
                                    </th>
                                    <th className="px-4 py-2 text-right">
                                      <Trans i18nKey="flash_sales:original_price">
                                        Original Price
                                      </Trans>
                                    </th>
                                    <th className="px-4 py-2 text-center">
                                      <Trans i18nKey="flash_sales:discount">
                                        Discount
                                      </Trans>
                                    </th>
                                    <th className="px-4 py-2 text-center">
                                      <Trans i18nKey="flash_sales:quantity_limit">
                                        Quantity Limit
                                      </Trans>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {filteredProducts.map((product) => {
                                    const isSelected =
                                      tempSelectedProducts.some(
                                        (p) => p.product_id === product.id,
                                      );
                                    const selectedProduct =
                                      tempSelectedProducts.find(
                                        (p) => p.product_id === product.id,
                                      );

                                    return (
                                      <tr
                                        key={product.id}
                                        className={cn(
                                          'hover:bg-muted/50 border-b transition-colors',
                                          isSelected && 'bg-primary/5',
                                        )}
                                        onClick={() =>
                                          handleProductSelection(product)
                                        }
                                      >
                                        <td className="px-4 py-2 text-center">
                                          <div className="flex h-full items-center justify-center">
                                            <div
                                              className={cn(
                                                'flex h-5 w-5 items-center justify-center rounded-sm border',
                                                isSelected
                                                  ? 'border-primary bg-primary text-primary-foreground'
                                                  : 'border-muted-foreground',
                                              )}
                                            >
                                              {isSelected && (
                                                <Check className="h-3.5 w-3.5" />
                                              )}
                                            </div>
                                          </div>
                                        </td>
                                        <td className="px-4 py-2">
                                          <div className="flex items-center space-x-2">
                                            {product.image_url ? (
                                              <img
                                                src={product.image_url}
                                                alt={product.name}
                                                className="h-8 w-8 rounded-md object-cover"
                                              />
                                            ) : (
                                              <div className="bg-muted flex h-8 w-8 items-center justify-center rounded-md">
                                                <Zap className="text-muted-foreground h-4 w-4" />
                                              </div>
                                            )}
                                            <span className="font-medium">
                                              {product.name}
                                            </span>
                                          </div>
                                        </td>
                                        <td className="px-4 py-2 text-right">
                                          {product.price.toLocaleString()}
                                        </td>
                                        <td className="px-4 py-2">
                                          {isSelected && (
                                            <div
                                              className="flex items-center justify-center space-x-1"
                                              onClick={(e) =>
                                                e.stopPropagation()
                                              }
                                            >
                                              <Input
                                                type="number"
                                                min="1"
                                                max="100"
                                                className="w-16 text-center"
                                                value={
                                                  selectedProduct?.discount_percentage ||
                                                  20
                                                }
                                                onChange={(e) =>
                                                  handleDiscountChange(
                                                    product.id,
                                                    parseInt(
                                                      e.target.value,
                                                      10,
                                                    ),
                                                  )
                                                }
                                              />
                                              <span className="text-muted-foreground">
                                                %
                                              </span>
                                            </div>
                                          )}
                                        </td>
                                        <td className="px-4 py-2">
                                          {isSelected && (
                                            <div
                                              onClick={(e) =>
                                                e.stopPropagation()
                                              }
                                            >
                                              <Input
                                                type="number"
                                                min="1"
                                                className="mx-auto w-20 text-center"
                                                placeholder="∞"
                                                value={
                                                  selectedProduct?.quantity_limit ||
                                                  ''
                                                }
                                                onChange={(e) =>
                                                  handleQuantityLimitChange(
                                                    product.id,
                                                    e.target.value,
                                                  )
                                                }
                                              />
                                            </div>
                                          )}
                                        </td>
                                      </tr>
                                    );
                                  })}
                                </tbody>
                              </table>
                            </div>
                          )}
                        </div>

                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => setIsProductDialogOpen(false)}
                          >
                            <Trans i18nKey="flash_sales:cancel">Cancel</Trans>
                          </Button>
                          <Button
                            onClick={handleConfirmProductSelection}
                            disabled={tempSelectedProducts.length === 0}
                          >
                            <Trans i18nKey="flash_sales:add_selected_products">
                              Add Selected Products
                            </Trans>
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    {form.formState.errors.products && (
                      <p className="mt-2 text-sm text-red-500">
                        {form.formState.errors.products.message}
                      </p>
                    )}
                  </div>
                </CardContent>

                <CardFooter className="bg-muted/10 flex justify-between border-t px-6 py-4">
                  <Button variant="outline" type="button" asChild>
                    <Link href={`/home/<USER>/flash-sales`}>
                      <Trans i18nKey="common:cancel">Cancel</Trans>
                    </Link>
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <Trans i18nKey="common:saving">Saving...</Trans>
                    ) : (
                      <Trans i18nKey="flash_sales:save_flash_sale">
                        Save Flash Sale
                      </Trans>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </div>
      </PageBody>
    </>
  );
}
