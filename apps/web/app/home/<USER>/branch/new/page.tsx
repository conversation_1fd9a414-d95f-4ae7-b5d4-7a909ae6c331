import { Suspense } from 'react';

import { Alert, AlertDescription } from '@kit/ui/alert';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { checkCanCreateResource } from '../../_lib/server/resource-access';
import { loadTeamWorkspace } from '../../_lib/server/team-account-workspace.loader';
import { NewBranchWrapper } from './_components/new-branch-wrapper';

export default async function NewBranchPage({
  params,
}: {
  params: { account: string };
}) {
  const { account } = await loadTeamWorkspace(params.account);

  if (!account) {
    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }

  // <PERSON><PERSON><PERSON> tra quyền tạo chi nhánh
  const { canCreate, reason, current: resourceCurrent, limit: resourceLimit } = await checkCanCreateResource(account.id, 'branches');

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="branch:create.title">Create New Branch</Trans>}
        description={
          <Trans i18nKey="branch:create.description">
            Create a new branch for your business
          </Trans>
        }
        account={account.slug}
      />
      <PageBody data-testid="branches-page">
        <Suspense fallback={<div>Loading...</div>}>
          <NewBranchWrapper
            accountId={account.id}
            accountSlug={account.slug}
            canCreate={canCreate}
            reason={reason}
            resourceCurrent={resourceCurrent}
            resourceLimit={resourceLimit}
          />
        </Suspense>
      </PageBody>
    </>
  );
}
