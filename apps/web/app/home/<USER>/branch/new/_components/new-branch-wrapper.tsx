'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ResourceLimitDialog } from '../../../_components/resource-limit-dialog';
import { CreateBranchForm } from './create-branch-form';

interface NewBranchWrapperProps {
  accountSlug: string;
  accountId: string;
  canCreate: boolean;
  reason?: string;
  resourceCurrent?: number;
  resourceLimit?: number;
}

export function NewBranchWrapper({
  accountSlug,
  accountId,
  canCreate,
  reason,
  resourceCurrent,
  resourceLimit,
}: NewBranchWrapperProps) {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(!canCreate);

  // Show dialog if user doesn't have permission
  useEffect(() => {
    if (!canCreate) {
      setIsDialogOpen(true);
    }
  }, [canCreate]);

  const handleDialogClose = async () => {
    setIsDialogOpen(false);
    // Navigate back to branches page
    try {
      router.push(`/home/<USER>/branch`);
    } catch (error) {
      console.error('Error navigating back to branches page:', error);
      // Fallback to using window.location
      window.location.href = `/home/<USER>/branch`;
    }
  };

  return (
    <>
      {canCreate ? (
        <CreateBranchForm accountId={accountId} accountSlug={accountSlug} />
      ) : null}

      <ResourceLimitDialog
        isOpen={isDialogOpen}
        onClose={handleDialogClose}
        reason={reason || 'no_permission'}
        resourceType="branches"
        accountSlug={accountSlug}
        current={resourceCurrent}
        limit={resourceLimit}
      />
    </>
  );
}
