'use server';

import { revalidatePath } from 'next/cache';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';
import { checkCanCreateResource, checkCanUpdateResource } from '~/home/<USER>/_lib/server/resource-access';

import type { BranchFormData } from '../branch.schema';

export async function createBranch({
  name,
  address,
  phone,
  location,
  accountId,
  accountSlug,
}: BranchFormData & { accountId: string; accountSlug: string }) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // Kiểm tra quyền tạo chi nhánh
    const { canCreate, reason } = await checkCanCreateResource(accountId, 'branches');

    if (!canCreate) {
      logger.info({
        accountId,
        reason
      }, 'User cannot create branch');

      return {
        success: false,
        error: reason
      };
    }

    const { data, error } = await supabase
      .from('branches')
      .insert({
        name,
        address,
        phone,
        location,
        account_id: accountId,
      })
      .select()
      .single();

    if (error) {
      logger.error({
        accountId,
        error
      }, 'Error creating branch');

      return {
        success: false,
        error: 'database_error'
      };
    }

    revalidatePath(`/home/<USER>/branch`);

    return {
      success: true,
      data
    };
  } catch (error) {
    logger.error({
      accountId,
      error
    }, 'Error in createBranch action');

    return {
      success: false,
      error: 'unknown_error'
    };
  }
}

export async function updateBranchActive(
  id: string,
  accountId: string,
  accountSlug: string,
  is_active: boolean,
) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // Kiểm tra quyền cập nhật chi nhánh
    const { canUpdate, reason } = await checkCanUpdateResource(accountId, 'branches');

    if (!canUpdate) {
      logger.info({
        accountId,
        branchId: id,
        reason
      }, 'User cannot update branch');

      return {
        success: false,
        error: reason
      };
    }

    const { error } = await supabase
      .from('branches')
      .update({ is_active })
      .eq('id', id)
      .eq('account_id', accountId);

    if (error) {
      logger.error({
        accountId,
        branchId: id,
        error
      }, 'Error updating branch active status');

      return {
        success: false,
        error: 'database_error'
      };
    }

    revalidatePath(`/home/<USER>/branch`);

    return {
      success: true
    };
  } catch (error) {
    logger.error({
      accountId,
      branchId: id,
      error
    }, 'Error in updateBranchActive action');

    return {
      success: false,
      error: 'unknown_error'
    };
  }
}

export async function updateBranch({
  id,
  name,
  address,
  phone,
  location,
  accountId,
  accountSlug,
}: BranchFormData & { id: string; accountId: string; accountSlug: string }) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // Kiểm tra quyền cập nhật chi nhánh
    const { canUpdate, reason } = await checkCanUpdateResource(accountId, 'branches');

    if (!canUpdate) {
      logger.info({
        accountId,
        branchId: id,
        reason
      }, 'User cannot update branch');

      return {
        success: false,
        error: reason
      };
    }

    const { error } = await supabase
      .from('branches')
      .update({
        name,
        address,
        phone,
        location,
      })
      .eq('id', id)
      .eq('account_id', accountId);

    if (error) {
      logger.error({
        accountId,
        branchId: id,
        error
      }, 'Error updating branch');

      return {
        success: false,
        error: 'database_error'
      };
    }

    revalidatePath(`/home/<USER>/branch`);

    return {
      success: true
    };
  } catch (error) {
    logger.error({
      accountId,
      branchId: id,
      error
    }, 'Error in updateBranch action');

    return {
      success: false,
      error: 'unknown_error'
    };
  }
}
