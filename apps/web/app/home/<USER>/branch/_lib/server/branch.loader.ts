import 'server-only';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function loadBranches(
  accountId: string,
  search?: string,
  statusFilter?: string,
) {
  const supabase = getSupabaseServerClient();

  let query = supabase
    .from('branches')
    .select(
      `
      *,
      customer_orders (
        id,
        total_amount
      )
    `,
    )
    .eq('account_id', accountId)
    .order('name', { ascending: true }); // Thêm sắp xếp theo tên A-Z

  if (search) {
    query = query.ilike('name', `%${search}%`);
  }

  if (statusFilter && statusFilter !== 'all') {
    query = query.eq('is_active', statusFilter === 'active');
  }

  const { data: branches, error } = await query;

  if (error) throw error;

  return branches.map((branch) => {
    // Xử lý tọa độ GPS từ location nếu có
    let gpsCoordinates = null;
    if (branch.location) {
      // Ki<PERSON>m tra nếu location có chứa tọa độ GPS (định dạng: số, số)
      const gpsMatch = branch.location.match(/(-?\d+\.\d+)\s*,\s*(-?\d+\.\d+)/);
      if (gpsMatch) {
        gpsCoordinates = {
          lat: parseFloat(gpsMatch[1]),
          lng: parseFloat(gpsMatch[2])
        };
      }
    }

    return {
      id: branch.id,
      name: branch.name,
      address: branch.address,
      is_active: branch.is_active,
      phone: branch.phone,
      location: branch.location,
      gps_coordinates: gpsCoordinates,
      orders: branch.customer_orders?.length || 0,
      revenues:
        branch.customer_orders?.reduce(
          (sum, order) => sum + (order.total_amount || 0),
          0,
        ) || 0,
    };
  });
}
