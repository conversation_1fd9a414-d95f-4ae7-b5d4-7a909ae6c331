import type { Branch } from '../_lib/branch.types';
import { BranchCard } from './branch-card';

interface BranchListProps {
  branches: Branch[];
  accountId: string;
  accountSlug: string;
}

export function BranchList({
  branches,
  accountId,
  accountSlug,
}: BranchListProps) {
  return (
    <div
      data-testid="branch-page-list"
      className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"
    >
      {branches.map((branch) => (
        <BranchCard
          key={branch.id}
          branch={branch}
          accountId={accountId}
          accountSlug={accountSlug}
          data-testid="branch-page-item"
        />
      ))}
    </div>
  );
}
