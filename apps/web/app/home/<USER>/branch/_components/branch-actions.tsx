'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { ResourceLimitDialog } from '../../_components/resource-limit-dialog';

interface BranchActionsProps {
  accountSlug: string;
  accountId: string;
  canCreate: boolean;
  reason?: string;
  resourceCurrent?: number;
  resourceLimit?: number;
}

export function BranchActions({
  accountSlug,
  accountId,
  canCreate,
  reason,
  resourceCurrent,
  resourceLimit,
}: BranchActionsProps) {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleNewBranchClick = async () => {
    try {
      setIsDialogOpen(true);
    } catch (error) {
      console.error('Error handling new branch click:', error);
    }
  };

  return (
    <>
      <Button
        data-testid="new-branch-button"
        onClick={handleNewBranchClick}
        variant={!canCreate ? 'outline' : 'default'}
        className={
          !canCreate
            ? 'bg-amber-50 text-amber-600 hover:bg-amber-100 hover:text-amber-700'
            : ''
        }
      >
        <Plus className="mr-2 h-4 w-4" />
        {!canCreate && reason === 'limit_reached' ? (
          <Trans
            i18nKey="branch:limitReached"
            values={{
              current: resourceCurrent,
              limit: resourceLimit,
            }}
          >
            Upgrade Plan ({resourceCurrent}/{resourceLimit})
          </Trans>
        ) : !canCreate && reason === 'no_active_subscription' ? (
          <Trans i18nKey="branch:subscriptionRequired">
            Subscribe to Add Branch
          </Trans>
        ) : !canCreate ? (
          <Trans i18nKey="branch:upgradeRequired">Upgrade Required</Trans>
        ) : (
          <Trans i18nKey="branch:createNew">Add Branch</Trans>
        )}
      </Button>

      <ResourceLimitDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        reason={reason || 'no_permission'}
        resourceType="branches"
        accountSlug={accountSlug}
        current={resourceCurrent}
        limit={resourceLimit}
      />
    </>
  );
}
