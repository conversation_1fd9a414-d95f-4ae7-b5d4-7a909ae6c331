'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { BranchActions } from './branch-actions';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import { branchSchema } from '../_lib/branch.schema';
import { createBranch } from '../_lib/server/branch.actions';

type BranchFormData = z.infer<typeof branchSchema>;

export default function CreateBranchModal({
  accountId,
  accountSlug,
  canCreate,
  reason,
  resourceCurrent,
  resourceLimit,
}: {
  accountId: string;
  accountSlug: string;
  canCreate: boolean;
  reason?: string;
  resourceCurrent?: number;
  resourceLimit?: number;
}) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const form = useForm<BranchFormData>({
    resolver: zodResolver(branchSchema),
    defaultValues: {
      name: '',
      address: '',
      phone: '',
      location: '',
    },
  });

  const onSubmit = form.handleSubmit(async (data) => {
    try {
      setLoading(true);
      await createBranch({ ...data, accountId, accountSlug });
      setOpen(false);
      form.reset();
      toast.success(
        <Trans i18nKey="branch:create:success">
          Branch created successfully
        </Trans>,
      );
      router.refresh();
    } catch (error) {
      toast.error(
        <Trans i18nKey="branch:create:error">Failed to create Branch</Trans>,
      );
    } finally {
      setLoading(false);
    }
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {canCreate ? (
        <DialogTrigger asChild>
          <Button data-testid="new-branch-button">
            <Plus className="mr-2 h-4 w-4" />
            <Trans i18nKey="branch:createNew">Add Branch</Trans>
          </Button>
        </DialogTrigger>
      ) : (
        <BranchActions
          accountSlug={accountSlug}
          accountId={accountId}
          canCreate={canCreate}
          reason={reason}
          resourceCurrent={resourceCurrent}
          resourceLimit={resourceLimit}
          onOpenCreateModal={() => setOpen(true)}
        />
      )}
      <DialogContent data-testid="branch-page-create-modal">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="branch:create:title">Create New Branch</Trans>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="branch:create:name">Branch Name</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} data-testid="branch-name-input" aria-label="Branch Name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="branch:create:address">Address</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      data-testid="branch-address-input"
                      aria-label="Address"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="branch:create:phone">Phone</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} data-testid="branch-phone-input" aria-label="Phone" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="branch:create:location">Location</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} data-testid="branch-location-input" aria-label="Location" placeholder="10.762622, 106.660172 hoặc Quận 1, TP.HCM" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              disabled={loading}
              data-testid="create-branch-button"
              id="create-branch-submit-button"
              className="branch-submit-button"
            >
              <Trans i18nKey="branch:create:submit">Create Branch</Trans>
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
