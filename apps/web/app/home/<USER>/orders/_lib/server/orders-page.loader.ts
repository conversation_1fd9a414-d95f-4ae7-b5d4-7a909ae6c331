import { SupabaseClient } from '@supabase/supabase-js';

interface LoadOrdersOptions {
  page?: number;
  query?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  customer_id?: string;
}

export type Order = {
  id: string;
  order_code: string;
  customer_id: string;
  customer_email: string;
  customer_name: string;
  branch_id: string | null;
  total_items: number;
  total_amount: number;
  status: string;
  payment_method: string;
  created_at: string;
};

const ITEMS_PER_PAGE = 10;

export async function loadOrders(
  client: SupabaseClient,
  accountId: string,
  options: LoadOrdersOptions = {},
) {
  const page = options.page || 1;
  const offset = (page - 1) * ITEMS_PER_PAGE;

  try {
    let countQuery = client
      .from('customer_orders')
      .select('*', { count: 'exact', head: true })
      .eq('account_id', accountId);

    let query = client
      .from('customer_orders')
      .select(
        `
        *,
        customer_order_items (
          product_id,
          quantity,
          price,
          product_attributes!customer_order_items_attribute_id_fkey (name, value, price_modifier),
          products!customer_order_items_product_id_fkey (id, name)
        )
      `,
      )
      .eq('account_id', accountId);

    // Apply filters
    if (options.query) {
      query = query.ilike(
        'customer_order_items.products.name',
        `%${options.query}%`,
      );
      countQuery = countQuery.ilike(
        'customer_order_items.products.name',
        `%${options.query}%`,
      );
    }
    if (options.status) {
      query = query.eq('status', options.status);
      countQuery = countQuery.eq('status', options.status);
    }
    if (options.startDate) {
      query = query.gte('created_at', options.startDate);
      countQuery = countQuery.gte('created_at', options.startDate);
    }
    if (options.endDate) {
      query = query.lte('created_at', options.endDate);
      countQuery = countQuery.lte('created_at', options.endDate);
    }
    if (options.customer_id) {
      query = query.eq('customer_id', options.customer_id);
      countQuery = countQuery.eq('customer_id', options.customer_id);
    }

    const { count } = await countQuery;
    const total = count || 0;

    const { data: orders, error } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + ITEMS_PER_PAGE - 1);

    if (error) throw error;

    // Get customer IDs from orders
    const customerIds = orders?.map(order => order.customer_id).filter(Boolean) || [];

    // Get customer information from accounts table
    const { data: customerAccounts } = await client
      .from('accounts')
      .select('id, primary_owner_user_id, name, email')
      .eq('is_personal_account', true)
      .in('primary_owner_user_id', customerIds);

    // Create a map of customer_id to customer info
    const customerMap = (customerAccounts || []).reduce((map, account) => {
      map[account.primary_owner_user_id] = account;
      return map;
    }, {} as Record<string, any>);

    const formattedOrders = (orders || []).map((order) => {
      const firstItem = order.customer_order_items[0] || {};
      const customerAccount = customerMap[order.customer_id] || {};

      // Calculate total items in the order
      const totalItems = order.customer_order_items.reduce((sum, item) => sum + (item.quantity || 0), 0);

      return {
        id: order.id,
        order_code: order.order_code || `ORD-${order.id.slice(0, 6)}`,
        customer_id: order.customer_id,
        customer_email: customerAccount.email || '',
        customer_name: customerAccount.name || '',
        branch_id: order.branch_id,
        total_items: totalItems,
        total_amount: order.total_amount,
        status: order.status,
        payment_method: order.payment_method,
        created_at: order.created_at,
      };
    }) as Order[];

    return {
      orders: formattedOrders,
      total,
      canManageOrders: true,
    };
  } catch (error) {
    console.error('Failed to load orders:', error);
    throw error;
  }
}
