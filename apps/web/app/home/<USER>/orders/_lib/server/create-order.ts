'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { OrderFormData } from '../order.schema';

interface CreateOrderParams extends OrderFormData {
  accountId: string;
  accountSlug: string;
  branch_id?: string; // Optional, added for flexibility
}

export async function createOrder({
  customer_id,
  product_id,
  branch_id,
  quantity,
  total_amount,
  status,
  payment_method,
  accountId,
  accountSlug,
}: CreateOrderParams) {
  const client = getSupabaseServerClient();

  // Prepare items for the order (single item for simplicity, extend for multi-item support)
  const items = [
    {
      product_id,
      attribute_id: null, // Placeholder; will be updated in UI later
      quantity,
      price: total_amount / quantity, // Assuming total_amount is pre-calculated
    },
  ];

  // Sử dụng hàm createOrderWithEvents để tạo đơn hàng và phát sự kiện
  const { createOrderWithEvents } = await import('./order-events');
  const data = await createOrderWithEvents({
    account_id: accountId,
    customer_id,
    branch_id: branch_id || null,
    items,
    subtotal: total_amount, // Giả sử subtotal = total_amount vì không có giảm giá
    discount: 0,
    total_amount,
    payment_method,
    status,
    team_account_id: accountId,
  });

  if (!data || data.status === 'error') {
    throw new Error(
      data?.message || 'Failed to create order',
    );
  }

  revalidatePath(`/home/<USER>/orders`);
  return { order_id: data.order_id };
}
