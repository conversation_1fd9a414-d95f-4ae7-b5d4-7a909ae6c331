import { z } from 'zod';

export const orderSchema = z.object({
  customer_id: z.string().uuid(),
  product_id: z.string().uuid(),
  branch_id: z.string().uuid().optional(),
  quantity: z.number().min(1),
  total_amount: z.number().min(0),
  status: z.enum(['pending', 'processing', 'completed', 'cancelled']),
  payment_method: z.enum(['cash', 'card', 'transfer']),
});

export type OrderFormData = z.infer<typeof orderSchema>;
