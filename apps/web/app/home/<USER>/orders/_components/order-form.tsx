'use client';

import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@kit/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';

import type { OrderDetail } from '../_lib/server/order-detail.loader';
import { updateOrder } from '../_lib/server/update-order';

const orderSchema = z.object({
  status: z.enum(['pending', 'processing', 'completed', 'cancelled']),
  payment_method: z.enum(['cash', 'card', 'transfer']),
});

type OrderFormData = z.infer<typeof orderSchema>;

interface OrderFormProps {
  order: OrderDetail & { account_id: string };
  account: { id: string; slug: string };
}

export default function OrderForm({ order, account }: OrderFormProps) {
  const router = useRouter();
  const { t } = useTranslation();
  const form = useForm<OrderFormData>({
    resolver: zodResolver(orderSchema),
    defaultValues: {
      status: order.status as any,
      payment_method: order.payment_method as any,
    },
  });

  const onSubmit = async (data: OrderFormData) => {
    try {
      await updateOrder({
        id: order.id,
        customer_id: order.customer_id,
        product_id: order.product_id,
        branch_id: order.branch_id,
        quantity: order.quantity,
        total_amount: order.total_amount,
        status: data.status,
        payment_method: data.payment_method,
        accountId: account.id,
        accountSlug: account.slug,
      });
      router.push(`/home/<USER>/orders`);
    } catch (error) {
      console.error('Failed to update order:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <Trans i18nKey="orders:form:status">Status</Trans>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="pending">{t('orders:status.pending', 'Pending')}</SelectItem>
                  <SelectItem value="processing">{t('orders:status.processing', 'Processing')}</SelectItem>
                  <SelectItem value="completed">{t('orders:status.completed', 'Completed')}</SelectItem>
                  <SelectItem value="cancelled">{t('orders:status.cancelled', 'Cancelled')}</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="payment_method"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <Trans i18nKey="orders:form:payment">Payment Method</Trans>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="cash">{t('orders:paymentMethods.cash', 'Cash')}</SelectItem>
                  <SelectItem value="card">{t('orders:paymentMethods.card', 'Card')}</SelectItem>
                  <SelectItem value="transfer">{t('orders:paymentMethods.transfer', 'Transfer')}</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-3">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            <Trans i18nKey="common:cancel">Cancel</Trans>
          </Button>
          <Button type="submit">
            <Trans i18nKey="common:save">Save</Trans>
          </Button>
        </div>
      </form>
    </Form>
  );
}
