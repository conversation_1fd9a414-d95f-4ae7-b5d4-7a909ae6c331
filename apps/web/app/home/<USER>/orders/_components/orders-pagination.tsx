'use client';

import { Pagination } from '@kit/ui/pagination';

interface OrdersPaginationProps {
  currentPage: number;
  limit: number;
  total: number;
  filters?: {
    query?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    customer_id?: string;
  };
}

export function OrdersPagination({
  currentPage,
  limit,
  total,
  filters,
}: OrdersPaginationProps) {
  const getPageUrl = (page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set('page', page.toString());

    // Add filters to URL if they exist
    if (filters?.query) {
      url.searchParams.set('query', filters.query);
    }
    if (filters?.status) {
      url.searchParams.set('status', filters.status);
    }
    if (filters?.startDate) {
      url.searchParams.set('startDate', filters.startDate);
    }
    if (filters?.endDate) {
      url.searchParams.set('endDate', filters.endDate);
    }
    if (filters?.customer_id) {
      url.searchParams.set('customer_id', filters.customer_id);
    }

    return url.toString();
  };

  return (
    <Pagination
      currentPage={currentPage}
      pageSize={limit}
      total={total}
      showItemsCounter
      showFirstLastButtons
      className="bg-white"
      data-testid="orders-pagination"
      getPageUrl={getPageUrl}
    />
  );
}
