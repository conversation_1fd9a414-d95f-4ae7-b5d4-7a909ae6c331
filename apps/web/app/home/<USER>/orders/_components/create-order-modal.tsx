'use client';

import React, { useEffect, useMemo, useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { SearchableSelect } from '@kit/ui/searchable-select';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import { orderSchema } from '../_lib/order.schema';
import { createOrder } from '../_lib/server/create-order';

interface CreateOrderModalProps {
  children: React.ReactNode;
  account: { id: string; slug: string };
}

interface Customer {
  id: string;
  name: string;
  email: string;
}

interface Branch {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
}

interface ProductAttribute {
  id: string;
  name: string;
  value: string;
  price_modifier: number;
}

interface PageData<T> {
  data: T[];
  nextPage: number | null;
}

export default function CreateOrderModal({
  children,
  account,
}: CreateOrderModalProps) {
  const [open, setOpen] = useState(false);
  const [productSearch, setProductSearch] = useState('');
  const [customerSearch, setCustomerSearch] = useState('');
  const [branchSearch, setBranchSearch] = useState('');
  const [selectedProductId, setSelectedProductId] = useState<
    string | undefined
  >();

  const client = useSupabase();
  const ITEMS_PER_PAGE = 20;

  // Fetch customers
  const { data: customersData } = useInfiniteQuery<PageData<Customer>>({
    queryKey: ['customers', account?.id, customerSearch],
    initialPageParam: 0,
    queryFn: async ({ pageParam }) => {
      const from = (pageParam as number) * ITEMS_PER_PAGE;
      const to = from + ITEMS_PER_PAGE - 1;
      let query = client
        .from('accounts')
        .select('id, email, name', { count: 'exact' })
        .eq('type', 'customer')
        .order('name')
        .range(from, to);
      if (customerSearch) {
        query = query.or(
          `name.ilike.%${customerSearch}%,email.ilike.%${customerSearch}%`,
        );
      }
      const { data, count } = await query;
      return {
        data: data || [],
        nextPage: count && from + ITEMS_PER_PAGE < count ? pageParam + 1 : null,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: !!account?.id,
  });

  const customers = useMemo(
    () => customersData?.pages.flatMap((page) => page.data) ?? [],
    [customersData],
  );

  // Fetch branches
  const { data: branchesData } = useInfiniteQuery<PageData<Branch>>({
    queryKey: ['branches', account?.id, branchSearch],
    initialPageParam: 0,
    queryFn: async ({ pageParam }) => {
      const from = (pageParam as number) * ITEMS_PER_PAGE;
      const to = from + ITEMS_PER_PAGE - 1;
      let query = client
        .from('branches')
        .select('id, name', { count: 'exact' })
        .eq('account_id', account.id)
        .eq('is_active', true)
        .order('name')
        .range(from, to);
      if (branchSearch) {
        query = query.ilike('name', `%${branchSearch}%`);
      }
      const { data, count } = await query;
      return {
        data: data || [],
        nextPage: count && from + ITEMS_PER_PAGE < count ? pageParam + 1 : null,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: !!account?.id,
  });

  const branches = useMemo(
    () => branchesData?.pages.flatMap((page) => page.data) ?? [],
    [branchesData],
  );

  // Fetch products
  const { data: productsData } = useInfiniteQuery<PageData<Product>>({
    queryKey: ['products', account?.id, productSearch],
    initialPageParam: 0,
    queryFn: async ({ pageParam }) => {
      const from = (pageParam as number) * ITEMS_PER_PAGE;
      const to = from + ITEMS_PER_PAGE - 1;
      let query = client
        .from('products')
        .select('id, name, price', { count: 'exact' })
        .eq('account_id', account.id)
        .eq('status', 'active')
        .order('name')
        .range(from, to);
      if (productSearch) {
        query = query.ilike('name', `%${productSearch}%`);
      }
      const { data, count } = await query;
      return {
        data: data || [],
        nextPage: count && from + ITEMS_PER_PAGE < count ? pageParam + 1 : null,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: !!account?.id,
  });

  const products = useMemo(
    () => productsData?.pages.flatMap((page) => page.data) ?? [],
    [productsData],
  );

  // Fetch attributes for selected product
  const { data: attributesData } = useInfiniteQuery<PageData<ProductAttribute>>(
    {
      queryKey: ['product_attributes', selectedProductId],
      initialPageParam: 0,
      queryFn: async ({ pageParam }) => {
        if (!selectedProductId) return { data: [], nextPage: null };
        const from = (pageParam as number) * ITEMS_PER_PAGE;
        const to = from + ITEMS_PER_PAGE - 1;
        const { data, count } = await client
          .from('product_attributes')
          .select('id, name, value, price_modifier')
          .eq('product_id', selectedProductId)
          .order('name')
          .range(from, to);
        return {
          data: data || [],
          nextPage:
            count && from + ITEMS_PER_PAGE < count ? pageParam + 1 : null,
        };
      },
      getNextPageParam: (lastPage) => lastPage.nextPage,
      enabled: !!selectedProductId,
    },
  );

  const attributes = useMemo(
    () => attributesData?.pages.flatMap((page) => page.data) ?? [],
    [attributesData],
  );

  const form = useForm({
    resolver: zodResolver(
      orderSchema.extend({ branch_id: z.string().uuid().optional() }),
    ),
    defaultValues: {
      customer_id: '',
      product_id: '',
      branch_id: '',
      quantity: 1,
      total_amount: 0,
      status: 'pending',
      payment_method: 'cash',
      attribute_id: '',
    },
  });

  const watchProduct = form.watch('product_id');
  const watchQuantity = form.watch('quantity');
  const watchAttribute = form.watch('attribute_id');

  useEffect(() => {
    setSelectedProductId(watchProduct);
  }, [watchProduct]);

  const selectedProduct = products.find((p) => p.id === watchProduct);
  const selectedAttribute = attributes.find((a) => a.id === watchAttribute);
  const totalAmount = selectedProduct
    ? (selectedProduct.price + (selectedAttribute?.price_modifier || 0)) *
      watchQuantity
    : 0;

  form.setValue('total_amount', totalAmount);

  const onSubmit = form.handleSubmit(async (data) => {
    try {
      await createOrder({
        ...data,
        accountId: account.id,
        accountSlug: account.slug,
        total_amount: totalAmount,
      });
      setOpen(false);
      form.reset();
      toast.success('Order created successfully');
    } catch (error) {
      toast.error('Failed to create order');
    }
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="orders:create:title">Create New Order</Trans>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="customer_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="orders:create:customer">Customer</Trans>
                  </FormLabel>
                  <FormControl>
                    <SearchableSelect
                      items={(customers || []).map((customer) => ({
                        id: customer.id,
                        label: customer.name || customer.email,
                      }))}
                      value={field.value}
                      onValueChange={field.onChange}
                      onSearch={setCustomerSearch}
                      placeholder="Select a customer"
                      searchPlaceholder="Search customers..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="product_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="orders:create:product">Product</Trans>
                  </FormLabel>
                  <FormControl>
                    <SearchableSelect
                      items={(products || []).map((product) => ({
                        id: product.id,
                        label: `${product.name} ($${product.price})`,
                      }))}
                      value={field.value}
                      onValueChange={field.onChange}
                      onSearch={setProductSearch}
                      placeholder="Select a product"
                      searchPlaceholder="Search products..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {attributes.length > 0 && (
              <FormField
                control={form.control}
                name="attribute_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Trans i18nKey="orders:create:attribute">Attribute</Trans>
                    </FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select attribute" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {attributes.map((attr) => (
                          <SelectItem key={attr.id} value={attr.id}>
                            {attr.name}: {attr.value}
                            {attr.price_modifier > 0 &&
                              ` (+${attr.price_modifier})`}
                            {attr.price_modifier < 0 &&
                              ` (${attr.price_modifier})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="branch_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="orders:create:branch">Branch</Trans>
                  </FormLabel>
                  <FormControl>
                    <SearchableSelect
                      items={(branches || []).map((branch) => ({
                        id: branch.id,
                        label: branch.name,
                      }))}
                      value={field.value}
                      onValueChange={field.onChange}
                      onSearch={setBranchSearch}
                      placeholder="Select a branch"
                      searchPlaceholder="Search branches..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="orders:create:quantity">Quantity</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value, 10))
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="orders:create:status">Status</Trans>
                  </FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="payment_method"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="orders:create:paymentMethod">
                      Payment Method
                    </Trans>
                  </FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="card">Card</SelectItem>
                      <SelectItem value="transfer">Transfer</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                <Trans i18nKey="common:cancel">Cancel</Trans>
              </Button>
              <Button type="submit">
                <Trans i18nKey="orders:create:submit">Create Order</Trans>
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
