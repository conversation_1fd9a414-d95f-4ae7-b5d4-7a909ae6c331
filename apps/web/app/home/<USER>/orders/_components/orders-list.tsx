'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';

import type { ColumnDef } from '@tanstack/react-table';
import { Eye, Trash2 } from 'lucide-react';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';

import { FormattedPrice } from '../../../../../components/currency/price-display';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { ResponsiveDataTable } from '@kit/ui/responsive-data-table';
import { formatDate } from '@kit/ui/utils';

import type { Order } from '../_lib/server/orders-page.loader';
import DeleteOrderButton from './delete-order-button';
import { OrdersPagination } from './orders-pagination';

interface OrdersListProps {
  orders: Order[];
  canManage: boolean;
  account: { id: string; slug: string };
  currentPage: number;
  limit: number;
  total: number;
  filters?: {
    query?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    customer_id?: string;
  };
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success';
    case 'processing':
      return 'warning';
    case 'cancelled':
      return 'destructive';
    default:
      return 'default';
  }
};

export default function OrdersList({
  orders,
  canManage,
  account,
  currentPage,
  limit,
  total,
  filters = {},
}: OrdersListProps) {
  const { t } = useTranslation();
  const columns: ColumnDef<Order>[] = [
    {
      accessorKey: 'order_code',
      header: t('orders:table:orderCode'),
      cell: ({ row }) => {
        const id = row.original.id;
        const orderCode = row.original.order_code || `ORD-${id.slice(0, 6)}`;
        return (
          <Link
            href={`/home/<USER>/orders/${id}`}
            className="text-blue-600 hover:underline"
          >
            <code className="text-sm">{orderCode}</code>
          </Link>
        );
      },
    },
    {
      accessorKey: 'customer_name',
      header: t('orders:table:customer'),
      cell: ({ row }) => {
        const name = row.original.customer_name || row.original.customer_email || 'N/A';
        const customerId = row.original.customer_id;

        if (!customerId) return <span>{name}</span>;

        return (
          <div className="flex items-center gap-2">
            <Link
              href={`/home/<USER>/customers/${customerId}`}
              className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
            >
              {name}
            </Link>
          </div>
        );
      },
    },
    {
      accessorKey: 'total_items',
      header: t('orders:table:totalItems'),
      cell: ({ row }) => {
        const totalItems = row.original.total_items || 0;
        return (
          <span className="font-medium">{totalItems} {totalItems === 1 ? t('orders:item') : t('orders:items')}</span>
        );
      },
    },
    {
      accessorKey: 'total_amount',
      header: t('orders:table:total'),
      cell: ({ row }) => <FormattedPrice amount={row.original.total_amount} />,
    },
    {
      accessorKey: 'status',
      header: t('orders:table:status'),
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge variant={getStatusColor(status)}>
            {t(`orders:status.${status}`, { defaultValue: status })}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'payment_method',
      header: t('orders:table:payment'),
      cell: ({ row }) => {
        const method = row.original.payment_method;
        return t(`orders:paymentMethods.${method}`, { defaultValue: method });
      },
    },
    {
      accessorKey: 'created_at',
      header: t('orders:table:createdAt'),
      cell: ({ row }) => formatDate(row.original.created_at, 'dd/MM/yyyy'),
    },
  ];

  if (canManage) {
    columns.push({
      id: 'actions',
      header: t('orders:table:actions'),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button size="icon" variant="ghost" asChild>
            <Link href={`/home/<USER>/orders/${row.original.id}`}>
              <Eye className="h-4 w-4" />
            </Link>
          </Button>
          <DeleteOrderButton order={row.original} account={account}>
            <Button size="icon" variant="ghost">
              <Trash2 className="h-4 w-4" />
            </Button>
          </DeleteOrderButton>
        </div>
      ),
    });
  }

  // Fetch customer name if customer_id filter is applied
  const [customerName, setCustomerName] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomerName = async () => {
      if (filters?.customer_id) {
        try {
          // This would typically be an API call, but for simplicity we'll check if the customer is in the orders list
          const customerOrder = orders.find(order => order.customer_id === filters.customer_id);
          if (customerOrder) {
            setCustomerName(customerOrder.customer_name);
          }
        } catch (error) {
          console.error('Error fetching customer name:', error);
        }
      }
    };

    fetchCustomerName();
  }, [filters?.customer_id, orders]);

  return (
    <div className="flex flex-col space-y-6">
      {filters?.customer_id && customerName && (
        <div className="mb-4 flex items-center rounded-md bg-blue-50 p-3 text-blue-800">
          <div className="mr-2 text-sm font-medium">
            {t('orders:filteringByCustomer', 'Filtering by customer')}: <span className="font-bold">{customerName}</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="ml-auto h-8 text-blue-600 hover:text-blue-800"
            asChild
          >
            <Link href={`/home/<USER>/orders`}>
              {t('orders:clearFilter', 'Clear filter')}
            </Link>
          </Button>
        </div>
      )}

      <ResponsiveDataTable
        data={orders}
        columns={columns}
        manualPagination={true}
        mobileCardFields={['order_code', 'customer_name', 'total_amount', 'status', 'created_at']}
        renderMobileCard={(row) => (
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex-1">
                <Link
                  href={`/home/<USER>/orders/${row.original.id}`}
                  className="text-blue-600 hover:text-blue-800 text-lg font-semibold flex items-center"
                >
                  <span className="mr-2">{row.original.order_code || `ORD-${row.original.id.slice(0, 6)}`}</span>
                </Link>
                <div className="text-xs text-muted-foreground mt-1">
                  {formatDate(row.original.created_at, 'dd/MM/yyyy')}
                </div>
              </div>
              <Badge variant={getStatusColor(row.original.status)} className="ml-2 px-3 py-1 text-xs font-medium uppercase">
                {t(`orders:status.${row.original.status}`, { defaultValue: row.original.status })}
              </Badge>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800/30 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('orders:table:customer')}</div>
                  <div className="font-medium">
                    {row.original.customer_id ? (
                      <Link
                        href={`/home/<USER>/customers/${row.original.customer_id}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {row.original.customer_name || row.original.customer_email || 'N/A'}
                      </Link>
                    ) : (
                      <span>{row.original.customer_name || row.original.customer_email || 'N/A'}</span>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('orders:table:total')}</div>
                  <div className="font-semibold text-lg"><FormattedPrice amount={row.original.total_amount} /></div>
                </div>
              </div>

              <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div>
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('orders:table:totalItems')}</div>
                  <div className="font-medium">{row.original.total_items || 0} {(row.original.total_items || 0) === 1 ? t('orders:item') : t('orders:items')}</div>
                </div>
              </div>
            </div>

            {canManage && (
              <div className="flex justify-end gap-2">
                <Button size="sm" variant="outline" className="rounded-full" asChild>
                  <Link href={`/home/<USER>/orders/${row.original.id}`}>
                    <Eye className="h-4 w-4 mr-1" />
                    <Trans i18nKey="orders:view">View</Trans>
                  </Link>
                </Button>
                <DeleteOrderButton order={row.original} account={account}>
                  <Button size="sm" variant="outline" className="rounded-full">
                    <Trash2 className="h-4 w-4 mr-1" />
                    <Trans i18nKey="common:actions:delete">Delete</Trans>
                  </Button>
                </DeleteOrderButton>
              </div>
            )}
          </div>
        )}
      />
      <OrdersPagination currentPage={currentPage} limit={limit} total={total} filters={filters} />
    </div>
  );
}
