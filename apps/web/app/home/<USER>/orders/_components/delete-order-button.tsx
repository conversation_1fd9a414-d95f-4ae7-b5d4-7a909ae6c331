'use client';

import { useState } from 'react';

import { toast } from 'sonner';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Trans } from '@kit/ui/trans';

import { deleteOrder } from '../_lib/server/delete-order';

interface DeleteOrderButtonProps {
  children: React.ReactNode;
  order: { id: string };
  account: { id: string; slug: string };
}

export default function DeleteOrderButton({
  children,
  order,
  account,
}: DeleteOrderButtonProps) {
  const [open, setOpen] = useState(false);

  const onDelete = async () => {
    try {
      await deleteOrder({
        id: order.id,
        accountId: account.id,
      });

      toast.success('Order deleted successfully');
    } catch (error) {
      toast.error('Failed to delete order');
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey="orders:delete:title">Delete Order</Trans>
          </AlertDialogTitle>
          <AlertDialogDescription>
            <Trans i18nKey="orders:delete:description">
              Are you sure you want to delete this order? This action cannot be
              undone.
            </Trans>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            <Trans i18nKey="common:actions:cancel">Cancel</Trans>
          </AlertDialogCancel>
          <AlertDialogAction onClick={onDelete} data-testid="confirm-delete-button">
            <Trans i18nKey="common:actions:delete">Delete</Trans>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
