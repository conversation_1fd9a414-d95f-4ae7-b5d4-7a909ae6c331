'use client';

import { useState } from 'react';

import { useParams } from 'next/navigation';

import { Button } from '@kit/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter, CardHeader, CardTitle } from '@kit/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';
import { useToast } from '@kit/ui/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { applyVoucherToOrderAction } from '../_lib/server/voucher.actions';

const VoucherFormSchema = z.object({
  voucherCode: z.string().min(1, 'Voucher code is required'),
});

type VoucherFormValues = z.infer<typeof VoucherFormSchema>;

interface VoucherFormProps {
  orderId: string;
  onSuccess?: (discountAmount: number, newTotal: number) => void;
}

export function VoucherForm({ orderId, onSuccess }: VoucherFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const params = useParams<{ account: string }>();
  const accountSlug = params.account;

  const form = useForm<VoucherFormValues>({
    resolver: zodResolver(VoucherFormSchema),
    defaultValues: {
      voucherCode: '',
    },
  });

  const onSubmit = async (values: VoucherFormValues) => {
    setIsSubmitting(true);

    try {
      const result = await applyVoucherToOrderAction({
        orderId,
        voucherCode: values.voucherCode,
        accountSlug,
      });

      if (result.success) {
        toast({
          title: 'Voucher applied',
          description: `Discount of ${result.discount_amount?.toLocaleString()} applied to order`,
          variant: 'success',
        });

        form.reset();

        if (onSuccess && result.discount_amount && result.new_total) {
          onSuccess(result.discount_amount, result.new_total);
        }
      } else {
        toast({
          title: 'Failed to apply voucher',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey="orders:apply_voucher">Apply Voucher</Trans>
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent>
            <FormField
              control={form.control}
              name="voucherCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="orders:voucher_code">Voucher Code</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter voucher code"
                      className="uppercase"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <Trans i18nKey="common:applying">Applying...</Trans>
              ) : (
                <Trans i18nKey="common:apply">Apply</Trans>
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
