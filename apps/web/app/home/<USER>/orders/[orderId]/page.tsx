import { Alert, AlertDescription } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '../../_lib/server/team-account-workspace.loader';
import { InvoiceHeader } from './_components/invoice-header';
import { InvoiceItems } from './_components/invoice-items';
import { OrderActions } from './_components/order-actions';
import { loadOrderDetail } from './_lib/server/order-detail.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('orders:pageTitle');

  return {
    title,
  };
};

interface OrderDetailPageProps {
  params: Promise<{
    account: string;
    orderId: string;
  }>;
}

async function OrderDetailPage({
  params,
}: OrderDetailPageProps) {
  const { account, orderId } = await params;
  try {
    const [order, { user, account: workspace }] = await Promise.all([
      loadOrderDetail(account, orderId),
      loadTeamWorkspace(account),
    ]);

    if (!order || !workspace) {
      return (
        <PageBody data-testid="order-detail-page">
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:orderNotFound">
                Order not found
              </Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    return (
      <>
        <TeamAccountLayoutPageHeader
          title={`Invoice: ${order.invoice_number}`}
          description={<AppBreadcrumbs />}
          account={workspace.slug}
        />

        <PageBody data-testid="order-detail-page" className="max-w-5xl mx-auto">
          <OrderActions
            orderId={orderId}
            accountSlug={account}
            status={order.status}
          />

          <div className="rounded-lg border border-gray-200 bg-white shadow-md overflow-hidden">
            <InvoiceHeader invoice={order} />
            <InvoiceItems
              items={order.items}
              total_amount={order.total_amount}
            />
          </div>
        </PageBody>
      </>
    );
  } catch (error: any) {
    console.error('Error loading order detail page:', {
      error: error.message,
      params: { account, orderId },
      context: 'order-detail.page',
    });

    return (
      <PageBody data-testid="order-error-page" className="max-w-5xl mx-auto">
        <div className="flex flex-col items-center justify-center rounded-lg border border-red-200 bg-red-50 p-8 text-center shadow-sm">
          <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 text-red-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="mb-2 text-2xl font-bold text-gray-900">
            <Trans i18nKey="orders:detail.errorTitle">Order Not Found</Trans>
          </h2>
          <p className="mb-6 text-gray-600">
            <Trans i18nKey="orders:detail.errorDescription">
              The order you are looking for does not exist or has been removed.
            </Trans>
          </p>
          <Button variant="outline" onClick={() => window.history.back()} className="gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            <Trans i18nKey="common:actions.back">Go Back</Trans>
          </Button>
        </div>
      </PageBody>
    );
  }
}

export default withI18n(OrderDetailPage);
