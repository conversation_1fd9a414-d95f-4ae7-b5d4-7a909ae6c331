'use client';

import { Download } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

export function PdfButton() {
  const handlePrint = () => {
    window.print();
  };

  return (
    <Button
      variant="outline"
      onClick={handlePrint}
      data-testid="pdf-button"
      className="gap-2 bg-blue-50 px-6 py-2 text-blue-700 shadow-sm transition-all hover:bg-blue-100 hover:shadow"
    >
      <Download className="h-4 w-4" />
      <Trans i18nKey="orders:detail.downloadPdf" />
    </Button>
  );
}
