'use client';

import { formatDate } from '@kit/ui/utils';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';

interface InvoiceHeaderProps {
  invoice: {
    order_code: string;
    invoice_number: string;
    created_at: string;
    due_date: string;
    status: string;
    payment_method?: string;
    company_info: {
      name: string;
      phone: string;
      address: string;
    };
    customer_name: string;
    customer_email: string;
    customer_address: string;
  };
}

export function InvoiceHeader({ invoice }: InvoiceHeaderProps) {
  const { t } = useTranslation();
  return (
    <div className="space-y-8 rounded-t-lg bg-gradient-to-r from-blue-100 via-blue-50 to-white p-8 shadow-sm" data-testid="invoice-header">
      <div className="flex flex-col justify-between gap-8 sm:flex-row">
        <div>
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-600 text-white shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              <Trans i18nKey="orders:detail.invoice" /> <span className="text-blue-600">{invoice.order_code}</span>
            </h1>
            <p className="text-sm text-gray-500 ml-12">
              {invoice.invoice_number}
            </p>
          </div>

          <div className="mt-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
            <h2 className="text-lg font-semibold text-gray-800">
              {invoice.company_info.name}
            </h2>
            <div className="mt-2 text-sm text-gray-600">
              <p>{invoice.company_info.address}</p>
              <p>{invoice.company_info.phone}</p>
            </div>
          </div>
        </div>

        <div className="flex flex-col justify-between">
          <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
            <h2 className="text-lg font-semibold text-gray-800"><Trans i18nKey="orders:detail.invoiceTo" /></h2>
            <div className="mt-2 text-sm text-gray-600">
              <p className="font-medium">{invoice.customer_name}</p>
              <p>{invoice.customer_email}</p>
              <p>{invoice.customer_address}</p>
            </div>
          </div>

          <div className="mt-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs font-medium uppercase text-gray-500"><Trans i18nKey="orders:detail.invoiceDate" /></p>
                <p className="text-sm font-semibold text-gray-800">{formatDate(invoice.created_at)}</p>
              </div>
              <div>
                <p className="text-xs font-medium uppercase text-gray-500"><Trans i18nKey="orders:detail.dueDate" /></p>
                <p className="text-sm font-semibold text-gray-800">{formatDate(invoice.due_date)}</p>
              </div>
              <div>
                <p className="text-xs font-medium uppercase text-gray-500"><Trans i18nKey="orders:fields.status" /></p>
                <p className={`text-sm font-semibold ${invoice.status === 'approved' || invoice.status === 'completed' ? 'text-green-600' : invoice.status === 'canceled' || invoice.status === 'cancelled' ? 'text-red-600' : invoice.status === 'delivered' ? 'text-purple-600' : 'text-yellow-600'}`}>
                  {t(`orders:status.${invoice.status}`, { defaultValue: invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1) })}
                </p>
              </div>
              <div>
                <p className="text-xs font-medium uppercase text-gray-500"><Trans i18nKey="orders:detail.paymentMethod" /></p>
                <p className="text-sm font-semibold text-gray-800">
                  {invoice.payment_method ? t(`orders:paymentMethods.${invoice.payment_method}`, { defaultValue: invoice.payment_method }) : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
