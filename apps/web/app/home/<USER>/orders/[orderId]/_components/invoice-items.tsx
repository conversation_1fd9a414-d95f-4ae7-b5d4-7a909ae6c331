import { Trans } from '@kit/ui/trans';
import { PdfButton } from './pdf-button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { FormattedPrice } from '../../../../../../components/currency/price-display';

interface InvoiceItemsProps {
  items: Array<{
    description: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  total_amount: number;
}

export function InvoiceItems({ items, total_amount }: InvoiceItemsProps) {
  return (
    <div className="p-8 bg-white" data-testid="invoice-items">
      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm">
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead className="py-3 font-semibold"><Trans i18nKey="orders:detail.description" /></TableHead>
              <TableHead className="py-3 text-right font-semibold"><Trans i18nKey="orders:detail.quantity" /></TableHead>
              <TableHead className="py-3 text-right font-semibold"><Trans i18nKey="orders:detail.price" /></TableHead>
              <TableHead className="py-3 text-right font-semibold"><Trans i18nKey="orders:detail.total" /></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item, index) => (
              <TableRow key={index} className="hover:bg-gray-50">
                <TableCell className="py-3 font-medium">{item.description}</TableCell>
                <TableCell className="py-3 text-right">{item.quantity}</TableCell>
                <TableCell className="py-3 text-right">
                  <FormattedPrice amount={item.price} />
                </TableCell>
                <TableCell className="py-3 text-right font-medium">
                  <FormattedPrice amount={item.total} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="mt-6 flex justify-end">
        <div className="w-full max-w-xs space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 shadow-sm">
          <div className="flex justify-between border-b pb-2">
            <span className="text-sm font-medium text-gray-800"><Trans i18nKey="orders:detail.totalAmount" /></span>
            <span className="text-sm font-bold text-blue-600">
              <FormattedPrice amount={total_amount} size="lg" />
            </span>
          </div>
        </div>
      </div>
      <div className="mt-8 rounded-lg border border-gray-200 bg-gray-50 p-5 text-sm text-gray-600">
        <h3 className="mb-3 font-semibold text-gray-800"><Trans i18nKey="orders:detail.termsAndConditions" /></h3>
        <div className="space-y-3">
          <p>
            <Trans i18nKey="orders:detail.termsAndConditionsText" />
          </p>
          <p>
            <Trans i18nKey="orders:detail.latePaymentText" />
          </p>
        </div>
      </div>

      <div className="mt-8 flex justify-center">
        <PdfButton />
      </div>
    </div>
  );
}
