import { Suspense } from 'react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { SearchListInput } from '@kit/ui/search-list-input';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '../_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';
import OrdersList from './_components/orders-list';
import { loadOrders } from './_lib/server/orders-page.loader';

interface PageProps {
  params: Promise<{ account: string }>;
  searchParams: Promise<{
    page?: string;
    query?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    customer_id?: string;
  }>;
}

export default async function OrdersPage({ params, searchParams }: PageProps) {
  const { account: accountSlug } = await params;
  const resolvedSearchParams = await searchParams;

  const client = getSupabaseServerClient();
  const api = createTeamAccountsApi(client);

  try {
    const teamAccount = await api.getTeamAccount(accountSlug);

    if (!teamAccount) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const currentPage = resolvedSearchParams.page
      ? parseInt(resolvedSearchParams.page)
      : 1;
    const query = resolvedSearchParams.query || '';
    const status = resolvedSearchParams.status;
    const startDate = resolvedSearchParams.startDate;
    const endDate = resolvedSearchParams.endDate;
    const customer_id = resolvedSearchParams.customer_id;

    const [{ orders, total }, { user, account: workspace }] = await Promise.all(
      [
        loadOrders(client, teamAccount.id, {
          page: currentPage,
          query,
          status,
          startDate,
          endDate,
          customer_id,
        }),
        loadTeamWorkspace(accountSlug),
      ],
    );

    const limit = 10; // Giống categories

    return (
      <>
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="common:routes.orders">Orders</Trans>}
          description={<AppBreadcrumbs />}
        />
        <PageBody data-testid="orders-page">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="flex items-center gap-4">
              <SearchListInput
                defaultValue={query}
                placeholder={<Trans i18nKey="orders:search:placeholder">Search orders...</Trans>}
                data-testid="order-search-input"
              />
            </CardTitle>
            {/*<If condition={true /* Có thể thay bằng logic canManageOrders *!/>*/}
            {/*  <CreateOrderModal account={workspace}>*/}
            {/*    <Button>*/}
            {/*      <Plus className="mr-2 h-4 w-4" />*/}
            {/*      <Trans i18nKey="orders:create:button">New Order</Trans>*/}
            {/*    </Button>*/}
            {/*  </CreateOrderModal>*/}
            {/*</If>*/}
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>Loading...</div>}>
              <OrdersList
                orders={orders}
                canManage={true /* Có thể thay bằng logic canManageOrders */}
                account={workspace}
                currentPage={currentPage - 1} // Chuyển về index 0
                limit={limit}
                total={total}
                filters={{ query, status, startDate, endDate, customer_id }}
              />
            </Suspense>
          </CardContent>
        </PageBody>
      </>
    );
  } catch (error) {
    console.error('Error loading orders:', error);
    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:generic">
              An error occurred. Please try again.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}
