import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { PageHeader } from '@kit/ui/page';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { CDPDashboard } from './_components/cdp-dashboard';

export const metadata = {
  title: 'Customer Data Platform',
  description: 'Unified customer profiles and analytics',
};

interface CDPPageProps {
  params: {
    account: string;
  };
}

export default function CDPPage({ params }: CDPPageProps) {
  return (
    <div className="flex flex-1 flex-col">
      <TeamAccountLayoutPageHeader
        account={params.account}
        description={
          <AppBreadcrumbs
            items={[
              {
                title: 'Trang chủ',
                url: `/home/<USER>
              },
              {
                title: 'Customer Data Platform',
                url: `/home/<USER>/cdp`,
              },
            ]}
          />
        }
      >
        <PageHeader title="Customer Data Platform" />
      </TeamAccountLayoutPageHeader>

      <div className="flex flex-1 flex-col space-y-6 p-6">
        <CDPDashboard accountId={params.account} />
      </div>
    </div>
  );
}
