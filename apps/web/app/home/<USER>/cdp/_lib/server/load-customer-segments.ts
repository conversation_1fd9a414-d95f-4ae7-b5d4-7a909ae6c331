import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

export interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  type: 'behavioral' | 'demographic' | 'value_based' | 'predictive';
  criteria: Record<string, any>;
  customer_count: number;
  growth_rate: number;
  is_auto_updating: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CustomerSegmentsResult {
  segments: CustomerSegment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export async function loadCustomerSegments(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery: string = '',
  filter: string = 'all',
  limit: number = 20
): Promise<CustomerSegmentsResult> {
  try {
    const offset = (page - 1) * limit;

    // Build query
    let query = client
      .from('customer_segments')
      .select(`
        id,
        name,
        description,
        type,
        criteria,
        customer_count,
        growth_rate,
        is_auto_updating,
        is_active,
        created_at,
        updated_at
      `, { count: 'exact' })
      .eq('account_id', accountId);

    // Apply search filter
    if (searchQuery) {
      query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
    }

    // Apply type filter
    if (filter !== 'all') {
      query = query.eq('type', filter);
    }

    // Apply sorting and pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: segments, error, count } = await query;

    if (error) {
      throw new Error(`Failed to load customer segments: ${error.message}`);
    }

    // Transform data
    const transformedSegments: CustomerSegment[] = segments?.map(segment => ({
      id: segment.id,
      name: segment.name,
      description: segment.description,
      type: segment.type as 'behavioral' | 'demographic' | 'value_based' | 'predictive',
      criteria: segment.criteria || {},
      customer_count: segment.customer_count || 0,
      growth_rate: segment.growth_rate || 0,
      is_auto_updating: segment.is_auto_updating || false,
      is_active: segment.is_active !== false, // default to true
      created_at: segment.created_at,
      updated_at: segment.updated_at
    })) || [];

    return {
      segments: transformedSegments,
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };

  } catch (error) {
    console.error('Error loading customer segments:', error);
    throw new Error(`Failed to load customer segments: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


