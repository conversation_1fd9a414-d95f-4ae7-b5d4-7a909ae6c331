import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

export interface CustomerProfile {
  id: string;
  account_id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  location?: string;
  created_at: string;
  updated_at: string;
  last_active_at?: string;
  total_orders: number;
  total_spent: number;
  avg_order_value: number;
  engagement_score: number;
  churn_risk_score: number;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  notes?: string;
  metadata: Record<string, any>;
}

export interface CustomerProfilesResult {
  profiles: CustomerProfile[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CreateCustomerProfileData {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  location?: string;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  notes?: string;
}

export async function getCustomerProfiles(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery: string = '',
  filter: string = 'all',
  pageSize: number = 20
): Promise<CustomerProfilesResult> {
  try {
    // Build query
    let query = client
      .from('customer_profiles')
      .select('*', { count: 'exact' })
      .eq('account_id', accountId);

    // Apply search filter
    if (searchQuery) {
      query = query.or(`first_name.ilike.%${searchQuery}%,last_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`);
    }

    // Apply value tier filter
    if (filter !== 'all') {
      query = query.eq('value_tier', filter);
    }

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    // Order by created_at desc
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to load customer profiles: ${error.message}`);
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);

    return {
      profiles: data || [],
      total,
      page,
      pageSize,
      totalPages,
    };
  } catch (error) {
    console.error('Error loading customer profiles:', error);
    throw new Error(`Failed to load customer profiles: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function createCustomerProfile(
  client: SupabaseClient<Database>,
  accountId: string,
  profileData: CreateCustomerProfileData
): Promise<CustomerProfile> {
  try {
    const { data, error } = await client
      .from('customer_profiles')
      .insert({
        account_id: accountId,
        ...profileData,
        engagement_score: Math.random() * 0.5 + 0.3, // Random score between 0.3-0.8
        churn_risk_score: Math.random() * 0.4, // Random score between 0-0.4
        total_orders: 0,
        total_spent: 0,
        avg_order_value: 0,
        last_active_at: new Date().toISOString(),
        metadata: {},
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create customer profile: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error creating customer profile:', error);
    throw new Error(`Failed to create customer profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function updateCustomerProfile(
  client: SupabaseClient<Database>,
  accountId: string,
  profileId: string,
  updates: Partial<CreateCustomerProfileData>
): Promise<CustomerProfile> {
  try {
    const { data, error } = await client
      .from('customer_profiles')
      .update(updates)
      .eq('id', profileId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update customer profile: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error updating customer profile:', error);
    throw new Error(`Failed to update customer profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function deleteCustomerProfile(
  client: SupabaseClient<Database>,
  accountId: string,
  profileId: string
): Promise<void> {
  try {
    const { error } = await client
      .from('customer_profiles')
      .delete()
      .eq('id', profileId)
      .eq('account_id', accountId);

    if (error) {
      throw new Error(`Failed to delete customer profile: ${error.message}`);
    }
  } catch (error) {
    console.error('Error deleting customer profile:', error);
    throw new Error(`Failed to delete customer profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getCustomerProfile(
  client: SupabaseClient<Database>,
  accountId: string,
  profileId: string
): Promise<CustomerProfile | null> {
  try {
    const { data, error } = await client
      .from('customer_profiles')
      .select('*')
      .eq('id', profileId)
      .eq('account_id', accountId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get customer profile: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error getting customer profile:', error);
    throw new Error(`Failed to get customer profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function addCustomerToSegment(
  client: SupabaseClient<Database>,
  segmentId: string,
  customerId: string
): Promise<void> {
  try {
    const { error } = await client
      .from('segment_members')
      .insert({
        segment_id: segmentId,
        customer_id: customerId,
      });

    if (error) {
      throw new Error(`Failed to add customer to segment: ${error.message}`);
    }
  } catch (error) {
    console.error('Error adding customer to segment:', error);
    throw new Error(`Failed to add customer to segment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function removeCustomerFromSegment(
  client: SupabaseClient<Database>,
  segmentId: string,
  customerId: string
): Promise<void> {
  try {
    const { error } = await client
      .from('segment_members')
      .delete()
      .eq('segment_id', segmentId)
      .eq('customer_id', customerId);

    if (error) {
      throw new Error(`Failed to remove customer from segment: ${error.message}`);
    }
  } catch (error) {
    console.error('Error removing customer from segment:', error);
    throw new Error(`Failed to remove customer from segment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getCustomerSegments(
  client: SupabaseClient<Database>,
  customerId: string
): Promise<string[]> {
  try {
    const { data, error } = await client
      .from('segment_members')
      .select('segment_id')
      .eq('customer_id', customerId);

    if (error) {
      throw new Error(`Failed to get customer segments: ${error.message}`);
    }

    return (data || []).map(item => item.segment_id);
  } catch (error) {
    console.error('Error getting customer segments:', error);
    throw new Error(`Failed to get customer segments: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
