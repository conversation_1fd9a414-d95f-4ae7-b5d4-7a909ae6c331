import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

export interface CustomerSegment {
  id: string;
  account_id: string;
  name: string;
  description: string;
  type: 'behavioral' | 'demographic' | 'value_based' | 'predictive';
  criteria: Record<string, any>;
  customer_count: number;
  growth_rate: number;
  is_auto_updating: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CustomerSegmentsResult {
  segments: CustomerSegment[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CreateCustomerSegmentData {
  name: string;
  description: string;
  type: 'behavioral' | 'demographic' | 'value_based' | 'predictive';
  criteria: Record<string, any>;
  is_auto_updating: boolean;
}

export async function getCustomerSegments(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery: string = '',
  filter: string = 'all',
  pageSize: number = 20
): Promise<CustomerSegmentsResult> {
  try {
    // Build query
    let query = client
      .from('customer_segments')
      .select('*', { count: 'exact' })
      .eq('account_id', accountId);

    // Apply search filter
    if (searchQuery) {
      query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
    }

    // Apply type filter
    if (filter !== 'all') {
      query = query.eq('type', filter);
    }

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    // Order by created_at desc
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to load customer segments: ${error.message}`);
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);

    return {
      segments: data || [],
      total,
      page,
      pageSize,
      totalPages,
    };
  } catch (error) {
    console.error('Error loading customer segments:', error);
    throw new Error(`Failed to load customer segments: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function createCustomerSegment(
  client: SupabaseClient<Database>,
  accountId: string,
  segmentData: CreateCustomerSegmentData
): Promise<CustomerSegment> {
  try {
    const { data, error } = await client
      .from('customer_segments')
      .insert({
        account_id: accountId,
        ...segmentData,
        customer_count: 0,
        growth_rate: 0,
        is_active: true,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create customer segment: ${error.message}`);
    }

    // If auto-updating, trigger segment calculation
    if (segmentData.is_auto_updating) {
      await calculateSegmentMembers(client, data.id, segmentData.criteria);
    }

    return data;
  } catch (error) {
    console.error('Error creating customer segment:', error);
    throw new Error(`Failed to create customer segment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function updateCustomerSegment(
  client: SupabaseClient<Database>,
  accountId: string,
  segmentId: string,
  updates: Partial<CreateCustomerSegmentData>
): Promise<CustomerSegment> {
  try {
    const { data, error } = await client
      .from('customer_segments')
      .update(updates)
      .eq('id', segmentId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update customer segment: ${error.message}`);
    }

    // If criteria changed and auto-updating, recalculate members
    if (updates.criteria && data.is_auto_updating) {
      await calculateSegmentMembers(client, segmentId, updates.criteria);
    }

    return data;
  } catch (error) {
    console.error('Error updating customer segment:', error);
    throw new Error(`Failed to update customer segment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function deleteCustomerSegment(
  client: SupabaseClient<Database>,
  accountId: string,
  segmentId: string
): Promise<void> {
  try {
    // First delete all segment members
    await client
      .from('segment_members')
      .delete()
      .eq('segment_id', segmentId);

    // Then delete the segment
    const { error } = await client
      .from('customer_segments')
      .delete()
      .eq('id', segmentId)
      .eq('account_id', accountId);

    if (error) {
      throw new Error(`Failed to delete customer segment: ${error.message}`);
    }
  } catch (error) {
    console.error('Error deleting customer segment:', error);
    throw new Error(`Failed to delete customer segment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getCustomerSegment(
  client: SupabaseClient<Database>,
  accountId: string,
  segmentId: string
): Promise<CustomerSegment | null> {
  try {
    const { data, error } = await client
      .from('customer_segments')
      .select('*')
      .eq('id', segmentId)
      .eq('account_id', accountId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get customer segment: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error getting customer segment:', error);
    throw new Error(`Failed to get customer segment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getSegmentMembers(
  client: SupabaseClient<Database>,
  segmentId: string,
  page: number = 1,
  pageSize: number = 20
): Promise<{
  members: any[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}> {
  try {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    const { data, error, count } = await client
      .from('segment_members')
      .select(`
        *,
        customer_profiles (*)
      `, { count: 'exact' })
      .eq('segment_id', segmentId)
      .range(from, to)
      .order('added_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get segment members: ${error.message}`);
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);

    return {
      members: data || [],
      total,
      page,
      pageSize,
      totalPages,
    };
  } catch (error) {
    console.error('Error getting segment members:', error);
    throw new Error(`Failed to get segment members: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function calculateSegmentMembers(
  client: SupabaseClient<Database>,
  segmentId: string,
  criteria: Record<string, any>
): Promise<void> {
  try {
    // Get segment info
    const { data: segment, error: segmentError } = await client
      .from('customer_segments')
      .select('account_id')
      .eq('id', segmentId)
      .single();

    if (segmentError) {
      throw new Error(`Failed to get segment: ${segmentError.message}`);
    }

    // Clear existing members
    await client
      .from('segment_members')
      .delete()
      .eq('segment_id', segmentId);

    // Get all customers for the account
    const { data: customers, error: customersError } = await client
      .from('customer_profiles')
      .select('*')
      .eq('account_id', segment.account_id);

    if (customersError) {
      throw new Error(`Failed to get customers: ${customersError.message}`);
    }

    // Filter customers based on criteria
    const matchingCustomers = (customers || []).filter(customer => 
      evaluateCustomerCriteria(customer, criteria)
    );

    // Add matching customers to segment
    if (matchingCustomers.length > 0) {
      const memberInserts = matchingCustomers.map(customer => ({
        segment_id: segmentId,
        customer_id: customer.id,
      }));

      const { error: insertError } = await client
        .from('segment_members')
        .insert(memberInserts);

      if (insertError) {
        throw new Error(`Failed to add segment members: ${insertError.message}`);
      }
    }

    // Update segment customer count
    await client
      .from('customer_segments')
      .update({ 
        customer_count: matchingCustomers.length,
        growth_rate: Math.random() * 20 - 5 // Random growth rate for demo
      })
      .eq('id', segmentId);

  } catch (error) {
    console.error('Error calculating segment members:', error);
    throw new Error(`Failed to calculate segment members: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

function evaluateCustomerCriteria(customer: any, criteria: Record<string, any>): boolean {
  try {
    // Value-based criteria
    if (criteria.total_spent) {
      const { operator, value, min, max } = criteria.total_spent;
      switch (operator) {
        case 'gte':
          if (customer.total_spent < value) return false;
          break;
        case 'lte':
          if (customer.total_spent > value) return false;
          break;
        case 'eq':
          if (customer.total_spent !== value) return false;
          break;
        case 'between':
          if (customer.total_spent < min || customer.total_spent > max) return false;
          break;
      }
    }

    if (criteria.total_orders) {
      const { operator, value, min, max } = criteria.total_orders;
      switch (operator) {
        case 'gte':
          if (customer.total_orders < value) return false;
          break;
        case 'lte':
          if (customer.total_orders > value) return false;
          break;
        case 'eq':
          if (customer.total_orders !== value) return false;
          break;
        case 'between':
          if (customer.total_orders < min || customer.total_orders > max) return false;
          break;
      }
    }

    // Behavioral criteria
    if (criteria.engagement_score) {
      const { operator, value } = criteria.engagement_score;
      switch (operator) {
        case 'gte':
          if (customer.engagement_score < value) return false;
          break;
        case 'lte':
          if (customer.engagement_score > value) return false;
          break;
      }
    }

    // Predictive criteria
    if (criteria.churn_risk_score) {
      const { operator, value } = criteria.churn_risk_score;
      switch (operator) {
        case 'gte':
          if (customer.churn_risk_score < value) return false;
          break;
        case 'lte':
          if (customer.churn_risk_score > value) return false;
          break;
      }
    }

    // Demographic criteria
    if (criteria.value_tier && customer.value_tier !== criteria.value_tier) {
      return false;
    }

    if (criteria.location && customer.location !== criteria.location) {
      return false;
    }

    // Activity criteria
    if (criteria.created_days_ago) {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - criteria.created_days_ago);
      if (new Date(customer.created_at) > daysAgo) return false;
    }

    return true;
  } catch (error) {
    console.error('Error evaluating customer criteria:', error);
    return false;
  }
}
