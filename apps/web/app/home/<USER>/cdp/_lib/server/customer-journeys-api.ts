import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

export interface CustomerJourney {
  id: string;
  account_id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'draft' | 'completed';
  trigger_type: 'segment_entry' | 'event' | 'date' | 'manual';
  trigger_config: Record<string, any>;
  steps: any[];
  participants: number;
  completion_rate: number;
  created_at: string;
  updated_at: string;
  published_at?: string;
  tags: string[];
}

export interface CustomerJourneysResult {
  journeys: CustomerJourney[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CreateCustomerJourneyData {
  name: string;
  description: string;
  trigger_type: 'segment_entry' | 'event' | 'date' | 'manual';
  trigger_config: Record<string, any>;
  steps: any[];
}

export async function getCustomerJourneys(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery: string = '',
  filter: string = 'all',
  pageSize: number = 20
): Promise<CustomerJourneysResult> {
  try {
    // Build query
    let query = client
      .from('customer_journeys')
      .select('*', { count: 'exact' })
      .eq('account_id', accountId);

    // Apply search filter
    if (searchQuery) {
      query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
    }

    // Apply status filter
    if (filter !== 'all') {
      query = query.eq('status', filter);
    }

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    // Order by created_at desc
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to load customer journeys: ${error.message}`);
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);

    return {
      journeys: data || [],
      total,
      page,
      pageSize,
      totalPages,
    };
  } catch (error) {
    console.error('Error loading customer journeys:', error);
    throw new Error(`Failed to load customer journeys: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function createCustomerJourney(
  client: SupabaseClient<Database>,
  accountId: string,
  journeyData: CreateCustomerJourneyData
): Promise<CustomerJourney> {
  try {
    const { data, error } = await client
      .from('customer_journeys')
      .insert({
        account_id: accountId,
        ...journeyData,
        status: 'draft',
        participants: 0,
        completion_rate: 0,
        tags: [],
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create customer journey: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error creating customer journey:', error);
    throw new Error(`Failed to create customer journey: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function updateCustomerJourney(
  client: SupabaseClient<Database>,
  accountId: string,
  journeyId: string,
  updates: Partial<CreateCustomerJourneyData & { status: string }>
): Promise<CustomerJourney> {
  try {
    const { data, error } = await client
      .from('customer_journeys')
      .update(updates)
      .eq('id', journeyId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update customer journey: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error updating customer journey:', error);
    throw new Error(`Failed to update customer journey: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function deleteCustomerJourney(
  client: SupabaseClient<Database>,
  accountId: string,
  journeyId: string
): Promise<void> {
  try {
    // First delete all journey participants
    await client
      .from('journey_participants')
      .delete()
      .eq('journey_id', journeyId);

    // Then delete the journey
    const { error } = await client
      .from('customer_journeys')
      .delete()
      .eq('id', journeyId)
      .eq('account_id', accountId);

    if (error) {
      throw new Error(`Failed to delete customer journey: ${error.message}`);
    }
  } catch (error) {
    console.error('Error deleting customer journey:', error);
    throw new Error(`Failed to delete customer journey: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getCustomerJourney(
  client: SupabaseClient<Database>,
  accountId: string,
  journeyId: string
): Promise<CustomerJourney | null> {
  try {
    const { data, error } = await client
      .from('customer_journeys')
      .select('*')
      .eq('id', journeyId)
      .eq('account_id', accountId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get customer journey: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error getting customer journey:', error);
    throw new Error(`Failed to get customer journey: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function publishCustomerJourney(
  client: SupabaseClient<Database>,
  accountId: string,
  journeyId: string
): Promise<CustomerJourney> {
  try {
    const { data, error } = await client
      .from('customer_journeys')
      .update({
        status: 'active',
        published_at: new Date().toISOString(),
      })
      .eq('id', journeyId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to publish customer journey: ${error.message}`);
    }

    // Start journey execution for eligible customers
    await startJourneyExecution(client, journeyId);

    return data;
  } catch (error) {
    console.error('Error publishing customer journey:', error);
    throw new Error(`Failed to publish customer journey: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function pauseCustomerJourney(
  client: SupabaseClient<Database>,
  accountId: string,
  journeyId: string
): Promise<CustomerJourney> {
  try {
    const { data, error } = await client
      .from('customer_journeys')
      .update({ status: 'paused' })
      .eq('id', journeyId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to pause customer journey: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error pausing customer journey:', error);
    throw new Error(`Failed to pause customer journey: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getJourneyParticipants(
  client: SupabaseClient<Database>,
  journeyId: string,
  page: number = 1,
  pageSize: number = 20
): Promise<{
  participants: any[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}> {
  try {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    const { data, error, count } = await client
      .from('journey_participants')
      .select(`
        *,
        customer_profiles (*)
      `, { count: 'exact' })
      .eq('journey_id', journeyId)
      .range(from, to)
      .order('started_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get journey participants: ${error.message}`);
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);

    return {
      participants: data || [],
      total,
      page,
      pageSize,
      totalPages,
    };
  } catch (error) {
    console.error('Error getting journey participants:', error);
    throw new Error(`Failed to get journey participants: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function addCustomerToJourney(
  client: SupabaseClient<Database>,
  journeyId: string,
  customerId: string
): Promise<void> {
  try {
    const { error } = await client
      .from('journey_participants')
      .insert({
        journey_id: journeyId,
        customer_id: customerId,
        current_step: 0,
        status: 'active',
        metadata: {},
      });

    if (error) {
      throw new Error(`Failed to add customer to journey: ${error.message}`);
    }

    // Update journey participants count
    await updateJourneyParticipantsCount(client, journeyId);
  } catch (error) {
    console.error('Error adding customer to journey:', error);
    throw new Error(`Failed to add customer to journey: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function startJourneyExecution(
  client: SupabaseClient<Database>,
  journeyId: string
): Promise<void> {
  try {
    // Get journey details
    const { data: journey, error: journeyError } = await client
      .from('customer_journeys')
      .select('*')
      .eq('id', journeyId)
      .single();

    if (journeyError) {
      throw new Error(`Failed to get journey: ${journeyError.message}`);
    }

    // Based on trigger type, find eligible customers
    let eligibleCustomers: any[] = [];

    if (journey.trigger_type === 'segment_entry' && journey.trigger_config.segment_id) {
      // Get customers from segment
      const { data: segmentMembers, error: segmentError } = await client
        .from('segment_members')
        .select('customer_id')
        .eq('segment_id', journey.trigger_config.segment_id);

      if (segmentError) {
        throw new Error(`Failed to get segment members: ${segmentError.message}`);
      }

      eligibleCustomers = segmentMembers || [];
    }

    // Add eligible customers to journey
    if (eligibleCustomers.length > 0) {
      const participantInserts = eligibleCustomers.map(member => ({
        journey_id: journeyId,
        customer_id: member.customer_id,
        current_step: 0,
        status: 'active',
        metadata: {},
      }));

      const { error: insertError } = await client
        .from('journey_participants')
        .insert(participantInserts);

      if (insertError) {
        throw new Error(`Failed to add journey participants: ${insertError.message}`);
      }

      // Update journey participants count
      await updateJourneyParticipantsCount(client, journeyId);
    }
  } catch (error) {
    console.error('Error starting journey execution:', error);
    throw new Error(`Failed to start journey execution: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function updateJourneyParticipantsCount(
  client: SupabaseClient<Database>,
  journeyId: string
): Promise<void> {
  try {
    const { count, error } = await client
      .from('journey_participants')
      .select('*', { count: 'exact', head: true })
      .eq('journey_id', journeyId);

    if (error) {
      throw new Error(`Failed to count journey participants: ${error.message}`);
    }

    await client
      .from('customer_journeys')
      .update({ 
        participants: count || 0,
        completion_rate: Math.random() * 0.5 + 0.3 // Random completion rate for demo
      })
      .eq('id', journeyId);
  } catch (error) {
    console.error('Error updating journey participants count:', error);
  }
}
