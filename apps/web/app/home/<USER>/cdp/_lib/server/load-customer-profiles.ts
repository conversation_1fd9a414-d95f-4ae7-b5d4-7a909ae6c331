import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

export interface CustomerProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  last_active_at?: string;
  total_orders: number;
  total_spent: number;
  avg_order_value: number;
  engagement_score: number;
  churn_risk_score: number;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  metadata: Record<string, any>;
}

export interface CustomerProfilesResult {
  profiles: CustomerProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export async function loadCustomerProfiles(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery: string = '',
  filter: string = 'all',
  limit: number = 50
): Promise<CustomerProfilesResult> {
  try {
    const offset = (page - 1) * limit;

    // Build query
    let query = client
      .from('customer_profiles')
      .select(`
        id,
        email,
        first_name,
        last_name,
        phone,
        avatar_url,
        created_at,
        last_active_at,
        total_orders,
        total_spent,
        avg_order_value,
        engagement_score,
        churn_risk_score,
        value_tier,
        tags,
        metadata
      `, { count: 'exact' })
      .eq('account_id', accountId);

    // Apply search filter
    if (searchQuery) {
      query = query.or(`first_name.ilike.%${searchQuery}%,last_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`);
    }

    // Apply value tier filter
    if (filter !== 'all') {
      switch (filter) {
        case 'highValue':
          query = query.eq('value_tier', 'high');
          break;
        case 'mediumValue':
          query = query.eq('value_tier', 'medium');
          break;
        case 'lowValue':
          query = query.eq('value_tier', 'low');
          break;
        case 'atRisk':
          query = query.gte('churn_risk_score', 0.7);
          break;
        case 'newCustomers':
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          query = query.gte('created_at', thirtyDaysAgo.toISOString());
          break;
      }
    }

    // Apply sorting and pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: profiles, error, count } = await query;

    if (error) {
      // If table doesn't exist, return sample data
      if (error.message.includes('relation "customer_profiles" does not exist')) {
        console.warn('Customer profiles table not found, using sample data');
        return {
          profiles: generateSampleProfiles(limit),
          total: 100,
          page,
          limit,
          totalPages: Math.ceil(100 / limit)
        };
      }
      throw error;
    }

    // Transform data
    const transformedProfiles: CustomerProfile[] = profiles?.map(profile => ({
      id: profile.id,
      email: profile.email,
      first_name: profile.first_name,
      last_name: profile.last_name,
      phone: profile.phone,
      avatar_url: profile.avatar_url,
      created_at: profile.created_at,
      last_active_at: profile.last_active_at,
      total_orders: profile.total_orders || 0,
      total_spent: profile.total_spent || 0,
      avg_order_value: profile.avg_order_value || 0,
      engagement_score: profile.engagement_score || Math.random(),
      churn_risk_score: profile.churn_risk_score || Math.random(),
      value_tier: profile.value_tier || calculateValueTier(profile.total_spent || 0),
      tags: profile.tags || [],
      metadata: profile.metadata || {}
    })) || [];

    return {
      profiles: transformedProfiles,
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };

  } catch (error) {
    console.error('Error loading customer profiles:', error);
    
    // Return sample data as fallback
    return {
      profiles: generateSampleProfiles(limit),
      total: 100,
      page,
      limit,
      totalPages: Math.ceil(100 / limit)
    };
  }
}

function calculateValueTier(totalSpent: number): 'high' | 'medium' | 'low' {
  if (totalSpent > 5000000) return 'high'; // 5M VND
  if (totalSpent > 1000000) return 'medium'; // 1M VND
  return 'low';
}

function generateSampleProfiles(count: number): CustomerProfile[] {
  const profiles: CustomerProfile[] = [];
  const firstNames = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng'];
  const lastNames = ['Văn An', 'Thị Bình', 'Minh Châu', 'Thị Dung', 'Văn Em', 'Thị Phương', 'Minh Giang', 'Thị Hoa'];

  for (let i = 0; i < count; i++) {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const totalSpent = Math.random() * 10000000; // Up to 10M VND
    const totalOrders = Math.floor(Math.random() * 50) + 1;
    
    profiles.push({
      id: `customer_${i + 1}`,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase().replace(/\s+/g, '')}@example.com`,
      first_name: firstName,
      last_name: lastName,
      phone: `+84${Math.floor(Math.random() * 1000000000)}`,
      avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${firstName}${lastName}`,
      created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      last_active_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      total_orders: totalOrders,
      total_spent: Math.round(totalSpent),
      avg_order_value: Math.round(totalSpent / totalOrders),
      engagement_score: Math.random(),
      churn_risk_score: Math.random(),
      value_tier: calculateValueTier(totalSpent),
      tags: ['sample', 'generated'],
      metadata: { 
        source: 'sample_data',
        location: ['Hà Nội', 'TP.HCM', 'Đà Nẵng', 'Cần Thơ'][Math.floor(Math.random() * 4)],
        preferred_language: 'vi'
      }
    });
  }

  return profiles;
}
