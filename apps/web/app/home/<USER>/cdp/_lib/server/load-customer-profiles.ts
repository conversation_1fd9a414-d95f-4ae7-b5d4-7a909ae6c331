import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

export interface CustomerProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  last_active_at?: string;
  total_orders: number;
  total_spent: number;
  avg_order_value: number;
  engagement_score: number;
  churn_risk_score: number;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  metadata: Record<string, any>;
}

export interface CustomerProfilesResult {
  profiles: CustomerProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export async function loadCustomerProfiles(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery: string = '',
  filter: string = 'all',
  limit: number = 50
): Promise<CustomerProfilesResult> {
  try {
    const offset = (page - 1) * limit;

    // First, get customer user IDs from accounts_memberships (same as customers page)
    const { data: membershipData, error: membershipError } = await client
      .from('accounts_memberships')
      .select('user_id')
      .eq('account_id', accountId)
      .eq('account_role', 'customer');

    if (membershipError) {
      console.error('Error loading customer memberships:', membershipError);
      throw membershipError;
    }

    // If no customers, return empty result
    if (!membershipData || membershipData.length === 0) {
      return {
        profiles: [],
        total: 0,
        page,
        limit,
        totalPages: 0
      };
    }

    // Get user IDs
    const userIds = membershipData.map((item) => item.user_id);

    // Query customer accounts (same logic as customers page)
    let query = client
      .from('accounts')
      .select('*', { count: 'exact' })
      .eq('is_personal_account', true)
      .in('primary_owner_user_id', userIds);

    // Apply search filter
    if (searchQuery) {
      const searchTerm = `%${searchQuery}%`;
      query = query.or(
        `name.ilike.${searchTerm},email.ilike.${searchTerm},phone.ilike.${searchTerm}`,
      );
    }

    // Apply value tier filter (based on total_spent in public_data)
    if (filter !== 'all') {
      switch (filter) {
        case 'highValue':
          // Filter for high value customers (total_spent > 5M VND)
          query = query.gte('public_data->total_spent', 5000000);
          break;
        case 'mediumValue':
          // Filter for medium value customers (1M - 5M VND)
          query = query.gte('public_data->total_spent', 1000000).lt('public_data->total_spent', 5000000);
          break;
        case 'lowValue':
          // Filter for low value customers (< 1M VND)
          query = query.lt('public_data->total_spent', 1000000);
          break;
        case 'atRisk':
          // Filter for at-risk customers (no activity in 30 days)
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          query = query.lt('public_data->last_active_at', thirtyDaysAgo.toISOString());
          break;
        case 'newCustomers':
          // Filter for new customers (created in last 30 days)
          const thirtyDaysAgoNew = new Date();
          thirtyDaysAgoNew.setDate(thirtyDaysAgoNew.getDate() - 30);
          query = query.gte('created_at', thirtyDaysAgoNew.toISOString());
          break;
      }
    }

    // Apply sorting and pagination
    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error loading customer accounts:', error);
      throw error;
    }

    // Transform accounts data to CustomerProfile format
    const transformedProfiles: CustomerProfile[] = (data || []).map((record) => {
      const publicData = record.public_data as any || {};
      const totalSpent = publicData.total_spent || 0;
      const totalOrders = publicData.total_orders || 0;

      return {
        id: record.id,
        email: record.email || '',
        first_name: record.name?.split(' ')[0] || '',
        last_name: record.name?.split(' ').slice(1).join(' ') || '',
        phone: record.phone || null,
        avatar_url: record.picture_url || publicData.avatar_url || null,
        created_at: record.created_at || new Date().toISOString(),
        last_active_at: publicData.last_active_at || null,
        total_orders: totalOrders,
        total_spent: totalSpent,
        avg_order_value: totalOrders > 0 ? totalSpent / totalOrders : 0,
        engagement_score: publicData.engagement_score || Math.random() * 0.5 + 0.3, // Random between 0.3-0.8
        churn_risk_score: publicData.churn_risk_score || Math.random() * 0.4 + 0.1, // Random between 0.1-0.5
        value_tier: calculateValueTier(totalSpent),
        tags: publicData.tags || ['customer'],
        metadata: publicData.metadata || {}
      };
    });

    return {
      profiles: transformedProfiles,
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };

  } catch (error) {
    console.error('Error loading customer profiles:', error);
    throw new Error(`Failed to load customer profiles: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

function calculateValueTier(totalSpent: number): 'high' | 'medium' | 'low' {
  if (totalSpent > 5000000) return 'high'; // 5M VND
  if (totalSpent > 1000000) return 'medium'; // 1M VND
  return 'low';
}

function generateSampleProfiles(count: number): CustomerProfile[] {
  const profiles: CustomerProfile[] = [];
  const firstNames = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng'];
  const lastNames = ['Văn An', 'Thị Bình', 'Minh Châu', 'Thị Dung', 'Văn Em', 'Thị Phương', 'Minh Giang', 'Thị Hoa'];

  for (let i = 0; i < count; i++) {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const totalSpent = Math.random() * 10000000; // Up to 10M VND
    const totalOrders = Math.floor(Math.random() * 50) + 1;

    profiles.push({
      id: `customer_${i + 1}`,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase().replace(/\s+/g, '')}@example.com`,
      first_name: firstName,
      last_name: lastName,
      phone: `+84${Math.floor(Math.random() * 1000000000)}`,
      avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${firstName}${lastName}`,
      created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      last_active_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      total_orders: totalOrders,
      total_spent: Math.round(totalSpent),
      avg_order_value: Math.round(totalSpent / totalOrders),
      engagement_score: Math.random(),
      churn_risk_score: Math.random(),
      value_tier: calculateValueTier(totalSpent),
      tags: ['sample', 'generated'],
      metadata: {
        source: 'sample_data',
        location: ['Hà Nội', 'TP.HCM', 'Đà Nẵng', 'Cần Thơ'][Math.floor(Math.random() * 4)],
        preferred_language: 'vi'
      }
    });
  }

  return profiles;
}
