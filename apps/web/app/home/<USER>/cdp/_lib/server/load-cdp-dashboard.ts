import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

export interface CDPDashboardData {
  totalCustomers: number;
  activeCustomers: number;
  monthlyRevenue: number;
  conversionRate: number;
  churnRate: number;
  avgOrderValue: number;
  customerLifetimeValue: number;
  engagementScore: number;
  growthRate: number;
  activeSegments: number;
  aiInsights: number;
  integrations: number;
  lastUpdated: string;
}

export async function loadCDPDashboard(
  client: SupabaseClient<Database>,
  accountId: string
): Promise<CDPDashboardData> {
  try {
    // Load real data from CDP tables
    const [
      customerProfilesResult,
      segmentsResult,
      journeysResult,
      analyticsResult,
      integrationsResult,
      aiInsightsResult
    ] = await Promise.all([
      // Customer profiles data
      client
        .from('customer_profiles')
        .select('*', { count: 'exact' })
        .eq('account_id', accountId),

      // Segments data
      client
        .from('customer_segments')
        .select('*', { count: 'exact' })
        .eq('account_id', accountId),

      // Journeys data
      client
        .from('customer_journeys')
        .select('*', { count: 'exact' })
        .eq('account_id', accountId),

      // Analytics data
      client
        .from('analytics_data')
        .select('*')
        .eq('account_id', accountId)
        .eq('period', 'current_month'),

      // Integrations data
      client
        .from('integration_statuses')
        .select('*', { count: 'exact' })
        .eq('account_id', accountId),

      // AI insights data
      client
        .from('ai_insights')
        .select('*', { count: 'exact' })
        .eq('account_id', accountId)
        .eq('status', 'active')
    ]);

    // Process customer profiles data
    const customerProfiles = customerProfilesResult.data || [];
    const totalCustomers = customerProfilesResult.count || 0;
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activeCustomers = customerProfiles.filter(p =>
      p.last_active_at && new Date(p.last_active_at) > thirtyDaysAgo
    ).length;

    const monthlyRevenue = customerProfiles.reduce((sum, p) => sum + (p.total_spent || 0), 0);
    const avgOrderValue = customerProfiles.length > 0
      ? customerProfiles.reduce((sum, p) => sum + (p.avg_order_value || 0), 0) / customerProfiles.length
      : 0;
    const churnRate = customerProfiles.length > 0
      ? customerProfiles.reduce((sum, p) => sum + (p.churn_risk_score || 0), 0) / customerProfiles.length
      : 0;
    const engagementScore = customerProfiles.length > 0
      ? customerProfiles.reduce((sum, p) => sum + (p.engagement_score || 0), 0) / customerProfiles.length
      : 0;
    const customerLifetimeValue = totalCustomers > 0 ? monthlyRevenue / totalCustomers : 0;

    // Fetch latest analytics data
    const { data: analyticsData } = await client
      .from('analytics_data')
      .select('*')
      .eq('account_id', accountId)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single();

    // Process segments data
    const segments = segmentsResult.data || [];
    const activeSegments = segments.filter(s => s.is_active).length;

    // Process journeys data
    const journeys = journeysResult.data || [];
    const activeJourneys = journeys.filter(j => j.status === 'active').length;
    const totalParticipants = journeys.reduce((sum, j) => sum + (j.participants || 0), 0);

    // Process analytics data
    const analytics = analyticsResult.data || [];
    const getMetricValue = (metricName: string) => {
      const metric = analytics.find(a => a.metric_name === metricName);
      return metric ? Number(metric.metric_value) : 0;
    };

    // Process integrations data
    const integrations = integrationsResult.data || [];
    const connectedIntegrations = integrations.filter(i => i.status === 'connected').length;

    // Process AI insights data
    const insights = aiInsightsResult.data || [];
    const aiInsights = aiInsightsResult.count || 0;

    // Calculate growth rate
    const newCustomersThisMonth = customerProfiles.filter(p =>
      new Date(p.created_at) > thirtyDaysAgo
    ).length;
    const growthRate = totalCustomers > newCustomersThisMonth
      ? newCustomersThisMonth / (totalCustomers - newCustomersThisMonth)
      : 0;

    // Get conversion rate from analytics or use default
    const conversionRate = getMetricValue('conversion_rate') || 0.034;

    // Calculate churn risk from customer profiles
    const churnRiskCount = customerProfiles.filter(p => p.churn_risk_score > 0.7).length;
    const churnRate = totalCustomers > 0 ? churnRiskCount / totalCustomers : 0;

    return {
      totalCustomers,
      activeCustomers,
      monthlyRevenue,
      conversionRate,
      churnRate,
      avgOrderValue,
      customerLifetimeValue,
      engagementScore,
      growthRate,
      activeSegments,
      aiInsights,
      integrations: connectedIntegrations,
      lastUpdated: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error loading CDP dashboard data:', error);
    throw new Error(`Failed to load CDP dashboard data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
