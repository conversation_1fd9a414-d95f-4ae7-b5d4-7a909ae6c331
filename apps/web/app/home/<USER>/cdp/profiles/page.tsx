import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { CustomerProfiles } from '../_components/customer-profiles';
import { loadCustomerProfiles } from '../_lib/server/load-customer-profiles';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('cdp:profiles.title');

  return {
    title,
  };
};

interface PageSearchParams {
  page?: string;
  query?: string;
  filter?: string;
}

async function ProfilesPage({
  params,
  searchParams,
}: {
  params: Promise<{ account: string }>;
  searchParams: Promise<PageSearchParams>;
}) {
  const client = getSupabaseServerClient();
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);

  const accountSlug = resolvedParams.account;
  const currentPage = resolvedSearchParams.page
    ? parseInt(resolvedSearchParams.page)
    : 1;
  const searchQuery = resolvedSearchParams.query || '';
  const filter = resolvedSearchParams.filter || 'all';

  const api = createTeamAccountsApi(client);

  try {
    const teamAccount = await api.getTeamAccount(accountSlug);

    const [profilesData, { user, account }] = await Promise.all([
      loadCustomerProfiles(
        client,
        teamAccount.id,
        currentPage,
        searchQuery,
        filter,
      ),
      loadTeamWorkspace(accountSlug),
    ]);

    return (
      <div className="flex flex-1 flex-col">
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="cdp:profiles.title" />}
          description={
            <AppBreadcrumbs
              items={[
                {
                  title: <Trans i18nKey="common:routes.home" />,
                  url: `/home/<USER>
                },
                {
                  title: <Trans i18nKey="cdp:title" />,
                  url: `/home/<USER>/cdp`,
                },
                {
                  title: <Trans i18nKey="cdp:profiles.title" />,
                  url: `/home/<USER>/cdp/profiles`,
                },
              ]}
            />
          }
        />

        <div className="flex flex-1 flex-col space-y-6 p-6">
          <CustomerProfiles
            accountId={teamAccount.id}
            accountSlug={accountSlug}
            profilesData={profilesData}
            currentPage={currentPage}
            searchQuery={searchQuery}
            filter={filter}
            user={user}
            account={account}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading customer profiles page:', error);

    // Return error page instead of fallback data
    return (
      <div className="flex flex-1 flex-col">
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="cdp:profiles.title" />}
          description={
            <AppBreadcrumbs
              items={[
                {
                  title: <Trans i18nKey="common:routes.home" />,
                  url: `/home/<USER>
                },
                {
                  title: <Trans i18nKey="cdp:title" />,
                  url: `/home/<USER>/cdp`,
                },
                {
                  title: <Trans i18nKey="cdp:profiles.title" />,
                  url: `/home/<USER>/cdp/profiles`,
                },
              ]}
            />
          }
        />

        <div className="flex flex-1 flex-col items-center justify-center p-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">
              <Trans i18nKey="common:errors.generic" />
            </h2>
            <p className="text-muted-foreground mb-6">
              {error instanceof Error ? error.message : 'Unknown error occurred'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <Trans i18nKey="common:refresh" />
            </button>
          </div>
        </div>
      </div>
    );
  }
}

export default withI18n(ProfilesPage);
