import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { ModernProfilesDashboard } from './_components/modern-profiles-dashboard';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('cdp:profiles.title');

  return {
    title,
  };
};

interface ProfilesPageProps {
  params: Promise<{
    account: string;
  }>;
}

async function ProfilesPage({ params }: ProfilesPageProps) {
  const { account } = await params;

  return (
    <div className="flex flex-1 flex-col">
      <TeamAccountLayoutPageHeader
        account={account}
        title={<Trans i18nKey="cdp:profiles.title" />}
        description={
          <AppBreadcrumbs
            items={[
              {
                title: <Trans i18nKey="common:routes.home" />,
                url: `/home/<USER>
              },
              {
                title: <Trans i18nKey="cdp:title" />,
                url: `/home/<USER>/cdp`,
              },
              {
                title: <Trans i18nKey="cdp:profiles.title" />,
                url: `/home/<USER>/cdp/profiles`,
              },
            ]}
          />
        }
      />

      <div className="flex flex-1 flex-col space-y-6 p-6">
        <ModernProfilesDashboard accountId={account} />
      </div>
    </div>
  );
}

export default withI18n(ProfilesPage);
