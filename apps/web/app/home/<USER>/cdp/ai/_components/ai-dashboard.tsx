'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Brain, 
  Target, 
  TrendingUp, 
  Users, 
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Lightbulb,
  RefreshCw,
  Star
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface PredictionResult {
  customer_id: string;
  model_type: string;
  prediction: number;
  confidence: number;
  explanation: string[];
  created_at: string;
}

interface Recommendation {
  id: string;
  customer_id: string;
  type: string;
  title: string;
  description: string;
  confidence: number;
  priority: number;
}

interface AutoSegment {
  id: string;
  name: string;
  description: string;
  algorithm: string;
  customer_count: number;
  confidence: number;
}

interface AIDashboardProps {
  accountId: string;
}

export function AIDashboard({ accountId }: AIDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [predictions, setPredictions] = useState<PredictionResult[]>([]);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [autoSegments, setAutoSegments] = useState<AutoSegment[]>([]);
  const [modelPerformance, setModelPerformance] = useState<any>(null);

  // Load AI data
  const loadAIData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load model performance
      const performanceResponse = await fetch('/api/cdp/ai?service=predictive&action=performance&modelType=churn_prediction');
      if (performanceResponse.ok) {
        const performanceData = await performanceResponse.json();
        setModelPerformance(performanceData.data);
      }

      // Load auto segments
      const segmentsResponse = await fetch('/api/cdp/ai?service=segmentation&action=segments');
      if (segmentsResponse.ok) {
        const segmentsData = await segmentsResponse.json();
        setAutoSegments(segmentsData.data || []);
      }

      // Simulate some predictions and recommendations for demo
      setPredictions([
        {
          customer_id: 'customer_1',
          model_type: 'churn_prediction',
          prediction: 0.75,
          confidence: 0.85,
          explanation: ['High churn risk - immediate intervention recommended'],
          created_at: new Date().toISOString()
        },
        {
          customer_id: 'customer_2',
          model_type: 'ltv_prediction',
          prediction: 5000000,
          confidence: 0.78,
          explanation: ['High lifetime value potential - upsell opportunities'],
          created_at: new Date().toISOString()
        }
      ]);

      setRecommendations([
        {
          id: 'rec_1',
          customer_id: 'customer_1',
          type: 'action',
          title: 'Send retention offer',
          description: 'Customer shows high churn risk',
          confidence: 0.9,
          priority: 1
        },
        {
          id: 'rec_2',
          customer_id: 'customer_2',
          type: 'product',
          title: 'Premium upgrade recommendation',
          description: 'Based on usage patterns and value tier',
          confidence: 0.8,
          priority: 2
        }
      ]);

    } catch (err) {
      setError('Failed to load AI data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAIData();
  }, []);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) return 'default';
    if (confidence >= 0.6) return 'secondary';
    return 'destructive';
  };

  const getPriorityIcon = (priority: number) => {
    if (priority === 1) return <AlertTriangle className="h-4 w-4 text-red-500" />;
    if (priority === 2) return <Clock className="h-4 w-4 text-yellow-500" />;
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">AI/ML Engine</h2>
          <p className="text-muted-foreground">
            Predictive analytics, recommendations, and intelligent automation
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={loadAIData}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* AI Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Models</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              Churn, LTV, Engagement, Conversion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Predictions Today</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground">
              +12% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Auto Segments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{autoSegments.length}</div>
            <p className="text-xs text-muted-foreground">
              ML-discovered segments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Model Accuracy</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {modelPerformance ? `${(modelPerformance.model.accuracy * 100).toFixed(1)}%` : '82.5%'}
            </div>
            <p className="text-xs text-muted-foreground">
              Average across all models
            </p>
          </CardContent>
        </Card>
      </div>

      {/* AI Services Tabs */}
      <Tabs defaultValue="predictions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="segments">Auto Segments</TabsTrigger>
          <TabsTrigger value="personalization">Personalization</TabsTrigger>
        </TabsList>

        <TabsContent value="predictions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Predictive Analytics
              </CardTitle>
              <CardDescription>
                AI-powered predictions for customer behavior
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {predictions.length > 0 ? (
                  predictions.map((prediction, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {prediction.model_type.replace('_', ' ').toUpperCase()}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            Customer: {prediction.customer_id}
                          </span>
                        </div>
                        <Badge variant={getConfidenceBadge(prediction.confidence)}>
                          {(prediction.confidence * 100).toFixed(1)}% confidence
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Prediction:</span>
                          <span className={`font-bold ${getConfidenceColor(prediction.confidence)}`}>
                            {prediction.model_type === 'ltv_prediction' 
                              ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(prediction.prediction)
                              : `${(prediction.prediction * 100).toFixed(1)}%`
                            }
                          </span>
                        </div>
                        
                        {prediction.explanation.length > 0 && (
                          <div>
                            <span className="text-sm font-medium">Explanation:</span>
                            <ul className="text-sm text-muted-foreground mt-1">
                              {prediction.explanation.map((exp, i) => (
                                <li key={i} className="flex items-start gap-2">
                                  <Lightbulb className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                  {exp}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Brain className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No predictions available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                AI Recommendations
              </CardTitle>
              <CardDescription>
                Personalized recommendations and next best actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.length > 0 ? (
                  recommendations.map((rec) => (
                    <div key={rec.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          {getPriorityIcon(rec.priority)}
                          <div>
                            <h4 className="font-medium">{rec.title}</h4>
                            <p className="text-sm text-muted-foreground">{rec.description}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline">{rec.type}</Badge>
                              <span className="text-xs text-muted-foreground">
                                Customer: {rec.customer_id}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant={getConfidenceBadge(rec.confidence)}>
                            {(rec.confidence * 100).toFixed(1)}%
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            Priority: {rec.priority}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Star className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No recommendations available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="segments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Auto-Discovered Segments
              </CardTitle>
              <CardDescription>
                ML-powered customer segmentation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {autoSegments.length > 0 ? (
                  autoSegments.map((segment) => (
                    <div key={segment.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{segment.name}</h4>
                        <Badge variant={getConfidenceBadge(segment.confidence)}>
                          {(segment.confidence * 100).toFixed(1)}% confidence
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">{segment.description}</p>
                      
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-4">
                          <span>
                            <strong>{segment.customer_count.toLocaleString()}</strong> customers
                          </span>
                          <Badge variant="outline">{segment.algorithm}</Badge>
                        </div>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No auto segments discovered yet</p>
                    <Button variant="outline" className="mt-4">
                      Discover Segments
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="personalization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Content Personalization
              </CardTitle>
              <CardDescription>
                AI-powered content optimization and personalization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <h4 className="font-medium">Email Personalization</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Open Rate Improvement</span>
                      <span className="font-medium text-green-600">+24%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Click Rate Improvement</span>
                      <span className="font-medium text-green-600">+18%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Personalization Score</span>
                      <span className="font-medium">8.5/10</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-medium">Web Personalization</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Conversion Rate Lift</span>
                      <span className="font-medium text-green-600">+15%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Engagement Increase</span>
                      <span className="font-medium text-green-600">+32%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Active Templates</span>
                      <span className="font-medium">12</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">AI Content Generation</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically generate personalized content for each customer
                    </p>
                  </div>
                  <Button>
                    Create Template
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
