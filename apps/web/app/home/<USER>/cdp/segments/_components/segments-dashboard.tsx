'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, Users, Target, TrendingUp, Activity, MoreHorizontal, Edit, Trash2, Play, Pause } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { SegmentBuilder } from './segment-builder';

interface CustomerSegment {
  id: string;
  name: string;
  description?: string;
  color: string;
  customer_count: number;
  is_active: boolean;
  is_computing: boolean;
  type: 'static' | 'dynamic';
  created_at: string;
  updated_at: string;
  last_computed_at?: string;
}

interface SegmentsDashboardProps {
  accountId: string;
}

export function SegmentsDashboard({ accountId }: SegmentsDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [segments, setSegments] = useState<CustomerSegment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingSegment, setEditingSegment] = useState<CustomerSegment | null>(null);

  // Load segments
  const loadSegments = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/cdp/segments');
      const data = await response.json();
      
      if (data.success) {
        setSegments(data.data || []);
      } else {
        setError(data.error || 'Failed to load segments');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Create segment
  const createSegment = async (segmentData: any) => {
    try {
      const response = await fetch('/api/cdp/segments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(segmentData),
      });

      const data = await response.json();

      if (data.success) {
        setShowBuilder(false);
        await loadSegments();
      } else {
        throw new Error(data.error || 'Failed to create segment');
      }
    } catch (error) {
      console.error('Failed to create segment:', error);
      throw error;
    }
  };

  // Update segment
  const updateSegment = async (segmentId: string, updates: any) => {
    try {
      const response = await fetch(`/api/cdp/segments/${segmentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const data = await response.json();

      if (data.success) {
        setEditingSegment(null);
        await loadSegments();
      } else {
        throw new Error(data.error || 'Failed to update segment');
      }
    } catch (error) {
      console.error('Failed to update segment:', error);
      throw error;
    }
  };

  // Delete segment
  const deleteSegment = async (segmentId: string) => {
    if (!confirm('Are you sure you want to delete this segment?')) {
      return;
    }

    try {
      const response = await fetch(`/api/cdp/segments/${segmentId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        await loadSegments();
      } else {
        setError(data.error || 'Failed to delete segment');
      }
    } catch (error) {
      setError('Network error occurred');
    }
  };

  // Toggle segment active status
  const toggleSegmentStatus = async (segment: CustomerSegment) => {
    await updateSegment(segment.id, { is_active: !segment.is_active });
  };

  useEffect(() => {
    loadSegments();
  }, []);

  // Calculate dashboard stats
  const stats = {
    totalSegments: segments.length,
    activeSegments: segments.filter(s => s.is_active).length,
    totalCustomers: segments.reduce((sum, s) => sum + s.customer_count, 0),
    computingSegments: segments.filter(s => s.is_computing).length,
  };

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Segments</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSegments}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeSegments} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Segmented Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCustomers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Across all segments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Segments</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeSegments}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Computing</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.computingSegments}</div>
            <p className="text-xs text-muted-foreground">
              Being processed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Segments</h2>
          <p className="text-muted-foreground">
            Create and manage dynamic customer segments based on behavior and attributes
          </p>
        </div>
        <Dialog open={showBuilder} onOpenChange={setShowBuilder}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Segment
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Segment</DialogTitle>
            </DialogHeader>
            <SegmentBuilder
              onSave={createSegment}
              onCancel={() => setShowBuilder(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Segments List */}
      <Card>
        <CardHeader>
          <CardTitle>All Segments</CardTitle>
          <CardDescription>
            Manage your customer segments and their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : segments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No segments found. Create your first segment to get started.
            </div>
          ) : (
            <div className="space-y-4">
              {segments.map((segment) => (
                <div
                  key={segment.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-center gap-4">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: segment.color }}
                    />
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{segment.name}</h3>
                        <Badge variant={segment.is_active ? 'default' : 'secondary'}>
                          {segment.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {segment.is_computing && (
                          <Badge variant="outline">Computing...</Badge>
                        )}
                        <Badge variant="outline">{segment.type}</Badge>
                      </div>
                      {segment.description && (
                        <p className="text-sm text-muted-foreground">
                          {segment.description}
                        </p>
                      )}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{segment.customer_count.toLocaleString()} customers</span>
                        <span>Updated {new Date(segment.updated_at).toLocaleDateString()}</span>
                        {segment.last_computed_at && (
                          <span>Computed {new Date(segment.last_computed_at).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setEditingSegment(segment)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => toggleSegmentStatus(segment)}>
                        {segment.is_active ? (
                          <>
                            <Pause className="mr-2 h-4 w-4" />
                            Deactivate
                          </>
                        ) : (
                          <>
                            <Play className="mr-2 h-4 w-4" />
                            Activate
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => deleteSegment(segment.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Segment Dialog */}
      <Dialog open={!!editingSegment} onOpenChange={() => setEditingSegment(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Segment</DialogTitle>
          </DialogHeader>
          {editingSegment && (
            <SegmentBuilder
              initialData={editingSegment}
              onSave={(data) => updateSegment(editingSegment.id, data)}
              onCancel={() => setEditingSegment(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
