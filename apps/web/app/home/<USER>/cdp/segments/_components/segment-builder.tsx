'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, X, ChevronDown, Users, Target, TrendingUp } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Badge } from '@kit/ui/badge';
import { Separator } from '@kit/ui/separator';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface SegmentCondition {
  id: string;
  field: string;
  operator: string;
  value: any;
  timeframe?: {
    period: string;
    value: number;
  };
}

interface SegmentCriteria {
  operator: 'AND' | 'OR';
  conditions: SegmentCondition[];
}

interface SegmentBuilderProps {
  onSave: (segment: any) => Promise<void>;
  onCancel: () => void;
  initialData?: any;
}

const FIELD_OPTIONS = [
  { value: 'total_sessions', label: 'Total Sessions', type: 'number' },
  { value: 'total_pageviews', label: 'Total Pageviews', type: 'number' },
  { value: 'total_purchases', label: 'Total Purchases', type: 'number' },
  { value: 'total_revenue', label: 'Total Revenue', type: 'number' },
  { value: 'lifecycle_stage', label: 'Lifecycle Stage', type: 'select', options: ['prospect', 'lead', 'customer', 'advocate', 'churned'] },
  { value: 'customer_value_tier', label: 'Customer Tier', type: 'select', options: ['low', 'medium', 'high', 'vip'] },
  { value: 'churn_risk_score', label: 'Churn Risk Score', type: 'number' },
  { value: 'lifetime_value_score', label: 'Lifetime Value', type: 'number' },
  { value: 'overall_engagement_score', label: 'Engagement Score', type: 'number' },
  { value: 'last_activity_at', label: 'Last Activity', type: 'date' },
  { value: 'last_order_at', label: 'Last Order', type: 'date' },
  { value: 'first_seen_at', label: 'First Seen', type: 'date' },
  { value: 'primary_email', label: 'Email', type: 'text' },
  { value: 'tags', label: 'Tags', type: 'array' },
];

const OPERATOR_OPTIONS = {
  number: [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'greater_than', label: 'Greater Than' },
    { value: 'greater_than_or_equal', label: 'Greater Than or Equal' },
    { value: 'less_than', label: 'Less Than' },
    { value: 'less_than_or_equal', label: 'Less Than or Equal' },
    { value: 'between', label: 'Between' },
  ],
  text: [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'contains', label: 'Contains' },
    { value: 'not_contains', label: 'Does Not Contain' },
    { value: 'starts_with', label: 'Starts With' },
    { value: 'ends_with', label: 'Ends With' },
    { value: 'is_null', label: 'Is Empty' },
    { value: 'is_not_null', label: 'Is Not Empty' },
  ],
  select: [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'in', label: 'In' },
    { value: 'not_in', label: 'Not In' },
  ],
  date: [
    { value: 'within_days', label: 'Within Last X Days' },
    { value: 'more_than_days_ago', label: 'More Than X Days Ago' },
    { value: 'between', label: 'Between Dates' },
    { value: 'is_null', label: 'Is Empty' },
    { value: 'is_not_null', label: 'Is Not Empty' },
  ],
  array: [
    { value: 'contains', label: 'Contains' },
    { value: 'not_contains', label: 'Does Not Contain' },
    { value: 'is_null', label: 'Is Empty' },
    { value: 'is_not_null', label: 'Is Not Empty' },
  ],
};

const SEGMENT_TEMPLATES = [
  {
    id: 'high-value',
    name: 'High Value Customers',
    description: 'Customers with high lifetime value and recent activity',
    icon: TrendingUp,
    criteria: {
      operator: 'AND' as const,
      conditions: [
        { id: '1', field: 'lifetime_value_score', operator: 'greater_than', value: 5000000 },
        { id: '2', field: 'total_purchases', operator: 'greater_than', value: 3 },
        { id: '3', field: 'last_activity_at', operator: 'within_days', value: 90 },
      ],
    },
  },
  {
    id: 'churn-risk',
    name: 'Churn Risk Customers',
    description: 'Customers at risk of churning',
    icon: Target,
    criteria: {
      operator: 'AND' as const,
      conditions: [
        { id: '1', field: 'churn_risk_score', operator: 'greater_than', value: 0.7 },
        { id: '2', field: 'last_activity_at', operator: 'more_than_days_ago', value: 30 },
        { id: '3', field: 'total_purchases', operator: 'greater_than', value: 0 },
      ],
    },
  },
  {
    id: 'new-customers',
    name: 'New Customers',
    description: 'Recently acquired customers',
    icon: Users,
    criteria: {
      operator: 'AND' as const,
      conditions: [
        { id: '1', field: 'first_seen_at', operator: 'within_days', value: 30 },
        { id: '2', field: 'lifecycle_stage', operator: 'in', value: ['prospect', 'lead', 'customer'] },
      ],
    },
  },
];

export function SegmentBuilder({ onSave, onCancel, initialData }: SegmentBuilderProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [segmentName, setSegmentName] = useState(initialData?.name || '');
  const [segmentDescription, setSegmentDescription] = useState(initialData?.description || '');
  const [criteria, setCriteria] = useState<SegmentCriteria>(
    initialData?.criteria || {
      operator: 'AND',
      conditions: [{ id: Date.now().toString(), field: '', operator: '', value: '' }],
    }
  );
  const [estimatedSize, setEstimatedSize] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addCondition = () => {
    setCriteria(prev => ({
      ...prev,
      conditions: [
        ...prev.conditions,
        { id: Date.now().toString(), field: '', operator: '', value: '' },
      ],
    }));
  };

  const removeCondition = (conditionId: string) => {
    setCriteria(prev => ({
      ...prev,
      conditions: prev.conditions.filter(c => c.id !== conditionId),
    }));
  };

  const updateCondition = (conditionId: string, updates: Partial<SegmentCondition>) => {
    setCriteria(prev => ({
      ...prev,
      conditions: prev.conditions.map(c =>
        c.id === conditionId ? { ...c, ...updates } : c
      ),
    }));
  };

  const applyTemplate = (template: typeof SEGMENT_TEMPLATES[0]) => {
    setSegmentName(template.name);
    setSegmentDescription(template.description);
    setCriteria(template.criteria);
  };

  const estimateSegmentSize = async () => {
    if (!criteria.conditions.some(c => c.field && c.operator)) {
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Call API to estimate segment size
      // const response = await fetch('/api/cdp/segments/estimate', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ criteria }),
      // });
      // const data = await response.json();
      // setEstimatedSize(data.estimatedSize);
      
      // Mock estimation for now
      setEstimatedSize(Math.floor(Math.random() * 1000) + 100);
    } catch (err) {
      console.error('Failed to estimate segment size:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!segmentName.trim()) {
      setError(t('cdp:errors.validationError'));
      return;
    }

    if (!criteria.conditions.some(c => c.field && c.operator)) {
      setError('Please add at least one valid condition');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await onSave({
        name: segmentName,
        description: segmentDescription,
        criteria,
        type: 'dynamic',
        is_active: true,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save segment');
    } finally {
      setIsLoading(false);
    }
  };

  const getFieldType = (fieldValue: string) => {
    const field = FIELD_OPTIONS.find(f => f.value === fieldValue);
    return field?.type || 'text';
  };

  const getOperatorOptions = (fieldValue: string) => {
    const fieldType = getFieldType(fieldValue);
    return OPERATOR_OPTIONS[fieldType as keyof typeof OPERATOR_OPTIONS] || OPERATOR_OPTIONS.text;
  };

  const renderValueInput = (condition: SegmentCondition) => {
    const field = FIELD_OPTIONS.find(f => f.value === condition.field);
    const fieldType = field?.type || 'text';

    if (fieldType === 'select' && field?.options) {
      return (
        <Select
          value={condition.value}
          onValueChange={(value) => updateCondition(condition.id, { value })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            {field.options.map(option => (
              <SelectItem key={option} value={option}>
                {t(`cdp:profiles.lifecycle.${option}`, option)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (fieldType === 'number') {
      return (
        <Input
          type="number"
          value={condition.value}
          onChange={(e) => updateCondition(condition.id, { value: parseFloat(e.target.value) || 0 })}
          placeholder="Enter number"
        />
      );
    }

    if (fieldType === 'date' && ['within_days', 'more_than_days_ago'].includes(condition.operator)) {
      return (
        <Input
          type="number"
          value={condition.value}
          onChange={(e) => updateCondition(condition.id, { value: parseInt(e.target.value) || 0 })}
          placeholder="Number of days"
        />
      );
    }

    return (
      <Input
        value={condition.value}
        onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
        placeholder="Enter value"
      />
    );
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="builder" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="builder">Custom Builder</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {SEGMENT_TEMPLATES.map((template) => {
              const IconComponent = template.icon;
              return (
                <Card
                  key={template.id}
                  className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
                  onClick={() => applyTemplate(template)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-5 w-5 text-primary" />
                      <CardTitle className="text-base">{template.name}</CardTitle>
                    </div>
                    <CardDescription className="text-sm">
                      {template.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-muted-foreground">Conditions:</div>
                      {template.criteria.conditions.map((condition, index) => (
                        <div key={index} className="text-xs text-muted-foreground">
                          • {FIELD_OPTIONS.find(f => f.value === condition.field)?.label} {condition.operator.replace('_', ' ')} {condition.value}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="builder" className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Segment Information</CardTitle>
              <CardDescription>
                Define the basic information for your customer segment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('cdp:profiles.fields.fullName')} *</Label>
                  <Input
                    id="name"
                    value={segmentName}
                    onChange={(e) => setSegmentName(e.target.value)}
                    placeholder="e.g., High Value Customers"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={segmentDescription}
                    onChange={(e) => setSegmentDescription(e.target.value)}
                    placeholder="Describe this segment..."
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Segment Criteria */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Segment Criteria</CardTitle>
                  <CardDescription>
                    Define the conditions that customers must meet to be included in this segment
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Label>Match:</Label>
                  <Select
                    value={criteria.operator}
                    onValueChange={(value: 'AND' | 'OR') => setCriteria(prev => ({ ...prev, operator: value }))}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AND">ALL</SelectItem>
                      <SelectItem value="OR">ANY</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-muted-foreground">conditions</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {criteria.conditions.map((condition, index) => (
                <div key={condition.id} className="space-y-3">
                  {index > 0 && (
                    <div className="flex items-center gap-2">
                      <Separator className="flex-1" />
                      <Badge variant="outline" className="text-xs">
                        {criteria.operator}
                      </Badge>
                      <Separator className="flex-1" />
                    </div>
                  )}
                  
                  <div className="grid gap-3 md:grid-cols-4">
                    <div>
                      <Select
                        value={condition.field}
                        onValueChange={(value) => updateCondition(condition.id, { field: value, operator: '', value: '' })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          {FIELD_OPTIONS.map(field => (
                            <SelectItem key={field.value} value={field.value}>
                              {field.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Select
                        value={condition.operator}
                        onValueChange={(value) => updateCondition(condition.id, { operator: value, value: '' })}
                        disabled={!condition.field}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                        <SelectContent>
                          {getOperatorOptions(condition.field).map(op => (
                            <SelectItem key={op.value} value={op.value}>
                              {op.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      {renderValueInput(condition)}
                    </div>

                    <div className="flex items-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(condition.id)}
                        disabled={criteria.conditions.length === 1}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}

              <Button
                variant="outline"
                onClick={addCondition}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Condition
              </Button>

              {criteria.conditions.some(c => c.field && c.operator) && (
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="text-sm">
                    <span className="font-medium">Estimated size:</span>
                    {estimatedSize !== null ? (
                      <span className="ml-2">{estimatedSize.toLocaleString()} customers</span>
                    ) : (
                      <span className="ml-2 text-muted-foreground">Click to estimate</span>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={estimateSegmentSize}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Estimating...' : 'Estimate'}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Actions */}
      <div className="flex justify-end gap-3">
        <Button variant="outline" onClick={onCancel}>
          {t('common:cancel')}
        </Button>
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? 'Saving...' : t('common:save')}
        </Button>
      </div>
    </div>
  );
}
