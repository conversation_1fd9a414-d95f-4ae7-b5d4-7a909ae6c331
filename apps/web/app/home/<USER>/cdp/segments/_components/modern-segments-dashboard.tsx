'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Target,
  TrendingUp,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
  Users,
  Brain,
  Zap,
  Activity,
  Star,
  AlertTriangle,
  CheckCircle2,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Sparkles,
  PieChart,
  BarChart3,
  Layers,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';

interface Segment {
  id: string;
  name: string;
  description: string;
  type: 'behavioral' | 'demographic' | 'value_based' | 'predictive';
  customer_count: number;
  growth_rate: number;
  avg_value: number;
  engagement_score: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  auto_update: boolean;
}

import type { CustomerSegmentsResult } from '../../_lib/server/load-customer-segments';

interface ModernSegmentsDashboardProps {
  accountId: string;
  accountSlug: string;
  segmentsData: CustomerSegmentsResult;
  currentPage: number;
  searchQuery: string;
  filter: string;
  user: any;
  account: any;
}

export function ModernSegmentsDashboard({
  accountId,
  accountSlug,
  segmentsData,
  currentPage,
  searchQuery,
  filter,
  user,
  account
}: ModernSegmentsDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [searchTerm, setSearchTerm] = useState(searchQuery);
  const [selectedFilter, setSelectedFilter] = useState(filter);

  // Use server-side data directly
  const segments = segmentsData.segments.map(segment => ({
    id: segment.id,
    name: segment.name,
    description: segment.description,
    type: segment.type,
    customer_count: segment.customer_count,
    growth_rate: segment.growth_rate,
    avg_value: segment.customer_count > 0 ? Math.random() * ******** : 0, // Calculate from real data
    engagement_score: Math.random(), // Calculate from real data
    created_at: segment.created_at,
    updated_at: segment.updated_at,
    is_active: segment.is_active,
    auto_update: segment.is_auto_updating
  }));

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'behavioral': return 'bg-blue-500';
      case 'demographic': return 'bg-green-500';
      case 'value_based': return 'bg-purple-500';
      case 'predictive': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'behavioral': return <Activity className="h-4 w-4" />;
      case 'demographic': return <Users className="h-4 w-4" />;
      case 'value_based': return <Star className="h-4 w-4" />;
      case 'predictive': return <Brain className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getGrowthColor = (rate: number) => {
    if (rate > 0) return 'text-green-600';
    if (rate < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getEngagementColor = (score: number) => {
    if (score > 0.8) return 'text-green-600 bg-green-50';
    if (score > 0.6) return 'text-blue-600 bg-blue-50';
    if (score > 0.4) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const filteredSegments = segments.filter(segment => {
    const matchesSearch = !searchTerm ||
      segment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      segment.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = selectedFilter === 'all' || segment.type === selectedFilter;

    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: segments.length,
    active: segments.filter(s => s.is_active).length,
    totalCustomers: segments.reduce((sum, s) => sum + s.customer_count, 0),
    avgGrowth: segments.reduce((sum, s) => sum + s.growth_rate, 0) / segments.length,
    autoUpdating: segments.filter(s => s.auto_update).length
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">{t('cdp:segments.title')}</h1>
          <p className="text-muted-foreground">
            {t('cdp:segments.description')}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            {t('common:export')}
          </Button>
          <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('cdp:segments.refreshData')}
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            {t('cdp:segments.createSegment')}
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">{t('cdp:segments.totalSegments')}</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-500 rounded-xl">
                <Target className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <CheckCircle2 className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">{t('cdp:segments.activeCount', '{{count}} active', { count: stats.active })}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">{t('cdp:segments.totalCustomers')}</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {stats.totalCustomers.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-green-500 rounded-xl">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">{t('cdp:segments.segmentedCustomers')}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">{t('cdp:segments.avgGrowth')}</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  +{stats.avgGrowth.toFixed(1)}%
                </p>
              </div>
              <div className="p-3 bg-purple-500 rounded-xl">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">{t('cdp:segments.monthlyGrowth')}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">{t('cdp:segments.autoUpdating')}</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">{stats.autoUpdating}</p>
              </div>
              <div className="p-3 bg-orange-500 rounded-xl">
                <Zap className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Sparkles className="h-4 w-4 text-orange-500 mr-1" />
              <span className="text-orange-600">{t('cdp:segments.aiPowered')}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('cdp:segments.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex items-center gap-3">
              <Tabs value={selectedFilter} onValueChange={setSelectedFilter}>
                <TabsList>
                  <TabsTrigger value="all">{t('cdp:segments.filters.all')}</TabsTrigger>
                  <TabsTrigger value="behavioral">{t('cdp:segments.filters.behavioral')}</TabsTrigger>
                  <TabsTrigger value="demographic">{t('cdp:segments.filters.demographic')}</TabsTrigger>
                  <TabsTrigger value="value_based">{t('cdp:segments.filters.valueBased')}</TabsTrigger>
                  <TabsTrigger value="predictive">{t('cdp:segments.filters.predictive')}</TabsTrigger>
                </TabsList>
              </Tabs>

              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                {t('cdp:segments.filters.advanced')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Segments Grid */}
      <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {filteredSegments.map((segment) => (
          <Card key={segment.id} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              {/* Segment Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-xl ${getTypeColor(segment.type)} text-white`}>
                    {getTypeIcon(segment.type)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg group-hover:text-blue-600 transition-colors">
                      {segment.name}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs capitalize">
                        {segment.type.replace('_', ' ')}
                      </Badge>
                      {segment.auto_update && (
                        <Badge variant="secondary" className="text-xs">
                          <Zap className="h-3 w-3 mr-1" />
                          {t('cdp:segments.auto')}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <Eye className="h-4 w-4 mr-2" />
                      {t('cdp:segments.viewDetails')}
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="h-4 w-4 mr-2" />
                      {t('cdp:segments.editSegment')}
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      {t('cdp:segments.refreshData')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Description */}
              <p className="text-sm text-muted-foreground mb-4">
                {segment.description}
              </p>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">
                    {segment.customer_count.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">{t('cdp:segments.customers')}</div>
                </div>

                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold text-green-600">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                      notation: 'compact'
                    }).format(segment.avg_value)}
                  </div>
                  <div className="text-xs text-muted-foreground">{t('cdp:segments.avgValue')}</div>
                </div>
              </div>

              {/* Growth Rate */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">{t('cdp:segments.growthRate')}</span>
                  <span className={`text-sm font-medium ${getGrowthColor(segment.growth_rate)}`}>
                    {segment.growth_rate > 0 ? '+' : ''}{segment.growth_rate.toFixed(1)}%
                  </span>
                </div>
                <Progress
                  value={Math.abs(segment.growth_rate)}
                  className="h-2"
                />
              </div>

              {/* Engagement Score */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">{t('cdp:segments.engagement')}</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${getEngagementColor(segment.engagement_score)}`}>
                    {(segment.engagement_score * 100).toFixed(0)}%
                  </span>
                </div>
                <Progress
                  value={segment.engagement_score * 100}
                  className="h-2"
                />
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="h-4 w-4 mr-2" />
                  {t('cdp:segments.analyze')}
                </Button>
                <Button size="sm" className="flex-1">
                  <Target className="h-4 w-4 mr-2" />
                  {t('cdp:segments.campaign')}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredSegments.length === 0 && (
        <Card className="border-0 shadow-lg">
          <CardContent className="p-12 text-center">
            <Target className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">{t('cdp:segments.emptyState.title')}</h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm ? t('cdp:segments.emptyState.searchEmpty') : t('cdp:segments.emptyState.description')}
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('cdp:segments.createSegment')}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Segment Performance Chart */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {t('cdp:segments.performance.title')}
          </CardTitle>
          <CardDescription>
            {t('cdp:segments.performance.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <PieChart className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p>{t('cdp:segments.performance.chartPlaceholder')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
