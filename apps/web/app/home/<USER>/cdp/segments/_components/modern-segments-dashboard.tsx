'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Target, 
  TrendingUp, 
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
  Users,
  Brain,
  Zap,
  Activity,
  Star,
  AlertTriangle,
  CheckCircle2,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Sparkles,
  PieChart,
  BarChart3,
  Layers,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';

interface Segment {
  id: string;
  name: string;
  description: string;
  type: 'behavioral' | 'demographic' | 'value_based' | 'predictive';
  customer_count: number;
  growth_rate: number;
  avg_value: number;
  engagement_score: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  auto_update: boolean;
}

interface ModernSegmentsDashboardProps {
  accountId: string;
}

export function ModernSegmentsDashboard({ accountId }: ModernSegmentsDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [segments, setSegments] = useState<Segment[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Mock data for demonstration
  const mockSegments: Segment[] = [
    {
      id: '1',
      name: 'High-Value Customers',
      description: 'Customers with lifetime value > 10M VND',
      type: 'value_based',
      customer_count: 1247,
      growth_rate: 15.2,
      avg_value: ********,
      engagement_score: 0.89,
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-03-20T14:22:00Z',
      is_active: true,
      auto_update: true
    },
    {
      id: '2',
      name: 'Frequent Shoppers',
      description: 'Customers with 5+ purchases in last 3 months',
      type: 'behavioral',
      customer_count: 2156,
      growth_rate: 8.7,
      avg_value: 8200000,
      engagement_score: 0.76,
      created_at: '2024-02-10T09:15:00Z',
      updated_at: '2024-03-19T16:45:00Z',
      is_active: true,
      auto_update: true
    },
    {
      id: '3',
      name: 'Churn Risk',
      description: 'Customers with high probability of churning',
      type: 'predictive',
      customer_count: 456,
      growth_rate: -12.3,
      avg_value: 3400000,
      engagement_score: 0.34,
      created_at: '2024-03-05T11:20:00Z',
      updated_at: '2024-03-18T13:30:00Z',
      is_active: true,
      auto_update: true
    },
    {
      id: '4',
      name: 'Young Professionals',
      description: 'Age 25-35, urban areas, high income',
      type: 'demographic',
      customer_count: 3421,
      growth_rate: 22.1,
      avg_value: 6800000,
      engagement_score: 0.68,
      created_at: '2024-01-20T14:45:00Z',
      updated_at: '2024-03-17T10:15:00Z',
      is_active: true,
      auto_update: false
    },
    {
      id: '5',
      name: 'Mobile-First Users',
      description: 'Primarily use mobile app for interactions',
      type: 'behavioral',
      customer_count: 5678,
      growth_rate: 18.9,
      avg_value: 4200000,
      engagement_score: 0.72,
      created_at: '2024-02-28T08:30:00Z',
      updated_at: '2024-03-16T12:20:00Z',
      is_active: true,
      auto_update: true
    }
  ];

  useEffect(() => {
    setSegments(mockSegments);
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'behavioral': return 'bg-blue-500';
      case 'demographic': return 'bg-green-500';
      case 'value_based': return 'bg-purple-500';
      case 'predictive': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'behavioral': return <Activity className="h-4 w-4" />;
      case 'demographic': return <Users className="h-4 w-4" />;
      case 'value_based': return <Star className="h-4 w-4" />;
      case 'predictive': return <Brain className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getGrowthColor = (rate: number) => {
    if (rate > 0) return 'text-green-600';
    if (rate < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getEngagementColor = (score: number) => {
    if (score > 0.8) return 'text-green-600 bg-green-50';
    if (score > 0.6) return 'text-blue-600 bg-blue-50';
    if (score > 0.4) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const filteredSegments = segments.filter(segment => {
    const matchesSearch = !searchTerm || 
      segment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      segment.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' || segment.type === selectedFilter;
    
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: segments.length,
    active: segments.filter(s => s.is_active).length,
    totalCustomers: segments.reduce((sum, s) => sum + s.customer_count, 0),
    avgGrowth: segments.reduce((sum, s) => sum + s.growth_rate, 0) / segments.length,
    autoUpdating: segments.filter(s => s.auto_update).length
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Smart Segments</h1>
          <p className="text-muted-foreground">
            AI-powered customer segmentation and targeting
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh All
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Create Segment
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Segments</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-500 rounded-xl">
                <Target className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <CheckCircle2 className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">{stats.active} active</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Total Customers</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {stats.totalCustomers.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-green-500 rounded-xl">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">Segmented customers</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Avg Growth</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  +{stats.avgGrowth.toFixed(1)}%
                </p>
              </div>
              <div className="p-3 bg-purple-500 rounded-xl">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">Monthly growth</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Auto-Updating</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">{stats.autoUpdating}</p>
              </div>
              <div className="p-3 bg-orange-500 rounded-xl">
                <Zap className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Sparkles className="h-4 w-4 text-orange-500 mr-1" />
              <span className="text-orange-600">AI-powered</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search segments by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center gap-3">
              <Tabs value={selectedFilter} onValueChange={setSelectedFilter}>
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="behavioral">Behavioral</TabsTrigger>
                  <TabsTrigger value="demographic">Demographic</TabsTrigger>
                  <TabsTrigger value="value_based">Value-Based</TabsTrigger>
                  <TabsTrigger value="predictive">Predictive</TabsTrigger>
                </TabsList>
              </Tabs>
              
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Advanced
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Segments Grid */}
      <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {filteredSegments.map((segment) => (
          <Card key={segment.id} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              {/* Segment Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-xl ${getTypeColor(segment.type)} text-white`}>
                    {getTypeIcon(segment.type)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg group-hover:text-blue-600 transition-colors">
                      {segment.name}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs capitalize">
                        {segment.type.replace('_', ' ')}
                      </Badge>
                      {segment.auto_update && (
                        <Badge variant="secondary" className="text-xs">
                          <Zap className="h-3 w-3 mr-1" />
                          Auto
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Segment
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh Data
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Description */}
              <p className="text-sm text-muted-foreground mb-4">
                {segment.description}
              </p>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">
                    {segment.customer_count.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">Customers</div>
                </div>
                
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold text-green-600">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                      notation: 'compact'
                    }).format(segment.avg_value)}
                  </div>
                  <div className="text-xs text-muted-foreground">Avg Value</div>
                </div>
              </div>

              {/* Growth Rate */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Growth Rate</span>
                  <span className={`text-sm font-medium ${getGrowthColor(segment.growth_rate)}`}>
                    {segment.growth_rate > 0 ? '+' : ''}{segment.growth_rate.toFixed(1)}%
                  </span>
                </div>
                <Progress 
                  value={Math.abs(segment.growth_rate)} 
                  className="h-2"
                />
              </div>

              {/* Engagement Score */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Engagement</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${getEngagementColor(segment.engagement_score)}`}>
                    {(segment.engagement_score * 100).toFixed(0)}%
                  </span>
                </div>
                <Progress 
                  value={segment.engagement_score * 100} 
                  className="h-2"
                />
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="h-4 w-4 mr-2" />
                  Analyze
                </Button>
                <Button size="sm" className="flex-1">
                  <Target className="h-4 w-4 mr-2" />
                  Campaign
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredSegments.length === 0 && (
        <Card className="border-0 shadow-lg">
          <CardContent className="p-12 text-center">
            <Target className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No segments found</h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm ? 'Try adjusting your search terms' : 'Create your first customer segment to get started'}
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Segment
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Segment Performance Chart */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Segment Performance Overview
          </CardTitle>
          <CardDescription>
            Customer distribution and engagement across segments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <PieChart className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p>Interactive segment performance chart would be displayed here</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
