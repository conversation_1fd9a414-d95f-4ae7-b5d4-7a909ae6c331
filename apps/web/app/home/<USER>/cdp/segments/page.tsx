import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-workspace-loader';

import { ModernSegmentsDashboard } from './_components/modern-segments-dashboard';
import { loadCustomerSegments } from '../_lib/server/load-customer-segments';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('cdp:segments.title');

  return {
    title,
  };
};

interface PageSearchParams {
  page?: string;
  query?: string;
  filter?: string;
}

async function SegmentsPage({
  params,
  searchParams,
}: {
  params: Promise<{ account: string }>;
  searchParams: Promise<PageSearchParams>;
}) {
  const client = getSupabaseServerClient();
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);

  const accountSlug = resolvedParams.account;
  const currentPage = resolvedSearchParams.page
    ? parseInt(resolvedSearchParams.page)
    : 1;
  const searchQuery = resolvedSearchParams.query || '';
  const filter = resolvedSearchParams.filter || 'all';

  const api = createTeamAccountsApi(client);

  try {
    const teamAccount = await api.getTeamAccount(accountSlug);

    const [segmentsData, { user, account }] = await Promise.all([
      loadCustomerSegments(client, teamAccount.id, currentPage, searchQuery, filter),
      loadTeamWorkspace(accountSlug),
    ]);

    return (
      <div className="flex flex-1 flex-col">
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="cdp:segments.title" />}
          description={
            <AppBreadcrumbs
              items={[
                {
                  title: <Trans i18nKey="common:routes.home" />,
                  url: `/home/<USER>
                },
                {
                  title: <Trans i18nKey="cdp:title" />,
                  url: `/home/<USER>/cdp`,
                },
                {
                  title: <Trans i18nKey="cdp:segments.title" />,
                  url: `/home/<USER>/cdp/segments`,
                },
              ]}
            />
          }
        />

        <div className="flex flex-1 flex-col space-y-6 p-6">
          <ModernSegmentsDashboard
            accountId={teamAccount.id}
            accountSlug={accountSlug}
            segmentsData={segmentsData}
            currentPage={currentPage}
            searchQuery={searchQuery}
            filter={filter}
            user={user}
            account={account}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading customer segments page:', error);

    // Return error page instead of fallback data
    return (
      <div className="flex flex-1 flex-col">
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="cdp:segments.title" />}
          description={
            <AppBreadcrumbs
              items={[
                {
                  title: <Trans i18nKey="common:routes.home" />,
                  url: `/home/<USER>
                },
                {
                  title: <Trans i18nKey="cdp:title" />,
                  url: `/home/<USER>/cdp`,
                },
                {
                  title: <Trans i18nKey="cdp:segments.title" />,
                  url: `/home/<USER>/cdp/segments`,
                },
              ]}
            />
          }
        />

        <div className="flex flex-1 flex-col items-center justify-center p-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">
              <Trans i18nKey="common:errors.generic" />
            </h2>
            <p className="text-muted-foreground mb-6">
              {error instanceof Error ? error.message : 'Unknown error occurred'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <Trans i18nKey="common:refresh" />
            </button>
          </div>
        </div>
      </div>
    );
  }
}

export default withI18n(SegmentsPage);
