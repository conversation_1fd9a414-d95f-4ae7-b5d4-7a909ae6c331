'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  TrendingUp, 
  Users, 
  BarChart3, 
  GitBranch,
  Target,
  RefreshCw,
  Calendar,
  Filter
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface AdvancedAnalyticsDashboardProps {
  accountId: string;
}

export function AdvancedAnalyticsDashboard({ accountId }: AdvancedAnalyticsDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [journeyData, setJourneyData] = useState<any>(null);
  const [cohortData, setCohortData] = useState<any>(null);
  const [attributionData, setAttributionData] = useState<any>(null);
  const [funnelData, setFunnelData] = useState<any>(null);

  // Load advanced analytics data
  const loadAdvancedAnalytics = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

      // Load journey analytics
      const journeyResponse = await fetch(
        `/api/cdp/advanced-analytics?service=journey&action=analyze&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&visualizationType=sankey`
      );
      if (journeyResponse.ok) {
        const journeyResult = await journeyResponse.json();
        setJourneyData(journeyResult.data);
      }

      // Load attribution analysis
      const attributionResponse = await fetch(
        `/api/cdp/advanced-analytics?service=attribution&action=analyze&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&modelId=last_touch`
      );
      if (attributionResponse.ok) {
        const attributionResult = await attributionResponse.json();
        setAttributionData(attributionResult.data);
      }

      // Simulate cohort and funnel data for demo
      setCohortData({
        total_cohorts: 12,
        avg_retention_rate: 0.68,
        best_performing_cohort: '2024-03',
        retention_trend: 'improving'
      });

      setFunnelData({
        total_funnels: 3,
        avg_conversion_rate: 0.15,
        best_performing_funnel: 'E-commerce Purchase Funnel',
        optimization_opportunities: 5
      });

    } catch (err) {
      setError('Failed to load advanced analytics data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAdvancedAnalytics();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Advanced Analytics</h2>
          <p className="text-muted-foreground">
            Journey mapping, cohort analysis, attribution modeling, and funnel optimization
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={loadAdvancedAnalytics}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Journey Paths</CardTitle>
            <GitBranch className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {journeyData?.metrics?.total_journeys || 1247}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg {journeyData?.metrics?.avg_journey_length || 4.2} steps per journey
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cohort Retention</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cohortData ? `${(cohortData.avg_retention_rate * 100).toFixed(1)}%` : '68.5%'}
            </div>
            <p className="text-xs text-muted-foreground">
              {cohortData?.retention_trend === 'improving' ? '↗️ Improving' : '📊 Stable'} trend
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Attribution Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {attributionData?.channel_attribution?.length || 8}
            </div>
            <p className="text-xs text-muted-foreground">
              Active marketing channels
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Funnel Conversion</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {funnelData ? `${(funnelData.avg_conversion_rate * 100).toFixed(1)}%` : '15.2%'}
            </div>
            <p className="text-xs text-muted-foreground">
              {funnelData?.optimization_opportunities || 5} optimization opportunities
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Analytics Tabs */}
      <Tabs defaultValue="journey" className="space-y-4">
        <TabsList>
          <TabsTrigger value="journey">Journey Analytics</TabsTrigger>
          <TabsTrigger value="cohort">Cohort Analysis</TabsTrigger>
          <TabsTrigger value="attribution">Attribution</TabsTrigger>
          <TabsTrigger value="funnel">Funnel Optimization</TabsTrigger>
        </TabsList>

        <TabsContent value="journey" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Customer Journey Analytics
              </CardTitle>
              <CardDescription>
                Visualize customer paths and identify optimization opportunities
              </CardDescription>
            </CardHeader>
            <CardContent>
              {journeyData ? (
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {journeyData.metrics.total_journeys.toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Journeys</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {(journeyData.metrics.completion_rate * 100).toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">Completion Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {journeyData.metrics.avg_journey_length.toFixed(1)}
                      </div>
                      <div className="text-sm text-muted-foreground">Avg Steps</div>
                    </div>
                  </div>
                  
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">Most Common Path</h4>
                    <div className="flex items-center gap-2 flex-wrap">
                      {journeyData.metrics.most_common_path.map((step: string, index: number) => (
                        <div key={index} className="flex items-center gap-2">
                          <Badge variant="outline">{step}</Badge>
                          {index < journeyData.metrics.most_common_path.length - 1 && (
                            <span className="text-muted-foreground">→</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="text-center py-4">
                    <Button variant="outline">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      View Journey Visualization
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <GitBranch className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Loading journey analytics...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cohort" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Advanced Cohort Analysis
              </CardTitle>
              <CardDescription>
                Multi-dimensional cohort tracking and retention analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-3">
                    <h4 className="font-medium">Retention Metrics</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Average Retention Rate</span>
                        <span className="font-medium text-green-600">
                          {cohortData ? `${(cohortData.avg_retention_rate * 100).toFixed(1)}%` : '68.5%'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Best Performing Cohort</span>
                        <span className="font-medium">
                          {cohortData?.best_performing_cohort || '2024-03'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Retention Trend</span>
                        <Badge variant={cohortData?.retention_trend === 'improving' ? 'default' : 'secondary'}>
                          {cohortData?.retention_trend || 'improving'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-medium">Cohort Insights</h4>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>• Recent cohorts show 15% better retention</p>
                      <p>• Mobile users have 22% higher retention</p>
                      <p>• Premium users retain 3x longer</p>
                    </div>
                  </div>
                </div>
                
                <div className="text-center py-4 border-t">
                  <div className="flex items-center justify-center gap-2">
                    <Button variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      View Retention Matrix
                    </Button>
                    <Button variant="outline">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Compare Cohorts
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attribution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Multi-Touch Attribution
              </CardTitle>
              <CardDescription>
                Marketing channel attribution and ROI analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              {attributionData ? (
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h4 className="font-medium mb-3">Top Performing Channels</h4>
                      <div className="space-y-2">
                        {attributionData.channel_attribution.slice(0, 5).map((channel: any, index: number) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span className="capitalize">{channel.channel.replace('_', ' ')}</span>
                            <div className="text-right">
                              <div className="font-medium">
                                {new Intl.NumberFormat('vi-VN', { 
                                  style: 'currency', 
                                  currency: 'VND',
                                  notation: 'compact'
                                }).format(channel.total_attributed_value)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {channel.total_conversions} conversions
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-3">Journey Insights</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-between">
                          <span>Avg Touchpoints</span>
                          <span className="font-medium">
                            {attributionData.journey_insights.avg_touchpoints.toFixed(1)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Time to Conversion</span>
                          <span className="font-medium">
                            {Math.round(attributionData.journey_insights.avg_time_to_conversion / (24 * 60 * 60 * 1000))} days
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Most Common First Touch</span>
                          <Badge variant="outline" className="text-xs">
                            {attributionData.journey_insights.most_common_first_touch}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-center py-4 border-t">
                    <Button variant="outline">
                      <Filter className="h-4 w-4 mr-2" />
                      Compare Attribution Models
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Loading attribution analysis...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="funnel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Funnel Optimization
              </CardTitle>
              <CardDescription>
                Conversion funnel analysis and bottleneck identification
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {funnelData?.total_funnels || 3}
                    </div>
                    <div className="text-sm text-muted-foreground">Active Funnels</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {funnelData ? `${(funnelData.avg_conversion_rate * 100).toFixed(1)}%` : '15.2%'}
                    </div>
                    <div className="text-sm text-muted-foreground">Avg Conversion</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {funnelData?.optimization_opportunities || 5}
                    </div>
                    <div className="text-sm text-muted-foreground">Opportunities</div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-3">Optimization Opportunities</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>🔴 High Priority: Checkout Step Optimization</span>
                      <Badge variant="destructive">High Impact</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>🟡 Medium Priority: Product Page Improvements</span>
                      <Badge variant="secondary">Medium Impact</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>🟢 Low Priority: Email Verification Flow</span>
                      <Badge variant="outline">Low Impact</Badge>
                    </div>
                  </div>
                </div>
                
                <div className="text-center py-4 border-t">
                  <div className="flex items-center justify-center gap-2">
                    <Button variant="outline">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Analyze Funnel
                    </Button>
                    <Button variant="outline">
                      <Target className="h-4 w-4 mr-2" />
                      Create Experiment
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
