import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { AdvancedAnalyticsDashboard } from './_components/advanced-analytics-dashboard';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('cdp:advancedAnalytics.title');

  return {
    title,
  };
};

interface AdvancedAnalyticsPageProps {
  params: Promise<{
    account: string;
  }>;
}

async function AdvancedAnalyticsPage({ params }: AdvancedAnalyticsPageProps) {
  const { account } = await params;

  return (
    <div className="flex flex-1 flex-col">
      <TeamAccountLayoutPageHeader
        account={account}
        title={<Trans i18nKey="cdp:advancedAnalytics.title" />}
        description={
          <AppBreadcrumbs
            items={[
              {
                title: <Trans i18nKey="common:routes.home" />,
                url: `/home/<USER>
              },
              {
                title: <Trans i18nKey="cdp:title" />,
                url: `/home/<USER>/cdp`,
              },
              {
                title: <Trans i18nKey="cdp:advancedAnalytics.title" />,
                url: `/home/<USER>/cdp/advanced-analytics`,
              },
            ]}
          />
        }
      />

      <div className="flex flex-1 flex-col space-y-6 p-6">
        <AdvancedAnalyticsDashboard accountId={account} />
      </div>
    </div>
  );
}

export default withI18n(AdvancedAnalyticsPage);
