'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { But<PERSON> } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Input } from '@kit/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { useTranslation } from 'react-i18next';
import {
  Users,
  Search,
  Filter,
  MoreHorizontal,
  Mail,
  Phone,
  Calendar,
  TrendingUp,
  TrendingDown,
  Minus,
  UserPlus,
  MessageSquare,
  UserCheck,
  Loader2,
  RefreshCw
} from 'lucide-react';

interface CustomerProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  created_at: Date;
  last_active_at?: Date;
  total_orders: number;
  total_spent: number;
  avg_order_value: number;
  engagement_score: number;
  churn_risk_score: number;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  metadata: Record<string, any>;
}

import type { CustomerProfilesResult } from '../_lib/server/load-customer-profiles';

interface CustomerProfilesProps {
  accountId: string;
  accountSlug: string;
  profilesData: CustomerProfilesResult;
  currentPage: number;
  searchQuery: string;
  filter: string;
  user: any;
  account: any;
}

export function CustomerProfiles({
  accountId,
  accountSlug,
  profilesData,
  currentPage,
  searchQuery,
  filter,
  user,
  account
}: CustomerProfilesProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const router = useRouter();

  // Use server-side data directly
  const profiles = profilesData.profiles;

  // Client state for form inputs (controlled by URL params)
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [selectedFilter, setSelectedFilter] = useState(filter);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Handle search and filter changes with URL navigation
  const handleSearchChange = (value: string) => {
    setLocalSearchQuery(value);

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Debounce search to avoid too many URL changes
    const timeoutId = setTimeout(() => {
      const params = new URLSearchParams();
      if (value) params.set('query', value);
      if (selectedFilter !== 'all') params.set('filter', selectedFilter);
      params.set('page', '1'); // Reset to first page

      const newUrl = `/home/<USER>/cdp/profiles${params.toString() ? '?' + params.toString() : ''}`;
      router.push(newUrl);
    }, 500);

    setSearchTimeout(timeoutId);
  };

  const handleFilterChange = (newFilter: string) => {
    setSelectedFilter(newFilter);

    const params = new URLSearchParams();
    if (localSearchQuery) params.set('query', localSearchQuery);
    if (newFilter !== 'all') params.set('filter', newFilter);
    params.set('page', '1'); // Reset to first page

    const newUrl = `/home/<USER>/cdp/profiles${params.toString() ? '?' + params.toString() : ''}`;
    router.push(newUrl);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  // Calculate stats from server data
  const stats = {
    totalProfiles: profilesData.total,
    activeProfiles: profiles.filter(p =>
      p.last_active_at && new Date(p.last_active_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length,
    newThisMonth: profiles.filter(p =>
      new Date(p.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length,
    avgEngagement: profiles.length > 0
      ? profiles.reduce((sum, p) => sum + p.engagement_score, 0) / profiles.length
      : 0
  };

  const getValueTierColor = (tier: string) => {
    switch (tier) {
      case 'high': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getChurnRiskColor = (score: number) => {
    if (score > 0.7) return 'bg-red-100 text-red-800 border-red-200';
    if (score > 0.4) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-green-100 text-green-800 border-green-200';
  };

  const getChurnRiskLevel = (score: number) => {
    if (score > 0.7) return t('cdp:profiles.churnRisk.high');
    if (score > 0.4) return t('cdp:profiles.churnRisk.medium');
    return t('cdp:profiles.churnRisk.low');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      notation: 'compact'
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('vi-VN');
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t('cdp:profiles.title')}</h2>
          <p className="text-muted-foreground">
            {t('cdp:profiles.description')}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('common:refresh')}
          </Button>

          <Button size="sm">
            <UserPlus className="h-4 w-4 mr-2" />
            {t('cdp:profiles.emptyState.createProfile')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('cdp:profiles.totalProfiles')}
                </p>
                <p className="text-2xl font-bold">
                  {stats.totalProfiles.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-xl">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('cdp:profiles.activeProfiles')}
                </p>
                <p className="text-2xl font-bold">
                  {stats.activeProfiles.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-xl">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('cdp:profiles.newThisMonth')}
                </p>
                <p className="text-2xl font-bold">
                  {stats.newThisMonth.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-xl">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('cdp:profiles.avgEngagement')}
                </p>
                <p className="text-2xl font-bold">
                  {(stats.avgEngagement * 100).toFixed(1)}%
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-xl">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder={t('cdp:profiles.searchPlaceholder')}
                  value={localSearchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <div className="flex gap-2">
                {[
                  { key: 'all', label: t('cdp:profiles.filters.all') },
                  { key: 'highValue', label: t('cdp:profiles.filters.highValue') },
                  { key: 'mediumValue', label: t('cdp:profiles.filters.mediumValue') },
                  { key: 'lowValue', label: t('cdp:profiles.filters.lowValue') },
                  { key: 'atRisk', label: t('cdp:profiles.filters.atRisk') },
                  { key: 'newCustomers', label: t('cdp:profiles.filters.newCustomers') }
                ].map((filter) => (
                  <Button
                    key={filter.key}
                    variant={selectedFilter === filter.key ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange(filter.key)}
                  >
                    {filter.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profiles List */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle>{t('cdp:profiles.title')}</CardTitle>
          <CardDescription>
            {t('cdp:profiles.profilesFound', `${profiles.length} profiles found`, { count: profiles.length })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {profiles.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('cdp:profiles.emptyState.title')}</h3>
              <p className="text-muted-foreground mb-6">{t('cdp:profiles.emptyState.description')}</p>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                {t('cdp:profiles.emptyState.createProfile')}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {profiles.map((profile) => (
                <div key={profile.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={profile.avatar_url} />
                      <AvatarFallback>
                        {getInitials(profile.first_name, profile.last_name)}
                      </AvatarFallback>
                    </Avatar>

                    <div>
                      <h4 className="font-semibold">
                        {profile.first_name} {profile.last_name}
                      </h4>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {profile.email}
                        </div>
                        {profile.phone && (
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {profile.phone}
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(profile.created_at)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {formatCurrency(profile.total_spent)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {profile.total_orders} orders
                      </div>
                    </div>

                    <div className="flex flex-col gap-1">
                      <Badge className={`${getValueTierColor(profile.value_tier)} border text-xs`}>
                        {t(`cdp:profiles.valueTiers.${profile.value_tier}`)}
                      </Badge>
                      <Badge className={`${getChurnRiskColor(profile.churn_risk_score)} border text-xs`}>
                        {getChurnRiskLevel(profile.churn_risk_score)}
                      </Badge>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        {t('cdp:profiles.viewProfile')}
                      </Button>
                      <Button variant="outline" size="sm">
                        <MessageSquare className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
