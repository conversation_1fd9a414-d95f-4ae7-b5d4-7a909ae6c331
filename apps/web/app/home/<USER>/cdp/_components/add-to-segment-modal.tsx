'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Checkbox } from '@kit/ui/checkbox';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Separator } from '@kit/ui/separator';
import {
  Target,
  Users,
  Search,
  Plus,
  Loader2,
  User,
  CheckCircle2,
} from 'lucide-react';

const addToSegmentSchema = z.object({
  segmentIds: z.array(z.string()).min(1, 'Please select at least one segment'),
  createNew: z.boolean().default(false),
  newSegmentName: z.string().optional(),
  newSegmentDescription: z.string().optional(),
});

type AddToSegmentFormData = z.infer<typeof addToSegmentSchema>;

interface Segment {
  id: string;
  name: string;
  description: string;
  type: string;
  customer_count: number;
  is_active: boolean;
}

interface AddToSegmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AddToSegmentFormData) => Promise<void>;
  customerName?: string;
  customerId?: string;
  availableSegments?: Segment[];
}

export function AddToSegmentModal({
  isOpen,
  onClose,
  onSubmit,
  customerName = '',
  customerId,
  availableSegments = [],
}: AddToSegmentModalProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock segments data if none provided
  const mockSegments: Segment[] = [
    {
      id: '1',
      name: 'Khách hàng VIP',
      description: 'Khách hàng có giá trị cao với chi tiêu trên 5 triệu VND',
      type: 'value_based',
      customer_count: 1247,
      is_active: true,
    },
    {
      id: '2',
      name: 'Khách hàng mới',
      description: 'Khách hàng đăng ký trong 30 ngày qua',
      type: 'behavioral',
      customer_count: 856,
      is_active: true,
    },
    {
      id: '3',
      name: 'Có nguy cơ rời bỏ',
      description: 'Khách hàng có điểm rủi ro cao (>0.7)',
      type: 'predictive',
      customer_count: 342,
      is_active: true,
    },
    {
      id: '4',
      name: 'Khách hàng trung thành',
      description: 'Khách hàng có hơn 10 đơn hàng và engagement cao',
      type: 'behavioral',
      customer_count: 2156,
      is_active: true,
    },
  ];

  const segments = availableSegments.length > 0 ? availableSegments : mockSegments;

  const form = useForm<AddToSegmentFormData>({
    resolver: zodResolver(addToSegmentSchema),
    defaultValues: {
      segmentIds: [],
      createNew: false,
      newSegmentName: '',
      newSegmentDescription: '',
    },
  });

  const createNew = form.watch('createNew');
  const selectedSegmentIds = form.watch('segmentIds');

  const handleSubmit = async (data: AddToSegmentFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      form.reset();
      onClose();
    } catch (error) {
      console.error('Error adding to segment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredSegments = segments.filter(segment =>
    segment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    segment.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getSegmentTypeColor = (type: string) => {
    switch (type) {
      case 'behavioral': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'demographic': return 'bg-green-100 text-green-800 border-green-200';
      case 'value_based': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'predictive': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {t('cdp:segments.addToSegment.title')}
          </DialogTitle>
          <DialogDescription>
            {t('cdp:segments.addToSegment.description', { name: customerName })}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Customer Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {t('cdp:segments.customerInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{customerName}</span>
                  {customerId && (
                    <Badge variant="outline" className="ml-auto">
                      ID: {customerId.slice(0, 8)}...
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Available Segments */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('cdp:segments.availableSegments')}
                </CardTitle>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder={t('cdp:segments.searchSegments')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {filteredSegments.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>{t('cdp:segments.noSegmentsFound')}</p>
                  </div>
                ) : (
                  <FormField
                    control={form.control}
                    name="segmentIds"
                    render={() => (
                      <FormItem>
                        <div className="space-y-3">
                          {filteredSegments.map((segment) => (
                            <FormField
                              key={`segment-${segment.id}`}
                              control={form.control}
                              name="segmentIds"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    className="flex flex-row items-start space-x-3 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(segment.id)}
                                        onCheckedChange={(checked) => {
                                          return checked
                                            ? field.onChange([...field.value, segment.id])
                                            : field.onChange(
                                                field.value?.filter(
                                                  (value) => value !== segment.id
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <div className="flex-1 space-y-1">
                                      <div className="flex items-center gap-2">
                                        <FormLabel className="text-sm font-medium">
                                          {segment.name}
                                        </FormLabel>
                                        <Badge className={`text-xs ${getSegmentTypeColor(segment.type)} border`}>
                                          {t(`cdp:segments.types.${segment.type}`)}
                                        </Badge>
                                        {segment.is_active && (
                                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                                        )}
                                      </div>
                                      <p className="text-xs text-muted-foreground">
                                        {segment.description}
                                      </p>
                                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                        <Users className="h-3 w-3" />
                                        <span>{segment.customer_count.toLocaleString()} {t('cdp:segments.customers')}</span>
                                      </div>
                                    </div>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </Card>

            {/* Create New Segment Option */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  {t('cdp:segments.createNewSegment')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="createNew"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          {t('cdp:segments.createNewSegmentOption')}
                        </FormLabel>
                        <FormDescription>
                          {t('cdp:segments.createNewSegmentDescription')}
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                {createNew && (
                  <>
                    <Separator />
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="newSegmentName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('cdp:segments.newSegmentName')}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t('cdp:segments.newSegmentNamePlaceholder')}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="newSegmentDescription"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('cdp:segments.newSegmentDescription')}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t('cdp:segments.newSegmentDescriptionPlaceholder')}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Selected Summary */}
            {selectedSegmentIds.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {t('cdp:segments.selectedSegments')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {selectedSegmentIds.map((segmentId) => {
                      const segment = segments.find(s => s.id === segmentId);
                      return segment ? (
                        <Badge key={segmentId} variant="secondary">
                          {segment.name}
                        </Badge>
                      ) : null;
                    })}
                    {createNew && form.watch('newSegmentName') && (
                      <Badge variant="outline" className="border-dashed">
                        <Plus className="h-3 w-3 mr-1" />
                        {form.watch('newSegmentName')}
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                {t('common:cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting || (selectedSegmentIds.length === 0 && !createNew)}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Target className="mr-2 h-4 w-4" />
                {t('cdp:segments.addToSegment.submit')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
