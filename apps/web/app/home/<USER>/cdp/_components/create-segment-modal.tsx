'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import { Switch } from '@kit/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Separator } from '@kit/ui/separator';
import {
  Target,
  Users,
  Brain,
  TrendingUp,
  Filter,
  Zap,
  Plus,
  Loader2,
} from 'lucide-react';

const createSegmentSchema = z.object({
  name: z.string().min(1, 'Segment name is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.enum(['behavioral', 'demographic', 'value_based', 'predictive']),
  is_auto_updating: z.boolean().default(true),
  criteria: z.object({
    total_spent: z.object({
      operator: z.enum(['gte', 'lte', 'eq', 'between']).optional(),
      value: z.number().optional(),
      min: z.number().optional(),
      max: z.number().optional(),
    }).optional(),
    total_orders: z.object({
      operator: z.enum(['gte', 'lte', 'eq', 'between']).optional(),
      value: z.number().optional(),
      min: z.number().optional(),
      max: z.number().optional(),
    }).optional(),
    engagement_score: z.object({
      operator: z.enum(['gte', 'lte', 'eq', 'between']).optional(),
      value: z.number().optional(),
      min: z.number().optional(),
      max: z.number().optional(),
    }).optional(),
    churn_risk_score: z.object({
      operator: z.enum(['gte', 'lte', 'eq', 'between']).optional(),
      value: z.number().optional(),
      min: z.number().optional(),
      max: z.number().optional(),
    }).optional(),
    value_tier: z.enum(['high', 'medium', 'low']).optional(),
    location: z.string().optional(),
    created_days_ago: z.number().optional(),
  }),
});

type CreateSegmentFormData = z.infer<typeof createSegmentSchema>;

interface CreateSegmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateSegmentFormData) => Promise<void>;
}

export function CreateSegmentModal({
  isOpen,
  onClose,
  onSubmit,
}: CreateSegmentModalProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CreateSegmentFormData>({
    resolver: zodResolver(createSegmentSchema),
    defaultValues: {
      name: '',
      description: '',
      type: 'behavioral',
      is_auto_updating: true,
      criteria: {},
    },
  });

  const handleSubmit = async (data: CreateSegmentFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      form.reset();
      onClose();
    } catch (error) {
      console.error('Error creating segment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const segmentTypes = [
    {
      value: 'behavioral',
      label: t('cdp:segments.types.behavioral'),
      description: t('cdp:segments.typeDescriptions.behavioral'),
      icon: <Users className="h-5 w-5" />,
    },
    {
      value: 'demographic',
      label: t('cdp:segments.types.demographic'),
      description: t('cdp:segments.typeDescriptions.demographic'),
      icon: <Target className="h-5 w-5" />,
    },
    {
      value: 'value_based',
      label: t('cdp:segments.types.value_based'),
      description: t('cdp:segments.typeDescriptions.value_based'),
      icon: <TrendingUp className="h-5 w-5" />,
    },
    {
      value: 'predictive',
      label: t('cdp:segments.types.predictive'),
      description: t('cdp:segments.typeDescriptions.predictive'),
      icon: <Brain className="h-5 w-5" />,
    },
  ];

  const selectedType = form.watch('type');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {t('cdp:segments.createSegment.title')}
          </DialogTitle>
          <DialogDescription>
            {t('cdp:segments.createSegment.description')}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('cdp:segments.createSegment.basicInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:segments.name')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('cdp:segments.namePlaceholder')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:segments.description')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('cdp:segments.descriptionPlaceholder')}
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_auto_updating"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base flex items-center gap-2">
                          <Zap className="h-4 w-4" />
                          {t('cdp:segments.autoUpdating')}
                        </FormLabel>
                        <FormDescription>
                          {t('cdp:segments.autoUpdatingDescription')}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Segment Type */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('cdp:segments.createSegment.segmentType')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="grid gap-3 md:grid-cols-2">
                          {segmentTypes.map((type) => (
                            <div
                              key={type.value}
                              className={`cursor-pointer rounded-lg border p-4 transition-all hover:border-primary ${
                                field.value === type.value
                                  ? 'border-primary bg-primary/5'
                                  : 'border-border'
                              }`}
                              onClick={() => field.onChange(type.value)}
                            >
                              <div className="flex items-start gap-3">
                                <div className={`p-2 rounded-lg ${
                                  field.value === type.value
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-muted'
                                }`}>
                                  {type.icon}
                                </div>
                                <div className="flex-1">
                                  <h4 className="font-medium">{type.label}</h4>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {type.description}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Criteria Builder */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  {t('cdp:segments.createSegment.criteria')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedType === 'value_based' && (
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label>{t('cdp:segments.criteria.totalSpent')}</Label>
                        <div className="flex gap-2 mt-2">
                          <Select>
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="≥" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="gte">≥</SelectItem>
                              <SelectItem value="lte">≤</SelectItem>
                              <SelectItem value="eq">=</SelectItem>
                            </SelectContent>
                          </Select>
                          <Input
                            type="number"
                            placeholder="5000000"
                            className="flex-1"
                          />
                        </div>
                      </div>
                      <div>
                        <Label>{t('cdp:segments.criteria.totalOrders')}</Label>
                        <div className="flex gap-2 mt-2">
                          <Select>
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="≥" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="gte">≥</SelectItem>
                              <SelectItem value="lte">≤</SelectItem>
                              <SelectItem value="eq">=</SelectItem>
                            </SelectContent>
                          </Select>
                          <Input
                            type="number"
                            placeholder="10"
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {selectedType === 'behavioral' && (
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label>{t('cdp:segments.criteria.engagementScore')}</Label>
                        <div className="flex gap-2 mt-2">
                          <Select>
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="≥" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="gte">≥</SelectItem>
                              <SelectItem value="lte">≤</SelectItem>
                            </SelectContent>
                          </Select>
                          <Input
                            type="number"
                            placeholder="0.8"
                            step="0.1"
                            min="0"
                            max="1"
                            className="flex-1"
                          />
                        </div>
                      </div>
                      <div>
                        <Label>{t('cdp:segments.criteria.recentActivity')}</Label>
                        <div className="flex gap-2 mt-2">
                          <Select>
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="Last" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="7">7 days</SelectItem>
                              <SelectItem value="30">30 days</SelectItem>
                              <SelectItem value="90">90 days</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {selectedType === 'predictive' && (
                  <div className="space-y-4">
                    <div>
                      <Label>{t('cdp:segments.criteria.churnRisk')}</Label>
                      <div className="flex gap-2 mt-2">
                        <Select>
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="≥" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gte">≥</SelectItem>
                            <SelectItem value="lte">≤</SelectItem>
                          </SelectContent>
                        </Select>
                        <Input
                          type="number"
                          placeholder="0.7"
                          step="0.1"
                          min="0"
                          max="1"
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {selectedType === 'demographic' && (
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label>{t('cdp:segments.criteria.location')}</Label>
                        <Select>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder={t('cdp:segments.criteria.selectLocation')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="hanoi">Hà Nội</SelectItem>
                            <SelectItem value="hcmc">TP.HCM</SelectItem>
                            <SelectItem value="danang">Đà Nẵng</SelectItem>
                            <SelectItem value="cantho">Cần Thơ</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>{t('cdp:segments.criteria.valueTier')}</Label>
                        <Select>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder={t('cdp:segments.criteria.selectValueTier')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="high">{t('cdp:profiles.valueTiers.high')}</SelectItem>
                            <SelectItem value="medium">{t('cdp:profiles.valueTiers.medium')}</SelectItem>
                            <SelectItem value="low">{t('cdp:profiles.valueTiers.low')}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                )}

                <div className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
                  {t('cdp:segments.createSegment.criteriaNote')}
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                {t('common:cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('cdp:segments.createSegment.submit')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
