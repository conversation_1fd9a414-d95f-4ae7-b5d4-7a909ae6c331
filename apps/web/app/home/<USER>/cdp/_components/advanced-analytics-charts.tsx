'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@kit/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Target,
  GitBranch,
  PieChart,
  LineChart,
  Activity,
  Zap,
  RefreshCw
} from 'lucide-react';

import { InteractiveChart } from './charts/interactive-chart';

interface AdvancedAnalyticsChartsProps {
  accountId: string;
}

export function AdvancedAnalyticsCharts({ accountId }: AdvancedAnalyticsChartsProps) {
  const [loading, setLoading] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');

  // Mock data for charts
  const customerJourneyData = [
    { label: 'Homepage', value: 10000, trend: 0.05 },
    { label: 'Product View', value: 7500, trend: 0.12 },
    { label: 'Add to Cart', value: 3200, trend: -0.03 },
    { label: 'Checkout', value: 1800, trend: 0.08 },
    { label: 'Purchase', value: 1200, trend: 0.15 }
  ];

  const cohortRetentionData = [
    { label: 'Week 1', value: 100, trend: 0 },
    { label: 'Week 2', value: 75, trend: -0.25 },
    { label: 'Week 3', value: 58, trend: -0.23 },
    { label: 'Week 4', value: 45, trend: -0.22 },
    { label: 'Week 8', value: 32, trend: -0.29 },
    { label: 'Week 12', value: 28, trend: -0.13 }
  ];

  const channelAttributionData = [
    { label: 'Organic Search', value: 35, color: '#3b82f6' },
    { label: 'Paid Search', value: 25, color: '#8b5cf6' },
    { label: 'Social Media', value: 20, color: '#ec4899' },
    { label: 'Email', value: 12, color: '#10b981' },
    { label: 'Direct', value: 8, color: '#f59e0b' }
  ];

  const conversionFunnelData = [
    { label: 'Awareness', value: 100000, trend: 0.08 },
    { label: 'Interest', value: 45000, trend: 0.12 },
    { label: 'Consideration', value: 18000, trend: -0.05 },
    { label: 'Intent', value: 8500, trend: 0.15 },
    { label: 'Purchase', value: 3200, trend: 0.22 }
  ];

  const revenueAnalyticsData = [
    { label: 'Jan', value: 125000, trend: 0.15 },
    { label: 'Feb', value: 142000, trend: 0.14 },
    { label: 'Mar', value: 138000, trend: -0.03 },
    { label: 'Apr', value: 165000, trend: 0.20 },
    { label: 'May', value: 178000, trend: 0.08 },
    { label: 'Jun', value: 195000, trend: 0.10 },
    { label: 'Jul', value: 210000, trend: 0.08 }
  ];

  const customerSegmentData = [
    { label: 'High Value', value: 1247, color: '#10b981' },
    { label: 'Medium Value', value: 3456, color: '#3b82f6' },
    { label: 'Low Value', value: 2890, color: '#f59e0b' },
    { label: 'At Risk', value: 567, color: '#ef4444' },
    { label: 'New Customers', value: 1234, color: '#8b5cf6' }
  ];

  const handleDataPointClick = (point: any) => {
    console.log('Data point clicked:', point);
    // Handle data point interaction
  };

  const handleExport = (chartType: string) => {
    console.log('Exporting chart:', chartType);
    // Handle chart export
  };

  const refreshData = async () => {
    setLoading(true);
    // Simulate data refresh
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Advanced Analytics</h2>
          <p className="text-muted-foreground">
            Interactive charts and deep customer insights
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Tabs value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <TabsList>
              <TabsTrigger value="24h">24H</TabsTrigger>
              <TabsTrigger value="7d">7D</TabsTrigger>
              <TabsTrigger value="30d">30D</TabsTrigger>
              <TabsTrigger value="90d">90D</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="journey" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="journey">Customer Journey</TabsTrigger>
          <TabsTrigger value="cohort">Cohort Analysis</TabsTrigger>
          <TabsTrigger value="attribution">Attribution</TabsTrigger>
          <TabsTrigger value="funnel">Conversion Funnel</TabsTrigger>
          <TabsTrigger value="revenue">Revenue Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="journey" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <InteractiveChart
              title="Customer Journey Flow"
              description="Step-by-step customer journey analysis"
              type="bar"
              data={customerJourneyData}
              height={350}
              showTrend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('journey-flow')}
            />
            
            <InteractiveChart
              title="Journey Conversion Rates"
              description="Conversion rates between journey steps"
              type="line"
              data={customerJourneyData.map((point, index) => ({
                ...point,
                value: index === 0 ? 100 : (point.value / customerJourneyData[0].value) * 100
              }))}
              height={350}
              showTrend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('journey-conversion')}
            />
          </div>
          
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Journey Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900">Biggest Drop-off</h4>
                  <p className="text-2xl font-bold text-blue-700">Product → Cart</p>
                  <p className="text-sm text-blue-600">57% drop-off rate</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-medium text-green-900">Best Converter</h4>
                  <p className="text-2xl font-bold text-green-700">Cart → Checkout</p>
                  <p className="text-sm text-green-600">78% conversion rate</p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-medium text-purple-900">Avg Journey Time</h4>
                  <p className="text-2xl font-bold text-purple-700">4.2 days</p>
                  <p className="text-sm text-purple-600">From first visit to purchase</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cohort" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <InteractiveChart
              title="Cohort Retention Analysis"
              description="Customer retention over time"
              type="line"
              data={cohortRetentionData}
              height={350}
              showTrend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('cohort-retention')}
            />
            
            <InteractiveChart
              title="Customer Segments"
              description="Customer distribution by value tier"
              type="pie"
              data={customerSegmentData}
              height={350}
              showLegend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('customer-segments')}
            />
          </div>
          
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Cohort Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">68.5%</div>
                  <div className="text-sm text-muted-foreground">30-day Retention</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">42.3%</div>
                  <div className="text-sm text-muted-foreground">90-day Retention</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">28.1%</div>
                  <div className="text-sm text-muted-foreground">1-year Retention</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">15.2%</div>
                  <div className="text-sm text-muted-foreground">Churn Risk</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attribution" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <InteractiveChart
              title="Marketing Channel Attribution"
              description="Revenue attribution by marketing channel"
              type="pie"
              data={channelAttributionData}
              height={350}
              showLegend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('channel-attribution')}
            />
            
            <InteractiveChart
              title="Attribution Model Comparison"
              description="First-touch vs Last-touch attribution"
              type="bar"
              data={[
                { label: 'First Touch', value: 45, trend: 0.08 },
                { label: 'Last Touch', value: 35, trend: -0.05 },
                { label: 'Linear', value: 20, trend: 0.12 }
              ]}
              height={350}
              showTrend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('attribution-comparison')}
            />
          </div>
          
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Attribution Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900">Top Channel</h4>
                  <p className="text-2xl font-bold text-blue-700">Organic Search</p>
                  <p className="text-sm text-blue-600">35% of conversions</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-medium text-green-900">Best ROI</h4>
                  <p className="text-2xl font-bold text-green-700">Email</p>
                  <p className="text-sm text-green-600">450% return on investment</p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-medium text-purple-900">Avg Touchpoints</h4>
                  <p className="text-2xl font-bold text-purple-700">3.8</p>
                  <p className="text-sm text-purple-600">Before conversion</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="funnel" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <InteractiveChart
              title="Conversion Funnel"
              description="Step-by-step conversion analysis"
              type="bar"
              data={conversionFunnelData}
              height={350}
              showTrend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('conversion-funnel')}
            />
            
            <InteractiveChart
              title="Funnel Conversion Rates"
              description="Conversion rates between funnel steps"
              type="line"
              data={conversionFunnelData.map((point, index) => ({
                ...point,
                value: index === 0 ? 100 : (point.value / conversionFunnelData[index - 1].value) * 100
              }))}
              height={350}
              showTrend={true}
              interactive={true}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('funnel-rates')}
            />
          </div>
          
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Funnel Optimization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">3.2%</div>
                  <div className="text-sm text-muted-foreground">Overall Conversion</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">45%</div>
                  <div className="text-sm text-muted-foreground">Interest → Consideration</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">47%</div>
                  <div className="text-sm text-muted-foreground">Intent → Purchase</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">+22%</div>
                  <div className="text-sm text-muted-foreground">Potential Improvement</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <div className="grid gap-6">
            <InteractiveChart
              title="Revenue Trend Analysis"
              description="Monthly revenue growth and trends"
              type="line"
              data={revenueAnalyticsData}
              height={400}
              showTrend={true}
              interactive={true}
              realTime={true}
              refreshInterval={10000}
              onDataPointClick={handleDataPointClick}
              onExport={() => handleExport('revenue-trend')}
            />
          </div>
          
          <div className="grid gap-6 lg:grid-cols-2">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Revenue Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Monthly Recurring Revenue</span>
                    <span className="text-lg font-bold text-green-600">$210,000</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Average Order Value</span>
                    <span className="text-lg font-bold text-blue-600">$156</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Customer Lifetime Value</span>
                    <span className="text-lg font-bold text-purple-600">$2,340</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Revenue Growth Rate</span>
                    <span className="text-lg font-bold text-orange-600">+8.2%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Revenue Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900">Best Month</h4>
                    <p className="text-sm text-green-700">July 2024 - $210,000 (+8% MoM)</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900">Growth Trend</h4>
                    <p className="text-sm text-blue-700">Consistent 8-12% monthly growth</p>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900">Forecast</h4>
                    <p className="text-sm text-purple-700">$245,000 projected for August</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
