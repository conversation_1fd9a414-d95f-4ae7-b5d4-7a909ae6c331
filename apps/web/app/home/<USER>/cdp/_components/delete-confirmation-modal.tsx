'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Card, CardContent } from '@kit/ui/card';
import {
  AlertTriangle,
  Trash2,
  Loader2,
  User,
  Target,
} from 'lucide-react';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  title: string;
  description: string;
  itemName: string;
  itemType: 'profile' | 'segment';
  requireConfirmation?: boolean;
}

export function DeleteConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  itemName,
  itemType,
  requireConfirmation = true,
}: DeleteConfirmationModalProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');

  const handleConfirm = async () => {
    if (requireConfirmation && confirmationText !== itemName) {
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirm();
      setConfirmationText('');
      onClose();
    } catch (error) {
      console.error('Error deleting item:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setConfirmationText('');
    onClose();
  };

  const isConfirmationValid = !requireConfirmation || confirmationText === itemName;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <Card className="border-destructive/20 bg-destructive/5">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              {itemType === 'profile' ? (
                <User className="h-8 w-8 text-destructive" />
              ) : (
                <Target className="h-8 w-8 text-destructive" />
              )}
              <div>
                <h4 className="font-medium text-destructive">
                  {itemName}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {itemType === 'profile' 
                    ? t('cdp:delete.profileType') 
                    : t('cdp:delete.segmentType')
                  }
                </p>
              </div>
            </div>

            <div className="space-y-3 text-sm text-muted-foreground">
              <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-destructive mt-2 flex-shrink-0" />
                <span>
                  {itemType === 'profile' 
                    ? t('cdp:delete.profileWarning1')
                    : t('cdp:delete.segmentWarning1')
                  }
                </span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-destructive mt-2 flex-shrink-0" />
                <span>
                  {itemType === 'profile' 
                    ? t('cdp:delete.profileWarning2')
                    : t('cdp:delete.segmentWarning2')
                  }
                </span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 rounded-full bg-destructive mt-2 flex-shrink-0" />
                <span>
                  {t('cdp:delete.irreversibleWarning')}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {requireConfirmation && (
          <div className="space-y-2">
            <Label htmlFor="confirmation">
              {t('cdp:delete.confirmationLabel')}
            </Label>
            <p className="text-sm text-muted-foreground">
              {t('cdp:delete.confirmationInstruction', { name: itemName })}
            </p>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={itemName}
              className={`${
                confirmationText && confirmationText !== itemName
                  ? 'border-destructive focus:border-destructive'
                  : ''
              }`}
            />
            {confirmationText && confirmationText !== itemName && (
              <p className="text-sm text-destructive">
                {t('cdp:delete.confirmationMismatch')}
              </p>
            )}
          </div>
        )}

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose}>
            {t('common:cancel')}
          </Button>
          <Button 
            type="button" 
            variant="destructive" 
            onClick={handleConfirm}
            disabled={isDeleting || !isConfirmationValid}
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Trash2 className="mr-2 h-4 w-4" />
            {t('cdp:delete.confirmDelete')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
