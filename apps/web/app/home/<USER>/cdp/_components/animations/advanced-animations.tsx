'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { 
  TrendingUp, 
  Users, 
  Activity,
  Zap,
  Target,
  BarChart3,
  ArrowRight,
  CheckCircle,
  Star,
  Heart,
  Sparkles
} from 'lucide-react';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  prefix?: string;
  suffix?: string;
  decimals?: number;
}

export function AnimatedCounter({ 
  value, 
  duration = 2000, 
  prefix = '', 
  suffix = '', 
  decimals = 0 
}: AnimatedCounterProps) {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCount(value * easeOutQuart);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isVisible, value, duration]);

  return (
    <div ref={ref} className="font-bold text-2xl">
      {prefix}{count.toFixed(decimals)}{suffix}
    </div>
  );
}

interface AnimatedProgressProps {
  value: number;
  duration?: number;
  color?: string;
  showValue?: boolean;
}

export function AnimatedProgress({ 
  value, 
  duration = 1500, 
  color = 'bg-blue-500',
  showValue = true 
}: AnimatedProgressProps) {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const elapsed = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function
      const easeOutCubic = 1 - Math.pow(1 - elapsed, 3);
      setProgress(value * easeOutCubic);

      if (elapsed < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isVisible, value, duration]);

  return (
    <div ref={ref} className="space-y-2">
      <div className="relative h-3 bg-gray-200 rounded-full overflow-hidden">
        <div 
          className={`h-full ${color} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${progress}%` }}
        />
      </div>
      {showValue && (
        <div className="text-sm text-gray-600 text-right">
          {progress.toFixed(1)}%
        </div>
      )}
    </div>
  );
}

interface FloatingElementProps {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
}

export function FloatingElement({ children, delay = 0, duration = 3000 }: FloatingElementProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delay);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [delay]);

  return (
    <div
      ref={ref}
      className={`transform transition-all duration-1000 ease-out ${
        isVisible 
          ? 'translate-y-0 opacity-100 scale-100' 
          : 'translate-y-8 opacity-0 scale-95'
      }`}
      style={{ 
        transitionDelay: `${delay}ms`,
        animation: isVisible ? `float ${duration}ms ease-in-out infinite` : 'none'
      }}
    >
      {children}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
      `}</style>
    </div>
  );
}

interface PulseElementProps {
  children: React.ReactNode;
  color?: string;
  intensity?: 'low' | 'medium' | 'high';
}

export function PulseElement({ children, color = 'blue', intensity = 'medium' }: PulseElementProps) {
  const intensityClasses = {
    low: 'animate-pulse',
    medium: 'animate-pulse',
    high: 'animate-pulse'
  };

  const colorClasses = {
    blue: 'shadow-blue-500/50',
    green: 'shadow-green-500/50',
    purple: 'shadow-purple-500/50',
    orange: 'shadow-orange-500/50'
  };

  return (
    <div className={`${intensityClasses[intensity]} shadow-lg ${colorClasses[color as keyof typeof colorClasses]}`}>
      {children}
    </div>
  );
}

interface GlowEffectProps {
  children: React.ReactNode;
  color?: string;
  intensity?: number;
}

export function GlowEffect({ children, color = '#3b82f6', intensity = 0.5 }: GlowEffectProps) {
  return (
    <div 
      className="relative"
      style={{
        filter: `drop-shadow(0 0 ${intensity * 20}px ${color}${Math.round(intensity * 255).toString(16).padStart(2, '0')})`
      }}
    >
      {children}
    </div>
  );
}

interface MorphingShapeProps {
  size?: number;
  color?: string;
  duration?: number;
}

export function MorphingShape({ size = 100, color = '#3b82f6', duration = 4000 }: MorphingShapeProps) {
  return (
    <div 
      className="relative"
      style={{ width: size, height: size }}
    >
      <div
        className="absolute inset-0 rounded-full"
        style={{
          background: `linear-gradient(45deg, ${color}, ${color}80)`,
          animation: `morph ${duration}ms ease-in-out infinite`
        }}
      />
      <style jsx>{`
        @keyframes morph {
          0%, 100% { 
            border-radius: 50% 50% 50% 50%;
            transform: rotate(0deg) scale(1);
          }
          25% { 
            border-radius: 60% 40% 30% 70%;
            transform: rotate(90deg) scale(1.1);
          }
          50% { 
            border-radius: 30% 60% 70% 40%;
            transform: rotate(180deg) scale(0.9);
          }
          75% { 
            border-radius: 70% 30% 40% 60%;
            transform: rotate(270deg) scale(1.05);
          }
        }
      `}</style>
    </div>
  );
}

interface ParticleEffectProps {
  count?: number;
  color?: string;
  size?: number;
}

export function ParticleEffect({ count = 20, color = '#3b82f6', size = 4 }: ParticleEffectProps) {
  const particles = Array.from({ length: count }, (_, i) => ({
    id: i,
    delay: Math.random() * 2000,
    duration: 3000 + Math.random() * 2000,
    x: Math.random() * 100,
    y: Math.random() * 100
  }));

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute rounded-full opacity-70"
          style={{
            width: size,
            height: size,
            backgroundColor: color,
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            animation: `particle ${particle.duration}ms ease-in-out infinite`,
            animationDelay: `${particle.delay}ms`
          }}
        />
      ))}
      <style jsx>{`
        @keyframes particle {
          0%, 100% { 
            transform: translateY(0px) scale(0);
            opacity: 0;
          }
          50% { 
            transform: translateY(-20px) scale(1);
            opacity: 0.7;
          }
        }
      `}</style>
    </div>
  );
}

// Demo component showcasing all animations
export function AdvancedAnimationsDemo() {
  const [showDemo, setShowDemo] = useState(false);

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Advanced Animations</h2>
          <p className="text-muted-foreground">
            Interactive animations and micro-interactions
          </p>
        </div>
        
        <Button onClick={() => setShowDemo(!showDemo)}>
          {showDemo ? 'Hide Demo' : 'Show Demo'}
        </Button>
      </div>

      {showDemo && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Animated Counters */}
          <FloatingElement delay={0}>
            <Card className="border-0 shadow-lg relative overflow-hidden">
              <ParticleEffect count={10} color="#3b82f6" />
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-blue-100 rounded-xl">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <Badge variant="secondary">Live</Badge>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <AnimatedCounter value={12847} suffix="" />
                  <div className="flex items-center gap-1 text-sm text-green-600">
                    <TrendingUp className="h-4 w-4" />
                    <span>+12.5% this month</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </FloatingElement>

          {/* Animated Progress */}
          <FloatingElement delay={200}>
            <PulseElement color="green" intensity="medium">
              <Card className="border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-green-100 rounded-xl">
                      <Target className="h-6 w-6 text-green-600" />
                    </div>
                    <GlowEffect color="#10b981" intensity={0.3}>
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    </GlowEffect>
                  </div>
                  <div className="space-y-4">
                    <p className="text-sm font-medium text-gray-600">Goal Progress</p>
                    <AnimatedProgress value={78.5} color="bg-green-500" />
                    <AnimatedProgress value={65.2} color="bg-blue-500" />
                    <AnimatedProgress value={92.1} color="bg-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </PulseElement>
          </FloatingElement>

          {/* Morphing Shape */}
          <FloatingElement delay={400}>
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-purple-100 rounded-xl">
                    <Activity className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="flex items-center gap-2">
                    <MorphingShape size={20} color="#8b5cf6" duration={3000} />
                    <span className="text-sm text-purple-600">Active</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">System Health</p>
                  <AnimatedCounter value={99.9} suffix="%" decimals={1} />
                  <div className="flex items-center gap-1 text-sm text-purple-600">
                    <Sparkles className="h-4 w-4" />
                    <span>All systems operational</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </FloatingElement>

          {/* Interactive Elements */}
          <FloatingElement delay={600}>
            <Card className="border-0 shadow-lg group hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-orange-100 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Zap className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-4 w-4 text-red-500 animate-pulse" />
                    <Star className="h-4 w-4 text-yellow-500 animate-bounce" />
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Engagement</p>
                  <AnimatedCounter value={87.3} suffix="%" decimals={1} />
                  <div className="flex items-center gap-1 text-sm text-orange-600">
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    <span>View details</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </FloatingElement>

          {/* Revenue Chart Animation */}
          <FloatingElement delay={800}>
            <Card className="border-0 shadow-lg relative overflow-hidden">
              <ParticleEffect count={15} color="#10b981" size={3} />
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-green-100 rounded-xl">
                    <BarChart3 className="h-6 w-6 text-green-600" />
                  </div>
                  <GlowEffect color="#10b981" intensity={0.4}>
                    <Badge variant="default" className="bg-green-500">
                      +15.2%
                    </Badge>
                  </GlowEffect>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Revenue</p>
                  <AnimatedCounter value={2450000} prefix="$" />
                  <AnimatedProgress value={85} color="bg-green-500" />
                </div>
              </CardContent>
            </Card>
          </FloatingElement>

          {/* Performance Metrics */}
          <FloatingElement delay={1000}>
            <PulseElement color="blue" intensity="low">
              <Card className="border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">CPU Usage</span>
                      <AnimatedCounter value={34} suffix="%" />
                    </div>
                    <AnimatedProgress value={34} color="bg-blue-500" showValue={false} />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Memory</span>
                      <AnimatedCounter value={67} suffix="%" />
                    </div>
                    <AnimatedProgress value={67} color="bg-purple-500" showValue={false} />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Storage</span>
                      <AnimatedCounter value={23} suffix="%" />
                    </div>
                    <AnimatedProgress value={23} color="bg-green-500" showValue={false} />
                  </div>
                </CardContent>
              </Card>
            </PulseElement>
          </FloatingElement>
        </div>
      )}
    </div>
  );
}
