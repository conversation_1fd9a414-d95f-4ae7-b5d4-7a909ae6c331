'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Progress } from '@kit/ui/progress';
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  ShoppingCart,
  TrendingUp,
  AlertTriangle,
  Star,
  Activity,
  CreditCard,
  MessageSquare,
  Edit,
  Trash2,
  UserPlus,
  Target,
} from 'lucide-react';

import { AddToSegmentModal } from './add-to-segment-modal';
import { SendMessageModal } from './send-message-modal';

interface CustomerProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  last_active_at?: string;
  total_orders: number;
  total_spent: number;
  avg_order_value: number;
  engagement_score: number;
  churn_risk_score: number;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  metadata: Record<string, any>;
}

interface CustomerProfileDetailModalProps {
  profile: CustomerProfile | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (profile: CustomerProfile) => void;
  onDelete: (profileId: string) => void;
  onAddToSegment: (profileId: string) => void;
  onSendMessage: (profileId: string) => void;
}

export function CustomerProfileDetailModal({
  profile,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onAddToSegment,
  onSendMessage,
}: CustomerProfileDetailModalProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [activeTab, setActiveTab] = useState('overview');

  // Nested modal states
  const [isAddToSegmentModalOpen, setIsAddToSegmentModalOpen] = useState(false);
  const [isSendMessageModalOpen, setIsSendMessageModalOpen] = useState(false);

  if (!profile) return null;

  const getValueTierColor = (tier: string) => {
    switch (tier) {
      case 'high': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getChurnRiskColor = (score: number) => {
    if (score > 0.7) return 'bg-red-100 text-red-800 border-red-200';
    if (score > 0.4) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-green-100 text-green-800 border-green-200';
  };

  const getChurnRiskLevel = (score: number) => {
    if (score > 0.7) return t('cdp:profiles.churnRiskLevels.high');
    if (score > 0.4) return t('cdp:profiles.churnRiskLevels.medium');
    return t('cdp:profiles.churnRiskLevels.low');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      notation: 'compact'
    }).format(amount);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('vi-VN');
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Nested modal handlers
  const handleAddToSegment = () => {
    setIsAddToSegmentModalOpen(true);
  };

  const handleSendMessage = () => {
    setIsSendMessageModalOpen(true);
  };

  const handleAddToSegmentSubmit = async (segmentIds: string[]) => {
    // Call the parent handler
    onAddToSegment(profile.id);
    setIsAddToSegmentModalOpen(false);
  };

  const handleSendMessageSubmit = async (data: any) => {
    // Call the parent handler
    onSendMessage(profile.id);
    setIsSendMessageModalOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={profile.avatar_url} />
              <AvatarFallback>
                {getInitials(profile.first_name, profile.last_name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-bold">
                {profile.first_name} {profile.last_name}
              </h2>
              <p className="text-sm text-muted-foreground">{profile.email}</p>
            </div>
          </DialogTitle>
          <DialogDescription>
            {t('cdp:profiles.detailModal.description')}
          </DialogDescription>
        </DialogHeader>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 border-b pb-4">
          <Button size="sm" onClick={() => onEdit(profile)}>
            <Edit className="h-4 w-4 mr-2" />
            {t('cdp:profiles.editProfile')}
          </Button>
          <Button size="sm" variant="outline" onClick={handleSendMessage}>
            <MessageSquare className="h-4 w-4 mr-2" />
            {t('cdp:profiles.sendMessage')}
          </Button>
          <Button size="sm" variant="outline" onClick={handleAddToSegment}>
            <Target className="h-4 w-4 mr-2" />
            {t('cdp:profiles.addToSegment')}
          </Button>
          <Button size="sm" variant="destructive" onClick={() => onDelete(profile.id)}>
            <Trash2 className="h-4 w-4 mr-2" />
            {t('common:delete')}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">{t('cdp:profiles.tabs.overview')}</TabsTrigger>
            <TabsTrigger value="activity">{t('cdp:profiles.tabs.activity')}</TabsTrigger>
            <TabsTrigger value="orders">{t('cdp:profiles.tabs.orders')}</TabsTrigger>
            <TabsTrigger value="insights">{t('cdp:profiles.tabs.insights')}</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Basic Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    {t('cdp:profiles.basicInfo')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{profile.email}</span>
                  </div>
                  {profile.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{profile.phone}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {t('cdp:profiles.joinedOn')} {formatDate(profile.created_at)}
                    </span>
                  </div>
                  {profile.last_active_at && (
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {t('cdp:profiles.lastActive')} {formatDate(profile.last_active_at)}
                      </span>
                    </div>
                  )}
                  {profile.metadata?.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{profile.metadata.location}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Customer Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    {t('cdp:profiles.customerMetrics')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:profiles.totalSpent')}</span>
                    <span className="text-lg font-bold text-green-600">
                      {formatCurrency(profile.total_spent)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:profiles.totalOrders')}</span>
                    <span className="text-lg font-bold">{profile.total_orders}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:profiles.avgOrderValue')}</span>
                    <span className="text-lg font-bold">
                      {formatCurrency(profile.avg_order_value)}
                    </span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('cdp:profiles.engagementScore')}</span>
                      <span className="text-sm font-bold">
                        {(profile.engagement_score * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={profile.engagement_score * 100} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Tags and Segments */}
            <Card>
              <CardHeader>
                <CardTitle>{t('cdp:profiles.tagsAndSegments')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium mb-2 block">
                      {t('cdp:profiles.valueTier')}
                    </span>
                    <Badge className={`${getValueTierColor(profile.value_tier)} border`}>
                      {t(`cdp:profiles.valueTiers.${profile.value_tier}`)}
                    </Badge>
                  </div>
                  <div>
                    <span className="text-sm font-medium mb-2 block">
                      {t('cdp:profiles.churnRisk')}
                    </span>
                    <Badge className={`${getChurnRiskColor(profile.churn_risk_score)} border`}>
                      {getChurnRiskLevel(profile.churn_risk_score)}
                    </Badge>
                  </div>
                  {profile.tags.length > 0 && (
                    <div>
                      <span className="text-sm font-medium mb-2 block">
                        {t('cdp:profiles.tags')}
                      </span>
                      <div className="flex flex-wrap gap-1">
                        {profile.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('cdp:profiles.recentActivity')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>{t('cdp:profiles.noActivityData')}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="orders" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('cdp:profiles.orderHistory')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>{t('cdp:profiles.noOrderData')}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="insights" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('cdp:profiles.aiInsights')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>{t('cdp:profiles.noInsightsData')}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>

      {/* Nested Modals */}
      <AddToSegmentModal
        isOpen={isAddToSegmentModalOpen}
        onClose={() => setIsAddToSegmentModalOpen(false)}
        onSubmit={handleAddToSegmentSubmit}
        customerName={`${profile.first_name} ${profile.last_name}`}
        customerId={profile.id}
        availableSegments={[]} // TODO: Pass real segments data
      />

      <SendMessageModal
        isOpen={isSendMessageModalOpen}
        onClose={() => setIsSendMessageModalOpen(false)}
        onSubmit={handleSendMessageSubmit}
        customerName={`${profile.first_name} ${profile.last_name}`}
        customerEmail={profile.email}
        customerPhone={profile.phone}
      />
    </Dialog>
  );
}
