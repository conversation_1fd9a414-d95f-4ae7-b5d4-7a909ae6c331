'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import {
  MessageSquare,
  Mail,
  Phone,
  Send,
  Loader2,
  User,
} from 'lucide-react';

const sendMessageSchema = z.object({
  type: z.enum(['email', 'sms', 'notification']),
  subject: z.string().min(1, 'Subject is required').optional(),
  message: z.string().min(1, 'Message is required'),
  template: z.string().optional(),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  sendNow: z.boolean().default(true),
  scheduledAt: z.string().optional(),
});

type SendMessageFormData = z.infer<typeof sendMessageSchema>;

interface SendMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: SendMessageFormData) => Promise<void>;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
}

export function SendMessageModal({
  isOpen,
  onClose,
  onSubmit,
  customerName = '',
  customerEmail = '',
  customerPhone = '',
}: SendMessageModalProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<SendMessageFormData>({
    resolver: zodResolver(sendMessageSchema),
    defaultValues: {
      type: 'email',
      subject: '',
      message: '',
      template: 'custom',
      priority: 'normal',
      sendNow: true,
      scheduledAt: '',
    },
  });

  const messageType = form.watch('type');
  const sendNow = form.watch('sendNow');

  const handleSubmit = async (data: SendMessageFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      form.reset();
      onClose();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const messageTemplates = {
    email: [
      { value: 'welcome', label: t('cdp:messages.templates.welcome') },
      { value: 'promotion', label: t('cdp:messages.templates.promotion') },
      { value: 'followup', label: t('cdp:messages.templates.followup') },
      { value: 'survey', label: t('cdp:messages.templates.survey') },
    ],
    sms: [
      { value: 'reminder', label: t('cdp:messages.templates.reminder') },
      { value: 'confirmation', label: t('cdp:messages.templates.confirmation') },
      { value: 'alert', label: t('cdp:messages.templates.alert') },
    ],
    notification: [
      { value: 'update', label: t('cdp:messages.templates.update') },
      { value: 'announcement', label: t('cdp:messages.templates.announcement') },
    ],
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {t('cdp:profiles.sendMessage.title')}
          </DialogTitle>
          <DialogDescription>
            {t('cdp:profiles.sendMessage.description', { name: customerName })}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Customer Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {t('cdp:messages.customerInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{customerName}</span>
                </div>
                {customerEmail && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{customerEmail}</span>
                  </div>
                )}
                {customerPhone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{customerPhone}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Message Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('cdp:messages.messageConfig')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Message Type */}
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:messages.messageType')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('cdp:messages.selectMessageType')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="email">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              {t('cdp:messages.types.email')}
                            </div>
                          </SelectItem>
                          <SelectItem value="sms">
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              {t('cdp:messages.types.sms')}
                            </div>
                          </SelectItem>
                          <SelectItem value="notification">
                            <div className="flex items-center gap-2">
                              <MessageSquare className="h-4 w-4" />
                              {t('cdp:messages.types.notification')}
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Template Selection */}
                <FormField
                  control={form.control}
                  name="template"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:messages.template')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('cdp:messages.selectTemplate')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="custom">{t('cdp:messages.customMessage')}</SelectItem>
                          {messageTemplates[messageType]?.map((template) => (
                            <SelectItem key={template.value} value={template.value}>
                              {template.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {t('cdp:messages.templateDescription')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Subject (for email) */}
                {messageType === 'email' && (
                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('cdp:messages.subject')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('cdp:messages.subjectPlaceholder')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Message Content */}
                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:messages.messageContent')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('cdp:messages.messagePlaceholder')}
                          className="min-h-[120px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {messageType === 'sms'
                          ? t('cdp:messages.smsLimit')
                          : t('cdp:messages.messageDescription')
                        }
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Priority */}
                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:messages.priority')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">{t('cdp:messages.priorities.low')}</SelectItem>
                          <SelectItem value="normal">{t('cdp:messages.priorities.normal')}</SelectItem>
                          <SelectItem value="high">{t('cdp:messages.priorities.high')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                {t('common:cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Send className="mr-2 h-4 w-4" />
                {t('cdp:profiles.sendMessage.sendNow')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
