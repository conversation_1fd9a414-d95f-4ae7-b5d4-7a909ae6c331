'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Users,
  TrendingUp,
  BarChart3,
  Target,
  RefreshCw,
  Plus,
  Eye,
  Filter,
  Download,
  Settings,
  Activity,
  Zap,
  Globe,
  Brain,
  Layers,
  Sparkles,
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  CheckCircle2,
  AlertCircle,
  TrendingDown,
  GitBranch,
  Plug,
  Mail,
  ShoppingCart,
  Share2,
  Megaphone
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Progress } from '@kit/ui/progress';

import { RealTimeDashboard } from './real-time-dashboard';
import { AdvancedAnalyticsCharts } from './advanced-analytics-charts';
import { InteractiveChart } from './charts/interactive-chart';

interface ModernCDPDashboardProps {
  accountId: string;
}

export function ModernCDPDashboard({ accountId }: ModernCDPDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration
  const stats = {
    totalCustomers: 12847,
    totalRevenue: **********,
    avgEngagement: 0.742,
    churnRisk: 156,
    growthRate: 12.5,
    activeSegments: 24,
    aiInsights: 8,
    integrations: 6
  };

  const quickActions = [
    {
      title: 'Customer Profiles',
      description: 'Unified customer data and insights',
      icon: <Users className="h-6 w-6" />,
      href: `/home/<USER>/cdp/profiles`,
      color: 'bg-blue-500',
      stats: '12.8K profiles'
    },
    {
      title: 'Smart Segments',
      description: 'AI-powered customer segmentation',
      icon: <Target className="h-6 w-6" />,
      href: `/home/<USER>/cdp/segments`,
      color: 'bg-green-500',
      stats: '24 segments'
    },
    {
      title: 'Journey Orchestration',
      description: 'Automated customer journeys',
      icon: <GitBranch className="h-6 w-6" />,
      href: `/home/<USER>/cdp/journeys`,
      color: 'bg-purple-500',
      stats: '15 active'
    },
    {
      title: 'Real-time Analytics',
      description: 'Live customer behavior insights',
      icon: <BarChart3 className="h-6 w-6" />,
      href: `/home/<USER>/cdp/analytics`,
      color: 'bg-orange-500',
      stats: '98% uptime'
    },
    {
      title: 'AI/ML Engine',
      description: 'Predictive analytics and recommendations',
      icon: <Brain className="h-6 w-6" />,
      href: `/home/<USER>/cdp/ai`,
      color: 'bg-pink-500',
      stats: '8 insights'
    },
    {
      title: 'Advanced Analytics',
      description: 'Journey mapping and attribution',
      icon: <Layers className="h-6 w-6" />,
      href: `/home/<USER>/cdp/advanced-analytics`,
      color: 'bg-indigo-500',
      stats: 'Enterprise'
    },
    {
      title: 'Integration Hub',
      description: 'Connect third-party platforms',
      icon: <Plug className="h-6 w-6" />,
      href: `/home/<USER>/cdp/integrations`,
      color: 'bg-teal-500',
      stats: '6 connected'
    }
  ];

  const recentActivity = [
    { type: 'profile', message: 'New customer profile created', time: '2 minutes ago', icon: <Users className="h-4 w-4" /> },
    { type: 'segment', message: 'High-value segment updated', time: '5 minutes ago', icon: <Target className="h-4 w-4" /> },
    { type: 'ai', message: 'Churn prediction model retrained', time: '15 minutes ago', icon: <Brain className="h-4 w-4" /> },
    { type: 'integration', message: 'SendGrid sync completed', time: '1 hour ago', icon: <Mail className="h-4 w-4" /> }
  ];

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Customer Data Platform</h1>
              <p className="text-blue-100 text-lg">
                Unified customer intelligence powered by AI
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="secondary" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button variant="secondary" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8">
            <div className="text-center">
              <div className="text-2xl font-bold">{stats.totalCustomers.toLocaleString()}</div>
              <div className="text-blue-100 text-sm">Total Customers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                  notation: 'compact'
                }).format(stats.totalRevenue)}
              </div>
              <div className="text-blue-100 text-sm">Total Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{(stats.avgEngagement * 100).toFixed(1)}%</div>
              <div className="text-blue-100 text-sm">Avg Engagement</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">+{stats.growthRate}%</div>
              <div className="text-blue-100 text-sm">Growth Rate</div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <Sparkles className="h-32 w-32" />
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Performance Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Growth</CardTitle>
            <div className="p-2 bg-blue-500 rounded-lg">
              <TrendingUp className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
              +{stats.growthRate}%
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <ArrowUpRight className="h-3 w-3 text-green-500" />
              <span>vs last month</span>
            </div>
            <Progress value={stats.growthRate * 4} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Engagement Score</CardTitle>
            <div className="p-2 bg-green-500 rounded-lg">
              <Activity className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 dark:text-green-300">
              {(stats.avgEngagement * 100).toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <ArrowUpRight className="h-3 w-3 text-green-500" />
              <span>+2.3% this week</span>
            </div>
            <Progress value={stats.avgEngagement * 100} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Insights</CardTitle>
            <div className="p-2 bg-orange-500 rounded-lg">
              <Brain className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">
              {stats.aiInsights}
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <Zap className="h-3 w-3 text-orange-500" />
              <span>actionable insights</span>
            </div>
            <Progress value={stats.aiInsights * 12.5} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Integrations</CardTitle>
            <div className="p-2 bg-purple-500 rounded-lg">
              <Plug className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
              {stats.integrations}
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <CheckCircle2 className="h-3 w-3 text-green-500" />
              <span>all connected</span>
            </div>
            <Progress value={100} className="mt-3" />
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions Grid */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">Quick Actions</h2>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View All
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {quickActions.map((action, index) => (
            <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 rounded-xl ${action.color} text-white group-hover:scale-110 transition-transform duration-300`}>
                    {action.icon}
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {action.stats}
                  </Badge>
                </div>

                <h3 className="font-semibold text-lg mb-2 group-hover:text-blue-600 transition-colors">
                  {action.title}
                </h3>
                <p className="text-muted-foreground text-sm mb-4">
                  {action.description}
                </p>

                <Button
                  asChild
                  variant="ghost"
                  size="sm"
                  className="w-full group-hover:bg-blue-50 group-hover:text-blue-600"
                >
                  <a href={action.href}>
                    Explore
                    <ArrowUpRight className="h-4 w-4 ml-2" />
                  </a>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Activity & System Status */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest updates across your CDP
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    {activity.icon}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.message}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Health
            </CardTitle>
            <CardDescription>
              Real-time system performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Data Processing</span>
                <div className="flex items-center gap-2">
                  <Progress value={98} className="w-20" />
                  <span className="text-sm text-green-600">98%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">API Response</span>
                <div className="flex items-center gap-2">
                  <Progress value={95} className="w-20" />
                  <span className="text-sm text-green-600">95ms</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Integration Sync</span>
                <div className="flex items-center gap-2">
                  <Progress value={100} className="w-20" />
                  <span className="text-sm text-green-600">100%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">AI Models</span>
                <div className="flex items-center gap-2">
                  <Progress value={92} className="w-20" />
                  <span className="text-sm text-green-600">Active</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
