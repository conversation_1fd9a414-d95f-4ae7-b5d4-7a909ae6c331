'use client';

import { useEffect, useState } from 'react';

import {
  Activity,
  AlertCircle,
  ArrowUpRight,
  BarChart3,
  Brain,
  CheckCircle2,
  Clock,
  Download,
  Eye,
  GitBranch,
  Layers,
  Mail,
  Plug,
  Settings,
  Sparkles,
  Target,
  TrendingUp,
  Users,
  Zap,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Alert, AlertDescription } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Progress } from '@kit/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { AdvancedAnalyticsCharts } from './advanced-analytics-charts';
import { AIInsightsDashboard } from './ai-insights-dashboard';
import { RealTimeDashboard } from './real-time-dashboard';

import type { CDPDashboardData } from '../_lib/server/load-cdp-dashboard';

interface ModernCDPDashboardProps {
  accountId: string;
  accountSlug: string;
  dashboardData: CDPDashboardData;
  activeTab: string;
  user: any;
  account: any;
}

export function ModernCDPDashboard({
  accountId,
  accountSlug,
  dashboardData,
  activeTab,
  user,
  account
}: ModernCDPDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use server-side data directly
  const stats = {
    totalCustomers: dashboardData.totalCustomers,
    totalRevenue: dashboardData.monthlyRevenue,
    avgEngagement: dashboardData.engagementScore,
    churnRisk: Math.round(dashboardData.totalCustomers * dashboardData.churnRate / 100),
    growthRate: dashboardData.growthRate,
    activeSegments: dashboardData.activeSegments,
    aiInsights: dashboardData.aiInsights,
    integrations: dashboardData.integrations,
  };

  const quickActions = [
    {
      title: t('cdp:profiles.title'),
      description: t('cdp:profiles.description'),
      icon: <Users className="h-6 w-6" />,
      href: `/home/<USER>/cdp/profiles`,
      color: 'bg-blue-500',
      stats: `${(stats.totalCustomers / 1000).toFixed(1)}K profiles`,
    },
    {
      title: t('cdp:segments.title'),
      description: t('cdp:segments.description'),
      icon: <Target className="h-6 w-6" />,
      href: `/home/<USER>/cdp/segments`,
      color: 'bg-green-500',
      stats: `${stats.activeSegments} segments`,
    },
    {
      title: t('cdp:journeys.title', 'Journey Orchestration'),
      description: t('cdp:journeys.description', 'Automated customer journeys'),
      icon: <GitBranch className="h-6 w-6" />,
      href: `/home/<USER>/cdp/journeys`,
      color: 'bg-purple-500',
      stats: t('cdp:journeys.stats', '15 active'),
    },
    {
      title: t('cdp:analytics.title'),
      description: t('cdp:analytics.description'),
      icon: <BarChart3 className="h-6 w-6" />,
      href: `/home/<USER>/cdp/analytics`,
      color: 'bg-orange-500',
      stats: '98% uptime',
    },
    {
      title: t('cdp:aiInsights.title'),
      description: t('cdp:aiInsights.description'),
      icon: <Brain className="h-6 w-6" />,
      href: `/home/<USER>/cdp/ai`,
      color: 'bg-pink-500',
      stats: `${stats.aiInsights} insights`,
    },
    {
      title: t('cdp:advancedAnalytics.title', 'Advanced Analytics'),
      description: t('cdp:advancedAnalytics.description', 'Journey mapping and attribution'),
      icon: <Layers className="h-6 w-6" />,
      href: `/home/<USER>/cdp/advanced-analytics`,
      color: 'bg-indigo-500',
      stats: t('cdp:advancedAnalytics.stats', 'Enterprise'),
    },
    {
      title: t('cdp:integrations.title'),
      description: t('cdp:integrations.description'),
      icon: <Plug className="h-6 w-6" />,
      href: `/home/<USER>/cdp/integrations`,
      color: 'bg-teal-500',
      stats: `${stats.integrations} connected`,
    },
  ];

  const recentActivity = [
    {
      type: 'profile',
      message: t('cdp:recentActivity.newCustomer'),
      time: '2 minutes ago',
      icon: <Users className="h-4 w-4" />,
    },
    {
      type: 'segment',
      message: t('cdp:recentActivity.segmentUpdated'),
      time: '5 minutes ago',
      icon: <Target className="h-4 w-4" />,
    },
    {
      type: 'ai',
      message: t('cdp:recentActivity.modelRetrained', 'Churn prediction model retrained'),
      time: t('cdp:recentActivity.time15min', '15 minutes ago'),
      icon: <Brain className="h-4 w-4" />,
    },
    {
      type: 'integration',
      message: t('cdp:recentActivity.integrationConnected'),
      time: '1 hour ago',
      icon: <Mail className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="mb-2 text-3xl font-bold">{t('cdp:title')}</h1>
              <p className="text-lg text-blue-100">
                {t('cdp:dashboard.subtitle')}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="secondary" size="sm" disabled={loading}>
                <Download className="mr-2 h-4 w-4" />
                {t('cdp:quickActions.exportData')}
              </Button>
              <Button variant="secondary" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                {t('common:settings')}
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="mt-8 grid grid-cols-2 gap-6 md:grid-cols-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {loading ? '...' : stats.totalCustomers.toLocaleString()}
              </div>
              <div className="text-sm text-blue-100">
                {t('cdp:metrics.totalCustomers')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                  notation: 'compact',
                }).format(stats.totalRevenue)}
              </div>
              <div className="text-sm text-blue-100">
                {t('cdp:metrics.monthlyRevenue')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {(stats.avgEngagement * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-blue-100">
                {t('cdp:metrics.engagementScore')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                +{stats.growthRate}%
              </div>
              <div className="text-sm text-blue-100">{t('cdp:metrics.growthRate', 'Growth Rate')}</div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <Sparkles className="h-32 w-32" />
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Performance Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg dark:from-blue-950 dark:to-blue-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('cdp:cards.customerGrowth')}
            </CardTitle>
            <div className="rounded-lg bg-blue-500 p-2">
              <TrendingUp className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
              +{stats.growthRate}%
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <ArrowUpRight className="h-3 w-3 text-green-500" />
              <span>vs last month</span>
            </div>
            <Progress value={stats.growthRate * 4} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-green-50 to-green-100 shadow-lg dark:from-green-950 dark:to-green-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('cdp:metrics.engagementScore')}
            </CardTitle>
            <div className="rounded-lg bg-green-500 p-2">
              <Activity className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 dark:text-green-300">
              {(stats.avgEngagement * 100).toFixed(1)}%
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <ArrowUpRight className="h-3 w-3 text-green-500" />
              <span>+2.3% this week</span>
            </div>
            <Progress value={stats.avgEngagement * 100} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg dark:from-orange-950 dark:to-orange-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('cdp:aiInsights.title')}
            </CardTitle>
            <div className="rounded-lg bg-orange-500 p-2">
              <Brain className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">
              {stats.aiInsights}
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <Zap className="h-3 w-3 text-orange-500" />
              <span>{t('cdp:aiInsights.activeInsights')}</span>
            </div>
            <Progress value={stats.aiInsights * 12.5} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg dark:from-purple-950 dark:to-purple-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('cdp:integrations.title')}
            </CardTitle>
            <div className="rounded-lg bg-purple-500 p-2">
              <Plug className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
              {stats.integrations}
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <CheckCircle2 className="h-3 w-3 text-green-500" />
              <span>{t('cdp:integrations.connected')}</span>
            </div>
            <Progress value={100} className="mt-3" />
          </CardContent>
        </Card>
      </div>

      {/* Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{t('cdp:tabs.overview')}</TabsTrigger>
          <TabsTrigger value="realtime">{t('cdp:tabs.realtime')}</TabsTrigger>
          <TabsTrigger value="analytics">{t('cdp:tabs.analytics')}</TabsTrigger>
          <TabsTrigger value="insights">{t('cdp:tabs.insights')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Quick Actions Grid */}
          <div>
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-2xl font-bold">
                {t('cdp:quickActions.title')}
              </h2>
              <Button variant="outline" size="sm">
                <Eye className="mr-2 h-4 w-4" />
                {t('cdp:quickActions.viewAll')}
              </Button>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {quickActions.map((action, index) => (
                <Card
                  key={index}
                  className="group cursor-pointer border-0 shadow-lg transition-all duration-300 hover:shadow-xl"
                >
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-start justify-between">
                      <div
                        className={`rounded-xl p-3 ${action.color} text-white transition-transform duration-300 group-hover:scale-110`}
                      >
                        {action.icon}
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {action.stats}
                      </Badge>
                    </div>

                    <h3 className="mb-2 text-lg font-semibold transition-colors group-hover:text-blue-600">
                      {action.title}
                    </h3>
                    <p className="text-muted-foreground mb-4 text-sm">
                      {action.description}
                    </p>

                    <Button
                      asChild
                      variant="ghost"
                      size="sm"
                      className="w-full group-hover:bg-blue-50 group-hover:text-blue-600"
                    >
                      <a href={action.href}>
                        Explore
                        <ArrowUpRight className="ml-2 h-4 w-4" />
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Recent Activity & System Status */}
          <div className="grid gap-6 lg:grid-cols-2">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  {t('cdp:recentActivity.title')}
                </CardTitle>
                <CardDescription>
                  Latest updates across your CDP
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div
                      key={index}
                      className="bg-muted/50 flex items-center gap-3 rounded-lg p-3"
                    >
                      <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900">
                        {activity.icon}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {activity.message}
                        </p>
                        <p className="text-muted-foreground text-xs">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  {t('cdp:systemHealth.title')}
                </CardTitle>
                <CardDescription>{t('cdp:systemHealth.description', 'Real-time system performance')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:systemHealth.dataProcessing')}</span>
                    <div className="flex items-center gap-2">
                      <Progress value={98} className="w-20" />
                      <span className="text-sm text-green-600">98%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:systemHealth.apiPerformance')}</span>
                    <div className="flex items-center gap-2">
                      <Progress value={95} className="w-20" />
                      <span className="text-sm text-green-600">95ms</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {t('cdp:systemHealth.integrationStatus')}
                    </span>
                    <div className="flex items-center gap-2">
                      <Progress value={100} className="w-20" />
                      <span className="text-sm text-green-600">100%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:systemHealth.mlModels')}</span>
                    <div className="flex items-center gap-2">
                      <Progress value={92} className="w-20" />
                      <span className="text-sm text-green-600">{t('cdp:systemHealth.active', 'Active')}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-6">
          <RealTimeDashboard accountId={accountId} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AdvancedAnalyticsCharts accountId={accountId} />
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <AIInsightsDashboard accountId={accountId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
