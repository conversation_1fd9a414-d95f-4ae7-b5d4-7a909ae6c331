'use client';

import { useState } from 'react';

import {
  Activity,
  AlertCircle,
  AlertTriangle,
  ArrowUpRight,
  BarChart3,
  Brain,
  CheckCircle2,
  Clock,
  Download,
  Eye,
  GitBranch,
  Layers,
  Mail,
  Plug,
  Settings,
  Sparkles,
  Target,
  TrendingUp,
  Users,
  Zap,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Alert, AlertDescription } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Progress } from '@kit/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { AdvancedAnalyticsCharts } from './advanced-analytics-charts';
import { RealTimeDashboard } from './real-time-dashboard';
import { AIInsightsDashboard } from './ai-insights-dashboard';

interface ModernCDPDashboardProps {
  accountId: string;
}

export function ModernCDPDashboard({ accountId }: ModernCDPDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration
  const stats = {
    totalCustomers: 12847,
    totalRevenue: **********,
    avgEngagement: 0.742,
    churnRisk: 156,
    growthRate: 12.5,
    activeSegments: 24,
    aiInsights: 8,
    integrations: 6,
  };

  const quickActions = [
    {
      title: 'Customer Profiles',
      description: 'Unified customer data and insights',
      icon: <Users className="h-6 w-6" />,
      href: `/home/<USER>/cdp/profiles`,
      color: 'bg-blue-500',
      stats: '12.8K profiles',
    },
    {
      title: 'Smart Segments',
      description: 'AI-powered customer segmentation',
      icon: <Target className="h-6 w-6" />,
      href: `/home/<USER>/cdp/segments`,
      color: 'bg-green-500',
      stats: '24 segments',
    },
    {
      title: 'Journey Orchestration',
      description: 'Automated customer journeys',
      icon: <GitBranch className="h-6 w-6" />,
      href: `/home/<USER>/cdp/journeys`,
      color: 'bg-purple-500',
      stats: '15 active',
    },
    {
      title: 'Real-time Analytics',
      description: 'Live customer behavior insights',
      icon: <BarChart3 className="h-6 w-6" />,
      href: `/home/<USER>/cdp/analytics`,
      color: 'bg-orange-500',
      stats: '98% uptime',
    },
    {
      title: 'AI/ML Engine',
      description: 'Predictive analytics and recommendations',
      icon: <Brain className="h-6 w-6" />,
      href: `/home/<USER>/cdp/ai`,
      color: 'bg-pink-500',
      stats: '8 insights',
    },
    {
      title: 'Advanced Analytics',
      description: 'Journey mapping and attribution',
      icon: <Layers className="h-6 w-6" />,
      href: `/home/<USER>/cdp/advanced-analytics`,
      color: 'bg-indigo-500',
      stats: 'Enterprise',
    },
    {
      title: 'Integration Hub',
      description: 'Connect third-party platforms',
      icon: <Plug className="h-6 w-6" />,
      href: `/home/<USER>/cdp/integrations`,
      color: 'bg-teal-500',
      stats: '6 connected',
    },
  ];

  const recentActivity = [
    {
      type: 'profile',
      message: 'New customer profile created',
      time: '2 minutes ago',
      icon: <Users className="h-4 w-4" />,
    },
    {
      type: 'segment',
      message: 'High-value segment updated',
      time: '5 minutes ago',
      icon: <Target className="h-4 w-4" />,
    },
    {
      type: 'ai',
      message: 'Churn prediction model retrained',
      time: '15 minutes ago',
      icon: <Brain className="h-4 w-4" />,
    },
    {
      type: 'integration',
      message: 'SendGrid sync completed',
      time: '1 hour ago',
      icon: <Mail className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="mb-2 text-3xl font-bold">
                Customer Data Platform
              </h1>
              <p className="text-lg text-blue-100">
                Unified customer intelligence powered by AI
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="secondary" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Export Data
              </Button>
              <Button variant="secondary" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="mt-8 grid grid-cols-2 gap-6 md:grid-cols-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {stats.totalCustomers.toLocaleString()}
              </div>
              <div className="text-sm text-blue-100">Total Customers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                  notation: 'compact',
                }).format(stats.totalRevenue)}
              </div>
              <div className="text-sm text-blue-100">Total Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {(stats.avgEngagement * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-blue-100">Avg Engagement</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">+{stats.growthRate}%</div>
              <div className="text-sm text-blue-100">Growth Rate</div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <Sparkles className="h-32 w-32" />
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Performance Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg dark:from-blue-950 dark:to-blue-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Customer Growth
            </CardTitle>
            <div className="rounded-lg bg-blue-500 p-2">
              <TrendingUp className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
              +{stats.growthRate}%
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <ArrowUpRight className="h-3 w-3 text-green-500" />
              <span>vs last month</span>
            </div>
            <Progress value={stats.growthRate * 4} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-green-50 to-green-100 shadow-lg dark:from-green-950 dark:to-green-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Engagement Score
            </CardTitle>
            <div className="rounded-lg bg-green-500 p-2">
              <Activity className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 dark:text-green-300">
              {(stats.avgEngagement * 100).toFixed(1)}%
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <ArrowUpRight className="h-3 w-3 text-green-500" />
              <span>+2.3% this week</span>
            </div>
            <Progress value={stats.avgEngagement * 100} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg dark:from-orange-950 dark:to-orange-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Insights</CardTitle>
            <div className="rounded-lg bg-orange-500 p-2">
              <Brain className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">
              {stats.aiInsights}
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <Zap className="h-3 w-3 text-orange-500" />
              <span>actionable insights</span>
            </div>
            <Progress value={stats.aiInsights * 12.5} className="mt-3" />
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg dark:from-purple-950 dark:to-purple-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Integrations</CardTitle>
            <div className="rounded-lg bg-purple-500 p-2">
              <Plug className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
              {stats.integrations}
            </div>
            <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
              <CheckCircle2 className="h-3 w-3 text-green-500" />
              <span>all connected</span>
            </div>
            <Progress value={100} className="mt-3" />
          </CardContent>
        </Card>
      </div>

      {/* Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
          <TabsTrigger value="analytics">Interactive Analytics</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Quick Actions Grid */}
          <div>
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-2xl font-bold">Quick Actions</h2>
              <Button variant="outline" size="sm">
                <Eye className="mr-2 h-4 w-4" />
                View All
              </Button>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {quickActions.map((action, index) => (
                <Card
                  key={index}
                  className="group cursor-pointer border-0 shadow-lg transition-all duration-300 hover:shadow-xl"
                >
                  <CardContent className="p-6">
                    <div className="mb-4 flex items-start justify-between">
                      <div
                        className={`rounded-xl p-3 ${action.color} text-white transition-transform duration-300 group-hover:scale-110`}
                      >
                        {action.icon}
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {action.stats}
                      </Badge>
                    </div>

                    <h3 className="mb-2 text-lg font-semibold transition-colors group-hover:text-blue-600">
                      {action.title}
                    </h3>
                    <p className="text-muted-foreground mb-4 text-sm">
                      {action.description}
                    </p>

                    <Button
                      asChild
                      variant="ghost"
                      size="sm"
                      className="w-full group-hover:bg-blue-50 group-hover:text-blue-600"
                    >
                      <a href={action.href}>
                        Explore
                        <ArrowUpRight className="ml-2 h-4 w-4" />
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Recent Activity & System Status */}
          <div className="grid gap-6 lg:grid-cols-2">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Latest updates across your CDP
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div
                      key={index}
                      className="bg-muted/50 flex items-center gap-3 rounded-lg p-3"
                    >
                      <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900">
                        {activity.icon}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {activity.message}
                        </p>
                        <p className="text-muted-foreground text-xs">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  System Health
                </CardTitle>
                <CardDescription>Real-time system performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Data Processing</span>
                    <div className="flex items-center gap-2">
                      <Progress value={98} className="w-20" />
                      <span className="text-sm text-green-600">98%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">API Response</span>
                    <div className="flex items-center gap-2">
                      <Progress value={95} className="w-20" />
                      <span className="text-sm text-green-600">95ms</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Integration Sync
                    </span>
                    <div className="flex items-center gap-2">
                      <Progress value={100} className="w-20" />
                      <span className="text-sm text-green-600">100%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">AI Models</span>
                    <div className="flex items-center gap-2">
                      <Progress value={92} className="w-20" />
                      <span className="text-sm text-green-600">Active</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-6">
          <RealTimeDashboard accountId={accountId} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AdvancedAnalyticsCharts accountId={accountId} />
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI-Powered Insights
              </CardTitle>
              <CardDescription>
                Machine learning insights and recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Sample AI Insights */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                    <div className="flex items-start gap-3">
                      <div className="rounded-lg bg-blue-500 p-2">
                        <TrendingUp className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-900">
                          Revenue Opportunity
                        </h4>
                        <p className="mt-1 text-sm text-blue-700">
                          Customers who view product pages 3+ times have 45%
                          higher conversion rate. Target these users with
                          personalized offers.
                        </p>
                        <Badge variant="secondary" className="mt-2">
                          +$125K potential revenue
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                    <div className="flex items-start gap-3">
                      <div className="rounded-lg bg-green-500 p-2">
                        <Users className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-medium text-green-900">
                          Churn Prevention
                        </h4>
                        <p className="mt-1 text-sm text-green-700">
                          156 customers at high churn risk. Send re-engagement
                          campaign within 48 hours to reduce churn by 23%.
                        </p>
                        <Badge variant="secondary" className="mt-2">
                          High Priority
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg border border-purple-200 bg-purple-50 p-4">
                    <div className="flex items-start gap-3">
                      <div className="rounded-lg bg-purple-500 p-2">
                        <Target className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-medium text-purple-900">
                          Segment Optimization
                        </h4>
                        <p className="mt-1 text-sm text-purple-700">
                          Create new segment for mobile-first users aged 25-34.
                          This segment shows 67% higher engagement rates.
                        </p>
                        <Badge variant="secondary" className="mt-2">
                          Recommended Action
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg border border-orange-200 bg-orange-50 p-4">
                    <div className="flex items-start gap-3">
                      <div className="rounded-lg bg-orange-500 p-2">
                        <Zap className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-medium text-orange-900">
                          Performance Alert
                        </h4>
                        <p className="mt-1 text-sm text-orange-700">
                          Email campaign open rates dropped 15% this week.
                          Consider A/B testing subject lines and send times.
                        </p>
                        <Badge variant="secondary" className="mt-2">
                          Action Required
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* AI Recommendations */}
                <div className="border-t pt-6">
                  <h3 className="mb-4 text-lg font-semibold">
                    Today's Recommendations
                  </h3>
                  <div className="space-y-3">
                    <div className="bg-muted/50 flex items-center gap-3 rounded-lg p-3">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">
                          Launch retargeting campaign for cart abandoners
                        </p>
                        <p className="text-muted-foreground text-sm">
                          Expected 12% conversion rate, $45K revenue
                        </p>
                      </div>
                    </div>

                    <div className="bg-muted/50 flex items-center gap-3 rounded-lg p-3">
                      <Clock className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="font-medium">
                          Optimize email send times for European customers
                        </p>
                        <p className="text-muted-foreground text-sm">
                          Potential 8% improvement in open rates
                        </p>
                      </div>
                    </div>

                    <div className="bg-muted/50 flex items-center gap-3 rounded-lg p-3">
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium">
                          Review pricing strategy for premium segment
                        </p>
                        <p className="text-muted-foreground text-sm">
                          Price sensitivity analysis suggests 5% increase
                          opportunity
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
