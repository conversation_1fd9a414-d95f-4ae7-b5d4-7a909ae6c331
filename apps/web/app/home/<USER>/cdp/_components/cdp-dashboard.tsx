'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Badge } from '@kit/ui/badge';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Loader2, Users, TrendingUp, Target, Activity, GitBranch } from 'lucide-react';

interface CustomerProfile {
  id: string;
  primary_email?: string;
  primary_phone?: string;
  first_name?: string;
  last_name?: string;
  lifecycle_stage: string;
  customer_value_tier: string;
  behavior: {
    total_sessions: number;
    total_pageviews: number;
    total_purchases: number;
    total_revenue: number;
  };
  engagement_scores: {
    overall_engagement_score: number;
  };
  predictive_scores: {
    churn_risk_score: number;
    lifetime_value_score: number;
  };
  created_at: string;
  updated_at: string;
}

interface CDPDashboardProps {
  accountId: string;
}

export function CDPDashboard({ accountId }: CDPDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [profiles, setProfiles] = useState<CustomerProfile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newProfile, setNewProfile] = useState({
    primary_email: '',
    first_name: '',
    last_name: '',
    primary_phone: '',
  });

  // Load profiles
  const loadProfiles = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/cdp/profiles');
      const data = await response.json();

      if (data.success) {
        setProfiles(data.data || []);
      } else {
        setError(data.error || t('cdp:errors.networkError'));
      }
    } catch (err) {
      setError(t('cdp:errors.networkError'));
    } finally {
      setLoading(false);
    }
  };

  // Create new profile
  const createProfile = async () => {
    if (!newProfile.primary_email) {
      setError(t('cdp:errors.emailRequired'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/cdp/profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newProfile),
      });

      const data = await response.json();

      if (data.success) {
        setNewProfile({
          primary_email: '',
          first_name: '',
          last_name: '',
          primary_phone: '',
        });
        await loadProfiles(); // Reload profiles
      } else {
        setError(data.error || t('cdp:errors.validationError'));
      }
    } catch (err) {
      setError(t('cdp:errors.networkError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProfiles();
  }, []);

  // Calculate dashboard stats
  const stats = {
    totalProfiles: profiles.length,
    totalRevenue: profiles.reduce((sum, p) => sum + (p.behavior?.total_revenue || 0), 0),
    avgEngagement: profiles.length > 0
      ? profiles.reduce((sum, p) => sum + (p.engagement_scores?.overall_engagement_score || 0), 0) / profiles.length
      : 0,
    highRiskChurn: profiles.filter(p => (p.predictive_scores?.churn_risk_score || 0) > 0.7).length,
  };

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('cdp:dashboard.stats.totalCustomers')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProfiles}</div>
            <p className="text-xs text-muted-foreground">
              {t('cdp:dashboard.stats.activeProfiles')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('cdp:dashboard.stats.totalRevenue')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
              }).format(stats.totalRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('cdp:dashboard.stats.lifetimeValue')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('cdp:dashboard.stats.avgEngagement')}</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats.avgEngagement * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {t('cdp:dashboard.stats.engagementScore')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('cdp:dashboard.stats.churnRisk')}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.highRiskChurn}</div>
            <p className="text-xs text-muted-foreground">
              {t('cdp:dashboard.stats.highRiskCustomers')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="profiles" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profiles">{t('cdp:profiles.title')}</TabsTrigger>
          <TabsTrigger value="segments">{t('cdp:segments.title')}</TabsTrigger>
          <TabsTrigger value="journeys">{t('cdp:journeys.title')}</TabsTrigger>
          <TabsTrigger value="create">{t('cdp:profiles.create.title')}</TabsTrigger>
        </TabsList>

        <TabsContent value="profiles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('cdp:profiles.title')}</CardTitle>
              <CardDescription>
                {t('cdp:profiles.description')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : profiles.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {t('cdp:profiles.search.noResults')}
                </div>
              ) : (
                <div className="space-y-4">
                  {profiles.map((profile) => (
                    <div
                      key={profile.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="space-y-1">
                        <div className="font-medium">
                          {profile.first_name && profile.last_name
                            ? `${profile.first_name} ${profile.last_name}`
                            : profile.primary_email || 'Unknown Customer'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {profile.primary_email}
                        </div>
                        <div className="flex gap-2">
                          <Badge variant="outline">
                            {t(`cdp:profiles.lifecycle.${profile.lifecycle_stage}`)}
                          </Badge>
                          <Badge variant="outline">
                            {t(`cdp:profiles.tiers.${profile.customer_value_tier}`)}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="text-sm font-medium">
                          {new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                          }).format(profile.behavior?.total_revenue || 0)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {profile.behavior?.total_purchases || 0} {t('cdp:profiles.fields.totalOrders').toLowerCase()}
                        </div>
                        <div className="text-xs">
                          {t('cdp:profiles.fields.engagement')}: {((profile.engagement_scores?.overall_engagement_score || 0) * 100).toFixed(0)}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="segments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('cdp:segments.title')}</CardTitle>
              <CardDescription>
                {t('cdp:segments.description')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Target className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Customer Segmentation</h3>
                <p className="text-muted-foreground mb-4">
                  Create dynamic customer segments based on behavior and attributes
                </p>
                <Button asChild>
                  <a href={`/home/<USER>/cdp/segments`}>
                    Go to Segments
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="journeys" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('cdp:journeys.title')}</CardTitle>
              <CardDescription>
                {t('cdp:journeys.description')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <GitBranch className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Customer Journey Orchestration</h3>
                <p className="text-muted-foreground mb-4">
                  Create automated marketing workflows and customer journey orchestration
                </p>
                <Button asChild>
                  <a href={`/home/<USER>/cdp/journeys`}>
                    Go to Journeys
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('cdp:profiles.create.title')}</CardTitle>
              <CardDescription>
                {t('cdp:profiles.create.description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="email">{t('cdp:profiles.fields.email')} *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={newProfile.primary_email}
                    onChange={(e) =>
                      setNewProfile({ ...newProfile, primary_email: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">{t('cdp:profiles.fields.phone')}</Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+84901234567"
                    value={newProfile.primary_phone}
                    onChange={(e) =>
                      setNewProfile({ ...newProfile, primary_phone: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="firstName">{t('cdp:profiles.fields.firstName')}</Label>
                  <Input
                    id="firstName"
                    placeholder="John"
                    value={newProfile.first_name}
                    onChange={(e) =>
                      setNewProfile({ ...newProfile, first_name: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">{t('cdp:profiles.fields.lastName')}</Label>
                  <Input
                    id="lastName"
                    placeholder="Doe"
                    value={newProfile.last_name}
                    onChange={(e) =>
                      setNewProfile({ ...newProfile, last_name: e.target.value })
                    }
                  />
                </div>
              </div>
              <Button onClick={createProfile} disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('cdp:profiles.create.button')}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
