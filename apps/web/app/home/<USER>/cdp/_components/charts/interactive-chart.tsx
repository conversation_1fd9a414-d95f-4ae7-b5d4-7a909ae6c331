'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  LineChart, 
  PieChart,
  Download,
  Maximize2,
  RefreshCw,
  Filter,
  Calendar
} from 'lucide-react';

interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  trend?: number;
  metadata?: Record<string, any>;
}

interface InteractiveChartProps {
  title: string;
  description?: string;
  type: 'line' | 'bar' | 'pie' | 'area' | 'donut';
  data: ChartDataPoint[];
  height?: number;
  showTrend?: boolean;
  showLegend?: boolean;
  interactive?: boolean;
  realTime?: boolean;
  refreshInterval?: number;
  onDataPointClick?: (point: ChartDataPoint) => void;
  onExport?: () => void;
}

export function InteractiveChart({
  title,
  description,
  type,
  data,
  height = 300,
  showTrend = true,
  showLegend = true,
  interactive = true,
  realTime = false,
  refreshInterval = 30000,
  onDataPointClick,
  onExport
}: InteractiveChartProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPoint, setSelectedPoint] = useState<ChartDataPoint | null>(null);
  const [chartData, setChartData] = useState(data);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const chartRef = useRef<HTMLDivElement>(null);

  // Real-time data updates
  useEffect(() => {
    if (!realTime) return;

    const interval = setInterval(() => {
      setIsLoading(true);
      // Simulate real-time data update
      setTimeout(() => {
        setChartData(prevData => 
          prevData.map(point => ({
            ...point,
            value: point.value + (Math.random() - 0.5) * point.value * 0.1,
            trend: Math.random() - 0.5
          }))
        );
        setIsLoading(false);
      }, 1000);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [realTime, refreshInterval]);

  const handleDataPointClick = (point: ChartDataPoint) => {
    setSelectedPoint(point);
    onDataPointClick?.(point);
  };

  const getTotalValue = () => {
    return chartData.reduce((sum, point) => sum + point.value, 0);
  };

  const getAverageTrend = () => {
    const trends = chartData.filter(point => point.trend !== undefined);
    if (trends.length === 0) return 0;
    return trends.reduce((sum, point) => sum + (point.trend || 0), 0) / trends.length;
  };

  const renderLineChart = () => {
    const maxValue = Math.max(...chartData.map(p => p.value));
    const minValue = Math.min(...chartData.map(p => p.value));
    const range = maxValue - minValue;

    return (
      <div className="relative" style={{ height }}>
        <svg width="100%" height="100%" className="overflow-visible">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => (
            <line
              key={index}
              x1="0"
              y1={height * ratio}
              x2="100%"
              y2={height * ratio}
              stroke="#e5e7eb"
              strokeWidth="1"
              strokeDasharray="2,2"
            />
          ))}
          
          {/* Data line */}
          <polyline
            fill="none"
            stroke="url(#lineGradient)"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            points={chartData.map((point, index) => {
              const x = (index / (chartData.length - 1)) * 100;
              const y = ((maxValue - point.value) / range) * (height - 40) + 20;
              return `${x}%,${y}`;
            }).join(' ')}
          />
          
          {/* Data points */}
          {chartData.map((point, index) => {
            const x = (index / (chartData.length - 1)) * 100;
            const y = ((maxValue - point.value) / range) * (height - 40) + 20;
            
            return (
              <circle
                key={index}
                cx={`${x}%`}
                cy={y}
                r="6"
                fill="#3b82f6"
                stroke="#ffffff"
                strokeWidth="2"
                className={`cursor-pointer transition-all duration-200 ${
                  interactive ? 'hover:r-8 hover:fill-blue-600' : ''
                }`}
                onClick={() => interactive && handleDataPointClick(point)}
              />
            );
          })}
          
          {/* Gradient definition */}
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#3b82f6" />
              <stop offset="50%" stopColor="#8b5cf6" />
              <stop offset="100%" stopColor="#ec4899" />
            </linearGradient>
          </defs>
        </svg>
        
        {/* X-axis labels */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500 mt-2">
          {chartData.map((point, index) => (
            <span key={index} className="transform -translate-x-1/2">
              {point.label}
            </span>
          ))}
        </div>
      </div>
    );
  };

  const renderBarChart = () => {
    const maxValue = Math.max(...chartData.map(p => p.value));

    return (
      <div className="relative" style={{ height }}>
        <div className="flex items-end justify-between h-full pb-8">
          {chartData.map((point, index) => {
            const barHeight = (point.value / maxValue) * (height - 60);
            
            return (
              <div
                key={index}
                className="flex flex-col items-center flex-1 mx-1"
              >
                <div
                  className={`w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-lg transition-all duration-300 ${
                    interactive ? 'hover:from-blue-600 hover:to-blue-500 cursor-pointer' : ''
                  } ${selectedPoint === point ? 'ring-2 ring-blue-300' : ''}`}
                  style={{ height: barHeight }}
                  onClick={() => interactive && handleDataPointClick(point)}
                />
                <span className="text-xs text-gray-500 mt-2 text-center">
                  {point.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderPieChart = () => {
    const total = getTotalValue();
    let currentAngle = 0;
    const radius = Math.min(height, 300) / 2 - 20;
    const centerX = radius + 20;
    const centerY = radius + 20;

    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <svg width={radius * 2 + 40} height={radius * 2 + 40}>
          {chartData.map((point, index) => {
            const percentage = point.value / total;
            const angle = percentage * 360;
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;
            
            const startX = centerX + radius * Math.cos((startAngle - 90) * Math.PI / 180);
            const startY = centerY + radius * Math.sin((startAngle - 90) * Math.PI / 180);
            const endX = centerX + radius * Math.cos((endAngle - 90) * Math.PI / 180);
            const endY = centerY + radius * Math.sin((endAngle - 90) * Math.PI / 180);
            
            const largeArcFlag = angle > 180 ? 1 : 0;
            
            const pathData = [
              `M ${centerX} ${centerY}`,
              `L ${startX} ${startY}`,
              `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY}`,
              'Z'
            ].join(' ');
            
            currentAngle += angle;
            
            const colors = [
              '#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', 
              '#10b981', '#ef4444', '#6366f1', '#84cc16'
            ];
            
            return (
              <path
                key={index}
                d={pathData}
                fill={point.color || colors[index % colors.length]}
                className={`transition-all duration-200 ${
                  interactive ? 'hover:opacity-80 cursor-pointer' : ''
                } ${selectedPoint === point ? 'opacity-90 stroke-white stroke-2' : ''}`}
                onClick={() => interactive && handleDataPointClick(point)}
              />
            );
          })}
        </svg>
      </div>
    );
  };

  const renderChart = () => {
    switch (type) {
      case 'line':
      case 'area':
        return renderLineChart();
      case 'bar':
        return renderBarChart();
      case 'pie':
      case 'donut':
        return renderPieChart();
      default:
        return renderLineChart();
    }
  };

  return (
    <Card className={`border-0 shadow-lg transition-all duration-300 ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {type === 'line' && <LineChart className="h-5 w-5" />}
              {type === 'bar' && <BarChart3 className="h-5 w-5" />}
              {(type === 'pie' || type === 'donut') && <PieChart className="h-5 w-5" />}
              {title}
              {realTime && (
                <Badge variant="secondary" className="ml-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1" />
                  Live
                </Badge>
              )}
            </CardTitle>
            {description && (
              <CardDescription>{description}</CardDescription>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {showTrend && (
              <div className="flex items-center gap-1 text-sm">
                {getAverageTrend() > 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={getAverageTrend() > 0 ? 'text-green-600' : 'text-red-600'}>
                  {Math.abs(getAverageTrend() * 100).toFixed(1)}%
                </span>
              </div>
            )}
            
            <Button variant="ghost" size="sm">
              <Filter className="h-4 w-4" />
            </Button>
            
            <Button variant="ghost" size="sm">
              <Calendar className="h-4 w-4" />
            </Button>
            
            {realTime && (
              <Button 
                variant="ghost" 
                size="sm"
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            )}
            
            {onExport && (
              <Button variant="ghost" size="sm" onClick={onExport}>
                <Download className="h-4 w-4" />
              </Button>
            )}
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div ref={chartRef} className="relative">
          {isLoading && (
            <div className="absolute inset-0 bg-white/50 flex items-center justify-center z-10">
              <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
            </div>
          )}
          
          {renderChart()}
          
          {/* Legend */}
          {showLegend && (type === 'pie' || type === 'donut') && (
            <div className="mt-4 flex flex-wrap gap-2">
              {chartData.map((point, index) => {
                const colors = [
                  '#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', 
                  '#10b981', '#ef4444', '#6366f1', '#84cc16'
                ];
                
                return (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: point.color || colors[index % colors.length] }}
                    />
                    <span>{point.label}</span>
                    <span className="text-gray-500">
                      ({((point.value / getTotalValue()) * 100).toFixed(1)}%)
                    </span>
                  </div>
                );
              })}
            </div>
          )}
          
          {/* Selected point details */}
          {selectedPoint && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900">{selectedPoint.label}</h4>
              <p className="text-blue-700">
                Value: {selectedPoint.value.toLocaleString()}
              </p>
              {selectedPoint.trend !== undefined && (
                <p className={`text-sm ${selectedPoint.trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  Trend: {selectedPoint.trend > 0 ? '+' : ''}{(selectedPoint.trend * 100).toFixed(1)}%
                </p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
