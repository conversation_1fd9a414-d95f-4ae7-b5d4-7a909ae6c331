'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { 
  Activity, 
  Users, 
  TrendingUp, 
  Zap,
  Globe,
  ShoppingCart,
  Mail,
  MousePointer,
  Eye,
  Clock,
  Wifi,
  WifiOff,
  Pause,
  Play,
  Settings
} from 'lucide-react';

interface RealTimeMetric {
  id: string;
  label: string;
  value: number;
  previousValue: number;
  unit?: string;
  icon: React.ReactNode;
  color: string;
  trend: 'up' | 'down' | 'stable';
  changePercent: number;
}

interface RealTimeEvent {
  id: string;
  type: 'user_action' | 'conversion' | 'error' | 'system';
  message: string;
  timestamp: Date;
  severity: 'info' | 'success' | 'warning' | 'error';
  metadata?: Record<string, any>;
}

interface RealTimeDashboardProps {
  accountId: string;
}

export function RealTimeDashboard({ accountId }: RealTimeDashboardProps) {
  const [isConnected, setIsConnected] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([]);
  const [events, setEvents] = useState<RealTimeEvent[]>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Initialize metrics
  useEffect(() => {
    const initialMetrics: RealTimeMetric[] = [
      {
        id: 'active_users',
        label: 'Active Users',
        value: 1247,
        previousValue: 1198,
        icon: <Users className="h-5 w-5" />,
        color: 'text-blue-600',
        trend: 'up',
        changePercent: 4.1
      },
      {
        id: 'page_views',
        label: 'Page Views',
        value: 8934,
        previousValue: 8756,
        unit: '/min',
        icon: <Eye className="h-5 w-5" />,
        color: 'text-green-600',
        trend: 'up',
        changePercent: 2.0
      },
      {
        id: 'conversions',
        label: 'Conversions',
        value: 156,
        previousValue: 142,
        unit: '/hour',
        icon: <ShoppingCart className="h-5 w-5" />,
        color: 'text-purple-600',
        trend: 'up',
        changePercent: 9.9
      },
      {
        id: 'bounce_rate',
        label: 'Bounce Rate',
        value: 23.4,
        previousValue: 26.1,
        unit: '%',
        icon: <MousePointer className="h-5 w-5" />,
        color: 'text-orange-600',
        trend: 'down',
        changePercent: -10.3
      },
      {
        id: 'email_opens',
        label: 'Email Opens',
        value: 2847,
        previousValue: 2634,
        unit: '/hour',
        icon: <Mail className="h-5 w-5" />,
        color: 'text-indigo-600',
        trend: 'up',
        changePercent: 8.1
      },
      {
        id: 'response_time',
        label: 'Avg Response Time',
        value: 245,
        previousValue: 289,
        unit: 'ms',
        icon: <Zap className="h-5 w-5" />,
        color: 'text-yellow-600',
        trend: 'down',
        changePercent: -15.2
      }
    ];
    
    setMetrics(initialMetrics);
  }, []);

  // Simulate real-time updates
  const updateMetrics = useCallback(() => {
    if (isPaused) return;

    setMetrics(prevMetrics => 
      prevMetrics.map(metric => {
        const changeRange = 0.05; // 5% max change
        const randomChange = (Math.random() - 0.5) * 2 * changeRange;
        const newValue = Math.max(0, metric.value * (1 + randomChange));
        const changePercent = ((newValue - metric.previousValue) / metric.previousValue) * 100;
        
        return {
          ...metric,
          previousValue: metric.value,
          value: parseFloat(newValue.toFixed(metric.unit === '%' || metric.unit === 'ms' ? 1 : 0)),
          changePercent: parseFloat(changePercent.toFixed(1)),
          trend: changePercent > 1 ? 'up' : changePercent < -1 ? 'down' : 'stable'
        };
      })
    );

    // Add random events
    if (Math.random() < 0.3) {
      const eventTypes = [
        { type: 'user_action', message: 'New user registered', severity: 'success' },
        { type: 'conversion', message: 'Purchase completed', severity: 'success' },
        { type: 'user_action', message: 'User started checkout', severity: 'info' },
        { type: 'system', message: 'Cache cleared', severity: 'info' },
        { type: 'error', message: 'Payment failed', severity: 'warning' }
      ] as const;

      const randomEvent = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      
      const newEvent: RealTimeEvent = {
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: randomEvent.type,
        message: randomEvent.message,
        timestamp: new Date(),
        severity: randomEvent.severity
      };

      setEvents(prevEvents => [newEvent, ...prevEvents.slice(0, 19)]); // Keep last 20 events
    }

    setLastUpdate(new Date());
  }, [isPaused]);

  // Real-time update interval
  useEffect(() => {
    const interval = setInterval(updateMetrics, 2000); // Update every 2 seconds
    return () => clearInterval(interval);
  }, [updateMetrics]);

  // Simulate connection status
  useEffect(() => {
    const connectionInterval = setInterval(() => {
      // Randomly simulate connection issues (5% chance)
      if (Math.random() < 0.05) {
        setIsConnected(false);
        setTimeout(() => setIsConnected(true), 3000);
      }
    }, 10000);

    return () => clearInterval(connectionInterval);
  }, []);

  const getTrendIcon = (trend: string, changePercent: number) => {
    if (trend === 'up') {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (trend === 'down') {
      return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
    }
    return <div className="w-4 h-4" />;
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'user_action': return <Users className="h-4 w-4" />;
      case 'conversion': return <ShoppingCart className="h-4 w-4" />;
      case 'error': return <Zap className="h-4 w-4" />;
      case 'system': return <Settings className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getEventColor = (severity: string) => {
    switch (severity) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-blue-600 bg-blue-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Real-time Dashboard</h2>
          <p className="text-muted-foreground">
            Live customer activity and system metrics
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPaused(!isPaused)}
          >
            {isPaused ? (
              <Play className="h-4 w-4 mr-2" />
            ) : (
              <Pause className="h-4 w-4 mr-2" />
            )}
            {isPaused ? 'Resume' : 'Pause'}
          </Button>
          
          <div className="text-sm text-muted-foreground">
            Last update: {lastUpdate.toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Real-time Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {metrics.map((metric) => (
          <Card key={metric.id} className="border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-xl bg-gray-100 ${metric.color}`}>
                  {metric.icon}
                </div>
                <div className="flex items-center gap-1">
                  {getTrendIcon(metric.trend, metric.changePercent)}
                  <span className={`text-sm font-medium ${
                    metric.trend === 'up' ? 'text-green-600' : 
                    metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {metric.changePercent > 0 ? '+' : ''}{metric.changePercent}%
                  </span>
                </div>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  {metric.label}
                </p>
                <p className="text-2xl font-bold">
                  {metric.value.toLocaleString()}
                  {metric.unit && <span className="text-sm text-gray-500 ml-1">{metric.unit}</span>}
                </p>
              </div>
              
              {/* Mini trend chart */}
              <div className="mt-4">
                <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className={`h-full transition-all duration-1000 ${
                      metric.trend === 'up' ? 'bg-green-500' : 
                      metric.trend === 'down' ? 'bg-red-500' : 'bg-gray-400'
                    }`}
                    style={{ 
                      width: `${Math.min(100, Math.abs(metric.changePercent) * 10)}%` 
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Real-time Activity Feed */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Live Activity Feed
            </CardTitle>
            <CardDescription>
              Real-time user actions and system events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {events.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Waiting for real-time events...</p>
                </div>
              ) : (
                events.map((event) => (
                  <div key={event.id} className="flex items-start gap-3 p-3 rounded-lg bg-muted/50">
                    <div className={`p-2 rounded-lg ${getEventColor(event.severity)}`}>
                      {getEventIcon(event.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{event.message}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Clock className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {event.timestamp.toLocaleTimeString()}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {event.type.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* System Performance */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              System Performance
            </CardTitle>
            <CardDescription>
              Real-time system health and performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">CPU Usage</span>
                  <span className="text-sm text-gray-500">34%</span>
                </div>
                <Progress value={34} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Memory Usage</span>
                  <span className="text-sm text-gray-500">67%</span>
                </div>
                <Progress value={67} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Database Load</span>
                  <span className="text-sm text-gray-500">23%</span>
                </div>
                <Progress value={23} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">API Response Time</span>
                  <span className="text-sm text-gray-500">245ms</span>
                </div>
                <Progress value={75} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Active Connections</span>
                  <span className="text-sm text-gray-500">1,247</span>
                </div>
                <Progress value={62} className="h-2" />
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">System Status</span>
                  <Badge variant="default" className="bg-green-500">
                    All Systems Operational
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
