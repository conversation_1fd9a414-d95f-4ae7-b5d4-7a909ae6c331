'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

interface ModalContextType {
  modalStack: string[];
  openModal: (modalId: string) => void;
  closeModal: (modalId: string) => void;
  isModalOpen: (modalId: string) => boolean;
  getModalZIndex: (modalId: string) => number;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export function ModalProvider({ children }: { children: ReactNode }) {
  const [modalStack, setModalStack] = useState<string[]>([]);

  const openModal = (modalId: string) => {
    setModalStack(prev => {
      if (!prev.includes(modalId)) {
        return [...prev, modalId];
      }
      return prev;
    });
  };

  const closeModal = (modalId: string) => {
    setModalStack(prev => prev.filter(id => id !== modalId));
  };

  const isModalOpen = (modalId: string) => {
    return modalStack.includes(modalId);
  };

  const getModalZIndex = (modalId: string) => {
    const index = modalStack.indexOf(modalId);
    return index >= 0 ? 50 + index : 50;
  };

  return (
    <ModalContext.Provider value={{
      modalStack,
      openModal,
      closeModal,
      isModalOpen,
      getModalZIndex,
    }}>
      {children}
    </ModalContext.Provider>
  );
}

export function useModal() {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
}
