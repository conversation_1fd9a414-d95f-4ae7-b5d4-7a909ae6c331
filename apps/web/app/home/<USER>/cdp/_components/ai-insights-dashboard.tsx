'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { <PERSON><PERSON> } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@kit/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Target,
  Zap,
  Users,
  DollarSign,
  Activity,
  Lightbulb,
  Star,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  Download,
  Settings,
  Play,
  Pause,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react';

interface AIInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'optimization' | 'prediction' | 'anomaly';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  category: 'revenue' | 'churn' | 'engagement' | 'conversion' | 'performance';
  data: Record<string, any>;
  recommendations: string[];
  created_at: Date;
  status: 'active' | 'dismissed' | 'implemented';
}

interface PredictiveModel {
  id: string;
  name: string;
  type: string;
  accuracy: number;
  last_trained: Date;
  status: 'training' | 'ready' | 'error';
  performance_metrics: {
    precision: number;
    recall: number;
    f1_score: number;
    auc_roc: number;
  };
}

interface CustomerPrediction {
  customer_id: string;
  model_id: string;
  prediction_type: string;
  predicted_value: number;
  confidence: number;
  factors: Array<{
    feature: string;
    importance: number;
    value: any;
  }>;
}

interface AIInsightsDashboardProps {
  accountId: string;
}

export function AIInsightsDashboard({ accountId }: AIInsightsDashboardProps) {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [models, setModels] = useState<PredictiveModel[]>([]);
  const [predictions, setPredictions] = useState<CustomerPrediction[]>([]);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Mock data for demonstration
  useEffect(() => {
    const mockInsights: AIInsight[] = [
      {
        id: '1',
        type: 'opportunity',
        title: 'High-Value Customer Upsell Opportunity',
        description: 'Customers who view product pages 3+ times have 45% higher conversion rate. Target these users with personalized offers.',
        confidence: 0.87,
        impact: 'high',
        category: 'revenue',
        data: {
          potential_revenue: 125000,
          affected_customers: 1247,
          conversion_lift: 0.45
        },
        recommendations: [
          'Create targeted email campaign for high-intent users',
          'Implement dynamic pricing for repeat viewers',
          'Add urgency elements to product pages'
        ],
        created_at: new Date(),
        status: 'active'
      },
      {
        id: '2',
        type: 'risk',
        title: 'High Churn Risk Detected',
        description: '156 customers at high churn risk. Send re-engagement campaign within 48 hours to reduce churn by 23%.',
        confidence: 0.92,
        impact: 'critical',
        category: 'churn',
        data: {
          at_risk_customers: 156,
          potential_revenue_loss: 89000,
          churn_reduction_potential: 0.23
        },
        recommendations: [
          'Send personalized re-engagement emails',
          'Offer exclusive discounts to at-risk customers',
          'Schedule customer success calls'
        ],
        created_at: new Date(),
        status: 'active'
      },
      {
        id: '3',
        type: 'optimization',
        title: 'Mobile-First Segment Opportunity',
        description: 'Create new segment for mobile-first users aged 25-34. This segment shows 67% higher engagement rates.',
        confidence: 0.84,
        impact: 'medium',
        category: 'engagement',
        data: {
          segment_size: 3421,
          engagement_lift: 0.67,
          recommended_channels: ['mobile_push', 'sms', 'in_app']
        },
        recommendations: [
          'Create mobile-optimized content',
          'Implement push notification campaigns',
          'Design mobile-first user journeys'
        ],
        created_at: new Date(),
        status: 'active'
      },
      {
        id: '4',
        type: 'anomaly',
        title: 'Email Performance Anomaly',
        description: 'Email campaign open rates dropped 15% this week. Consider A/B testing subject lines and send times.',
        confidence: 0.91,
        impact: 'medium',
        category: 'performance',
        data: {
          metric: 'email_open_rate',
          current_value: 0.18,
          expected_value: 0.21,
          deviation: -0.15
        },
        recommendations: [
          'A/B test subject lines',
          'Optimize send times by timezone',
          'Review email content relevance'
        ],
        created_at: new Date(),
        status: 'active'
      }
    ];

    const mockModels: PredictiveModel[] = [
      {
        id: 'churn_prediction',
        name: 'Customer Churn Prediction',
        type: 'churn_prediction',
        accuracy: 0.87,
        last_trained: new Date(),
        status: 'ready',
        performance_metrics: {
          precision: 0.85,
          recall: 0.89,
          f1_score: 0.87,
          auc_roc: 0.92
        }
      },
      {
        id: 'ltv_prediction',
        name: 'Customer Lifetime Value',
        type: 'ltv_prediction',
        accuracy: 0.82,
        last_trained: new Date(),
        status: 'ready',
        performance_metrics: {
          precision: 0.80,
          recall: 0.84,
          f1_score: 0.82,
          auc_roc: 0.88
        }
      },
      {
        id: 'conversion_prediction',
        name: 'Conversion Probability',
        type: 'conversion_prediction',
        accuracy: 0.79,
        last_trained: new Date(),
        status: 'ready',
        performance_metrics: {
          precision: 0.77,
          recall: 0.81,
          f1_score: 0.79,
          auc_roc: 0.85
        }
      }
    ];

    setInsights(mockInsights);
    setModels(mockModels);
  }, []);

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="h-5 w-5 text-green-500" />;
      case 'risk': return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'optimization': return <Target className="h-5 w-5 text-blue-500" />;
      case 'prediction': return <Brain className="h-5 w-5 text-purple-500" />;
      case 'anomaly': return <Zap className="h-5 w-5 text-orange-500" />;
      default: return <Lightbulb className="h-5 w-5 text-gray-500" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'revenue': return <DollarSign className="h-4 w-4" />;
      case 'churn': return <Users className="h-4 w-4" />;
      case 'engagement': return <Activity className="h-4 w-4" />;
      case 'conversion': return <Target className="h-4 w-4" />;
      case 'performance': return <BarChart3 className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const handleDismissInsight = (insightId: string) => {
    setInsights(prev => prev.map(insight => 
      insight.id === insightId ? { ...insight, status: 'dismissed' } : insight
    ));
  };

  const handleImplementInsight = (insightId: string) => {
    setInsights(prev => prev.map(insight => 
      insight.id === insightId ? { ...insight, status: 'implemented' } : insight
    ));
  };

  const refreshInsights = async () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const activeInsights = insights.filter(insight => insight.status === 'active');
  const criticalInsights = activeInsights.filter(insight => insight.impact === 'critical');
  const highImpactInsights = activeInsights.filter(insight => insight.impact === 'high');

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">AI-Powered Insights</h2>
          <p className="text-muted-foreground">
            Machine learning insights and predictive analytics
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? (
              <Pause className="h-4 w-4 mr-2" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            {autoRefresh ? 'Pause' : 'Resume'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={refreshInsights}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Active Insights</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{activeInsights.length}</p>
              </div>
              <div className="p-3 bg-blue-500 rounded-xl">
                <Brain className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">+3 new this week</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600 dark:text-red-400">Critical Issues</p>
                <p className="text-2xl font-bold text-red-700 dark:text-red-300">{criticalInsights.length}</p>
              </div>
              <div className="p-3 bg-red-500 rounded-xl">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Clock className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-red-600">Requires immediate attention</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">High Impact</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">{highImpactInsights.length}</p>
              </div>
              <div className="p-3 bg-green-500 rounded-xl">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Star className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">Revenue opportunities</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">ML Models</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{models.length}</p>
              </div>
              <div className="p-3 bg-purple-500 rounded-xl">
                <Brain className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <CheckCircle2 className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">All models active</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="insights" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="models">ML Models</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid gap-6">
            {activeInsights.map((insight) => (
              <Card key={insight.id} className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      {getInsightIcon(insight.type)}
                      <div>
                        <CardTitle className="text-lg">{insight.title}</CardTitle>
                        <CardDescription className="mt-1">
                          {insight.description}
                        </CardDescription>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={`${getImpactColor(insight.impact)} border`}>
                        {insight.impact}
                      </Badge>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        {getCategoryIcon(insight.category)}
                        <span className="capitalize">{insight.category}</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-4">
                    {/* Confidence Score */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Confidence Score</span>
                        <span className="text-sm text-muted-foreground">
                          {(insight.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                      <Progress value={insight.confidence * 100} className="h-2" />
                    </div>
                    
                    {/* Key Metrics */}
                    {insight.data && (
                      <div className="grid gap-4 md:grid-cols-3">
                        {Object.entries(insight.data).map(([key, value]) => (
                          <div key={key} className="text-center p-3 bg-muted/50 rounded-lg">
                            <div className="text-lg font-bold">
                              {typeof value === 'number' && key.includes('revenue') 
                                ? new Intl.NumberFormat('vi-VN', {
                                    style: 'currency',
                                    currency: 'VND',
                                    notation: 'compact'
                                  }).format(value)
                                : typeof value === 'number' && key.includes('rate')
                                ? `${(value * 100).toFixed(1)}%`
                                : String(value)
                              }
                            </div>
                            <div className="text-xs text-muted-foreground capitalize">
                              {key.replace(/_/g, ' ')}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* Recommendations */}
                    <div>
                      <h4 className="font-medium mb-2">Recommended Actions</h4>
                      <ul className="space-y-1">
                        {insight.recommendations.map((rec, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center gap-2 pt-4 border-t">
                      <Button 
                        size="sm"
                        onClick={() => handleImplementInsight(insight.id)}
                      >
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        Implement
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDismissInsight(insight.id)}
                      >
                        Dismiss
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Settings className="h-4 w-4 mr-2" />
                        Configure
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Customer Predictions</CardTitle>
              <CardDescription>
                AI-powered predictions for individual customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Brain className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Predictive Analytics</h3>
                <p className="text-muted-foreground mb-6">
                  Select a customer to view AI-powered predictions and risk assessments
                </p>
                <Button>
                  <Users className="h-4 w-4 mr-2" />
                  Browse Customers
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {models.map((model) => (
              <Card key={model.id} className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{model.name}</CardTitle>
                    <Badge variant={model.status === 'ready' ? 'default' : 'secondary'}>
                      {model.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    {model.type.replace('_', ' ')}
                  </CardDescription>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Accuracy</span>
                        <span className="text-sm text-muted-foreground">
                          {(model.accuracy * 100).toFixed(1)}%
                        </span>
                      </div>
                      <Progress value={model.accuracy * 100} className="h-2" />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Precision</span>
                        <div className="font-medium">
                          {(model.performance_metrics.precision * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Recall</span>
                        <div className="font-medium">
                          {(model.performance_metrics.recall * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">F1 Score</span>
                        <div className="font-medium">
                          {(model.performance_metrics.f1_score * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">AUC-ROC</span>
                        <div className="font-medium">
                          {(model.performance_metrics.auc_roc * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-xs text-muted-foreground">
                      Last trained: {model.last_trained.toLocaleDateString()}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Retrain
                      </Button>
                      <Button size="sm" variant="ghost">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Today's AI Recommendations</CardTitle>
              <CardDescription>
                Prioritized actions based on AI analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="p-2 bg-green-500 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-green-900">Launch retargeting campaign for cart abandoners</h4>
                    <p className="text-sm text-green-700">Expected 12% conversion rate, $45K revenue</p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">High Priority</Badge>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="p-2 bg-blue-500 rounded-lg">
                    <Clock className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-blue-900">Optimize email send times for European customers</h4>
                    <p className="text-sm text-blue-700">Potential 8% improvement in open rates</p>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">Medium Priority</Badge>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="p-2 bg-orange-500 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-orange-900">Review pricing strategy for premium segment</h4>
                    <p className="text-sm text-orange-700">Price sensitivity analysis suggests 5% increase opportunity</p>
                  </div>
                  <Badge className="bg-orange-100 text-orange-800">Low Priority</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
