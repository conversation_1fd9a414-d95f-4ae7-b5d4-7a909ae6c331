'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  MousePointer, 
  ShoppingCart,
  Mail,
  Smartphone,
  BarChart3,
  PieChart,
  LineChart,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Progress } from '@kit/ui/progress';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface RealTimeMetric {
  name: string;
  value: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
  timestamp: Date;
}

interface RealTimeDashboardProps {
  accountId: string;
}

export function RealTimeDashboard({ accountId }: RealTimeDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [metrics, setMetrics] = useState<Record<string, RealTimeMetric>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Load real-time metrics
  const loadMetrics = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/cdp/analytics?type=dashboard');
      const data = await response.json();
      
      if (data.success) {
        setMetrics(data.data || {});
        setLastUpdated(new Date());
      } else {
        setError(data.error || 'Failed to load metrics');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Auto-refresh metrics
  useEffect(() => {
    loadMetrics();
    
    const interval = setInterval(loadMetrics, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const getMetricIcon = (metricName: string) => {
    const iconMap: Record<string, any> = {
      total_events: Activity,
      page_view: MousePointer,
      purchase: ShoppingCart,
      email_open: Mail,
      email_click: Mail,
      mobile_app_open: Smartphone,
      active_customers: Users,
      total_revenue: TrendingUp
    };
    
    return iconMap[metricName] || BarChart3;
  };

  const formatValue = (metricName: string, value: number) => {
    if (metricName.includes('revenue')) {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value);
    }
    
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    }
    
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    
    return value.toLocaleString();
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return TrendingUp;
      case 'down': return TrendingDown;
      default: return Activity;
    }
  };

  // Key metrics to display prominently
  const keyMetrics = [
    'total_events',
    'active_customers', 
    'page_view',
    'purchase',
    'email_open',
    'total_revenue'
  ];

  const keyMetricsData = keyMetrics
    .map(key => ({ key, ...metrics[key] }))
    .filter(metric => metric.value !== undefined);

  const otherMetrics = Object.entries(metrics)
    .filter(([key]) => !keyMetrics.includes(key))
    .map(([key, metric]) => ({ key, ...metric }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Real-time Analytics</h2>
          <p className="text-muted-foreground">
            Live customer behavior and engagement metrics
          </p>
        </div>
        <div className="flex items-center gap-2">
          {lastUpdated && (
            <span className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={loadMetrics}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Key Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {keyMetricsData.map((metric) => {
          const Icon = getMetricIcon(metric.key);
          const TrendIcon = getTrendIcon(metric.trend);
          
          return (
            <Card key={metric.key}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium capitalize">
                  {metric.key.replace(/_/g, ' ')}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatValue(metric.key, metric.value)}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  <TrendIcon className={`h-3 w-3 ${getTrendColor(metric.trend)}`} />
                  <span className={getTrendColor(metric.trend)}>
                    {metric.changePercent > 0 ? '+' : ''}{metric.changePercent.toFixed(1)}%
                  </span>
                  <span className="text-muted-foreground">vs yesterday</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Event Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Event Distribution
                </CardTitle>
                <CardDescription>
                  Real-time breakdown of customer events
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {keyMetricsData.slice(2).map((metric) => {
                    const total = keyMetricsData.reduce((sum, m) => sum + (m.value || 0), 0);
                    const percentage = total > 0 ? (metric.value / total) * 100 : 0;
                    
                    return (
                      <div key={metric.key} className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="capitalize">{metric.key.replace(/_/g, ' ')}</span>
                          <span className="font-medium">
                            {formatValue(metric.key, metric.value)} ({percentage.toFixed(1)}%)
                          </span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Top Performing
                </CardTitle>
                <CardDescription>
                  Metrics with highest growth
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...keyMetricsData]
                    .sort((a, b) => (b.changePercent || 0) - (a.changePercent || 0))
                    .slice(0, 5)
                    .map((metric) => {
                      const Icon = getMetricIcon(metric.key);
                      
                      return (
                        <div key={metric.key} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm capitalize">
                              {metric.key.replace(/_/g, ' ')}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={metric.changePercent > 0 ? 'default' : 'secondary'}>
                              {metric.changePercent > 0 ? '+' : ''}{metric.changePercent.toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Event Activity</CardTitle>
              <CardDescription>
                Real-time customer event tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {otherMetrics.length > 0 ? (
                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                    {otherMetrics.map((metric) => {
                      const Icon = getMetricIcon(metric.key);
                      
                      return (
                        <div key={metric.key} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm capitalize">
                              {metric.key.replace(/_/g, ' ')}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              {formatValue(metric.key, metric.value)}
                            </div>
                            <div className={`text-xs ${getTrendColor(metric.trend)}`}>
                              {metric.changePercent > 0 ? '+' : ''}{metric.changePercent.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Activity className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No additional event data available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Activity</CardTitle>
              <CardDescription>
                Real-time customer engagement metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <h4 className="font-medium">Active Customers</h4>
                  <div className="text-3xl font-bold">
                    {formatValue('active_customers', metrics.active_customers?.value || 0)}
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-green-600">
                      +{metrics.active_customers?.changePercent?.toFixed(1) || 0}%
                    </span>
                    <span className="text-muted-foreground">vs yesterday</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-medium">Total Revenue</h4>
                  <div className="text-3xl font-bold">
                    {formatValue('total_revenue', metrics.total_revenue?.value || 0)}
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-green-600">
                      +{metrics.total_revenue?.changePercent?.toFixed(1) || 0}%
                    </span>
                    <span className="text-muted-foreground">vs yesterday</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Engagement Metrics</CardTitle>
              <CardDescription>
                Email and digital engagement tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Email Performance</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Opens</span>
                      <span className="font-medium">
                        {formatValue('email_open', metrics.email_open?.value || 0)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Clicks</span>
                      <span className="font-medium">
                        {formatValue('email_click', metrics.email_click?.value || 0)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Click Rate</span>
                      <span className="font-medium">
                        {metrics.email_open?.value && metrics.email_click?.value
                          ? ((metrics.email_click.value / metrics.email_open.value) * 100).toFixed(1)
                          : 0}%
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Digital Engagement</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Page Views</span>
                      <span className="font-medium">
                        {formatValue('page_view', metrics.page_view?.value || 0)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Mobile App Opens</span>
                      <span className="font-medium">
                        {formatValue('mobile_app_open', metrics.mobile_app_open?.value || 0)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
