'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Progress } from '@kit/ui/progress';
import {
  GitBranch,
  Play,
  Pause,
  Edit,
  Trash2,
  Users,
  TrendingUp,
  Clock,
  Mail,
  MessageSquare,
  Bell,
  CheckCircle2,
  XCircle,
  AlertCircle,
  BarChart3,
} from 'lucide-react';

interface Journey {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'draft' | 'completed';
  trigger: string;
  steps: number;
  participants: number;
  completion_rate: number;
  created_at: string;
  updated_at: string;
  tags: string[];
}

interface JourneyDetailModalProps {
  journey: Journey | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (journey: Journey) => void;
  onDelete: (journeyId: string) => void;
}

export function JourneyDetailModal({
  journey,
  isOpen,
  onClose,
  onEdit,
  onDelete,
}: JourneyDetailModalProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [activeTab, setActiveTab] = useState('overview');

  if (!journey) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'paused': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'draft': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'completed': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="h-3 w-3" />;
      case 'paused': return <Pause className="h-3 w-3" />;
      case 'draft': return <Edit className="h-3 w-3" />;
      case 'completed': return <CheckCircle2 className="h-3 w-3" />;
      default: return <AlertCircle className="h-3 w-3" />;
    }
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('vi-VN');
  };

  // Mock journey steps data
  const journeySteps = [
    {
      id: '1',
      name: 'Welcome Email',
      type: 'email',
      status: 'completed',
      participants: journey.participants,
      completion_rate: 0.95,
      delay_hours: 0
    },
    {
      id: '2',
      name: 'Wait 24 Hours',
      type: 'wait',
      status: 'completed',
      participants: Math.floor(journey.participants * 0.95),
      completion_rate: 1.0,
      delay_hours: 24
    },
    {
      id: '3',
      name: 'Follow-up SMS',
      type: 'sms',
      status: 'active',
      participants: Math.floor(journey.participants * 0.85),
      completion_rate: 0.78,
      delay_hours: 0
    }
  ];

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="h-4 w-4" />;
      case 'sms': return <MessageSquare className="h-4 w-4" />;
      case 'push': return <Bell className="h-4 w-4" />;
      case 'wait': return <Clock className="h-4 w-4" />;
      default: return <GitBranch className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <GitBranch className="h-6 w-6" />
            <div>
              <h2 className="text-xl font-bold">{journey.name}</h2>
              <p className="text-sm text-muted-foreground">{journey.description}</p>
            </div>
          </DialogTitle>
          <DialogDescription>
            {t('cdp:journeys.detailModal.description')}
          </DialogDescription>
        </DialogHeader>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 border-b pb-4">
          <Badge className={`${getStatusColor(journey.status)} border`}>
            {getStatusIcon(journey.status)}
            <span className="ml-1">{t(`cdp:journeys.status.${journey.status}`)}</span>
          </Badge>
          <div className="flex-1" />
          <Button size="sm" onClick={() => onEdit(journey)}>
            <Edit className="h-4 w-4 mr-2" />
            {t('cdp:journeys.editJourney')}
          </Button>
          <Button size="sm" variant="destructive" onClick={() => onDelete(journey.id)}>
            <Trash2 className="h-4 w-4 mr-2" />
            {t('common:delete')}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">{t('cdp:journeys.tabs.overview')}</TabsTrigger>
            <TabsTrigger value="steps">{t('cdp:journeys.tabs.steps')}</TabsTrigger>
            <TabsTrigger value="analytics">{t('cdp:journeys.tabs.analytics')}</TabsTrigger>
            <TabsTrigger value="settings">{t('cdp:journeys.tabs.settings')}</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t('cdp:journeys.participants')}
                      </p>
                      <p className="text-2xl font-bold">{journey.participants.toLocaleString()}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t('cdp:journeys.completion')}
                      </p>
                      <p className="text-2xl font-bold">{(journey.completion_rate * 100).toFixed(1)}%</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t('cdp:journeys.steps')}
                      </p>
                      <p className="text-2xl font-bold">{journey.steps}</p>
                    </div>
                    <GitBranch className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t('cdp:journeys.avgDuration')}
                      </p>
                      <p className="text-2xl font-bold">3.2d</p>
                    </div>
                    <Clock className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Journey Information */}
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>{t('cdp:journeys.journeyInfo')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:journeys.trigger')}:</span>
                    <span className="text-sm">{journey.trigger}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:journeys.created')}:</span>
                    <span className="text-sm">{formatDate(journey.created_at)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:journeys.lastUpdated')}:</span>
                    <span className="text-sm">{formatDate(journey.updated_at)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('cdp:journeys.tags')}:</span>
                    <div className="flex gap-1">
                      {journey.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('cdp:journeys.performance')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">{t('cdp:journeys.completionRate')}</span>
                      <span className="text-sm font-bold">{(journey.completion_rate * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={journey.completion_rate * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">{t('cdp:journeys.engagementRate')}</span>
                      <span className="text-sm font-bold">68.5%</span>
                    </div>
                    <Progress value={68.5} className="h-2" />
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">{t('cdp:journeys.conversionRate')}</span>
                      <span className="text-sm font-bold">12.3%</span>
                    </div>
                    <Progress value={12.3} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="steps" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('cdp:journeys.journeySteps')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {journeySteps.map((step, index) => (
                    <div key={step.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-500 text-white font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {getStepIcon(step.type)}
                          <span className="font-medium">{step.name}</span>
                          <Badge variant="outline">
                            {t(`cdp:journeys.stepTypes.${step.type}`)}
                          </Badge>
                          <Badge className={step.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {t(`cdp:journeys.stepStatus.${step.status}`)}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">{t('cdp:journeys.participants')}: </span>
                            <span className="font-medium">{step.participants.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">{t('cdp:journeys.completion')}: </span>
                            <span className="font-medium">{(step.completion_rate * 100).toFixed(1)}%</span>
                          </div>
                          {step.delay_hours > 0 && (
                            <div>
                              <span className="text-muted-foreground">{t('cdp:journeys.delay')}: </span>
                              <span className="font-medium">{step.delay_hours}h</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  {t('cdp:journeys.analyticsData')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p>{t('cdp:journeys.analyticsPlaceholder')}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('cdp:journeys.journeySettings')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Edit className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p>{t('cdp:journeys.settingsPlaceholder')}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
