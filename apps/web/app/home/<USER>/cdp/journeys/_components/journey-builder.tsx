'use client';

import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Plus, 
  Mail, 
  Clock, 
  GitBranch, 
  Webhook, 
  UserCheck, 
  Settings,
  Play,
  Save,
  X,
  ArrowDown,
  ArrowRight
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';
import { Badge } from '@kit/ui/badge';
import { Separator } from '@kit/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';

interface JourneyStep {
  id: string;
  type: 'email' | 'sms' | 'wait' | 'condition' | 'webhook' | 'segment_check' | 'update_profile';
  name: string;
  config: Record<string, any>;
  position: { x: number; y: number };
}

interface JourneyBuilderProps {
  onSave: (journey: any) => Promise<void>;
  onCancel: () => void;
  initialData?: any;
}

const STEP_TYPES = [
  {
    type: 'email',
    name: 'Send Email',
    icon: Mail,
    description: 'Send a personalized email to the customer',
    color: 'bg-blue-500'
  },
  {
    type: 'sms',
    name: 'Send SMS',
    icon: Mail,
    description: 'Send an SMS message to the customer',
    color: 'bg-green-500'
  },
  {
    type: 'wait',
    name: 'Wait',
    icon: Clock,
    description: 'Wait for a specified amount of time',
    color: 'bg-yellow-500'
  },
  {
    type: 'condition',
    name: 'Condition',
    icon: GitBranch,
    description: 'Branch the journey based on customer data',
    color: 'bg-purple-500'
  },
  {
    type: 'webhook',
    name: 'Webhook',
    icon: Webhook,
    description: 'Call an external API or service',
    color: 'bg-orange-500'
  },
  {
    type: 'segment_check',
    name: 'Segment Check',
    icon: UserCheck,
    description: 'Check if customer belongs to a segment',
    color: 'bg-indigo-500'
  }
];

const TRIGGER_TYPES = [
  { value: 'segment_entry', label: 'Segment Entry', description: 'When customer enters a segment' },
  { value: 'event', label: 'Event', description: 'When a specific event occurs' },
  { value: 'date', label: 'Date/Time', description: 'At a specific date and time' },
  { value: 'manual', label: 'Manual', description: 'Manually triggered' }
];

export function JourneyBuilder({ onSave, onCancel, initialData }: JourneyBuilderProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [loading, setLoading] = useState(false);
  const [showStepDialog, setShowStepDialog] = useState(false);
  const [editingStep, setEditingStep] = useState<JourneyStep | null>(null);

  const [journeyData, setJourneyData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || '',
    trigger_type: initialData?.trigger_type || 'segment_entry',
    trigger_config: initialData?.trigger_config || {},
  });

  const [steps, setSteps] = useState<JourneyStep[]>(
    initialData?.steps || []
  );

  const addStep = useCallback((stepType: string) => {
    const newStep: JourneyStep = {
      id: `step_${Date.now()}`,
      type: stepType as any,
      name: STEP_TYPES.find(t => t.type === stepType)?.name || stepType,
      config: {},
      position: { x: 0, y: steps.length * 120 }
    };

    setSteps(prev => [...prev, newStep]);
    setEditingStep(newStep);
    setShowStepDialog(false);
  }, [steps.length]);

  const updateStep = useCallback((stepId: string, updates: Partial<JourneyStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  }, []);

  const removeStep = useCallback((stepId: string) => {
    setSteps(prev => prev.filter(step => step.id !== stepId));
  }, []);

  const handleSave = async () => {
    if (!journeyData.name.trim()) {
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...journeyData,
        steps: steps.map(({ position, ...step }) => step) // Remove position for API
      });
    } catch (error) {
      console.error('Failed to save journey:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStepIcon = (type: string) => {
    const stepType = STEP_TYPES.find(t => t.type === type);
    return stepType?.icon || Settings;
  };

  const getStepColor = (type: string) => {
    const stepType = STEP_TYPES.find(t => t.type === type);
    return stepType?.color || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* Journey Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Journey Settings
          </CardTitle>
          <CardDescription>
            Configure the basic settings for your customer journey
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Journey Name *</Label>
              <Input
                id="name"
                placeholder="Welcome Onboarding"
                value={journeyData.name}
                onChange={(e) => setJourneyData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="trigger">Trigger Type</Label>
              <Select
                value={journeyData.trigger_type}
                onValueChange={(value) => setJourneyData(prev => ({ ...prev, trigger_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select trigger" />
                </SelectTrigger>
                <SelectContent>
                  {TRIGGER_TYPES.map(trigger => (
                    <SelectItem key={trigger.value} value={trigger.value}>
                      <div>
                        <div className="font-medium">{trigger.label}</div>
                        <div className="text-xs text-muted-foreground">{trigger.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe what this journey does..."
              value={journeyData.description}
              onChange={(e) => setJourneyData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Journey Flow */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Journey Flow
              </CardTitle>
              <CardDescription>
                Design the steps customers will go through
              </CardDescription>
            </div>
            <Dialog open={showStepDialog} onOpenChange={setShowStepDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Step
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Journey Step</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4">
                  {STEP_TYPES.map(stepType => {
                    const Icon = stepType.icon;
                    return (
                      <Button
                        key={stepType.type}
                        variant="outline"
                        className="h-auto p-4 justify-start"
                        onClick={() => addStep(stepType.type)}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded ${stepType.color} text-white`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div className="text-left">
                            <div className="font-medium">{stepType.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {stepType.description}
                            </div>
                          </div>
                        </div>
                      </Button>
                    );
                  })}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {steps.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <GitBranch className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No steps added yet</h3>
              <p className="mb-4">Add your first step to start building the journey</p>
              <Button onClick={() => setShowStepDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add First Step
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Journey Start */}
              <div className="flex items-center gap-4">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-500 text-white">
                  <Play className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">Journey Start</div>
                  <div className="text-sm text-muted-foreground">
                    Trigger: {TRIGGER_TYPES.find(t => t.value === journeyData.trigger_type)?.label}
                  </div>
                </div>
              </div>

              {/* Steps */}
              {steps.map((step, index) => {
                const Icon = getStepIcon(step.type);
                return (
                  <div key={step.id}>
                    {/* Connector */}
                    <div className="flex justify-center">
                      <ArrowDown className="h-6 w-6 text-muted-foreground" />
                    </div>

                    {/* Step */}
                    <div className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/50">
                      <div className={`flex items-center justify-center w-10 h-10 rounded-full text-white ${getStepColor(step.type)}`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{step.name}</span>
                          <Badge variant="outline">{step.type}</Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Step {index + 1} • Click to configure
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingStep(step)}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeStep(step.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Journey End */}
              <div className="flex justify-center">
                <ArrowDown className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-500 text-white">
                  <X className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">Journey End</div>
                  <div className="text-sm text-muted-foreground">
                    Customer completes the journey
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave} disabled={loading || !journeyData.name.trim()}>
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Journey
            </>
          )}
        </Button>
      </div>

      {/* Step Configuration Dialog */}
      <Dialog open={!!editingStep} onOpenChange={() => setEditingStep(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Step: {editingStep?.name}</DialogTitle>
          </DialogHeader>
          {editingStep && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="stepName">Step Name</Label>
                <Input
                  id="stepName"
                  value={editingStep.name}
                  onChange={(e) => {
                    const updated = { ...editingStep, name: e.target.value };
                    setEditingStep(updated);
                    updateStep(editingStep.id, { name: e.target.value });
                  }}
                />
              </div>

              {/* Step-specific configuration */}
              {editingStep.type === 'email' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Email Template</Label>
                    <Input placeholder="welcome_email" />
                  </div>
                  <div className="space-y-2">
                    <Label>Subject Line</Label>
                    <Input placeholder="Welcome to our platform!" />
                  </div>
                </div>
              )}

              {editingStep.type === 'wait' && (
                <div className="space-y-2">
                  <Label>Wait Duration (hours)</Label>
                  <Input type="number" placeholder="24" />
                </div>
              )}

              {editingStep.type === 'condition' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Condition</Label>
                    <Input placeholder="engagement_score > 0.5" />
                  </div>
                  <div className="grid gap-2 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>True Path</Label>
                      <Input placeholder="Next step if true" />
                    </div>
                    <div className="space-y-2">
                      <Label>False Path</Label>
                      <Input placeholder="Next step if false" />
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingStep(null)}>
                  Cancel
                </Button>
                <Button onClick={() => setEditingStep(null)}>
                  Save Configuration
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
