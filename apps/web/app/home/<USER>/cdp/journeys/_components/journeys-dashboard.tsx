'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Plus, 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  GitBranch,
  Users,
  TrendingUp,
  Activity,
  Clock
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';

import { JourneyBuilder } from './journey-builder';

interface CustomerJourney {
  id: string;
  name: string;
  description?: string;
  trigger_type: string;
  is_active: boolean;
  is_draft: boolean;
  total_entries: number;
  total_completions: number;
  total_exits: number;
  conversion_rate: number;
  steps: any[];
  created_at: string;
  updated_at: string;
  published_at?: string;
}

interface JourneysDashboardProps {
  accountId: string;
}

export function JourneysDashboard({ accountId }: JourneysDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [journeys, setJourneys] = useState<CustomerJourney[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingJourney, setEditingJourney] = useState<CustomerJourney | null>(null);

  // Load journeys
  const loadJourneys = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/cdp/journeys');
      const data = await response.json();
      
      if (data.success) {
        setJourneys(data.data || []);
      } else {
        setError(data.error || 'Failed to load journeys');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Create journey
  const createJourney = async (journeyData: any) => {
    try {
      const response = await fetch('/api/cdp/journeys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(journeyData),
      });

      const data = await response.json();

      if (data.success) {
        setShowBuilder(false);
        await loadJourneys();
      } else {
        throw new Error(data.error || 'Failed to create journey');
      }
    } catch (error) {
      console.error('Failed to create journey:', error);
      throw error;
    }
  };

  // Update journey
  const updateJourney = async (journeyId: string, updates: any) => {
    try {
      const response = await fetch(`/api/cdp/journeys/${journeyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const data = await response.json();

      if (data.success) {
        setEditingJourney(null);
        await loadJourneys();
      } else {
        throw new Error(data.error || 'Failed to update journey');
      }
    } catch (error) {
      console.error('Failed to update journey:', error);
      throw error;
    }
  };

  // Delete journey
  const deleteJourney = async (journeyId: string) => {
    if (!confirm('Are you sure you want to delete this journey?')) {
      return;
    }

    try {
      const response = await fetch(`/api/cdp/journeys/${journeyId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        await loadJourneys();
      } else {
        setError(data.error || 'Failed to delete journey');
      }
    } catch (error) {
      setError('Network error occurred');
    }
  };

  // Toggle journey status
  const toggleJourneyStatus = async (journey: CustomerJourney) => {
    await updateJourney(journey.id, { is_active: !journey.is_active });
  };

  useEffect(() => {
    loadJourneys();
  }, []);

  // Calculate dashboard stats
  const stats = {
    totalJourneys: journeys.length,
    activeJourneys: journeys.filter(j => j.is_active).length,
    draftJourneys: journeys.filter(j => j.is_draft).length,
    totalCustomers: journeys.reduce((sum, j) => sum + j.total_entries, 0),
    avgConversion: journeys.length > 0 
      ? journeys.reduce((sum, j) => sum + j.conversion_rate, 0) / journeys.length 
      : 0,
  };

  const getTriggerLabel = (triggerType: string) => {
    const triggers = {
      segment_entry: 'Segment Entry',
      event: 'Event',
      date: 'Date/Time',
      manual: 'Manual'
    };
    return triggers[triggerType as keyof typeof triggers] || triggerType;
  };

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Journeys</CardTitle>
            <GitBranch className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalJourneys}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeJourneys} active, {stats.draftJourneys} draft
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Journey Entries</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCustomers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Customers in journeys
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Journeys</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeJourneys}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Conversion</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(stats.avgConversion * 100).toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Journey completion rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Journeys</h2>
          <p className="text-muted-foreground">
            Create and manage automated customer journey workflows
          </p>
        </div>
        <Dialog open={showBuilder} onOpenChange={setShowBuilder}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Journey
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Journey</DialogTitle>
            </DialogHeader>
            <JourneyBuilder
              onSave={createJourney}
              onCancel={() => setShowBuilder(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Journeys List */}
      <Card>
        <CardHeader>
          <CardTitle>All Journeys</CardTitle>
          <CardDescription>
            Manage your customer journeys and their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : journeys.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <GitBranch className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No journeys found</h3>
              <p className="mb-4">Create your first customer journey to get started</p>
              <Button onClick={() => setShowBuilder(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create First Journey
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {journeys.map((journey) => (
                <div
                  key={journey.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-500 text-white">
                      <GitBranch className="h-5 w-5" />
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{journey.name}</h3>
                        <Badge variant={journey.is_active ? 'default' : 'secondary'}>
                          {journey.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {journey.is_draft && (
                          <Badge variant="outline">Draft</Badge>
                        )}
                        <Badge variant="outline">{getTriggerLabel(journey.trigger_type)}</Badge>
                      </div>
                      {journey.description && (
                        <p className="text-sm text-muted-foreground">
                          {journey.description}
                        </p>
                      )}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{journey.steps.length} steps</span>
                        <span>{journey.total_entries.toLocaleString()} entries</span>
                        <span>{(journey.conversion_rate * 100).toFixed(1)}% conversion</span>
                        <span>Updated {new Date(journey.updated_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setEditingJourney(journey)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => toggleJourneyStatus(journey)}>
                        {journey.is_active ? (
                          <>
                            <Pause className="mr-2 h-4 w-4" />
                            Deactivate
                          </>
                        ) : (
                          <>
                            <Play className="mr-2 h-4 w-4" />
                            Activate
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => deleteJourney(journey.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Journey Dialog */}
      <Dialog open={!!editingJourney} onOpenChange={() => setEditingJourney(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Journey</DialogTitle>
          </DialogHeader>
          {editingJourney && (
            <JourneyBuilder
              initialData={editingJourney}
              onSave={(data) => updateJourney(editingJourney.id, data)}
              onCancel={() => setEditingJourney(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
