'use client';

import { useState } from 'react';

import {
  <PERSON>ert<PERSON>ircle,
  CheckCircle2,
  Copy,
  Download,
  Edit,
  Eye,
  Filter,
  GitBranch,
  MoreHorizontal,
  Pause,
  Play,
  Plus,
  RefreshCw,
  Search,
  Trash2,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { Input } from '@kit/ui/input';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { CreateJourneyModal } from './create-journey-modal';
import { JourneyDetailModal } from './journey-detail-modal';

interface Journey {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'draft' | 'completed';
  trigger: string;
  steps: number;
  participants: number;
  completion_rate: number;
  created_at: string;
  updated_at: string;
  tags: string[];
}

interface JourneysDashboardProps {
  accountId: string;
  accountSlug: string;
  activeTab: string;
  statusFilter: string;
  user: any;
  account: any;
}

export function JourneysDashboard({
  accountId,
  accountSlug,
  activeTab: initialTab,
  statusFilter,
  user,
  account,
}: JourneysDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [activeTab, setActiveTab] = useState(initialTab);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedJourney, setSelectedJourney] = useState<Journey | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Mock data - replace with real data
  const journeys: Journey[] = [
    {
      id: '1',
      name: 'Welcome New Customers',
      description: 'Onboarding journey for new customer registration',
      status: 'active',
      trigger: 'Customer Registration',
      steps: 5,
      participants: 1247,
      completion_rate: 0.78,
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-03-20T14:22:00Z',
      tags: ['onboarding', 'email'],
    },
    {
      id: '2',
      name: 'Cart Abandonment Recovery',
      description: 'Re-engage customers who abandoned their cart',
      status: 'active',
      trigger: 'Cart Abandonment',
      steps: 3,
      participants: 856,
      completion_rate: 0.45,
      created_at: '2024-02-10T09:15:00Z',
      updated_at: '2024-03-19T16:45:00Z',
      tags: ['recovery', 'sms', 'email'],
    },
    {
      id: '3',
      name: 'VIP Customer Rewards',
      description: 'Special rewards program for high-value customers',
      status: 'paused',
      trigger: 'VIP Status Achieved',
      steps: 4,
      participants: 234,
      completion_rate: 0.92,
      created_at: '2024-03-05T11:20:00Z',
      updated_at: '2024-03-18T13:30:00Z',
      tags: ['vip', 'rewards'],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Play className="h-3 w-3" />;
      case 'paused':
        return <Pause className="h-3 w-3" />;
      case 'draft':
        return <Edit className="h-3 w-3" />;
      case 'completed':
        return <CheckCircle2 className="h-3 w-3" />;
      default:
        return <AlertCircle className="h-3 w-3" />;
    }
  };

  const filteredJourneys = journeys.filter((journey) => {
    const matchesTab = activeTab === 'all' || journey.status === activeTab;
    const matchesSearch =
      !searchQuery ||
      journey.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      journey.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesTab && matchesSearch;
  });

  const stats = {
    total: journeys.length,
    active: journeys.filter((j) => j.status === 'active').length,
    totalParticipants: journeys.reduce((sum, j) => sum + j.participants, 0),
    avgCompletion:
      journeys.reduce((sum, j) => sum + j.completion_rate, 0) / journeys.length,
  };

  // Action handlers
  const handleViewJourney = (journey: Journey) => {
    setSelectedJourney(journey);
    setIsDetailModalOpen(true);
  };

  const handleEditJourney = (journey: Journey) => {
    console.log('Edit journey:', journey);
  };

  const handleDuplicateJourney = (journey: Journey) => {
    console.log('Duplicate journey:', journey);
  };

  const handleDeleteJourney = (journeyId: string) => {
    console.log('Delete journey:', journeyId);
  };

  const handlePlayJourney = (journeyId: string) => {
    console.log('Play journey:', journeyId);
  };

  const handlePauseJourney = (journeyId: string) => {
    console.log('Pause journey:', journeyId);
  };

  const handleCreateJourney = async (data: any) => {
    console.log('Create journey:', data);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('cdp:journeys.title')}</h1>
          <p className="text-muted-foreground">
            {t('cdp:journeys.description')}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            {t('common:export')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            {t('common:refresh')}
          </Button>
          <Button size="sm" onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            {t('cdp:journeys.createJourney.title')}
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">
                  {t('cdp:journeys.totalJourneys')}
                </p>
                <p className="text-2xl font-bold text-blue-700">
                  {stats.total}
                </p>
              </div>
              <div className="rounded-xl bg-blue-500 p-3">
                <GitBranch className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-green-50 to-green-100 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">
                  {t('cdp:journeys.activeJourneys')}
                </p>
                <p className="text-2xl font-bold text-green-700">
                  {stats.active}
                </p>
              </div>
              <div className="rounded-xl bg-green-500 p-3">
                <Play className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">
                  {t('cdp:journeys.totalParticipants')}
                </p>
                <p className="text-2xl font-bold text-purple-700">
                  {stats.totalParticipants.toLocaleString()}
                </p>
              </div>
              <div className="rounded-xl bg-purple-500 p-3">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">
                  {t('cdp:journeys.avgCompletion')}
                </p>
                <p className="text-2xl font-bold text-orange-700">
                  {(stats.avgCompletion * 100).toFixed(1)}%
                </p>
              </div>
              <div className="rounded-xl bg-orange-500 p-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row">
            <div className="relative flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
              <Input
                placeholder={t('cdp:journeys.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex items-center gap-3">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList>
                  <TabsTrigger value="all">
                    {t('cdp:journeys.filters.all')}
                  </TabsTrigger>
                  <TabsTrigger value="active">
                    {t('cdp:journeys.filters.active')}
                  </TabsTrigger>
                  <TabsTrigger value="paused">
                    {t('cdp:journeys.filters.paused')}
                  </TabsTrigger>
                  <TabsTrigger value="draft">
                    {t('cdp:journeys.filters.draft')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                {t('cdp:journeys.filters.advanced')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Journeys List */}
      <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {filteredJourneys.map((journey) => (
          <Card
            key={journey.id}
            className="group border-0 shadow-lg transition-all duration-300 hover:shadow-xl"
          >
            <CardContent className="p-6">
              {/* Journey Header */}
              <div className="mb-4 flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="mb-2 text-lg font-semibold transition-colors group-hover:text-blue-600">
                    {journey.name}
                  </h3>
                  <p className="text-muted-foreground mb-3 text-sm">
                    {journey.description}
                  </p>
                  <div className="flex items-center gap-2">
                    <Badge
                      className={`${getStatusColor(journey.status)} border text-xs`}
                    >
                      {getStatusIcon(journey.status)}
                      <span className="ml-1">
                        {t(`cdp:journeys.status.${journey.status}`)}
                      </span>
                    </Badge>
                  </div>
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem
                      onClick={() => handleViewJourney(journey)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      {t('cdp:journeys.viewDetails')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleEditJourney(journey)}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('cdp:journeys.editJourney')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleDuplicateJourney(journey)}
                    >
                      <Copy className="mr-2 h-4 w-4" />
                      {t('cdp:journeys.duplicateJourney')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleDeleteJourney(journey.id)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t('common:delete')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Journey Stats */}
              <div className="mb-4 grid grid-cols-2 gap-4">
                <div className="bg-muted/50 rounded-lg p-3 text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {journey.participants.toLocaleString()}
                  </div>
                  <div className="text-muted-foreground text-xs">
                    {t('cdp:journeys.participants')}
                  </div>
                </div>

                <div className="bg-muted/50 rounded-lg p-3 text-center">
                  <div className="text-lg font-bold text-green-600">
                    {(journey.completion_rate * 100).toFixed(1)}%
                  </div>
                  <div className="text-muted-foreground text-xs">
                    {t('cdp:journeys.completion')}
                  </div>
                </div>
              </div>

              {/* Journey Info */}
              <div className="mb-4 space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    {t('cdp:journeys.trigger')}:
                  </span>
                  <span className="font-medium">{journey.trigger}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    {t('cdp:journeys.steps')}:
                  </span>
                  <span className="font-medium">{journey.steps}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                {journey.status === 'active' ? (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handlePauseJourney(journey.id)}
                  >
                    <Pause className="mr-2 h-4 w-4" />
                    {t('cdp:journeys.pause')}
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handlePlayJourney(journey.id)}
                  >
                    <Play className="mr-2 h-4 w-4" />
                    {t('cdp:journeys.start')}
                  </Button>
                )}
                <Button
                  size="sm"
                  className="flex-1"
                  onClick={() => handleViewJourney(journey)}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  {t('cdp:journeys.analyze')}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredJourneys.length === 0 && (
        <Card className="border-0 shadow-lg">
          <CardContent className="p-12 text-center">
            <GitBranch className="text-muted-foreground mx-auto mb-4 h-16 w-16" />
            <h3 className="mb-2 text-xl font-semibold">
              {t('cdp:journeys.emptyState.title')}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchQuery
                ? t('cdp:journeys.emptyState.searchEmpty')
                : t('cdp:journeys.emptyState.description')}
            </p>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              {t('cdp:journeys.createJourney.title')}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Modals */}
      <CreateJourneyModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateJourney}
      />

      <JourneyDetailModal
        journey={selectedJourney}
        isOpen={isDetailModalOpen}
        onClose={() => {
          setIsDetailModalOpen(false);
          setSelectedJourney(null);
        }}
        onEdit={handleEditJourney}
        onDelete={handleDeleteJourney}
      />
    </div>
  );
}
