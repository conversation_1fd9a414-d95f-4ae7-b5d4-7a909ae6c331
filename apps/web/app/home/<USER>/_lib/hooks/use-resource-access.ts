'use client';

import { useParams } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';

import {
  checkCanCreateResource,
  checkCanReadResource,
  checkCanUpdateResource,
  checkCanDeleteResource,
  checkCanSendZns,
  getResourceUsage,
} from '~/home/<USER>/_lib/server/resource-access';

/**
 * Hook để kiểm tra quyền tạo tài nguyên
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export function useResourceLimits(resourceType: string) {
  const { account } = useParams();
  const accountId = account as string;

  const { data, isLoading } = useQuery({
    queryKey: [`can-create-${resourceType}`, accountId],
    queryFn: () => checkCanCreateResource(accountId, resourceType),
  });

  return { canCreate: data?.canCreate || false, isLoading, limits: data };
}

/**
 * Hook để kiểm tra quyền đọc tài nguyên
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export function useCanReadResource(resourceType: string) {
  const { account } = useParams();
  const accountId = account as string;

  const { data, isLoading } = useQuery({
    queryKey: [`can-read-${resourceType}`, accountId],
    queryFn: () => checkCanReadResource(accountId, resourceType),
  });

  return { canRead: data?.canRead || false, isLoading, reason: data?.reason };
}

/**
 * Hook để kiểm tra quyền cập nhật tài nguyên
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export function useCanUpdateResource(resourceType: string) {
  const { account } = useParams();
  const accountId = account as string;

  const { data, isLoading } = useQuery({
    queryKey: [`can-update-${resourceType}`, accountId],
    queryFn: () => checkCanUpdateResource(accountId, resourceType),
  });

  return { canUpdate: data?.canUpdate || false, isLoading, reason: data?.reason };
}

/**
 * Hook để kiểm tra quyền xóa tài nguyên
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export function useCanDeleteResource(resourceType: string) {
  const { account } = useParams();
  const accountId = account as string;

  const { data, isLoading } = useQuery({
    queryKey: [`can-delete-${resourceType}`, accountId],
    queryFn: () => checkCanDeleteResource(accountId, resourceType),
  });

  return { canDelete: data?.canDelete || false, isLoading, reason: data?.reason };
}

/**
 * Hook để kiểm tra quyền gửi ZNS
 */
export function useZnsCredits() {
  const { account } = useParams();
  const accountId = account as string;

  const { data, isLoading } = useQuery({
    queryKey: ['zns-credits', accountId],
    queryFn: () => checkCanSendZns(accountId),
  });

  return {
    canSend: data?.canSend || false,
    isLoading,
    currentCredits: data?.currentCredits,
    reason: data?.reason,
  };
}

/**
 * Hook để lấy thống kê sử dụng tài nguyên
 */
export function useUsageStats() {
  const { account } = useParams();
  const accountId = account as string;

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['usage-stats', accountId],
    queryFn: () => getResourceUsage(accountId),
  });

  return {
    usage: data || { resources: {}, znsCredits: 0, znsLimit: 0 },
    isLoading,
    refetch,
  };
}

/**
 * Hook để lấy thông tin sử dụng tài nguyên
 * @param customAccountId ID tài khoản tùy chỉnh (nếu không cung cấp, sẽ lấy từ params)
 */
export function useResourceUsage(customAccountId?: string) {
  const { account } = useParams();
  const accountId = customAccountId || (account as string);

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['resource-usage', accountId],
    queryFn: () => getResourceUsage(accountId),
  });

  return {
    resourceUsage: data?.resources || {},
    znsCredits: data?.znsCredits || 0,
    znsLimit: data?.znsLimit || 0,
    lastUpdated: data?.lastUpdated,
    isLoading,
    refetch,
  };
}
