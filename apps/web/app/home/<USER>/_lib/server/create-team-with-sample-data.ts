import 'server-only';

import { revalidatePath } from 'next/cache';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { sampleDataTemplates } from '~/config/mockup.config';

interface CreateTeamWithSampleDataParams {
  name: string;
  industry: keyof typeof sampleDataTemplates;
  userId: string;
}

export async function createTeamWithSampleData({
  name,
  industry,
  userId,
}: CreateTeamWithSampleDataParams) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();
  const adminClient = getSupabaseServerAdminClient();

  try {
    logger.info({ name, industry, userId }, 'Creating team with sample data');

    // 1. Create the team account
    const { data: teamData, error: teamError } = await supabase.rpc(
      'create_team_account',
      {
        account_name: name,
      },
    );

    if (teamError || !teamData) {
      logger.error({ error: teamError }, 'Failed to create team account');
      throw new Error('Failed to create team account');
    }

    const teamId = teamData.id;
    const teamSlug = teamData.slug;

    logger.info({ teamId, teamSlug }, 'Team account created successfully');
    // 2. Create sample data based on the selected industry
    if (industry && sampleDataTemplates[industry]) {
      try {
        // Get the sample data template
        const templateData = sampleDataTemplates[industry];

        // Make sure we're sending the right structure
        // The SQL function expects arrays directly in the root of the JSON
        const dataToSend = {
          categories: templateData.categories || [],
          products: templateData.products || [],
          branches: templateData.branches || [],
          branch_products: templateData.branch_products || [],
          customer_orders: templateData.customer_orders || [],
          account_themes: templateData.account_themes || [],
        };

        // Convert the sample data template to a JSON string
        const industryData = JSON.stringify(dataToSend);

        const { data: sampleData, error: sampleError } = await adminClient
          .rpc('create_sample_data_for_account', {
            p_account_id: teamId,
            p_industry_template: industryData,
          })
          .single();

        console.log('Sample data creation response:', {
          sampleData,
          sampleError,
        });

        if (sampleError || !sampleData || !sampleData.success) {
          logger.error(
            {
              error: sampleError,
              errorMessage: sampleError?.message,
              errorDetails: sampleError?.details,
              errorCode: sampleError?.code,
              hint: sampleError?.hint,
              sampleData,
            },
            'Failed to create sample data',
          );

          // Log the full error object for debugging
          console.error(
            'Sample data creation error:',
            JSON.stringify(sampleError, null, 2),
          );

          // Throw error to fail the team creation if sample data creation fails
          throw new Error('Failed to create sample data for the team');
        } else {
          logger.info({ sampleData }, 'Sample data created successfully');
          console.log('Sample data created successfully:', sampleData);

          // Check if sample data was actually created
          try {
            // Query products to see if any were created
            const { data: products, error: productsError } = await adminClient
              .from('products')
              .select('id, name, category_id, price')
              .eq('account_id', teamId);

            console.log('Products query result:', {
              productsCount: products?.length || 0,
              productsError,
              firstProduct: products?.[0] || null,
            });

            logger.info(
              {
                productsCount: products?.length || 0,
                productsError,
                firstProduct: products?.[0] || null,
              },
              'Products query result',
            );

            // Query categories to see if any were created
            const { data: categories, error: categoriesError } =
              await adminClient
                .from('categories')
                .select('id, name, description')
                .eq('account_id', teamId);

            console.log('Categories query result:', {
              categoriesCount: categories?.length || 0,
              categoriesError,
              firstCategory: categories?.[0] || null,
            });

            logger.info(
              {
                categoriesCount: categories?.length || 0,
                categoriesError,
                firstCategory: categories?.[0] || null,
              },
              'Categories query result',
            );

            // If no data was created, log a warning and throw an error
            if (
              (!products || products.length === 0) &&
              (!categories || categories.length === 0)
            ) {
              logger.warn(
                { teamId, industry },
                'No sample data was created for the account',
              );
              console.warn('No sample data was created for the account:', {
                teamId,
                industry,
              });

              // Try to get more information about the account
              const { data: account } = await adminClient
                .from('accounts')
                .select('id, name, slug, primary_owner_user_id')
                .eq('id', teamId)
                .single();

              logger.info({ account }, 'Account information');
              console.log('Account information:', account);

              // Try to get more information about the user's membership
              const { data: membership } = await adminClient
                .from('accounts_memberships')
                .select('user_id, account_id, account_role')
                .eq('account_id', teamId)
                .eq('user_id', userId);

              logger.info({ membership }, 'User membership information');
              console.log('User membership information:', membership);

              // Throw error to fail the team creation if no sample data was created
              throw new Error('No sample data was created for the team');
            }

            // Wait for a moment to ensure data is fully committed
            await new Promise((resolve) => setTimeout(resolve, 1000));
          } catch (error) {
            logger.error({ error }, 'Error checking sample data');
            console.error('Error checking sample data:', error);
            throw error; // Re-throw to fail the team creation
          }
        }
      } catch (error) {
        logger.error({ error }, 'Error creating sample data');
        // Continue even if sample data creation fails
      }
    }

    // Revalidate the home page to update the user workspace data
    revalidatePath('/home');

    return {
      success: true,
      teamId,
      teamSlug,
    };
  } catch (error) {
    logger.error({ error }, 'Failed to create team with sample data');
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
