'use server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

/**
 * Kiểm tra quyền truy cập menu
 * @param accountId ID của tài khoản
 * @param permission Tên quyền đầy đủ (orders.manage, customers.manage, billing.manage, v.v.)
 * @param userId ID của người dùng
 * @returns Kết quả kiểm tra quyền truy cập
 */
export async function checkMenuAccess(accountId: string, permission: string, userId: string) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();
  const teamAccountsApi = createTeamAccountsApi(supabase);

  try {
    // Kiểm tra userId được truyền vào
    if (!userId) {
      return {
        canAccess: false,
        reason: 'not_authenticated',
      };
    }

    try {
      // Kiểm tra quyền sử dụng hàm hasPermission
      const hasPermission = await teamAccountsApi.hasPermission({
        accountId,
        userId,
        permission: permission as any,
      });

      // Đặc biệt kiểm tra billing.manage
      if (permission === 'billing.manage' && !hasPermission) {
        return {
          canAccess: false,
          reason: 'no_permission'
        };
      }

      return {
        canAccess: hasPermission,
        reason: hasPermission ? undefined : 'no_permission',
      };
    } catch (error) {
      logger.error(
        { error, accountId, permission, userId },
        'Error checking menu permission',
      );

      return {
        canAccess: false,
        reason: 'permission_error',
      };
    }
  } catch (error) {
    logger.error(
      { error, accountId, permission },
      'Error checking menu access',
    );

    return {
      canAccess: false,
      reason: 'unknown_error',
    };
  }
}

/**
 * Kiểm tra quyền truy cập cho nhiều menu
 * @param accountId ID của tài khoản
 * @param permissions Danh sách quyền đầy đủ cần kiểm tra (orders.manage, customers.manage, v.v.)
 * @param userId ID của người dùng
 * @returns Kết quả kiểm tra quyền truy cập cho từng quyền
 */
export async function checkMultipleMenuAccess(
  accountId: string,
  permissions: string[],
  userId: string,
) {
  const results: Record<string, { canAccess: boolean; reason?: string }> = {};

  await Promise.all(
    permissions.map(async (permission) => {
      results[permission] = await checkMenuAccess(accountId, permission, userId);
    }),
  );

  return results;
}
