'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

/**
 * L<PERSON>y dữ liệu lượt xem trang theo ngày
 * @param accountId ID của tài k<PERSON>n
 * @param days Số ngày cần lấy dữ liệu (mặc định: 30)
 * @returns Dữ liệu lượt xem trang theo ngày
 */
export async function getDailyPageviews(accountId: string, days: number = 30) {
  try {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    
    // Lấy ngày bắt đầu
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const startDateStr = startDate.toISOString().split('T')[0];
    
    // Truy vấn dữ liệu từ view daily_pageviews
    const { data, error } = await supabase
      .from('daily_pageviews')
      .select('*')
      .eq('account_id', accountId)
      .gte('date', startDateStr)
      .order('date', { ascending: true });
    
    if (error) {
      logger.error({ error, accountId }, 'Error fetching daily pageviews');
      return [];
    }
    
    return data || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, accountId }, 'Unexpected error in getDailyPageviews');
    return [];
  }
}

/**
 * Lấy dữ liệu sự kiện sản phẩm theo ngày
 * @param accountId ID của tài khoản
 * @param days Số ngày cần lấy dữ liệu (mặc định: 30)
 * @returns Dữ liệu sự kiện sản phẩm theo ngày
 */
export async function getDailyProductEvents(accountId: string, days: number = 30) {
  try {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    
    // Lấy ngày bắt đầu
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const startDateStr = startDate.toISOString().split('T')[0];
    
    // Truy vấn dữ liệu từ view daily_product_events
    const { data, error } = await supabase
      .from('daily_product_events')
      .select('*')
      .eq('account_id', accountId)
      .gte('date', startDateStr)
      .order('date', { ascending: true });
    
    if (error) {
      logger.error({ error, accountId }, 'Error fetching daily product events');
      return [];
    }
    
    return data || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, accountId }, 'Unexpected error in getDailyProductEvents');
    return [];
  }
}

/**
 * Lấy dữ liệu đơn hàng theo ngày
 * @param accountId ID của tài khoản
 * @param days Số ngày cần lấy dữ liệu (mặc định: 30)
 * @returns Dữ liệu đơn hàng theo ngày
 */
export async function getDailyOrders(accountId: string, days: number = 30) {
  try {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    
    // Lấy ngày bắt đầu
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const startDateStr = startDate.toISOString().split('T')[0];
    
    // Truy vấn dữ liệu từ view daily_orders
    const { data, error } = await supabase
      .from('daily_orders')
      .select('*')
      .eq('account_id', accountId)
      .gte('date', startDateStr)
      .order('date', { ascending: true });
    
    if (error) {
      logger.error({ error, accountId }, 'Error fetching daily orders');
      return [];
    }
    
    return data || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, accountId }, 'Unexpected error in getDailyOrders');
    return [];
  }
}

/**
 * Lấy dữ liệu tổng quan cho dashboard
 * @param accountId ID của tài khoản
 * @returns Dữ liệu tổng quan cho dashboard
 */
export async function getDashboardOverview(accountId: string) {
  try {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    
    // Lấy ngày bắt đầu (30 ngày trước)
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    const startDateStr = startDate.toISOString().split('T')[0];
    
    // Truy vấn dữ liệu tổng quan
    const { data, error } = await supabase.rpc('get_dashboard_overview', {
      p_account_id: accountId,
      p_start_date: startDateStr,
    });
    
    if (error) {
      logger.error({ error, accountId }, 'Error fetching dashboard overview');
      return {
        total_visitors: 0,
        total_pageviews: 0,
        total_orders: 0,
        total_revenue: 0,
        conversion_rate: 0,
      };
    }
    
    return data || {
      total_visitors: 0,
      total_pageviews: 0,
      total_orders: 0,
      total_revenue: 0,
      conversion_rate: 0,
    };
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, accountId }, 'Unexpected error in getDashboardOverview');
    return {
      total_visitors: 0,
      total_pageviews: 0,
      total_orders: 0,
      total_revenue: 0,
      conversion_rate: 0,
    };
  }
}
