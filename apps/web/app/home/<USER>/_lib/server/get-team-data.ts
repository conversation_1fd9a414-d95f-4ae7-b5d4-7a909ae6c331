'use server';

import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function getTeamMembersCount(teamId: string) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    // Get team data
    const membersCount = await api.getMembersCount(teamId);
    
    return { success: true, membersCount };
  } catch (error) {
    console.error('Error getting team members count:', error);
    return { success: false, error: 'Failed to get team members count' };
  }
}

export async function getTeamSubscription(teamId: string) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    // Get subscription data
    const subscription = await api.getSubscription(teamId);
    
    return { success: true, subscription };
  } catch (error) {
    console.error('Error getting team subscription:', error);
    return { success: false, error: 'Failed to get team subscription' };
  }
}
