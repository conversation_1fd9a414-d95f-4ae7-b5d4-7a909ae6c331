'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { TeamNameSchema } from '@kit/team-accounts/schema/create-team.schema';

import { sampleDataTemplates } from '~/config/mockup.config';

import { createTeamWithSampleData } from './create-team-with-sample-data';

// Create schema for team creation with industry
const CreateTeamWithIndustrySchema = z.object({
  name: TeamNameSchema,
  industry: z.enum(Object.keys(sampleDataTemplates) as [string, ...string[]]),
});

/**
 * @name createTeamWithSampleDataAction
 * @description Creates a team with sample data based on the selected industry
 */
export const createTeamWithSampleDataAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const ctx = {
      name: 'team-accounts.create-with-sample-data',
      userId: user.id,
      teamName: data.name,
      industry: data.industry,
    };

    logger.info(ctx, 'Creating team with sample data...');

    // Create the team with sample data
    const result = await createTeamWithSampleData({
      name: data.name,
      industry: data.industry as keyof typeof sampleDataTemplates,
      userId: user.id,
    });

    if (!result.success) {
      logger.error(
        { ...ctx, error: result.error },
        'Failed to create team with sample data',
      );

      return {
        success: false,
        error: result.error,
      };
    }

    logger.info(
      { ...ctx, teamId: result.teamId, teamSlug: result.teamSlug },
      'Team with sample data created successfully',
    );

    // Revalidate the home page to show the new team
    revalidatePath('/home');

    // Return the team slug instead of redirecting
    // This allows the client to handle navigation
    return {
      success: true,
      teamSlug: result.teamSlug,
      teamId: result.teamId
    };
  },
  {
    schema: CreateTeamWithIndustrySchema,
  },
);

/**
 * @name createTeamWithSampleDataFormAction
 * @description Form action version that parses FormData
 */
export async function createTeamWithSampleDataFormAction(formData: FormData) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();

  try {
    // Get the current user
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Parse and validate the form data
    const name = formData.get('name') as string;
    const industry = formData.get('industry') as string;

    const validatedData = CreateTeamWithIndustrySchema.parse({
      name,
      industry,
    });

    // Create the team with sample data
    const result = await createTeamWithSampleData({
      name: validatedData.name,
      industry: validatedData.industry as keyof typeof sampleDataTemplates,
      userId: user.id,
    });

    if (!result.success) {
      logger.error(
        { error: result.error },
        'Failed to create team with sample data',
      );
      return { success: false, error: result.error };
    }

    // Revalidate the home page to show the new team
    revalidatePath('/home');

    // Return success with the team slug
    return {
      success: true,
      teamSlug: result.teamSlug,
    };
  } catch (error) {
    logger.error({ error }, 'Error in createTeamWithSampleDataFormAction');

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: 'Validation error',
        validationErrors: error.errors,
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
