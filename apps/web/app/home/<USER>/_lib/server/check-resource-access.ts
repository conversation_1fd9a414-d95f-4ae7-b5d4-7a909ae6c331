'use server';

import {getLogger} from '@kit/shared/logger';
import {getSupabaseServerClient} from '@kit/supabase/server-client';
import {createTeamAccountsApi} from '@kit/team-accounts/api';

// getResourceUsage: Tr<PERSON> về cả counters và ZNS credits để hiển thị trong UI.

export async function checkCanCreateResource(
    accountId: string,
    resourceType: string,
) {
    // Đặc biệt xử lý cho integrations
    if (resourceType === 'integrations') {
        return await checkCanCreateIntegration(accountId);
    }
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    const teamAccountsApi = createTeamAccountsApi(supabase);

    try {
        // Kiểm tra quyền của người dùng
        try {
            const userId = (await supabase.auth.getUser()).data.user?.id || '';
            const hasPermission = await teamAccountsApi.hasPermission({
                accountId,
                userId,
                permission: `${resourceType}.manage`,
            });

            if (!hasPermission) {
                // Kiểm tra xem người dùng không có quyền vì chưa đăng ký hay vì lý do khác
                const subscription = await teamAccountsApi.getSubscription(accountId);

                if (!subscription) {
                    return {canCreate: false, reason: 'no_active_subscription'};
                } else {
                    return {canCreate: false, reason: 'no_permission'};
                }
            }
        } catch (permissionError) {
            logger.error(
                {error: permissionError, accountId, resourceType},
                'Error checking permission',
            );
            return {canCreate: false, reason: 'permission_error'};
        }

        // Kiểm tra giới hạn tài nguyên
        const {data: canCreate} = await supabase.rpc('can_create_resource', {
            p_account_id: accountId,
            p_resource_type: resourceType,
        });

        if (!canCreate) {
            const {data: stats} = await supabase
                .from('account_usage_stats')
                .select('counters')
                .eq('account_id', accountId)
                .single();

            const subscription = await teamAccountsApi.getSubscription(accountId);

            // Metadata is stored in subscription_items, not directly in subscription
            const limit = subscription?.items?.[0]?.metadata?.[`${resourceType}_limit`];
            const current = stats?.counters?.[resourceType] || 0;

            console.log('Subscription:', JSON.stringify(subscription, null, 2));
            console.log('Resource type:', resourceType);
            console.log('Limit:', limit);

            // If limit is undefined but we have a subscription, assume it's unlimited
            if (limit === undefined && subscription) {
                return {canCreate: true, reason: null, current, limit: -1}; // -1 means unlimited
            }

            return {canCreate: false, reason: 'limit_reached', current, limit};
        }
        return {canCreate: true};
    } catch (error) {
        logger.error(
            {error, accountId, resourceType},
            'Error checking resource limit',
        );
        return {canCreate: false, reason: 'unknown_error'};
    }
}

// checkCanSendZns và consumeZnsCredits: Quản lý tín dụng ZNS riêng biệt.

export async function checkCanSendZns(
    accountId: string,
    creditsRequired: number = 1,
) {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    const teamAccountsApi = createTeamAccountsApi(supabase);

    try {
        // Kiểm tra quyền của người dùng
        try {
            const userId = (await supabase.auth.getUser()).data.user?.id || '';
            const hasPermission = await teamAccountsApi.hasPermission({
                accountId,
                userId,
                permission: 'zns.manage',
            });

            if (!hasPermission) {
                return {canSend: false, reason: 'no_permission'};
            }
        } catch (permissionError) {
            logger.error(
                {error: permissionError, accountId},
                'Error checking ZNS permission',
            );
            return {canSend: false, reason: 'permission_error'};
        }

        // Kiểm tra credits
        const {data: hasCredits} = await supabase.rpc('has_zns_credits', {
            p_account_id: accountId,
            p_required_credits: creditsRequired,
        });

        if (!hasCredits) {
            const {data: stats} = await supabase
                .from('account_usage_stats')
                .select('zns_credits')
                .eq('account_id', accountId)
                .single();
            return {canSend: false, currentCredits: stats?.zns_credits || 0};
        }
        return {canSend: true};
    } catch (error) {
        logger.error({error, accountId}, 'Error checking ZNS credits');
        return {canSend: false, reason: 'unknown_error'};
    }
}

export async function consumeZnsCredits(
    accountId: string,
    credits: number = 1,
) {
    const supabase = getSupabaseServerClient();
    const {error} = await supabase.rpc('consume_zns_credits', {
        p_account_id: accountId,
        p_consumed_credits: credits,
    });
    if (error) throw new Error('Failed to consume ZNS credits');
}

// checkCanCreateResource: Kiểm tra giới hạn cho products, branches, v.v.
// Kiểm tra giới hạn tích hợp
export async function checkCanCreateIntegration(accountId: string) {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    const teamAccountsApi = createTeamAccountsApi(supabase);

    try {
        // Kiểm tra quyền của người dùng
        try {
            const userId = (await supabase.auth.getUser()).data.user?.id || '';
            const hasPermission = await teamAccountsApi.hasPermission({
                accountId,
                userId,
                permission: 'integrations.manage',
            });

            if (!hasPermission) {
                // Kiểm tra xem người dùng không có quyền vì chưa đăng ký hay vì lý do khác
                const subscription = await teamAccountsApi.getSubscription(accountId);

                if (!subscription) {
                    return {canCreate: false, reason: 'no_active_subscription'};
                } else {
                    return {canCreate: false, reason: 'no_permission'};
                }
            }
        } catch (permissionError) {
            logger.error(
                {error: permissionError, accountId},
                'Error checking integration permission',
            );
            return {canCreate: false, reason: 'permission_error'};
        }

        // Đếm số lượng tích hợp hiện tại
        const {data: integrations, error: countError} = await supabase
            .from('integrations')
            .select('id')
            .eq('account_id', accountId);

        if (countError) {
            logger.error(
                {error: countError, accountId},
                'Error counting integrations',
            );
            return {canCreate: false, reason: 'count_error'};
        }

        const currentCount = integrations?.length || 0;

        // Lấy giới hạn từ subscription
        const subscription = await teamAccountsApi.getSubscription(accountId);
        const limit = subscription?.items?.[0]?.metadata?.integrations_limit;

        // Nếu không có subscription hoặc không có giới hạn
        if (!subscription) {
            return {canCreate: false, reason: 'no_active_subscription'};
        }

        // Nếu limit là undefined nhưng có subscription, giả định là không giới hạn
        if (limit === undefined) {
            return {canCreate: true, reason: null, current: currentCount, limit: -1}; // -1 means unlimited
        }

        // Kiểm tra giới hạn
        if (currentCount >= limit) {
            return {canCreate: false, reason: 'limit_reached', current: currentCount, limit};
        }

        return {canCreate: true, current: currentCount, limit};
    } catch (error) {
        logger.error(
            {error, accountId},
            'Error checking integration limit',
        );
        return {canCreate: false, reason: 'unknown_error'};
    }
}

export async function getResourceUsage(accountId: string) {
    const supabase = getSupabaseServerClient();
    const {data: stats} = await supabase
        .from('account_usage_stats')
        .select('counters, zns_credits, last_updated')
        .eq('account_id', accountId)
        .single();

    // Sử dụng API có sẵn để lấy subscription
    const teamAccountsApi = createTeamAccountsApi(supabase);
    const subscription = await teamAccountsApi.getSubscription(accountId);

    const resources = {};
    if (stats?.counters) {
        for (const [type, count] of Object.entries(stats.counters)) {
            // Metadata is stored in subscription_items, not directly in subscription
            const limit = subscription?.items?.[0]?.metadata?.[`${type}_limit`];
            resources[type] = {
                current: count,
                // If limit is undefined but we have a subscription, assume it's unlimited (-1)
                limit: limit === undefined && subscription ? -1 : (limit ?? null),
                // Add percentage for UI
                percentage: limit ? Math.min(100, Math.round((count as number) / (limit as number) * 100)) : null,
            };
        }
    }

    return {
        resources,
        znsCredits: stats?.zns_credits || 0,
        // Metadata is stored in subscription_items, not directly in subscription
        // If zns_limit is undefined but we have a subscription, assume it's unlimited (-1)
        znsLimit: subscription?.items?.[0]?.metadata?.zns_limit === undefined && subscription ? -1 : (subscription?.items?.[0]?.metadata?.zns_limit ?? 0),
        lastUpdated: stats?.last_updated,
    };
}
