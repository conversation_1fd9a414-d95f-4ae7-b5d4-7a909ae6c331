import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getDashboardStats, DashboardStats } from './dashboard-stats';

export interface DashboardData {
  dashboardStats: DashboardStats;
}

export async function loadDashboardData(accountId: string): Promise<DashboardData> {
  try {
    // L<PERSON>y thống kê dashboard
    const dashboardStats = await getDashboardStats(accountId);

    return {
      dashboardStats,
    };
  } catch (error) {
    console.error('Error loading dashboard data:', error);
    throw error;
  }
}
