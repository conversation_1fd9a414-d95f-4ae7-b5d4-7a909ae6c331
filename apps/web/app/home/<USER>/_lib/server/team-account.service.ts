import 'server-only';

import { cache } from 'react';
import { redirect } from 'next/navigation';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

import pathsConfig from '~/config/paths.config';

/**
 * Get the team account data for the given slug.
 * This function is cached so that the data is only fetched once per request.
 * 
 * @param slug The slug of the team account
 * @returns The team account data
 */
export const getTeamAccount = cache(async (slug: string) => {
  const client = getSupabaseServerClient();
  const api = createTeamAccountsApi(client);

  try {
    const account = await api.getTeamAccount(slug);
    
    if (!account) {
      console.error(`Account with slug ${slug} not found`);
      redirect(pathsConfig.app.home);
    }
    
    return account;
  } catch (error) {
    console.error('Error loading team account:', error);
    redirect(pathsConfig.app.home);
  }
});

/**
 * Get the team account data for the given ID.
 * This function is cached so that the data is only fetched once per request.
 * 
 * @param id The ID of the team account
 * @returns The team account data
 */
export const getTeamAccountById = cache(async (id: string) => {
  const client = getSupabaseServerClient();
  const api = createTeamAccountsApi(client);

  try {
    const account = await api.getTeamAccountById(id);
    
    if (!account) {
      console.error(`Account with ID ${id} not found`);
      return null;
    }
    
    return account;
  } catch (error) {
    console.error('Error loading team account by ID:', error);
    return null;
  }
});
