'use server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

/**
 * Kiểm tra quyền truy cập tài nguyên
 * @param accountId ID của tài khoản
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 * @param action Hành động (create, read, update, delete)
 * @param userId ID của người dùng (nếu không cung cấp, sẽ lấy từ session)
 */
export async function checkResourceAccess(
  accountId: string,
  resourceType: string,
  action: 'create' | 'read' | 'update' | 'delete',
  userId?: string,
) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // 1. Kiểm tra quyền của người dùng
    const permissionName = `${resourceType}.manage` as any;
    const teamAccountsApi = createTeamAccountsApi(supabase);
    const subscription = await teamAccountsApi.getSubscription(accountId);

    let hasPermission = false;
    try {
      // Sử dụng API có sẵn để kiểm tra quyền
      hasPermission = await teamAccountsApi.hasPermission({
        accountId,
        userId: userId || (await supabase.auth.getUser()).data.user?.id || '',
        permission: permissionName,
      });
    } catch (permissionError) {
      logger.error(
        { error: permissionError, accountId, resourceType, action },
        'Error checking permission',
      );
      return {
        canAccess: false,
        reason: 'permission_error',
      };
    }
    if (!hasPermission) {
      return {
        canAccess: false,
        reason: 'no_permission',
      };
    }

    // 2. Nếu là hành động tạo mới, kiểm tra giới hạn tài nguyên
    if (action === 'create') {
      if (!subscription) {
        return {
          canAccess: false,
          reason: 'no_active_subscription',
        };
      }

      const { data: canCreate, error: limitError } = await supabase.rpc(
        'can_create_resource',
        {
          p_account_id: accountId,
          p_resource_type: resourceType,
        },
      );

      if (limitError) {
        logger.error(
          { error: limitError, accountId, resourceType },
          'Error checking resource limit',
        );
        return {
          canAccess: false,
          reason: 'limit_error',
        };
      }

      if (!canCreate) {
        // Lấy thông tin giới hạn và số lượng hiện tại
        const { data: stats } = await supabase
          .from('account_usage_stats')
          .select('counters')
          .eq('account_id', accountId)
          .single();

        // Lấy thông tin subscription (sử dụng teamAccountsApi đã khởi tạo trước đó)
        const subscription = await teamAccountsApi.getSubscription(accountId);

        // Metadata is stored in subscription_items, not directly in subscription
        const limit =
          subscription?.items?.[0]?.metadata?.[`${resourceType}_limit`];
        const current = stats?.counters?.[resourceType] || 0;

        console.log('Subscription:', JSON.stringify(subscription, null, 2));
        console.log('Resource type:', resourceType);
        console.log('Limit:', limit);

        return {
          canAccess: false,
          reason: 'limit_reached',
          current,
          limit,
        };
      }
    }

    // Nếu mọi kiểm tra đều thành công
    return {
      canAccess: true,
    };
  } catch (error) {
    logger.error(
      { error, accountId, resourceType, action },
      'Error checking resource access',
    );
    return {
      canAccess: false,
      reason: 'unknown_error',
    };
  }
}

/**
 * Kiểm tra quyền tạo tài nguyên
 * @param accountId ID của tài khoản
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export async function checkCanCreateResource(
  accountId: string,
  resourceType: string,
) {
  const result = await checkResourceAccess(accountId, resourceType, 'create');
  // If limit is undefined but we have a subscription, assume it's unlimited (-1)
  if (result.reason === 'limit_reached' && result.limit === undefined) {
    // Check if we have a subscription
    const teamAccountsApi = createTeamAccountsApi(getSupabaseServerClient());
    const subscription = await teamAccountsApi.getSubscription(accountId);

    console.log(
      'Subscription in checkCanCreateResource:',
      JSON.stringify(subscription, null, 2),
    );

    if (subscription) {
      // Check if we have a limit defined in the subscription items
      const limit =
        subscription?.items?.[0]?.metadata?.[`${resourceType}_limit`];
      console.log(`Limit for ${resourceType} in subscription items:`, limit);

      if (limit !== undefined) {
        // If we have a defined limit, use it
        return {
          canCreate: result.current < limit,
          reason: result.current < limit ? null : 'limit_reached',
          current: result.current,
          limit,
        };
      } else {
        // If we have a subscription but no limit defined, assume it's unlimited
        return {
          canCreate: true,
          reason: null,
          current: result.current,
          limit: -1, // -1 means unlimited
        };
      }
    }
  }

  return {
    canCreate: result.canAccess,
    reason: result.reason,
    current: result.current,
    limit: result.limit,
  };
}

/**
 * Kiểm tra quyền đọc tài nguyên
 * @param accountId ID của tài khoản
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export async function checkCanReadResource(
  accountId: string,
  resourceType: string,
) {
  const result = await checkResourceAccess(accountId, resourceType, 'read');
  return {
    canRead: result.canAccess,
    reason: result.reason,
  };
}

/**
 * Kiểm tra quyền cập nhật tài nguyên
 * @param accountId ID của tài khoản
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export async function checkCanUpdateResource(
  accountId: string,
  resourceType: string,
) {
  const result = await checkResourceAccess(accountId, resourceType, 'update');
  return {
    canUpdate: result.canAccess,
    reason: result.reason,
  };
}

/**
 * Kiểm tra quyền xóa tài nguyên
 * @param accountId ID của tài khoản
 * @param resourceType Loại tài nguyên (products, branches, v.v.)
 */
export async function checkCanDeleteResource(
  accountId: string,
  resourceType: string,
) {
  const result = await checkResourceAccess(accountId, resourceType, 'delete');
  return {
    canDelete: result.canAccess,
    reason: result.reason,
  };
}

/**
 * Kiểm tra quyền gửi ZNS
 * @param accountId ID của tài khoản
 * @param creditsRequired Số credits cần thiết
 */
export async function checkCanSendZns(
  accountId: string,
  creditsRequired: number = 1,
) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // 1. Kiểm tra quyền của người dùng
    const teamAccountsApi = createTeamAccountsApi(supabase);

    let hasPermission = false;
    try {
      // Sử dụng API có sẵn để kiểm tra quyền
      hasPermission = await teamAccountsApi.hasPermission({
        accountId,
        userId: (await supabase.auth.getUser()).data.user?.id || '',
        permission: 'zns.manage',
      });
    } catch (permissionError) {
      logger.error(
        { error: permissionError, accountId },
        'Error checking ZNS permission',
      );
      return {
        canSend: false,
        reason: 'permission_error',
      };
    }

    if (!hasPermission) {
      return {
        canSend: false,
        reason: 'no_permission',
      };
    }

    // 2. Kiểm tra credits
    const { data: hasCredits, error: creditsError } = await supabase.rpc(
      'has_zns_credits',
      {
        p_account_id: accountId,
        p_required_credits: creditsRequired,
      },
    );

    if (creditsError) {
      logger.error(
        { error: creditsError, accountId },
        'Error checking ZNS credits',
      );
      return {
        canSend: false,
        reason: 'credits_error',
      };
    }

    if (!hasCredits) {
      const { data: stats } = await supabase
        .from('account_usage_stats')
        .select('zns_credits')
        .eq('account_id', accountId)
        .single();

      return {
        canSend: false,
        reason: 'no_credits',
        currentCredits: stats?.zns_credits || 0,
      };
    }

    // Nếu mọi kiểm tra đều thành công
    return {
      canSend: true,
    };
  } catch (error) {
    logger.error({ error, accountId }, 'Error checking ZNS access');
    return {
      canSend: false,
      reason: 'unknown_error',
    };
  }
}

/**
 * Tiêu thụ ZNS credits
 * @param accountId ID của tài khoản
 * @param credits Số credits cần tiêu thụ
 */
export async function consumeZnsCredits(
  accountId: string,
  credits: number = 1,
) {
  const supabase = getSupabaseServerClient();
  const { error } = await supabase.rpc('consume_zns_credits', {
    p_account_id: accountId,
    p_consumed_credits: credits,
  });

  if (error) throw new Error('Failed to consume ZNS credits');
}

/**
 * Lấy thông tin sử dụng tài nguyên
 * @param accountId ID của tài khoản
 */
export async function getResourceUsage(accountId: string) {
  const supabase = getSupabaseServerClient();
  const { data: stats } = await supabase
    .from('account_usage_stats')
    .select('counters, zns_credits, last_updated')
    .eq('account_id', accountId)
    .single();

  // Sử dụng API có sẵn để lấy subscription
  const teamAccountsApi = createTeamAccountsApi(supabase);
  const subscription = await teamAccountsApi.getSubscription(accountId);

  const resources = {};
  if (stats?.counters) {
    for (const [type, count] of Object.entries(stats.counters)) {
      // Metadata is stored in subscription_items, not directly in subscription
      const limit = subscription?.items?.[0]?.metadata?.[`${type}_limit`];
      resources[type] = {
        current: count,
        limit: limit === undefined && subscription ? -1 : (limit ?? null),
      };
    }
  }

  return {
    resources,
    znsCredits: stats?.zns_credits || 0,
    // Metadata is stored in subscription_items, not directly in subscription
    znsLimit: subscription?.items?.[0]?.metadata?.zns_limit ?? 0,
    lastUpdated: stats?.last_updated,
  };
}
