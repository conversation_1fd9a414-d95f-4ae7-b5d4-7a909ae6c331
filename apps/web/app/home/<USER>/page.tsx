import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

// local imports
import { HomeLayoutPageHeader } from './_components/home-page-header';
import { MemberHome } from './_components/member-home';
import { OwnerWithTeams } from './_components/owner-with-teams';
import { OwnerWithoutTeam } from './_components/owner-without-team';
import { getAccountsWithTeamCreatePermission } from './_lib/server/check-team-permissions';
import { loadUserWorkspace } from './_lib/server/load-user-workspace';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('account:homePage');

  return {
    title,
  };
};

async function UserHomePage() {
  const { user, accounts } = await loadUserWorkspace();

  // L<PERSON><PERSON> danh sách account IDs
  const accountIds = accounts
    .filter((account) => account.id)
    .map((account) => account.id as string);

  // Kiểm tra quyền tạo team cho mỗi account
  const accountsWithCreatePermission =
    await getAccountsWithTeamCreatePermission(accountIds);

  const ownedTeams = accounts
    .filter((account) => account.role === 'owner')
    .map((account) => ({
      id: account.id,
      name: account.label,
      picture_url: account.image,
      role: account.role,
      slug: account.value,
      value: account.value,
      isOwner: true,
      // Đánh dấu account có quyền tạo team
      canCreateTeam: account.id
        ? accountsWithCreatePermission.includes(account.id)
        : false,
    }));

  const memberTeams = accounts
    .filter((account) => account.role !== 'owner')
    .map((account) => ({
      account: {
        id: account.id,
        name: account.label,
        picture_url: account.image,
        role: account.role,
        slug: account.value,
        value: account.value,
        // Đánh dấu account có quyền tạo team
        canCreateTeam: account.id
          ? accountsWithCreatePermission.includes(account.id)
          : false,
      },
      role: account.role || 'member',
    }));

  const isFirstLogin = accounts.length === 0;
  const isOwner = ownedTeams.length > 0;
  const isMember = memberTeams.length > 0;

  return (
    <>
      <HomeLayoutPageHeader
        title={<Trans i18nKey={'common:routes.home'} />}
        description={<Trans i18nKey={'common:homeTabDescription'} />}
      />

      <PageBody>
        {/* Owner without team */}
        {isFirstLogin && (
          <OwnerWithoutTeam
            isFirstLogin={isFirstLogin}
            userName={user.user_metadata?.name || user.email}
          />
        )}

        {/* Owner with teams */}
        {!isFirstLogin && isOwner && (
          <OwnerWithTeams
            ownedTeams={ownedTeams}
            memberTeams={memberTeams.map((team) => ({
              account: team,
              role: team.role || 'member',
            }))}
          />
        )}

        {/* Member only */}
        {!isFirstLogin && !isOwner && isMember && (
          <MemberHome
            memberTeams={memberTeams.map((team) => ({
              account: {
                ...team.account,
                permissions: team.account.permissions || [],
              },
              role: team.role,
            }))}
          />
        )}
      </PageBody>
    </>
  );
}

export default withI18n(UserHomePage);
