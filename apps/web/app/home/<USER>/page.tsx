import { use } from 'react';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { StatsCards } from './_components/dashboard-stats-cards';
import { CustomersChart } from './_components/dashboard-customers-chart';
import { SubscriptionCard } from './_components/dashboard-subscription-card';
import { TeamAccountLayoutPageHeader } from './_components/team-account-layout-page-header';
import { loadDashboardData } from './_lib/server/dashboard.loader';
import { getTeamAccount } from './_lib/server/team-account.service';
import { VisitorsChart } from './_components/dashboard-visitors-chart';
import { PageViewsChart } from './_components/dashboard-pageviews-chart';
import { TopMiniApps } from './_components/dashboard-top-miniapps';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';

interface TeamAccountHomePageProps {
  params: Promise<{ account: string }>;
}

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('teams:home.pageTitle');

  return {
    title,
  };
};

function TeamAccountHomePage({ params }: TeamAccountHomePageProps) {
  const accountSlug = use(params).account;

  // Lấy thông tin account
  const teamAccountPromise = getTeamAccount(accountSlug);
  const teamAccount = use(teamAccountPromise);
  if (!teamAccount) {
    throw new Error(`Account with slug ${accountSlug} not found`);
  }

  // Lấy dữ liệu dashboard
  const dashboardDataPromise = loadDashboardData(teamAccount.id);
  const dashboardData = use(dashboardDataPromise);
  const { dashboardStats } = dashboardData;

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={accountSlug}
        title={<Trans i18nKey={'common:routes.dashboard'} />}
        description={<AppBreadcrumbs />}
      />

      <PageBody className="space-y-8" data-testid="dashboard-page">
        {/* Dashboard Overview Section */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="space-y-1">
              <h2 className="text-2xl font-bold tracking-tight">
                <Trans i18nKey="dashboard:overviewTitle">Tổng quan</Trans>
              </h2>
              <p className="text-muted-foreground">
                <Trans i18nKey="dashboard:overviewSubtitle">Thống kê hoạt động kinh doanh của bạn</Trans>
              </p>
            </div>
            <div className="flex items-center gap-2">
              <select className="rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm">
                <option value="30">30 ngày qua</option>
                <option value="7">7 ngày qua</option>
                <option value="90">90 ngày qua</option>
              </select>
            </div>
          </div>

          <StatsCards
            mrr={dashboardStats.mrr}
            revenue={dashboardStats.revenue}
            newCustomers={dashboardStats.newCustomers}
            usagePercent={dashboardStats.usagePercent}
          />

          <div className="grid gap-6 mt-6 md:grid-cols-2 lg:grid-cols-7">
            <div className="md:col-span-2 lg:col-span-3">
              <SubscriptionCard
                plan={dashboardStats.subscription.plan}
                status={dashboardStats.subscription.status}
                usagePercent={dashboardStats.usagePercent}
                daysLeft={dashboardStats.subscription.daysLeft}
                features={dashboardStats.subscription.features}
                nextPlan={dashboardStats.subscription.nextPlan}
              />
            </div>
            <div className="md:col-span-2 lg:col-span-4">
              <CustomersChart
                data={dashboardStats.customersByMiniApp}
                total={dashboardStats.totalCustomers}
              />
            </div>
          </div>
        </div>

        {/* Phần Mini App Analytics */}
        <div className="border-t pt-8">
          <div className="flex items-center justify-between mb-6">
            <div className="space-y-1">
              <h2 className="text-2xl font-bold tracking-tight">
                <Trans i18nKey="dashboard:miniAppAnalyticsTitle">Phân tích Mini App</Trans>
              </h2>
              <p className="text-muted-foreground">
                <Trans i18nKey="dashboard:miniAppAnalyticsSubtitle">Thống kê hoạt động của khách hàng trên các Mini App</Trans>
              </p>
            </div>
            <div className="flex items-center gap-2">
              <select className="rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm">
                <option value="30">30 ngày qua</option>
                <option value="7">7 ngày qua</option>
                <option value="90">90 ngày qua</option>
              </select>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <Trans i18nKey="dashboard:totalVisitors">Tổng lượt truy cập</Trans>
                </CardTitle>
                <div className="rounded-full p-2 w-10 h-10 flex items-center justify-center bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold tracking-tight">{dashboardStats.miniAppStats.visitors.total.toLocaleString()}</div>
                <div className="mt-2 flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    <Trans i18nKey="dashboard:last30Days">30 ngày qua</Trans>
                  </p>
                  <div className="flex items-center text-xs text-green-600 dark:text-green-400 font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 mr-1">
                      <path fillRule="evenodd" d="M12.577 4.878a.75.75 0 01.919-.53l4.78 1.281a.75.75 0 01.531.919l-1.281 4.78a.75.75 0 01-1.449-.387l.81-3.022a19.407 19.407 0 00-5.594 *********** 0 01-1.139.093L7 10.06l-4.72 4.72a.75.75 0 01-1.06-1.061l5.25-5.25a.75.75 0 011.06 0l3.074 3.073a20.923 20.923 0 015.545-4.931l-3.042-.815a.75.75 0 01-.53-.919z" clipRule="evenodd" />
                    </svg>
                    +12.5%
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <Trans i18nKey="dashboard:totalPageViews">Tổng lượt xem trang</Trans>
                </CardTitle>
                <div className="rounded-full p-2 w-10 h-10 flex items-center justify-center bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5"
                  >
                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold tracking-tight">{dashboardStats.miniAppStats.pageViews.total.toLocaleString()}</div>
                <div className="mt-2 flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    <Trans i18nKey="dashboard:last30Days">30 ngày qua</Trans>
                  </p>
                  <div className="flex items-center text-xs text-green-600 dark:text-green-400 font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 mr-1">
                      <path fillRule="evenodd" d="M12.577 4.878a.75.75 0 01.919-.53l4.78 1.281a.75.75 0 01.531.919l-1.281 4.78a.75.75 0 01-1.449-.387l.81-3.022a19.407 19.407 0 00-5.594 *********** 0 01-1.139.093L7 10.06l-4.72 4.72a.75.75 0 01-1.06-1.061l5.25-5.25a.75.75 0 011.06 0l3.074 3.073a20.923 20.923 0 015.545-4.931l-3.042-.815a.75.75 0 01-.53-.919z" clipRule="evenodd" />
                    </svg>
                    +18.2%
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <Trans i18nKey="dashboard:activeCustomers">Khách hàng hoạt động</Trans>
                </CardTitle>
                <div className="rounded-full p-2 w-10 h-10 flex items-center justify-center bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5"
                  >
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                    <circle cx="12" cy="7" r="4" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold tracking-tight">{Math.round(dashboardStats.miniAppStats.visitors.total * 0.75).toLocaleString()}</div>
                <div className="mt-2 flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    <Trans i18nKey="dashboard:percentOfTotal" values={{ percent: 75 }}>
                      75% tổng lượt truy cập
                    </Trans>
                  </p>
                  <div className="flex items-center text-xs text-green-600 dark:text-green-400 font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 mr-1">
                      <path fillRule="evenodd" d="M12.577 4.878a.75.75 0 01.919-.53l4.78 1.281a.75.75 0 01.531.919l-1.281 4.78a.75.75 0 01-1.449-.387l.81-3.022a19.407 19.407 0 00-5.594 *********** 0 01-1.139.093L7 10.06l-4.72 4.72a.75.75 0 01-1.06-1.061l5.25-5.25a.75.75 0 011.06 0l3.074 3.073a20.923 20.923 0 015.545-4.931l-3.042-.815a.75.75 0 01-.53-.919z" clipRule="evenodd" />
                    </svg>
                    +8.3%
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <Trans i18nKey="dashboard:averageSessionTime">Thời gian trung bình</Trans>
                </CardTitle>
                <div className="rounded-full p-2 w-10 h-10 flex items-center justify-center bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <polyline points="12 6 12 12 16 14" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold tracking-tight">4:35</div>
                <div className="mt-2 flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    <Trans i18nKey="dashboard:minutesPerSession">phút mỗi phiên</Trans>
                  </p>
                  <div className="flex items-center text-xs text-green-600 dark:text-green-400 font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 mr-1">
                      <path fillRule="evenodd" d="M12.577 4.878a.75.75 0 01.919-.53l4.78 1.281a.75.75 0 01.531.919l-1.281 4.78a.75.75 0 01-1.449-.387l.81-3.022a19.407 19.407 0 00-5.594 *********** 0 01-1.139.093L7 10.06l-4.72 4.72a.75.75 0 01-1.06-1.061l5.25-5.25a.75.75 0 011.06 0l3.074 3.073a20.923 20.923 0 015.545-4.931l-3.042-.815a.75.75 0 01-.53-.919z" clipRule="evenodd" />
                    </svg>
                    +5.2%
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 mt-6 md:grid-cols-12">
            <div className="md:col-span-5">
              <TopMiniApps miniApps={dashboardStats.miniAppStats.topMiniApps} />
            </div>
            <div className="md:col-span-7">
              <VisitorsChart
                data={dashboardStats.miniAppStats.visitors.chartData}
                total={dashboardStats.miniAppStats.visitors.byDevice}
              />
            </div>
          </div>

          <div className="mt-6">
            <PageViewsChart
              data={dashboardStats.miniAppStats.pageViews.chartData}
              total={dashboardStats.miniAppStats.pageViews.byDevice}
            />
          </div>
        </div>
      </PageBody>
    </>
  );
}

export default withI18n(TeamAccountHomePage);
