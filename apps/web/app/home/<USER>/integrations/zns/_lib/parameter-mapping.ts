/**
 * ZNS Parameter Mapping Logic
 * Sử dụng config mặc định từ logic gửi ZNS để phân loại parameters
 */

import {
  ZNS_DEFAULT_KEYS,
  getZnsKeyConfig,
  isDefaultKey,
  generateAutoMapping,
  categorizeTemplateParams,
  validateParameterMapping as validateParams,
  type ZnsDataPath,
} from '@kit/zns';

export interface ParameterConfig {
  name: string;
  type: string;
  isAutoMapped: boolean;
  autoPath?: string; // Đường dẫn tự động trong data
  description: string;
  placeholder?: string;
  category: 'customer' | 'order' | 'payment' | 'system' | 'product' | 'custom';
  example?: string;
}

/**
 * Chuyển đổi ZnsDataPath thành ParameterConfig
 */
function znsDataPathToParameterConfig(znsPath: ZnsDataPath, paramName: string): ParameterConfig {
  return {
    name: paramName,
    type: znsPath.paramType,
    isAutoMapped: true,
    autoPath: znsPath.path,
    description: znsPath.description,
    category: znsPath.category,
    example: znsPath.example,
  };
}

/**
 * Phân loại parameter thành auto hoặc manual dựa trên config mặc định
 */
export function categorizeParameter(paramName: string): ParameterConfig | null {
  const znsConfig = getZnsKeyConfig(paramName);

  if (znsConfig) {
    // Parameter có trong config mặc định → auto
    return znsDataPathToParameterConfig(znsConfig, paramName);
  } else {
    // Parameter không có trong config → manual
    return {
      name: paramName,
      type: '13', // CUSTOM_LABEL
      isAutoMapped: false,
      description: `Tham số tùy chỉnh: ${paramName}`,
      placeholder: `Nhập giá trị cho ${paramName}`,
      category: 'custom',
    };
  }
}

/**
 * Tạo parameter mapping tự động cho template
 */
export function generateAutoParameterMapping(templateParams: any[]): Record<string, string> {
  return generateAutoMapping(templateParams);
}

/**
 * Lấy danh sách parameters cần user config (manual)
 */
export function getManualParameters(templateParams: any[]): ParameterConfig[] {
  const { manualParams } = categorizeTemplateParams(templateParams);

  return manualParams.map(param => ({
    name: param.name,
    type: param.type || '13',
    isAutoMapped: false,
    description: `Tham số tùy chỉnh: ${param.name}`,
    placeholder: `Nhập giá trị cho ${param.name}`,
    category: 'custom' as const,
  }));
}

/**
 * Lấy danh sách parameters đã được auto-map
 */
export function getAutoMappedParameters(templateParams: any[]): ParameterConfig[] {
  const { autoParams } = categorizeTemplateParams(templateParams);

  return autoParams.map(({ param, config }) =>
    znsDataPathToParameterConfig(config, param.name)
  );
}

/**
 * Validate parameter mapping
 */
export function validateParameterMapping(
  templateParams: any[],
  parameterMapping: Record<string, string>
): { isValid: boolean; errors: string[] } {
  return validateParams(templateParams, parameterMapping);
}

/**
 * Lấy danh sách tất cả key mặc định để hiển thị trong template creation
 */
export function getDefaultKeys(): ZnsDataPath[] {
  return ZNS_DEFAULT_KEYS;
}

/**
 * Lấy key theo category để hiển thị trong UI
 */
export function getKeysByCategory(category: string): ZnsDataPath[] {
  return ZNS_DEFAULT_KEYS.filter(key => key.category === category);
}
