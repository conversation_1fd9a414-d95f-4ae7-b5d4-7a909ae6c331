'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getZaloUserDetail, sendZnsMessage } from '@kit/zns';

// Import types from local file
import { ZnsEventType, ZnsNotificationParams } from './types';

import {
  checkCanSendZns,
  consumeZnsCredits,
} from '~/home/<USER>/_lib/server/resource-access';

// Schema cho form gửi ZNS
const SendZnsSchema = z.object({
  accountId: z.string().uuid(),
  eventType: z.string() as z.ZodType<ZnsEventType>,
  params: z.record(z.string()) as z.ZodType<ZnsNotificationParams>,
});

/**
 * Server action để gửi ZNS
 */
export const sendZnsAction = enhanceAction(
  async (data: z.infer<typeof SendZnsSchema>) => {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();

    try {
      // Kiểm tra quyền gửi ZNS
      const { canSend, reason, currentCredits } = await checkCanSendZns(
        data.accountId,
      );

      if (!canSend) {
        logger.info(
          {
            accountId: data.accountId,
            eventType: data.eventType,
            reason,
            currentCredits,
          },
          'User cannot send ZNS',
        );

        return {
          success: false,
          error: reason,
          currentCredits,
        };
      }

      // Gửi ZNS
      // Lấy thông tin OA configuration
      const { data: integration } = await supabase
        .from('integrations')
        .select('metadata')
        .eq('account_id', data.accountId)
        .eq('type', 'zns')
        .single();

      if (!integration?.metadata?.oa_config_id) {
        throw new Error('ZNS integration not found');
      }

      // Gửi ZNS message
      const result = await sendZnsMessage(
        supabase,
        integration.metadata.oa_config_id,
        data.params.customer_phone,
        data.params.template_id || '',
        data.params,
      );

      // Tiêu thụ credits
      await consumeZnsCredits(data.accountId);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      logger.error(
        {
          accountId: data.accountId,
          eventType: data.eventType,
          error,
        },
        'Error in sendZns action',
      );

      return {
        success: false,
        error: 'unknown_error',
      };
    }
  },
  {
    schema: SendZnsSchema,
  },
);

// Schema cho form cập nhật ZNS template
const UpdateZnsTemplateSchema = z.object({
  accountId: z.string().uuid(),
  templateId: z.string().uuid(),
  eventType: z.string() as z.ZodType<ZnsEventType>,
  enabled: z.boolean(),
  params: z.record(z.string()).optional(),
  content: z.string().optional(),
});

/**
 * Server action để cập nhật ZNS template
 */
export const updateZnsTemplateAction = enhanceAction(
  async (data: z.infer<typeof UpdateZnsTemplateSchema>) => {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();

    try {
      // Kiểm tra quyền cập nhật ZNS template
      const { canSend, reason } = await checkCanSendZns(data.accountId, 0);

      if (!canSend) {
        logger.info(
          {
            accountId: data.accountId,
            templateId: data.templateId,
            reason,
          },
          'User cannot update ZNS template',
        );

        return {
          success: false,
          error: reason,
        };
      }

      // Cập nhật template
      const { error } = await supabase
        .from('zns_templates')
        .update({
          enabled: data.enabled,
          params: data.params,
          content: data.content,
        })
        .eq('id', data.templateId)
        .eq('account_id', data.accountId)
        .eq('event_type', data.eventType);

      if (error) {
        logger.error(
          {
            accountId: data.accountId,
            templateId: data.templateId,
            error,
          },
          'Error updating ZNS template',
        );

        return {
          success: false,
          error: 'database_error',
        };
      }

      return {
        success: true,
      };
    } catch (error) {
      logger.error(
        {
          accountId: data.accountId,
          templateId: data.templateId,
          error,
        },
        'Error in updateZnsTemplate action',
      );

      return {
        success: false,
        error: 'unknown_error',
      };
    }
  },
  {
    schema: UpdateZnsTemplateSchema,
  },
);

/**
 * Kiểm tra kết nối ZNS
 * @param accountSlug Slug của account
 * @param oaConfigId ID của OA configuration
 * @returns Kết quả kiểm tra kết nối
 */
export async function testZNSConnection(
  accountSlug: string,
  oaConfigId: string
): Promise<{ success: boolean; message?: string }> {
  try {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();

    // Lấy thông tin OA configuration
    const { data: oaConfig, error: oaConfigError } = await supabase
      .from('oa_configurations')
      .select('*')
      .eq('id', oaConfigId)
      .single();

    if (oaConfigError || !oaConfig) {
      throw new Error('OA configuration not found');
    }

    // Kiểm tra token có hợp lệ không
    const userInfo = await getZaloUserDetail(supabase, oaConfigId);

    if (!userInfo) {
      throw new Error('Failed to get user info');
    }

    // Cập nhật thông tin user_info trong oa_metadata
    await supabase
      .from('oa_configurations')
      .update({
        oa_metadata: {
          ...oaConfig.oa_metadata,
          user_info: userInfo,
          health_status: {
            apiLatency: 0,
            lastError: null,
            errorCount: 0,
            uptime: 100,
          },
        },
      })
      .eq('id', oaConfigId);

    logger.info(
      {
        accountSlug,
        oaConfigId,
        userInfo,
      },
      'ZNS connection test successful'
    );

    return { success: true };
  } catch (error: any) {
    console.error('Error testing ZNS connection:', error);
    return { success: false, message: error.message };
  }
}
