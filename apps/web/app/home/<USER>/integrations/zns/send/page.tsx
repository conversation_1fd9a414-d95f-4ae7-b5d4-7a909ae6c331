'use client';

import { useEffect, useState } from 'react';

import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Copy,
  Eye,
  Loader2,
  MessageSquare,
  RefreshCw,
  Send,
  Settings,
  Shield,
  Sparkles,
  Target,
  Zap,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import * as z from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Skeleton } from '@kit/ui/skeleton';
import {
  ZnsSendingMode,
  getZnsTemplateDetail,
  sendZnsMessage,
  sendZnsMessageInDevelopmentMode,
  sendZnsMessageWithHashPhone,
} from '@kit/zns';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { ZNSBreadcrumbs } from '../_components/zns-breadcrumbs';

// Define form schema
const sendZnsSchema = z.object({
  phone: z.string().min(1, 'Phone number is required'),
  templateId: z.string().min(1, 'Template ID is required'),
  sendingMode: z.enum(['normal', 'exceed_quota', 'development']),
  useHashPhone: z.boolean().default(false),
  trackingId: z.string().optional(),
  params: z.record(z.string().optional()),
});

type SendZnsFormValues = z.infer<typeof sendZnsSchema>;

// Helper function to get max length for parameter type
const getMaxLengthForType = (type: number): number => {
  const typeConstraints: Record<number, number> = {
    1: 30,   // CUSTOMER_NAME
    2: 50,   // PHONE_NUMBER
    3: 100,  // ADDRESS
    4: 30,   // CODE
    5: 200,  // CUSTOM_LABEL
    6: 30,   // TRANSACTION_STATUS
    7: 100,  // CONTACT_INFO
    8: 10,   // GENDER
    9: 100,  // PRODUCT_NAME
    10: 20,  // QUANTITY_AMOUNT
    11: 20,  // TIME
    12: 10,  // OTP
    13: 200, // URL
    14: 20,  // CURRENCY/STATUS
    15: 50,  // BANK_TRANSFER_NOTE
  };
  return typeConstraints[type] || 255;
};

// Helper function to get safe sample value
const getSafeSampleValue = (param: any): string => {
  const originalValue = param.sample_value || '';
  const maxLength = getMaxLengthForType(param.type);

  // Type-specific safe sample values
  const safeSamples: Record<number, string> = {
    1: 'Nguyễn Văn A',           // CUSTOMER_NAME (11 chars)
    2: '**********',             // PHONE_NUMBER (10 chars)
    3: '123 Đường ABC, Q1',      // ADDRESS (16 chars)
    4: 'ORD123',                 // CODE (6 chars)
    5: 'Sản phẩm ABC',           // CUSTOM_LABEL (12 chars)
    6: 'Thành công',             // TRANSACTION_STATUS (10 chars)
    7: '<EMAIL>',       // CONTACT_INFO (16 chars)
    8: 'Nam',                    // GENDER (3 chars)
    9: 'iPhone 15',              // PRODUCT_NAME (9 chars)
    10: '1',                     // QUANTITY_AMOUNT (1 char)
    11: '10:00 01/01/2023',      // TIME (14 chars)
    12: '123456',                // OTP (6 chars)
    13: 'https://example.com',   // URL (19 chars)
    14: '500,000',               // CURRENCY (7 chars)
    15: 'Chuyển khoản',          // BANK_TRANSFER_NOTE (13 chars)
  };

  // Use safe sample if original is too long or use original if safe
  const safeValue = safeSamples[param.type] || originalValue;

  // Truncate if still too long
  if (safeValue.length > maxLength) {
    return safeValue.substring(0, maxLength - 3) + '...';
  }

  return safeValue || originalValue;
};

export default function ZnsSendPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const searchParams = useSearchParams();
  const templateIdParam = searchParams.get('templateId');
  const { account, user } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();
  const [isSending, setIsSending] = useState(false);
  const [sendResult, setSendResult] = useState<any>(null);

  // Form
  const form = useForm<SendZnsFormValues>({
    resolver: zodResolver(sendZnsSchema),
    defaultValues: {
      phone: '',
      templateId: templateIdParam || '',
      sendingMode: 'normal',
      useHashPhone: false,
      trackingId: `zns_${Date.now()}`,
      params: {},
    },
  });

  // Fetch ZNS integration
  const { data: integration, isLoading: isLoadingIntegration } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error(
            'Error fetching ZNS integration:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error(
          'Exception fetching ZNS integration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration
  const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
    queryKey: ['zns-oa-config', integration?.metadata?.oa_config_id],
    queryFn: async () => {
      if (!integration?.metadata?.oa_config_id) return null;

      try {
        const { data, error } = await supabase
          .from('oa_configurations')
          .select('*')
          .eq('id', integration.metadata.oa_config_id)
          .maybeSingle();

        if (error) {
          console.error(
            'Error fetching OA config:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        return data;
      } catch (err) {
        console.error(
          'Exception fetching OA config:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!integration?.metadata?.oa_config_id,
  });

  // Check if token is valid
  const hasValidToken =
    oaConfig?.access_token &&
    oaConfig?.token_expires_at &&
    new Date(oaConfig.token_expires_at) > new Date();

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  const isReallyConnected = integration && hasValidToken;

  // Fetch available templates
  const { data: availableTemplates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['zns-templates', accountId],
    queryFn: async () => {
      if (!accountId) return [];

      try {
        const { data: templates, error } = await supabase
          .from('zns_templates')
          .select('id, template_id, template_name, status, enabled')
          .eq('account_id', accountId)
          .eq('enabled', true)
          .order('template_name');

        if (error) {
          console.error('Error fetching templates:', error);
          return [];
        }

        return templates || [];
      } catch (error) {
        console.error('Exception fetching templates:', error);
        return [];
      }
    },
    enabled: !!accountId,
  });

  // Fetch template details
  const { data: templateDetail, isLoading: isLoadingTemplateDetail } = useQuery(
    {
      queryKey: [
        'template-detail-db',
        accountId,
        form.watch('templateId'),
      ],
      queryFn: async () => {
        const templateId = form.watch('templateId');
        if (!templateId || !accountId)
          return null;

        try {
          // Fetch template from database to get all params with sample values
          const { data: dbTemplate, error: dbError } = await supabase
            .from('zns_templates')
            .select('metadata, template_name')
            .eq('account_id', accountId)
            .eq('template_id', templateId)
            .single();

          if (dbError) {
            console.error('Could not fetch template from database:', dbError);
            return null;
          }

          // Use database params directly (they have all the info we need)
          if (dbTemplate?.metadata?.original_request?.params) {
            const dbParams = dbTemplate.metadata.original_request.params;

            // Convert database params to the expected format with safe sample values
            const listParams = dbParams.map((param: any) => {
              // Get safe sample value based on parameter type and constraints
              const safeSampleValue = getSafeSampleValue(param);

              return {
                name: param.name,
                type: param.type?.toString() || '1',
                require: true, // Default to required
                maxLength: getMaxLengthForType(param.type) || 255,
                minLength: 1,   // Default min length
                acceptNull: false,
                sample_value: safeSampleValue
              };
            });

            console.log('Using database template params with sample values:', listParams);
            return {
              templateId: templateId,
              templateName: dbTemplate.template_name || 'Unknown Template',
              listParams: listParams,
              previewText: null
            };
          }

          return null;
        } catch (error) {
          console.error('Error fetching template detail:', error);
          toast.error(
            t(
              'integrations:zns.send.fetchTemplateError',
              'Lỗi khi tải thông tin mẫu tin',
            ),
          );
          return null;
        }
      },
      enabled:
        Boolean(form.watch('templateId')) &&
        Boolean(accountId),
    },
  );

  // Update form params when template detail changes
  useEffect(() => {
    if (templateDetail?.listParams && templateDetail.listParams.length > 0) {
      const initialParams: Record<string, string> = {};
      templateDetail.listParams.forEach((param) => {
        // Preserve existing value or use sample_value or set empty string
        const existingValue = form.getValues(`params.${param.name}`);
        const finalValue = existingValue || param.sample_value || '';
        initialParams[param.name] = finalValue;

        console.log(
          `Parameter ${param.name}: existing=${existingValue}, sample=${param.sample_value}, final=${finalValue}`,
        );
      });

      console.log('Setting form params:', initialParams);
      form.setValue('params', initialParams);
    }
  }, [templateDetail, form]);

  // Update form params with sample data
  const handleUseSampleData = () => {
    if (templateDetail?.listParams) {
      const sampleParams: Record<string, string> = {};
      templateDetail.listParams.forEach((param) => {
        if (param.sample_value) {
          sampleParams[param.name] = param.sample_value;
        }
      });

      if (Object.keys(sampleParams).length > 0) {
        form.setValue('params', sampleParams);
        toast.success(
          t(
            'integrations:zns.send.sampleDataApplied',
            'Đã áp dụng dữ liệu mẫu',
          ),
        );
      }
    }
  };

  // Handle form submission
  const onSubmit = async (values: SendZnsFormValues) => {
    if (!oaConfig?.id || !isReallyConnected) {
      toast.error(t('integrations:zns.connect.send.notConnected'));
      return;
    }

    setIsSending(true);
    setSendResult(null);

    try {
      let result;

      // Filter out empty params
      const filteredParams: Record<string, string> = {};
      Object.entries(values.params).forEach(([key, value]) => {
        if (value) {
          filteredParams[key] = value;
        }
      });

      if (values.sendingMode === 'development') {
        // Send in development mode
        result = await sendZnsMessageInDevelopmentMode(
          supabase,
          oaConfig.id,
          values.phone,
          values.templateId,
          filteredParams,
          values.trackingId,
        );
      } else if (values.useHashPhone) {
        // Send with hash phone
        result = await sendZnsMessageWithHashPhone(
          supabase,
          oaConfig.id,
          values.phone,
          values.templateId,
          filteredParams,
          values.trackingId,
        );
      } else {
        // Send normal message
        result = await sendZnsMessage(
          supabase,
          oaConfig.id,
          values.phone,
          values.templateId,
          filteredParams,
          values.trackingId,
          values.sendingMode === 'exceed_quota'
            ? ZnsSendingMode.EXCEED_QUOTA
            : ZnsSendingMode.NORMAL,
        );
      }

      setSendResult(result);
      toast.success(t('integrations:zns.connect.send.success'));

      // Save to zns_usage table
      await supabase.from('zns_usage').insert({
        account_id: accountId,
        oa_config_id: oaConfig.id,
        status: 'success',
        oa_type: oaConfig.oa_type,
        event_type: 'manual',
        metadata: {
          msg_id: result.msg_id,
          sent_time: result.sent_time,
          template_id: values.templateId,
          phone: values.phone,
          params: filteredParams,
          tracking_id: values.trackingId,
          sending_mode: values.sendingMode,
          use_hash_phone: values.useHashPhone,
        },
      });
    } catch (error: any) {
      console.error('Error sending ZNS:', error);
      toast.error(error.message || t('integrations:zns.connect.send.error'));

      // Save to zns_usage table
      await supabase.from('zns_usage').insert({
        account_id: accountId,
        oa_config_id: oaConfig.id,
        status: 'failed',
        oa_type: oaConfig.oa_type,
        event_type: 'manual',
        metadata: {
          error: error.message,
          template_id: values.templateId,
          phone: values.phone,
          params: values.params,
          tracking_id: values.trackingId,
          sending_mode: values.sendingMode,
          use_hash_phone: values.useHashPhone,
        },
      });
    } finally {
      setIsSending(false);
    }
  };

  // Loading state
  const isLoading =
    isLoadingIntegration ||
    isLoadingOaConfig ||
    isLoadingTemplates ||
    isLoadingTemplateDetail;

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={t('integrations:zns.connect.send.title')}
        description={<ZNSBreadcrumbs accountSlug={accountSlug} />}
        account={accountSlug}
      />
      <PageBody data-testid="zns-send-page">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                router.push(`/home/<USER>/integrations/zns/templates`)
              }
              className="gap-1"
            >
              <ArrowLeft className="h-3.5 w-3.5" />
              {t('common:back')}
            </Button>
          </div>

          {!isReallyConnected && !isLoading && (
            <Alert
              variant="destructive"
              className="border-l-destructive border-l-4"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {t('integrations:zns.connect.send.notConnected')}
              </AlertTitle>
              <AlertDescription>
                {t('integrations:zns.connect.send.notConnectedDescription')}
              </AlertDescription>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() =>
                  router.push(`/home/<USER>/integrations/zns/connect`)
                }
              >
                {t('integrations:zns.connect.send.connectNow')}
              </Button>
            </Alert>
          )}

          {/* Modern Header Card */}
          <Card className="border-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 shadow-lg dark:from-blue-950/30 dark:via-indigo-950/30 dark:to-purple-950/30">
            <CardHeader className="pb-6">
              <div className="flex items-center gap-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                  <MessageSquare className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-2xl font-bold text-transparent">
                    {t('integrations:zns.connect.send.title', 'Gửi tin ZNS')}
                  </CardTitle>
                  <CardDescription className="text-base text-gray-600 dark:text-gray-300">
                    {t(
                      'integrations:zns.connect.send.description',
                      'Gửi tin nhắn thông báo đến khách hàng qua Zalo',
                    )}
                  </CardDescription>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div className="flex items-center gap-3 rounded-lg bg-white/60 p-3 backdrop-blur-sm dark:bg-gray-800/60">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/30">
                    <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Đã kết nối
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      ZNS sẵn sàng
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-lg bg-white/60 p-3 backdrop-blur-sm dark:bg-gray-800/60">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                    <Target className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Templates
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Sẵn sàng sử dụng
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-lg bg-white/60 p-3 backdrop-blur-sm dark:bg-gray-800/60">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/30">
                    <Zap className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Tức thì
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Gửi ngay lập tức
                    </p>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Main Form Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="border-b border-gray-100 dark:border-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-gray-500" />
                  <CardTitle className="text-lg">
                    {t(
                      'integrations:zns.connect.send.formTitle',
                      'Cấu hình tin nhắn',
                    )}
                  </CardTitle>
                </div>
                <Badge
                  variant="outline"
                  className="border-blue-200 bg-blue-50 text-blue-700"
                >
                  <Sparkles className="mr-1 h-3 w-3" />
                  {t('integrations:zns.connect.send.quickSend', 'Gửi nhanh')}
                </Badge>
              </div>
              <CardDescription>
                {t(
                  'integrations:zns.connect.send.formDescription',
                  'Điền thông tin để gửi tin ZNS',
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              {isLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-40 w-full" />
                </div>
              ) : (
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="templateId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Target className="h-4 w-4 text-purple-500" />
                              {t(
                                'integrations:zns.send.templateId',
                                'Chọn mẫu tin',
                              )}
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                              disabled={
                                isLoadingTemplates ||
                                !availableTemplates ||
                                availableTemplates.length === 0
                              }
                            >
                              <FormControl>
                                <SelectTrigger className="border-gray-200 focus:border-purple-500 focus:ring-purple-500">
                                  <SelectValue
                                    placeholder={
                                      isLoadingTemplates
                                        ? t(
                                            'integrations:zns.send.loadingTemplates',
                                            'Đang tải mẫu tin...',
                                          )
                                        : !availableTemplates ||
                                            availableTemplates.length === 0
                                          ? t(
                                              'integrations:zns.send.noTemplatesAvailable',
                                              'Không có mẫu tin khả dụng',
                                            )
                                          : t(
                                              'integrations:zns.send.selectTemplate',
                                              'Chọn mẫu tin ZNS',
                                            )
                                    }
                                  />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {isLoadingTemplates ? (
                                  <div className="flex items-center gap-2 px-2 py-1.5 text-sm text-gray-500">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    {t(
                                      'integrations:zns.send.loadingTemplates',
                                      'Đang tải mẫu tin...',
                                    )}
                                  </div>
                                ) : availableTemplates &&
                                  availableTemplates.length > 0 ? (
                                  availableTemplates.map((template) => (
                                    <SelectItem
                                      key={template.id}
                                      value={template.template_id}
                                    >
                                      <div className="flex items-center gap-2">
                                        <div className="flex h-6 w-6 items-center justify-center rounded bg-purple-100 text-xs font-medium text-purple-600">
                                          {template.template_id.slice(-2)}
                                        </div>
                                        <div className="flex flex-col">
                                          <span className="font-medium">
                                            {template.template_name}
                                          </span>
                                          <span className="text-xs text-gray-500">
                                            ID: {template.template_id}
                                          </span>
                                        </div>
                                      </div>
                                    </SelectItem>
                                  ))
                                ) : (
                                  <div className="flex items-center gap-2 px-2 py-1.5 text-sm text-gray-500">
                                    <AlertCircle className="h-4 w-4 text-orange-500" />
                                    {t(
                                      'integrations:zns.send.noTemplatesAvailable',
                                      'Không có mẫu tin khả dụng',
                                    )}
                                  </div>
                                )}
                              </SelectContent>
                            </Select>
                            <FormDescription className="text-xs">
                              {t(
                                'integrations:zns.send.templateIdDescription',
                                'Chọn mẫu tin ZNS đã được duyệt để gửi',
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('integrations:zns.connect.send.phone')}
                            </FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="84987654321" />
                            </FormControl>
                            <FormDescription>
                              {t(
                                'integrations:zns.connect.send.phoneDescription',
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                      <FormField
                        control={form.control}
                        name="sendingMode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('integrations:zns.connect.send.sendingMode')}
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue
                                    placeholder={t(
                                      'integrations:zns.connect.send.selectSendingMode',
                                    )}
                                  />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="normal">
                                  {t(
                                    'integrations:zns.connect.send.sendingModes.normal',
                                  )}
                                </SelectItem>
                                <SelectItem value="exceed_quota">
                                  {t(
                                    'integrations:zns.connect.send.sendingModes.exceedQuota',
                                  )}
                                </SelectItem>
                                <SelectItem value="development">
                                  {t(
                                    'integrations:zns.connect.send.sendingModes.development',
                                  )}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              {t(
                                'integrations:zns.connect.send.sendingModeDescription',
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="useHashPhone"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                            <FormControl>
                              <input
                                type="checkbox"
                                checked={field.value}
                                onChange={field.onChange}
                                className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                {t(
                                  'integrations:zns.connect.send.useHashPhone',
                                )}
                              </FormLabel>
                              <FormDescription>
                                {t(
                                  'integrations:zns.connect.send.useHashPhoneDescription',
                                )}
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="trackingId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('integrations:zns.connect.send.trackingId')}
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormDescription>
                              {t(
                                'integrations:zns.connect.send.trackingIdDescription',
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* No Templates Alert */}
                    {!isLoadingTemplates &&
                      availableTemplates &&
                      availableTemplates.length === 0 && (
                        <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/30">
                          <AlertCircle className="h-4 w-4 text-orange-600" />
                          <AlertTitle className="text-orange-800 dark:text-orange-200">
                            {t(
                              'integrations:zns.send.noTemplatesTitle',
                              'Chưa có mẫu tin nào',
                            )}
                          </AlertTitle>
                          <AlertDescription className="text-orange-700 dark:text-orange-300">
                            {t(
                              'integrations:zns.send.noTemplatesMessage',
                              'Bạn cần tạo và duyệt ít nhất một mẫu tin ZNS trước khi có thể gửi tin nhắn.',
                            )}
                          </AlertDescription>
                          <div className="mt-3 flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                router.push(
                                  `/home/<USER>/integrations/zns/templates`,
                                )
                              }
                              className="border-orange-300 text-orange-700 hover:bg-orange-100"
                            >
                              <Target className="mr-1.5 h-3.5 w-3.5" />
                              {t(
                                'integrations:zns.send.createTemplate',
                                'Tạo mẫu tin',
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.location.reload()}
                              className="border-orange-300 text-orange-700 hover:bg-orange-100"
                            >
                              <RefreshCw className="mr-1.5 h-3.5 w-3.5" />
                              {t(
                                'integrations:zns.send.refreshTemplates',
                                'Làm mới',
                              )}
                            </Button>
                          </div>
                        </Alert>
                      )}

                    {/* Template Preview */}
                    {templateDetail && (
                      <Card className="border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950/30">
                        <CardHeader className="pb-3">
                          <div className="flex items-center gap-2">
                            <Eye className="h-4 w-4 text-purple-600" />
                            <CardTitle className="text-sm font-medium text-purple-800 dark:text-purple-200">
                              {t(
                                'integrations:zns.send.templatePreview',
                                'Xem trước mẫu tin',
                              )}
                            </CardTitle>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
                            <div className="mb-2 flex items-center gap-2">
                              <div className="h-2 w-2 rounded-full bg-green-500"></div>
                              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                {templateDetail.templateName ||
                                  form.watch('templateId')}
                              </span>
                            </div>
                            <div className="text-sm text-gray-800 dark:text-gray-200">
                              {templateDetail.previewText ||
                                t(
                                  'integrations:zns.send.noPreviewAvailable',
                                  'Không có bản xem trước',
                                )}
                            </div>
                            {templateDetail.listParams &&
                              templateDetail.listParams.length > 0 && (
                                <div className="mt-3 flex flex-wrap gap-1">
                                  {templateDetail.listParams.map((param) => (
                                    <Badge
                                      key={param.name}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {param.name}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {templateDetail &&
                      templateDetail.listParams &&
                      templateDetail.listParams.length > 0 && (
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">
                              {t('integrations:zns.send.parameters', 'Tham số')}
                            </h3>
                            {templateDetail.listParams.some(
                              (param) => param.sample_value,
                            ) && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={handleUseSampleData}
                                className="gap-2 border-blue-200 text-blue-700 hover:bg-blue-50"
                              >
                                <Sparkles className="h-3.5 w-3.5" />
                                {t(
                                  'integrations:zns.send.useSampleData',
                                  'Sử dụng dữ liệu mẫu',
                                )}
                              </Button>
                            )}
                          </div>
                          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            {templateDetail.listParams.map((param, index) => (
                              <FormField
                                key={index}
                                control={form.control}
                                name={`params.${param.name}`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="flex items-center gap-2">
                                      <span>{param.name}</span>
                                      {param.require && (
                                        <span className="text-destructive">
                                          *
                                        </span>
                                      )}
                                      {param.sample_value &&
                                        field.value === param.sample_value && (
                                          <Badge
                                            variant="secondary"
                                            className="text-xs"
                                          >
                                            <Sparkles className="mr-1 h-2.5 w-2.5" />
                                            {t(
                                              'integrations:zns.send.sampleValue',
                                              'Mẫu',
                                            )}
                                          </Badge>
                                        )}
                                    </FormLabel>
                                    <FormControl>
                                      <div className="relative">
                                        <Input
                                          {...field}
                                          value={field.value || ''}
                                          onChange={(e) =>
                                            field.onChange(e.target.value)
                                          }
                                          placeholder={
                                            param.sample_value ||
                                            `Nhập ${param.name.toLowerCase()}`
                                          }
                                          className={
                                            param.sample_value &&
                                            field.value === param.sample_value
                                              ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/30'
                                              : ''
                                          }
                                        />
                                        {param.sample_value &&
                                          field.value ===
                                            param.sample_value && (
                                            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                              <Sparkles className="h-3.5 w-3.5 text-blue-500" />
                                            </div>
                                          )}
                                      </div>
                                    </FormControl>
                                    <FormDescription className="flex items-center justify-between">
                                      <span>
                                        {t(
                                          'integrations:zns.send.paramTypeDescription',
                                          'Loại: {{type}}, Độ dài: {{min}}-{{max}}',
                                          {
                                            type: param.type,
                                            min: param.minLength,
                                            max: param.maxLength,
                                          },
                                        )}
                                      </span>
                                      {param.sample_value &&
                                        field.value !== param.sample_value && (
                                          <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              field.onChange(param.sample_value)
                                            }
                                            className="h-auto p-1 text-xs text-blue-600 hover:text-blue-800"
                                          >
                                            <Sparkles className="mr-1 h-3 w-3" />
                                            {t(
                                              'integrations:zns.send.useSample',
                                              'Dùng mẫu',
                                            )}
                                          </Button>
                                        )}
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            ))}
                          </div>
                        </div>
                      )}

                    {/* Submit Section */}
                    <div className="border-t border-gray-100 pt-6 dark:border-gray-800">
                      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span>
                            {t(
                              'integrations:zns.send.secureDelivery',
                              'Gửi tin an toàn và bảo mật',
                            )}
                          </span>
                        </div>

                        <div className="flex items-center gap-3">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => form.reset()}
                            className="gap-2"
                          >
                            <RefreshCw className="h-4 w-4" />
                            {t('integrations:zns.send.reset', 'Đặt lại')}
                          </Button>

                          <Button
                            type="submit"
                            disabled={
                              isSending ||
                              !isReallyConnected ||
                              !form.watch('templateId') ||
                              !availableTemplates ||
                              availableTemplates.length === 0
                            }
                            className="gap-2 bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg hover:from-blue-600 hover:to-purple-700"
                            size="lg"
                          >
                            {isSending ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin" />
                                {t(
                                  'integrations:zns.send.sending',
                                  'Đang gửi...',
                                )}
                              </>
                            ) : (
                              <>
                                <Send className="h-4 w-4" />
                                {t(
                                  'integrations:zns.send.sendButton',
                                  'Gửi tin ZNS',
                                )}
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>

          {/* Success Result Card */}
          {sendResult && (
            <Card className="border-0 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg dark:from-green-950/30 dark:to-emerald-950/30">
              <CardHeader className="border-b border-green-100 dark:border-green-800">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold text-green-800 dark:text-green-200">
                      {t('integrations:zns.send.success', 'Gửi tin thành công')}
                    </CardTitle>
                    <CardDescription className="text-green-600 dark:text-green-300">
                      {t(
                        'integrations:zns.send.resultDescription',
                        'Tin ZNS đã được gửi thành công',
                      )}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {/* Message ID */}
                  <div className="rounded-lg bg-white/60 p-4 backdrop-blur-sm dark:bg-gray-800/60">
                    <div className="mb-2 flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t('integrations:zns.send.messageId', 'ID tin nhắn')}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 rounded bg-green-100 px-2 py-1 font-mono text-sm text-green-800 dark:bg-green-900/30 dark:text-green-200">
                        {sendResult.msg_id}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          navigator.clipboard.writeText(sendResult.msg_id)
                        }
                        className="h-8 w-8 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Sent Time */}
                  <div className="rounded-lg bg-white/60 p-4 backdrop-blur-sm dark:bg-gray-800/60">
                    <div className="mb-2 flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t('integrations:zns.send.sentTime', 'Thời gian gửi')}
                      </span>
                    </div>
                    <p className="font-mono text-sm text-gray-900 dark:text-gray-100">
                      {new Date(parseInt(sendResult.sent_time)).toLocaleString(
                        'vi-VN',
                      )}
                    </p>
                  </div>

                  {/* Quota Info */}
                  {sendResult.quota && (
                    <div className="rounded-lg bg-white/60 p-4 backdrop-blur-sm dark:bg-gray-800/60">
                      <div className="mb-2 flex items-center gap-2">
                        <Target className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t(
                            'integrations:zns.send.remainingQuota',
                            'Hạn mức còn lại',
                          )}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-purple-600">
                          {sendResult.quota.remainingQuota}
                        </span>
                        <span className="text-sm text-gray-500">
                          / {sendResult.quota.dailyQuota}
                        </span>
                      </div>
                      <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
                        <div
                          className="h-2 rounded-full bg-purple-500"
                          style={{
                            width: `${((sendResult.quota.dailyQuota - sendResult.quota.remainingQuota) / sendResult.quota.dailyQuota) * 100}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="mt-6 flex flex-wrap gap-3">
                  <Button
                    variant="outline"
                    onClick={() => form.reset()}
                    className="gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    {t('integrations:zns.send.sendAnother', 'Gửi tin khác')}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() =>
                      window.open(
                        `/home/<USER>/integrations/zns/analytics`,
                        '_blank',
                      )
                    }
                    className="gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    {t('integrations:zns.send.viewAnalytics', 'Xem thống kê')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </PageBody>
    </>
  );
}
