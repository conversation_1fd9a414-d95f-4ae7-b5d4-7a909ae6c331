'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import {
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  ChevronDown,
  Copy,
  Eye,
  ImageIcon,
  LayoutTemplate,
  Link,
  MousePointerClick,
  Phone,
  PlusCircle,
  Settings2,
  Smartphone,
  Trash2,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Checkbox } from '@kit/ui/checkbox';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@kit/ui/collapsible';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Skeleton } from '@kit/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Textarea } from '@kit/ui/textarea';
import {
  ZnsButtonType,
  ZnsParamType,
  ZnsTemplateLayout,
  uploadZnsImageFromUrl,
  validateZnsTemplate,
} from '@kit/zns';

import { templateSamples } from '~/config/zns-template-samples';
import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { buttonTypeOptions } from './button-types';
import { getTemplateIcon } from './template-icons';
import { templateTypeOptions } from './template-types';

// Schema cho form tạo template
const createTemplateSchema = z.object({
  // Template type and purpose
  templateType: z.enum(['1', '2', '3', '4', '5']).default('1'), // 1: Custom, 2: Authentication, 3: Payment, 4: Voucher, 5: Rating
  templateTag: z.enum(['1', '2', '3']).default('2'), // 1: Transaction, 2: Customer Care, 3: Promotion
  eventType: z.string().optional(), // Event type for key suggestions

  // Basic info
  templateName: z.string().min(1, 'Template name is required'),
  templateNote: z.string().optional(),

  // Header image/logo
  headerImageLight: z.string().optional(),
  headerImageDark: z.string().optional(),
  headerImageLightMediaId: z.string().optional(),
  headerImageDarkMediaId: z.string().optional(),

  // Content
  templateTitle: z.string().min(1, 'Template title is required').default(''),
  templateContent: z.string().min(1, 'Template content is required'),

  // Table data
  tableData: z
    .array(
      z.object({
        title: z.string(),
        value: z.string(),
        row_type: z.number().optional(),
      }),
    )
    .optional(),

  // Action buttons
  buttons: z
    .array(
      z.object({
        text: z.string(),
        actionType: z.string(),
        data: z.string(),
      }),
    )
    .optional(),

  // Parameters
  parameters: z
    .array(
      z.object({
        name: z.string(),
        type: z.string(),
        sample_value: z.string(),
      }),
    )
    .optional(),
});

type CreateTemplateFormValues = z.infer<typeof createTemplateSchema>;

// ZNS Template Type enum
enum ZnsTemplateType {
  CUSTOM = 1,
  AUTHENTICATION = 2,
  PAYMENT_REQUEST = 3,
  VOUCHER = 4,
  SERVICE_RATING = 5,
}

export default function ZnsTemplateCreatePage() {
  const { account: accountSlug } = useParams<{
    account: string;
  }>();
  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [isReallyConnected, setIsReallyConnected] = useState(false);
  const [previewTemplateId, setPreviewTemplateId] = useState<string | null>(
    null,
  );
  const [currentStep, setCurrentStep] = useState<1 | 2>(1);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [isSampleDialogOpen, setIsSampleDialogOpen] = useState(false);

  // Form
  const form = useForm<CreateTemplateFormValues>({
    resolver: zodResolver(createTemplateSchema),
    defaultValues: {
      // Template type and purpose
      templateType: '1', // Custom template
      templateTag: '2', // CUSTOMER_CARE

      // Basic info
      templateName: '',
      templateNote: '',

      // Header image/logo
      headerImageLight: '',
      headerImageDark: '',
      headerImageLightMediaId: '',
      headerImageDarkMediaId: '',

      // Content
      templateTitle: 'Xin chào <customer_name>,',
      templateContent:
        'Cảm ơn bạn đã mua sản phẩm <product_name> tại cửa hàng chúng tôi.',

      // Table data
      tableData: [
        { title: 'Mã đơn hàng', value: '<order_code>' },
        { title: 'Trạng thái', value: '<payment_status>' },
      ],

      // Action buttons
      buttons: [
        {
          text: 'Xem chi tiết',
          actionType: '1', // Đến trang của doanh nghiệp
          data: 'https://example.com',
        },
      ],

      // Parameters
      parameters: [
        {
          name: 'customer_name',
          type: '1', // CUSTOMER_NAME
          sample_value: 'Nguyễn Văn A',
        },
        {
          name: 'product_name',
          type: '5', // CUSTOM_LABEL
          sample_value: 'iPhone 15 Pro',
        },
        {
          name: 'order_code',
          type: '4', // CODE
          sample_value: 'ORD123456',
        },
        {
          name: 'payment_status',
          type: '5', // CUSTOM_LABEL
          sample_value: 'Đã thanh toán',
        },
      ],
    },
  });

  // File input references
  const fileInputLightRef = useRef<HTMLInputElement>(null);
  const fileInputDarkRef = useRef<HTMLInputElement>(null);
  const [isUploadingLight, setIsUploadingLight] = useState(false);
  const [isUploadingDark, setIsUploadingDark] = useState(false);

  // Tìm template mẫu theo ID
  const findTemplateSample = useCallback(
    (templateId: string) => {
      for (const industry of templateSamples) {
        for (const template of industry.presetTemplate) {
          if (template.id === templateId) {
            return template;
          }
        }
      }
      return null;
    },
    [templateSamples],
  );

  // Mở dialog preview template
  const openPreviewDialog = (templateId: string) => {
    setPreviewTemplateId(templateId);
    setIsPreviewDialogOpen(true);
  };

  // Áp dụng template mẫu
  const applyTemplateSample = async (templateId: string) => {
    // Tìm template trong danh sách template mẫu
    const foundTemplate = findTemplateSample(templateId);

    if (!foundTemplate) {
      toast.error(
        t(
          'integrations:zns.connect.templates.templateNotFound',
          'Template not found',
        ),
      );
      return;
    }

    if (!oaConfig) {
      toast.error(
        t('integrations:zns.connect.templates.notConnected', 'Not Connected'),
      );
      return;
    }

    setSelectedTemplate(templateId);

    // Lấy thông tin từ template
    const titleComponent = foundTemplate.section?.body?.components?.find(
      (comp) => comp.TITLE,
    );
    const paragraphComponent = foundTemplate.section?.body?.components?.find(
      (comp) => comp.PARAGRAPH,
    );
    const tableComponent = foundTemplate.section?.body?.components?.find(
      (comp) => comp.TABLE,
    );
    const buttonComponent = foundTemplate.section?.footer?.components?.find(
      (comp) => comp.BUTTON,
    );

    // Điền thông tin vào form
    form.setValue('templateName', foundTemplate.name || '');
    form.setValue(
      'templateTag',
      foundTemplate.reasonTag === 1
        ? '1' // TRANSACTION
        : foundTemplate.reasonTag === 2
          ? '2' // CUSTOMER_CARE
          : '3', // PROMOTION
    );

    // Sử dụng nội dung từ template
    form.setValue(
      'templateContent',
      foundTemplate.notifyMessage ||
        paragraphComponent?.PARAGRAPH?.value ||
        'Chào <n>,\n\nĐây là nội dung mẫu tin thông báo từ Zalo Notification Service.',
    );

    // Áp dụng các tham số từ template mẫu
    if (
      foundTemplate.technicalParams &&
      foundTemplate.technicalParams.length > 0
    ) {
      const templateParams = foundTemplate.technicalParams.map((param) => ({
        name: param.name,
        type: String(param.znsType),
        sample_value: param.sampleValue || `Sample ${param.name}`,
      }));

      form.setValue('parameters', templateParams);
    } else {
      // Nếu không có tham số kỹ thuật, tạo tham số mặc định
      form.setValue('parameters', [
        {
          name: 'name',
          type: ZnsParamType.CUSTOM_LABEL, // CUSTOMER_NAME
          sample_value: 'Nguyễn Văn A',
        },
      ]);
    }

    // Áp dụng dữ liệu bảng nếu có
    if (tableComponent?.TABLE?.rows && tableComponent.TABLE.rows.length > 0) {
      const tableData = tableComponent.TABLE.rows.map((row) => ({
        title: row.key || row.title || '',
        value: row.value || '',
        row_type: row.type || row.row_type || undefined,
      }));
      form.setValue('tableData', tableData);
    } else {
      // Sử dụng dữ liệu bảng mặc định
      form.setValue('tableData', [
        { title: 'Mã đơn', value: '' },
        { title: 'Giá tiền', value: '' },
      ]);
    }

    // Áp dụng cài đặt nút nếu có
    if (
      buttonComponent?.BUTTON?.buttons &&
      buttonComponent.BUTTON.buttons.length > 0
    ) {
      const templateButtons = buttonComponent.BUTTON.buttons.map((button) => {
        return {
          text: button.text || 'Xem chi tiết',
          actionType: button.actionType
            ? button.actionType.toString()
            : ZnsButtonType.TO_BUSINESS_PAGE.toString(),
          data: button.data || 'https://example.com',
        };
      });
      form.setValue('buttons', templateButtons);
    } else {
      // Sử dụng cài đặt nút mặc định
      form.setValue('buttons', [
        {
          text: 'Xem chi tiết',
          actionType: ZnsButtonType.TO_BUSINESS_PAGE.toString(), // Đến trang của doanh nghiệp
          data: 'https://example.com',
        },
      ]);
    }

    // Chỉ đặt URL ảnh mẫu mà không upload
    const defaultImageUrl =
      'https://cdn-icons-png.flaticon.com/512/3135/3135715.png';

    // Đặt URL ảnh cho preview
    form.setValue('headerImageLight', defaultImageUrl);
    form.setValue('headerImageDark', defaultImageUrl);

    // Xóa các media_id cũ (nếu có) để tránh gửi lên server khi submit
    form.setValue('headerImageLightMediaId', '');
    form.setValue('headerImageDarkMediaId', '');

    toast.success(
      t(
        'integrations:zns.connect.templates.templateApplied',
        'Template sample applied successfully',
      ),
    );
  };

  // Fetch ZNS integration
  const { data: integration, isLoading: isLoadingIntegration } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error(
            'Error fetching ZNS integration:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error(
          'Exception fetching ZNS integration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration
  const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
    queryKey: ['oa-config', integration?.metadata?.oa_config_id],
    queryFn: async () => {
      if (!integration?.metadata?.oa_config_id) return null;

      try {
        const { data, error } = await supabase
          .from('oa_configurations')
          .select('*')
          .eq('id', integration.metadata.oa_config_id)
          .maybeSingle();

        if (error) {
          console.error(
            'Error fetching OA config:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        return data;
      } catch (err) {
        console.error(
          'Exception fetching OA config:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!integration?.metadata?.oa_config_id,
  });

  // Check if token is valid
  const hasValidToken =
    oaConfig?.access_token &&
    oaConfig?.token_expires_at &&
    new Date(oaConfig.token_expires_at) > new Date();

  // Tổng hợp trạng thái tải
  const isLoading = isLoadingIntegration || isLoadingOaConfig;

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  useEffect(() => {
    if (!isLoading) {
      setIsReallyConnected(Boolean(integration && hasValidToken));
    }
  }, [integration, hasValidToken, isLoading]);

  // Tạo dữ liệu preview từ template mẫu
  const [previewTemplateData, setPreviewTemplateData] = useState<any>(null);
  const [isLoadingPreviewTemplate, setIsLoadingPreviewTemplate] =
    useState(false);

  // Hàm tạo HTML từ template mẫu
  const generateTemplateHtml = useCallback((template: any) => {
    if (!template) return null;

    // Tạo HTML từ dữ liệu template
    let html = '';

    // Xử lý phần body
    if (
      template.section &&
      template.section.body &&
      template.section.body.components
    ) {
      const bodyComponents = template.section.body.components;

      // Xử lý tiêu đề
      const titleComponent = bodyComponents.find((comp) => comp.TITLE);
      if (titleComponent && titleComponent.TITLE) {
        html += `<h3 class="font-bold text-lg mb-2">${titleComponent.TITLE.value}</h3>`;
      }

      // Xử lý đoạn văn bản
      const paragraphComponent = bodyComponents.find((comp) => comp.PARAGRAPH);
      if (paragraphComponent && paragraphComponent.PARAGRAPH) {
        // Thay thế các tham số bằng giá trị mẫu
        let paragraphText = paragraphComponent.PARAGRAPH.value;

        // Thay thế <name> bằng giá trị mẫu
        paragraphText = paragraphText.replace('<name>', 'Nguyễn Văn A');

        // Thay thế các tham số khác
        if (template.technicalParams) {
          template.technicalParams.forEach((param) => {
            const paramPattern = new RegExp(`<${param.name}>`, 'g');
            paragraphText = paragraphText.replace(
              paramPattern,
              param.sampleValue || `[${param.name}]`,
            );
          });
        }

        // Chuyển \n thành <br>
        paragraphText = paragraphText.replace(/\n/g, '<br>');

        html += `<p class="text-sm mb-4">${paragraphText}</p>`;
      }

      // Xử lý bảng
      const tableComponent = bodyComponents.find((comp) => comp.TABLE);
      if (tableComponent && tableComponent.TABLE && tableComponent.TABLE.rows) {
        html += '<div class="border rounded-md overflow-hidden mb-4">';
        html += '<table class="w-full">';
        html += '<tbody>';

        tableComponent.TABLE.rows.forEach((row) => {
          let rowValue = row.value;

          // Thay thế các tham số trong giá trị hàng
          if (template.technicalParams) {
            template.technicalParams.forEach((param) => {
              const paramPattern = new RegExp(`<${param.name}>`, 'g');
              rowValue = rowValue.replace(
                paramPattern,
                param.sampleValue || `[${param.name}]`,
              );
            });
          }

          html += `
            <tr class="border-b last:border-b-0">
              <td class="py-2 px-3 font-medium text-sm">${row.key || row.title}</td>
              <td class="py-2 px-3 text-sm">${rowValue}</td>
            </tr>
          `;
        });

        html += '</tbody>';
        html += '</table>';
        html += '</div>';
      }
    }

    // Xử lý phần footer (nút)
    if (
      template.section &&
      template.section.footer &&
      template.section.footer.components
    ) {
      const footerComponents = template.section.footer.components;

      // Xử lý nút
      const buttonComponent = footerComponents.find((comp) => comp.BUTTON);
      if (
        buttonComponent &&
        buttonComponent.BUTTON &&
        buttonComponent.BUTTON.buttons
      ) {
        html += '<div class="flex flex-col gap-2">';

        buttonComponent.BUTTON.buttons.forEach((button) => {
          html += `<button class="w-full py-2 px-4 bg-blue-50 text-blue-600 rounded-md text-sm font-medium">${button.text}</button>`;
        });

        html += '</div>';
      }
    }

    return {
      templateId: template.id,
      templateName: template.name,
      templateContent: template.notifyMessage,
      html,
      status: template.status,
      reasonTag: template.reasonTag,
      params:
        template.technicalParams?.map((param) => ({
          name: param.name,
          type: String(param.znsType),
          sampleValue: param.sampleValue,
        })) || [],
    };
  }, []);

  // Cập nhật dữ liệu preview khi previewTemplateId thay đổi
  useEffect(() => {
    if (!previewTemplateId || !isPreviewDialogOpen) {
      setPreviewTemplateData(null);
      return;
    }

    setIsLoadingPreviewTemplate(true);

    // Tìm template trong danh sách template mẫu
    const foundTemplate = findTemplateSample(previewTemplateId);

    if (foundTemplate) {
      // Tạo dữ liệu preview từ template mẫu
      const previewData = generateTemplateHtml(foundTemplate);
      setPreviewTemplateData(previewData);
    } else {
      toast.error(
        t(
          'integrations:zns.connect.templates.templateNotFound',
          'Template not found',
        ),
      );
    }

    setIsLoadingPreviewTemplate(false);
  }, [
    previewTemplateId,
    isPreviewDialogOpen,
    generateTemplateHtml,
    t,
    findTemplateSample,
  ]);

  // Watch for changes in content fields and auto-extract parameters
  const watchedTitle = form.watch('templateTitle');
  const watchedContent = form.watch('templateContent');
  const watchedTableData = form.watch('tableData');

  // Auto-extract parameters when content changes
  useEffect(() => {
    if (currentStep === 1) {
      // Use a timeout to debounce the extraction
      const timeoutId = setTimeout(() => {
        const extractedParams = extractParametersFromContent();
        const currentParams = form.getValues('parameters') || [];

        // Only update if parameters have actually changed
        const currentParamNames = currentParams.map(p => p.name).sort();
        const extractedParamNames = extractedParams.map(p => p.name).sort();

        if (JSON.stringify(currentParamNames) !== JSON.stringify(extractedParamNames)) {
          form.setValue('parameters', extractedParams);
          console.log('Auto-extracted parameters:', extractedParams);
        }
      }, 300); // 300ms debounce

      return () => clearTimeout(timeoutId);
    }
  }, [watchedTitle, watchedContent, watchedTableData, currentStep, form]);

  // Function to extract parameters from all content sources
  const extractParametersFromContent = () => {
    const title = form.getValues('templateTitle') || '';
    const content = form.getValues('templateContent') || '';
    const tableData = form.getValues('tableData') || [];
    const existingParams = form.getValues('parameters') || [];

    // Extract parameters from title, content, and table using <param> format
    const paramRegex = /<([^>]+)>/g;

    // Extract from title
    const titleMatches = [...title.matchAll(paramRegex)];
    const titleParams = titleMatches.map((match) => match[1]);

    // Extract from content
    const contentMatches = [...content.matchAll(paramRegex)];
    const contentParams = contentMatches.map((match) => match[1]);

    // Extract from table data values and titles
    const tableParams = [];
    tableData.forEach((row) => {
      // Check title
      const titleValue = row.title || '';
      const titleMatches = [...titleValue.matchAll(paramRegex)];
      titleMatches.forEach((match) => tableParams.push(match[1]));

      // Check value
      const value = row.value || '';
      const valueMatches = [...value.matchAll(paramRegex)];
      valueMatches.forEach((match) => tableParams.push(match[1]));
    });

    // Combine all unique parameters
    const allParamNames = [...new Set([...titleParams, ...contentParams, ...tableParams])];

    // Create parameter objects
    const newParams = allParamNames.map((name) => {
      // Check if parameter already exists
      const existingParam = existingParams.find((p) => p.name === name);
      if (existingParam) {
        return existingParam;
      }

      // Create new parameter with default values
      return {
        name,
        type:
          name === 'name'
            ? '1' // Customer Name
            : name.includes('code')
              ? '2' // Code
              : name.includes('price') || name.includes('amount')
                ? '3' // Amount
                : name.includes('time') || name.includes('date')
                  ? '4' // Time
                  : name.includes('phone') || name.includes('contact')
                    ? '5' // Contact
                    : '5', // Default to Custom Label
        sample_value:
          name === 'name'
            ? 'Nguyễn Văn A'
            : name.includes('code')
              ? 'ORD-12345'
              : name.includes('price') || name.includes('amount')
                ? '100000'
                : name.includes('time') || name.includes('date')
                  ? '10:00 01/01/2023'
                  : name.includes('phone')
                    ? '0987654321'
                    : `Mẫu ${name}`,
      };
    });

    // Ensure 'name' parameter exists
    if (!newParams.some((p) => p.name === 'name')) {
      newParams.unshift({
        name: 'name',
        type: '1', // Customer Name
        sample_value: 'Nguyễn Văn A',
      });
    }

    return newParams;
  };

  // Function to generate real-time preview from current form data
  const generateRealTimePreview = useCallback(() => {
    const title = form.getValues('templateTitle') || '';
    const content = form.getValues('templateContent') || '';
    const tableData = form.getValues('tableData') || [];
    const currentParams = form.getValues('parameters') || [];

    let html = '';

    // Process title
    if (title) {
      let processedTitle = title;
      currentParams.forEach((param) => {
        const paramPattern = new RegExp(`<${param.name}>`, 'g');
        processedTitle = processedTitle.replace(
          paramPattern,
          param.sample_value || `[${param.name}]`,
        );
      });
      html += `<h3 class="font-bold text-lg mb-2">${processedTitle}</h3>`;
    }

    // Process content
    if (content) {
      let processedContent = content;
      currentParams.forEach((param) => {
        const paramPattern = new RegExp(`<${param.name}>`, 'g');
        processedContent = processedContent.replace(
          paramPattern,
          param.sample_value || `[${param.name}]`,
        );
      });
      // Convert \n to <br>
      processedContent = processedContent.replace(/\n/g, '<br>');
      html += `<p class="text-sm mb-4">${processedContent}</p>`;
    }

    // Process table
    if (tableData && tableData.length > 0) {
      html += '<div class="border rounded-md overflow-hidden mb-4">';
      html += '<table class="w-full">';
      html += '<tbody>';

      tableData.forEach((row) => {
        let processedTitle = row.title || '';
        let processedValue = row.value || '';

        // Replace parameters in both title and value
        currentParams.forEach((param) => {
          const paramPattern = new RegExp(`<${param.name}>`, 'g');
          processedTitle = processedTitle.replace(
            paramPattern,
            param.sample_value || `[${param.name}]`,
          );
          processedValue = processedValue.replace(
            paramPattern,
            param.sample_value || `[${param.name}]`,
          );
        });

        html += `
          <tr class="border-b last:border-b-0">
            <td class="py-2 px-3 font-medium text-sm">${processedTitle}</td>
            <td class="py-2 px-3 text-sm">${processedValue}</td>
          </tr>
        `;
      });

      html += '</tbody>';
      html += '</table>';
      html += '</div>';
    }

    return html;
  }, [form]);

  // Handle next step
  const handleNextStep = () => {
    // Validate current step fields
    if (currentStep === 1) {
      // Validate required fields for step 1 using form validation
      const {
        templateName,
        templateTitle,
        templateContent,
        templateType,
        templateTag,
      } = form.getValues();
      let hasErrors = false;

      // Check each required field and set specific errors
      if (!templateName) {
        form.setError('templateName', {
          type: 'manual',
          message: t(
            'integrations:zns.connect.templates.nameRequired',
            'Template name is required',
          ),
        });
        hasErrors = true;
      }

      if (!templateTitle) {
        form.setError('templateTitle', {
          type: 'manual',
          message: t(
            'integrations:zns.connect.templates.titleRequired',
            'Template title is required',
          ),
        });
        hasErrors = true;
      }

      if (!templateContent) {
        form.setError('templateContent', {
          type: 'manual',
          message: t(
            'integrations:zns.connect.templates.contentRequired',
            'Template content is required',
          ),
        });
        hasErrors = true;
      }

      if (!templateType) {
        form.setError('templateType', {
          type: 'manual',
          message: t(
            'integrations:zns.connect.templates.typeRequired',
            'Template type is required',
          ),
        });
        hasErrors = true;
      }

      if (!templateTag) {
        form.setError('templateTag', {
          type: 'manual',
          message: t(
            'integrations:zns.connect.templates.tagRequired',
            'Template tag is required',
          ),
        });
        hasErrors = true;
      }

      // Check for header images if they're required
      const headerImageLight = form.getValues('headerImageLight');
      const headerImageDark = form.getValues('headerImageDark');
      const headerImageLightMediaId = form.getValues('headerImageLightMediaId');
      const headerImageDarkMediaId = form.getValues('headerImageDarkMediaId');

      if (!headerImageLight && !headerImageLightMediaId) {
        form.setError('headerImageLight', {
          type: 'manual',
          message: t(
            'integrations:zns.connect.templates.lightImageRequired',
            'Light mode header image is required',
          ),
        });
        hasErrors = true;
      }

      if (!headerImageDark && !headerImageDarkMediaId) {
        form.setError('headerImageDark', {
          type: 'manual',
          message: t(
            'integrations:zns.connect.templates.darkImageRequired',
            'Dark mode header image is required',
          ),
        });
        hasErrors = true;
      }

      if (hasErrors) {
        toast.error(
          t(
            'integrations:zns.connect.templates.validationErrors',
            'Please correct the errors before proceeding',
          ),
        );
        return;
      }

      // Extract parameters from content and table data
      const extractedParams = extractParametersFromContent();
      form.setValue('parameters', extractedParams);

      // Move to step 2
      setCurrentStep(2);
      window.scrollTo(0, 0);
    }
  };

  // Handle previous step
  const handlePrevStep = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
      window.scrollTo(0, 0);
    }
  };

  // Handle form submission
  const onSubmit = async (values: CreateTemplateFormValues) => {
    console.log('Form submission started', values);

    // Validate all required fields first
    let hasErrors = false;

    // Check required fields in step 1
    if (!values.templateName) {
      form.setError('templateName', {
        type: 'manual',
        message: t(
          'integrations:zns.templates.nameRequired',
          'Template name is required',
        ),
      });
      hasErrors = true;
    }

    if (!values.templateTitle) {
      form.setError('templateTitle', {
        type: 'manual',
        message: t(
          'integrations:zns.templates.titleRequired',
          'Template title is required',
        ),
      });
      hasErrors = true;
    }

    if (!values.templateContent) {
      form.setError('templateContent', {
        type: 'manual',
        message: t(
          'integrations:zns.templates.contentRequired',
          'Template content is required',
        ),
      });
      hasErrors = true;
    }

    if (!values.templateType) {
      form.setError('templateType', {
        type: 'manual',
        message: t(
          'integrations:zns.templates.typeRequired',
          'Template type is required',
        ),
      });
      hasErrors = true;
    }

    if (!values.templateTag) {
      form.setError('templateTag', {
        type: 'manual',
        message: t(
          'integrations:zns.templates.tagRequired',
          'Template tag is required',
        ),
      });
      hasErrors = true;
    }

    // Check for header images
    if (!values.headerImageLightMediaId && !values.headerImageLight) {
      form.setError('headerImageLight', {
        type: 'manual',
        message: t(
          'integrations:zns.templates.lightImageRequired',
          'Light mode header image is required',
        ),
      });
      hasErrors = true;
    }

    if (!values.headerImageDarkMediaId && !values.headerImageDark) {
      form.setError('headerImageDark', {
        type: 'manual',
        message: t(
          'integrations:zns.templates.darkImageRequired',
          'Dark mode header image is required',
        ),
      });
      hasErrors = true;
    }

    if (hasErrors) {
      toast.error(
        t(
          'integrations:zns.templates.validationErrors',
          'Please correct the errors before submitting',
        ),
      );
      return;
    }

    setIsSubmitting(true);

    if (!isReallyConnected || !oaConfig) {
      console.log('Not connected, aborting submission');
      toast.error(
        t('integrations:zns.connect.templates.notConnected', 'Not Connected'),
      );
      setIsSubmitting(false);
      return;
    }

    // Log all form values for debugging
    console.log('All form values:', form.getValues());

    // Validate step 2 fields if we're on step 2
    if (currentStep === 2) {
      console.log('Validating step 2 fields');
      let hasErrors = false;

      // Check if terms are agreed
      if (!agreeTerms) {
        console.log('Terms not agreed');
        toast.error(
          t(
            'integrations:zns.connect.templates.agreeTermsRequired',
            'Please agree to the terms and conditions',
          ),
        );
        setIsSubmitting(false);
        return;
      }

      console.log('Terms agreed, continuing submission');

      // Validate parameters
      const parameters = form.getValues('parameters') || [];
      parameters.forEach((param, index) => {
        if (!param.sample_value) {
          form.setError(`parameters.${index}.sample_value`, {
            type: 'manual',
            message: t(
              'integrations:zns.connect.templates.paramValueRequired',
              'Parameter value is required',
            ),
          });
          hasErrors = true;
        }

        // Check if type exists and is valid
        const paramType = Number(param.type);
        if (
          !param.type ||
          isNaN(paramType) ||
          paramType < 1 ||
          paramType > 15
        ) {
          form.setError(`parameters.${index}.type`, {
            type: 'manual',
            message: t(
              'integrations:zns.connect.templates.paramTypeRequired',
              'Parameter type is required and must be between 1 and 15',
            ),
          });
          hasErrors = true;
        }
      });

      if (hasErrors) {
        // Show more specific error message with details
        toast.error(
          t(
            'integrations:zns.connect.templates.validationErrors',
            'Some parameters have errors. Submission will continue but may fail if errors are not fixed.',
          ),
        );
        // Continue with submission despite errors
      }
    }

    setIsSubmitting(true);

    try {
      // Kiểm tra loại template
      const templateType = Number(values.templateType) as ZnsTemplateType;

      // Tạo layout template trước để kiểm tra validation
      let templateLayout: ZnsTemplateLayout;
      let params: ZnsTemplateParam[] = [];

      try {
        // Tạo các đoạn văn bản từ nội dung
        const paragraphs = values.templateContent
          .split('\n\n')
          .filter((p) => p.trim())
          .map((p) => ({ value: p, params: extractParamsFromText(p) }));

        // Tạo bảng dữ liệu nếu có
        const table =
          values.tableData && values.tableData.length > 0
            ? {
                rows: values.tableData.map((row) => ({
                  title: row.title || 'Mục',
                  value: row.value,
                  row_type: row.row_type || undefined,
                })),
              }
            : undefined;

        // Tạo header image tạm thời (sử dụng media_id nếu có, nếu không thì sử dụng giá trị tạm thời)
        const headerImage = {
          light_media_id: values.headerImageLightMediaId || 'temp_light_id',
          dark_media_id: values.headerImageDarkMediaId || 'temp_dark_id',
        };

        // Tạo buttons
        const buttons =
          values.buttons?.map((button) => {
            return {
              content: button.data,
              title: button.text,
              type: Number(button.actionType) as ZnsButtonType,
            };
          }) || [];

        // Tạo layout từ các thành phần dựa trên loại template
        switch (templateType) {
          case 1: // Custom template
            templateLayout = createCustomTemplateLayout(
              values.templateTitle,
              paragraphs,
              table,
              headerImage,
              buttons,
            );
            break;
          case 2: // Authentication template
            toast.error('Authentication template type not implemented yet');
            setIsSubmitting(false);
            return;
          case 3: // Payment Request template
            toast.error('Payment Request template type not implemented yet');
            setIsSubmitting(false);
            return;
          case 4: // Voucher template
            toast.error('Voucher template type not implemented yet');
            setIsSubmitting(false);
            return;
          case 5: // Service Rating template
            toast.error('Service Rating template type not implemented yet');
            setIsSubmitting(false);
            return;
          default:
            toast.error('Invalid template type');
            setIsSubmitting(false);
            return;
        }

        // Trích xuất tham số từ nội dung
        params = extractParametersFromContent();

        // Kiểm tra validation của template layout sử dụng hàm validateZnsTemplate từ package
        const validationResult = validateZnsTemplate(
          templateType,
          templateLayout,
          params,
        );

        if (!validationResult.valid) {
          toast.error(
            `Template validation failed: ${validationResult.error?.details || validationResult.error?.message}`,
          );
          setIsSubmitting(false);
          return;
        }
      } catch (error) {
        console.error('Error creating template layout:', error);
        toast.error(
          t(
            'integrations:zns.templates.layoutError',
            'Error creating template layout: {{message}}',
            { message: error instanceof Error ? error.message : String(error) },
          ),
        );
        setIsSubmitting(false);
        return;
      }

      // Sau khi validation thành công, tiến hành upload ảnh
      if (!values.headerImageLightMediaId || !values.headerImageDarkMediaId) {
        // Nếu chưa có media_id nhưng có URL ảnh, thử upload ảnh
        try {
          toast.info(
            t(
              'integrations:zns.connect.templates.uploadingImages',
              'Uploading header images...',
            ),
          );

          // Upload light mode image
          if (values.headerImageLight && !values.headerImageLightMediaId) {
            const resultLight = await uploadZnsImageFromUrl(
              supabase,
              oaConfig.id,
              values.headerImageLight,
            );
            values.headerImageLightMediaId = resultLight.media_id;
          }

          // Upload dark mode image
          if (values.headerImageDark && !values.headerImageDarkMediaId) {
            const resultDark = await uploadZnsImageFromUrl(
              supabase,
              oaConfig.id,
              values.headerImageDark,
            );
            values.headerImageDarkMediaId = resultDark.media_id;
          }

          // Kiểm tra lại sau khi upload
          if (
            !values.headerImageLightMediaId ||
            !values.headerImageDarkMediaId
          ) {
            toast.error(
              t(
                'integrations:zns.connect.templates.headerImageRequired',
                'Header images are required for both light and dark modes',
              ),
            );
            setIsSubmitting(false);
            return;
          }
        } catch (error) {
          console.error('Error uploading images:', error);
          toast.error(
            t(
              'integrations:zns.connect.templates.imageUploadError',
              'Error uploading images. Please try uploading images manually.',
            ),
          );
          setIsSubmitting(false);
          return;
        }
      }

      // Cập nhật lại header image với media_id thật
      templateLayout.header.components.forEach((component) => {
        if (component.LOGO) {
          component.LOGO.light.media_id = values.headerImageLightMediaId;
          component.LOGO.dark.media_id = values.headerImageDarkMediaId;
        }
      });

      // Hàm tạo layout cho template tùy chỉnh
      function createCustomTemplateLayout(
        title: string,
        paragraphs: { value: string; params: string[] }[],
        table?: { rows: { title: string; value: string; row_type?: number }[] },
        headerImage?: { light_media_id: string; dark_media_id: string },
        buttons?: { content: string; title: string; type: ZnsButtonType }[],
      ): ZnsTemplateLayout {
        const layout: ZnsTemplateLayout = {
          header: {
            components: [],
          },
          body: {
            components: [
              {
                TITLE: {
                  value: title,
                },
              },
            ],
          },
          footer: {
            components: [],
          },
        };

        // Thêm ảnh header
        if (headerImage) {
          layout.header.components.push({
            LOGO: {
              light: {
                type: 'IMAGE',
                media_id: headerImage.light_media_id,
              },
              dark: {
                type: 'IMAGE',
                media_id: headerImage.dark_media_id,
              },
            },
          });
        }

        // Thêm các đoạn văn bản
        paragraphs.forEach((paragraph) => {
          layout.body.components.push({
            PARAGRAPH: {
              value: paragraph.value,
            },
          });
        });

        // Thêm bảng nếu có
        if (table && table.rows.length > 0) {
          layout.body.components.push({
            TABLE: {
              rows: table.rows,
            },
          });
        }

        // Thêm buttons
        if (buttons && buttons.length > 0) {
          layout.footer.components.push({
            BUTTONS: {
              items: buttons.map((button) => ({
                title: button.title,
                type: button.type,
                content: button.content,
              })),
            },
          });
        }

        return layout;
      }

      // Hàm trích xuất các tham số từ văn bản
      function extractParamsFromText(text: string): string[] {
        const paramRegex = /{{([^}]+)}}/g;
        const matches = [...text.matchAll(paramRegex)];
        return matches.map((match) => match[1]);
      }

      // Đảm bảo có tham số 'name' nếu chưa có
      if (!params.some((p) => p.name === 'name')) {
        params.unshift({
          type: 1, // CUSTOMER_NAME - use number instead of string
          name: 'name',
          sample_value: 'Nguyễn Văn A',
        });
      }

      // Convert all param types to numbers (not strings)
      params = params.map((param) => ({
        ...param,
        type:
          typeof param.type === 'string'
            ? parseInt(param.type, 10)
            : param.type,
      }));

      // Ensure all param types are valid (1-15)
      params = params.filter((param) => {
        const type = Number(param.type);
        return type >= 1 && type <= 15;
      });

      // Tạo template thông qua API route (server-to-server)
      const response = await axios.post('/api/zns/templates', {
        oaConfigId: oaConfig.id,
        accountId: account?.id,
        templateName: values.templateName,
        templateTag: values.templateTag,
        templateType: values.templateType,
        layout: templateLayout,
        params: params,
        templateNote: values.templateNote,
        trackingId: `template_${Date.now()}`, // tracking_id
      });

      // Kiểm tra kết quả
      if (response.data.error !== 0) {
        throw new Error(`Failed to create template: ${response.data.message}`);
      }

      const result = response.data.data;

      toast.success(
        t(
          'integrations:zns.connect.templates.createSuccess',
          'Template created successfully',
        ),
      );

      // Chuyển hướng đến trang xem template
      router.push(
        `/home/<USER>/integrations/zns/templates/view/${result.template_id}`,
      );
    } catch (error: any) {
      console.error('Error creating template:', error);
      console.log(
        'Error details:',
        error instanceof Error ? error.message : error,
      );

      // Kiểm tra xem có thông báo lỗi chi tiết không
      const errorResponse = error.response?.data;
      let errorMessage = '';

      if (errorResponse?.details) {
        // Sử dụng thông báo lỗi chi tiết nếu có
        errorMessage = errorResponse.details;
      } else if (errorResponse?.error) {
        // Sử dụng thông báo lỗi chung nếu không có chi tiết
        errorMessage = errorResponse.error;
      } else if (error.message) {
        // Sử dụng thông báo lỗi của Error object
        errorMessage = error.message;
      } else {
        // Thông báo lỗi mặc định
        errorMessage = t(
          'integrations:zns.connect.templates.createError',
          'Error creating template',
        );
      }

      // Kiểm tra nếu là lỗi có thể retry
      const errorCode = errorResponse?.error?.error || errorResponse?.error;
      if (errorCode === -153) {
        toast.error(`${errorMessage}\n\nHệ thống đã tự động thử lại 3 lần nhưng vẫn thất bại. Vui lòng thử lại sau ít phút.`);
      } else {
        // Hiển thị thông báo lỗi chi tiết
        toast.error(`Error: ${errorMessage}`);
      }
    } finally {
      console.log('Form submission completed');
      setIsSubmitting(false);
    }
  };

  // Use the isLoading state defined above

  // Lấy template mẫu hiện tại
  const currentTemplate = selectedTemplate
    ? findTemplateSample(selectedTemplate)
    : null;

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={t(
          'integrations:zns.connect.templates.createTemplate',
          'Create New Template',
        )}
        description={
          <AppBreadcrumbs
            values={{
              home: t('common:routes.home', 'Trang chủ'),
              [accountSlug]: accountSlug,
              integrations: t('common:routes.integrations', 'Tích hợp'),
              zns: 'ZNS',
              templates: t('integrations:zns.connect.templates.title', 'Mẫu'),
              create: t(
                'integrations:zns.connect.templates.createTemplate',
                'Tạo mẫu mới',
              ),
            }}
          />
        }
        account={accountSlug}
      />
      <PageBody data-testid="zns-template-create-page">
        <div className="relative space-y-6 pb-8">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                router.push(`/home/<USER>/integrations/zns/templates`)
              }
              className="gap-1"
            >
              <ArrowLeft className="h-3.5 w-3.5" />
              {t('common:back')}
            </Button>
          </div>

          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-[300px] w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : (
            <>
              {/* Hiển thị thông báo khi chưa kết nối */}
              {!isReallyConnected && (
                <Alert
                  variant="destructive"
                  className="border-l-destructive border-l-4"
                >
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>
                    {t(
                      'integrations:zns.connect.templates.notConnected',
                      'Not Connected',
                    )}
                  </AlertTitle>
                  <AlertDescription>
                    {t(
                      'integrations:zns.connect.templates.notConnectedDescription',
                      'Your Zalo OA is not connected. Please connect to create templates.',
                    )}
                  </AlertDescription>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() =>
                      router.push(
                        `/home/<USER>/integrations/zns/connect`,
                      )
                    }
                  >
                    {t(
                      'integrations:zns.connect.templates.connectNow',
                      'Connect Now',
                    )}
                  </Button>
                </Alert>
              )}

              {/* Thông báo khi token không hợp lệ */}
              {integration && !hasValidToken && (
                <Alert
                  variant="destructive"
                  className="border-l-destructive border-l-4"
                >
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>{t('integrations:zns.tokenExpired')}</AlertTitle>
                  <AlertDescription>
                    {t('integrations:zns.tokenExpiredDescription')}
                  </AlertDescription>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() =>
                      router.push(
                        `/home/<USER>/integrations/zns/connect`,
                      )
                    }
                  >
                    {t('integrations:zns.reconnect')}
                  </Button>
                </Alert>
              )}

              {/* Hiển thị form khi đã kết nối */}
              {isReallyConnected && (
                <Card>
                  <CardHeader className="pb-6">
                    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                      <div>
                        <CardTitle className="text-2xl font-bold">
                          {currentStep === 1
                            ? t(
                                'integrations:zns.connect.templates.createTemplate',
                                'Create New Template',
                              )
                            : t(
                                'integrations:zns.connect.templates.configureParameters',
                                'Configure Parameters',
                              )}
                        </CardTitle>
                        <CardDescription className="mt-1.5 text-base">
                          {currentStep === 1
                            ? t(
                                'integrations:zns.connect.templates.createTemplateDescription',
                                'Create a new template for Zalo Notification Service',
                              )
                            : t(
                                'integrations:zns.connect.templates.configureParametersDescription',
                                'Configure parameters and add review notes to help with approval',
                              )}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-2 shadow-sm">
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={currentStep === 1 ? 'default' : 'outline'}
                            className="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium"
                          >
                            1
                          </Badge>
                          <span
                            className={`text-sm font-medium ${currentStep === 1 ? 'text-primary' : 'text-muted-foreground'}`}
                          >
                            {t(
                              'integrations:zns.connect.templates.stepDesign',
                              'Design',
                            )}
                          </span>
                        </div>
                        <div className="h-px w-6 bg-gray-200"></div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={currentStep === 2 ? 'default' : 'outline'}
                            className="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium"
                          >
                            2
                          </Badge>
                          <span
                            className={`text-sm font-medium ${currentStep === 2 ? 'text-primary' : 'text-muted-foreground'}`}
                          >
                            {t(
                              'integrations:zns.connect.templates.stepParams',
                              'Parameters',
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {currentStep === 1 ? (
                      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                        {/* Form bên trái - Step 1 */}
                        <div className="space-y-6">
                          <div className="flex items-center justify-between rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4 shadow-sm">
                            <h3 className="flex items-center gap-2 text-lg font-medium text-blue-700">
                              <Settings2 className="h-5 w-5 text-blue-500" />
                              {t(
                                'integrations:zns.connect.templates.editTemplate',
                                'Template Settings',
                              )}
                            </h3>
                            <Dialog
                              open={isSampleDialogOpen}
                              onOpenChange={setIsSampleDialogOpen}
                            >
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="gap-1 border-blue-200 bg-white text-blue-700 hover:bg-blue-50"
                                  onClick={() => setIsSampleDialogOpen(true)}
                                >
                                  <LayoutTemplate className="h-3.5 w-3.5" />
                                  {t(
                                    'integrations:zns.connect.templates.useSample',
                                    'Use Sample',
                                  )}
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[600px]">
                                <DialogHeader>
                                  <DialogTitle>
                                    {t(
                                      'integrations:zns.connect.templates.sampleTemplates',
                                      'Sample Templates',
                                    )}
                                  </DialogTitle>
                                  <DialogDescription>
                                    {t(
                                      'integrations:zns.connect.templates.selectSampleTemplate',
                                      'Select a sample template to get started quickly',
                                    )}
                                  </DialogDescription>
                                </DialogHeader>
                                <Tabs
                                  defaultValue={
                                    templateSamples[0]?.industryId?.toString() ||
                                    '1'
                                  }
                                  className="mt-4"
                                >
                                  <div className="overflow-x-auto pb-2">
                                    <TabsList className="mb-4 inline-flex w-auto">
                                      {templateSamples.map((industry) => (
                                        <TabsTrigger
                                          key={industry.industryId}
                                          value={
                                            industry.industryId?.toString() ||
                                            ''
                                          }
                                          className="whitespace-nowrap"
                                        >
                                          {industry.industryName}
                                        </TabsTrigger>
                                      ))}
                                    </TabsList>
                                  </div>

                                  {templateSamples.map((industry) => (
                                    <TabsContent
                                      key={industry.industryId}
                                      value={
                                        industry.industryId?.toString() || ''
                                      }
                                      className="max-h-[400px] overflow-y-auto pr-4"
                                    >
                                      <div className="space-y-2">
                                        {industry.presetTemplate.map(
                                          (template) => (
                                            <div
                                              key={template.id}
                                              className={`hover:bg-accent flex cursor-pointer items-start gap-4 rounded-lg border p-4 transition-colors ${selectedTemplate === template.id ? 'border-primary bg-accent' : ''}`}
                                            >
                                              <div className="flex-1">
                                                <div className="mb-2 flex items-center justify-between">
                                                  <h4 className="text-sm font-medium">
                                                    {template.name}
                                                  </h4>
                                                  <Badge
                                                    variant="outline"
                                                    className="text-xs"
                                                  >
                                                    {template.reasonTag === 1
                                                      ? 'TRANSACTION'
                                                      : template.reasonTag === 2
                                                        ? 'CUSTOMER_CARE'
                                                        : 'PROMOTION'}
                                                  </Badge>
                                                </div>
                                                <p className="text-muted-foreground mb-3 text-xs">
                                                  {template.description?.substring(
                                                    0,
                                                    60,
                                                  ) || ''}
                                                  ...
                                                </p>
                                                <div className="flex items-center justify-between">
                                                  <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="gap-1"
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      openPreviewDialog(
                                                        template.id,
                                                      );
                                                    }}
                                                  >
                                                    <Eye className="h-3.5 w-3.5" />
                                                    {t(
                                                      'integrations:zns.templates.preview',
                                                      'Preview',
                                                    )}
                                                  </Button>
                                                  <Button
                                                    variant="default"
                                                    size="sm"
                                                    className="gap-1"
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      applyTemplateSample(
                                                        template.id,
                                                      );
                                                      setIsSampleDialogOpen(
                                                        false,
                                                      );
                                                    }}
                                                  >
                                                    <Copy className="h-3.5 w-3.5" />
                                                    {t(
                                                      'integrations:zns.templates.use',
                                                      'Use',
                                                    )}
                                                  </Button>
                                                </div>
                                              </div>
                                            </div>
                                          ),
                                        )}
                                      </div>
                                    </TabsContent>
                                  ))}
                                </Tabs>
                              </DialogContent>
                            </Dialog>
                          </div>
                          <Form {...form}>
                            <form
                              onSubmit={form.handleSubmit(onSubmit)}
                              className="space-y-6"
                            >
                              <FormField
                                control={form.control}
                                name="templateType"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>
                                      {t(
                                        'integrations:zns.connect.templates.templateType',
                                        'Template Type',
                                      )}
                                    </FormLabel>
                                    <FormControl>
                                      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5">
                                        {templateTypeOptions.map((option) => (
                                          <div
                                            key={option.id}
                                            className={`hover:border-primary cursor-pointer rounded-md border p-4 transition-all ${field.value === option.id ? 'border-primary bg-primary/5' : ''}`}
                                            onClick={() =>
                                              field.onChange(option.id)
                                            }
                                          >
                                            <div className="flex flex-col items-center space-y-2 text-center">
                                              <div className="h-6 w-6">
                                                {getTemplateIcon(option.icon)}
                                              </div>
                                              <div>
                                                <p className="mb-1 text-sm font-medium">
                                                  {option.name}
                                                </p>
                                                <p className="text-xs text-blue-600">
                                                  {option.price}
                                                </p>
                                              </div>
                                              {option.isNew && (
                                                <span className="rounded bg-blue-100 px-1.5 py-0.5 text-[10px] text-blue-800">
                                                  {t(
                                                    'integrations:zns.templates.new',
                                                    'Mới',
                                                  )}
                                                </span>
                                              )}
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="templateName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>
                                      {t(
                                        'integrations:zns.connect.templates.templateName',
                                        'Template Name',
                                      )}
                                    </FormLabel>
                                    <FormControl>
                                      <Input {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="templateTag"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>
                                      {t(
                                        'integrations:zns.connect.templates.templateTag',
                                        'Template Tag',
                                      )}
                                    </FormLabel>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue
                                            placeholder={t(
                                              'integrations:zns.connect.templates.selectTag',
                                              'Select tag',
                                            )}
                                          />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="1">
                                          {t(
                                            'integrations:zns.connect.templates.tag.transaction',
                                            'Transaction',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="2">
                                          {t(
                                            'integrations:zns.connect.templates.tag.customerCare',
                                            'Customer Care',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="3">
                                          {t(
                                            'integrations:zns.connect.templates.tag.promotion',
                                            'Promotion',
                                          )}
                                        </SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <div className="space-y-4">
                                <h3 className="text-lg font-medium">
                                  {t(
                                    'integrations:zns.connect.templates.logoImages',
                                    'Logo/Images',
                                  )}
                                </h3>
                                <p className="text-sm text-gray-500">
                                  {t(
                                    'integrations:zns.connect.templates.logoImagesDescription',
                                    'Upload logos for light and dark modes. These are required for ZNS templates.',
                                  )}
                                </p>

                                <div className="grid grid-cols-2 gap-4">
                                  {/* Light Mode Logo */}
                                  <FormField
                                    control={form.control}
                                    name="headerImageLight"
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>
                                          {t(
                                            'integrations:zns.connect.templates.headerImageLight',
                                            'Light Mode Logo',
                                          )}
                                        </FormLabel>
                                        <FormControl>
                                          <div className="space-y-2">
                                            <input
                                              type="file"
                                              accept="image/png"
                                              className="hidden"
                                              ref={fileInputLightRef}
                                              onChange={async (e) => {
                                                const file =
                                                  e.target.files?.[0];
                                                if (!file || !oaConfig) return;

                                                try {
                                                  // Validate file type
                                                  if (
                                                    file.type !== 'image/png'
                                                  ) {
                                                    toast.error(
                                                      t(
                                                        'integrations:zns.connect.templates.imageTypeError',
                                                        'Only PNG format is allowed',
                                                      ),
                                                    );
                                                    return;
                                                  }

                                                  // Validate image dimensions
                                                  const img =
                                                    document.createElement(
                                                      'img',
                                                    );
                                                  img.src =
                                                    URL.createObjectURL(file);

                                                  img.onload = async () => {
                                                    if (
                                                      img.width < 400 ||
                                                      img.height < 96
                                                    ) {
                                                      toast.error(
                                                        t(
                                                          'integrations:zns.templates.imageSizeError',
                                                          'Image must be at least 400x96 pixels',
                                                        ),
                                                      );
                                                      URL.revokeObjectURL(
                                                        img.src,
                                                      );
                                                      return;
                                                    }

                                                    URL.revokeObjectURL(
                                                      img.src,
                                                    );
                                                    setIsUploadingLight(true);

                                                    // Create a temporary URL for preview
                                                    const previewUrl =
                                                      URL.createObjectURL(file);
                                                    form.setValue(
                                                      'headerImageLight',
                                                      previewUrl,
                                                    );

                                                    // Convert file to base64
                                                    const reader =
                                                      new FileReader();
                                                    reader.readAsDataURL(file);
                                                    reader.onload =
                                                      async () => {
                                                        try {
                                                          // Upload image to Zalo
                                                          const result =
                                                            await uploadZnsImageFromUrl(
                                                              supabase,
                                                              oaConfig.id,
                                                              reader.result as string,
                                                            );

                                                          // Save media_id
                                                          form.setValue(
                                                            'headerImageLightMediaId',
                                                            result.media_id,
                                                          );
                                                          toast.success(
                                                            t(
                                                              'integrations:zns.templates.imageUploaded',
                                                              'Image uploaded successfully',
                                                            ),
                                                          );
                                                        } catch (error) {
                                                          console.error(
                                                            'Error uploading image:',
                                                            error,
                                                          );
                                                          toast.error(
                                                            error instanceof
                                                              Error
                                                              ? error.message
                                                              : t(
                                                                  'integrations:zns.templates.imageUploadError',
                                                                  'Error uploading image',
                                                                ),
                                                          );
                                                          form.setValue(
                                                            'headerImageLight',
                                                            '',
                                                          );
                                                        } finally {
                                                          setIsUploadingLight(
                                                            false,
                                                          );
                                                        }
                                                      };
                                                  };
                                                } catch (error) {
                                                  console.error(
                                                    'Error handling file:',
                                                    error,
                                                  );
                                                  setIsUploadingLight(false);
                                                  toast.error(
                                                    t(
                                                      'integrations:zns.templates.imageUploadError',
                                                      'Error uploading image',
                                                    ),
                                                  );
                                                }
                                              }}
                                            />

                                            {field.value ? (
                                              <div className="relative">
                                                <img
                                                  src={field.value}
                                                  alt="Light Mode Logo"
                                                  className="h-32 w-full rounded-md object-cover"
                                                />
                                                <Button
                                                  type="button"
                                                  variant="destructive"
                                                  size="sm"
                                                  className="absolute top-2 right-2"
                                                  onClick={() => {
                                                    form.setValue(
                                                      'headerImageLight',
                                                      '',
                                                    );
                                                    form.setValue(
                                                      'headerImageLightMediaId',
                                                      '',
                                                    );
                                                  }}
                                                >
                                                  {t(
                                                    'integrations:zns.templates.removeImage',
                                                    'Remove',
                                                  )}
                                                </Button>
                                              </div>
                                            ) : (
                                              <div
                                                className="border-input bg-background hover:bg-accent hover:text-accent-foreground flex h-32 cursor-pointer flex-col items-center justify-center rounded-md border border-dashed text-center"
                                                onClick={() =>
                                                  fileInputLightRef.current?.click()
                                                }
                                              >
                                                {isUploadingLight ? (
                                                  <div className="flex flex-col items-center justify-center space-y-2">
                                                    <div className="border-primary h-8 w-8 animate-spin rounded-full border-2 border-t-transparent"></div>
                                                    <p className="text-muted-foreground text-sm">
                                                      {t(
                                                        'integrations:zns.templates.uploading',
                                                        'Uploading...',
                                                      )}
                                                    </p>
                                                  </div>
                                                ) : (
                                                  <>
                                                    <div className="bg-muted/30 mb-2 rounded-full p-2">
                                                      <ImageIcon className="text-muted-foreground h-6 w-6" />
                                                    </div>
                                                    <p className="text-muted-foreground text-center text-sm">
                                                      {t(
                                                        'integrations:zns.templates.uploadLightLogo',
                                                        'Upload light mode logo (required)',
                                                      )}
                                                      <br />
                                                      <span className="text-xs">
                                                        {t(
                                                          'integrations:zns.templates.imageRequirements',
                                                          'Định dạng PNG. Kích thước tối thiểu 400x96 px.',
                                                        )}
                                                      </span>
                                                    </p>
                                                  </>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  {/* Dark Mode Logo */}
                                  <FormField
                                    control={form.control}
                                    name="headerImageDark"
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>
                                          {t(
                                            'integrations:zns.templates.headerImageDark',
                                            'Dark Mode Logo',
                                          )}
                                        </FormLabel>
                                        <FormControl>
                                          <div className="space-y-2">
                                            <input
                                              type="file"
                                              accept="image/png"
                                              className="hidden"
                                              ref={fileInputDarkRef}
                                              onChange={async (e) => {
                                                const file =
                                                  e.target.files?.[0];
                                                if (!file || !oaConfig) return;

                                                try {
                                                  // Validate file type
                                                  if (
                                                    file.type !== 'image/png'
                                                  ) {
                                                    toast.error(
                                                      t(
                                                        'integrations:zns.templates.imageTypeError',
                                                        'Only PNG format is allowed',
                                                      ),
                                                    );
                                                    return;
                                                  }

                                                  // Validate image dimensions
                                                  const img =
                                                    document.createElement(
                                                      'img',
                                                    );
                                                  img.src =
                                                    URL.createObjectURL(file);

                                                  img.onload = async () => {
                                                    if (
                                                      img.width < 400 ||
                                                      img.height < 96
                                                    ) {
                                                      toast.error(
                                                        t(
                                                          'integrations:zns.templates.imageSizeError',
                                                          'Image must be at least 400x96 pixels',
                                                        ),
                                                      );
                                                      URL.revokeObjectURL(
                                                        img.src,
                                                      );
                                                      return;
                                                    }

                                                    URL.revokeObjectURL(
                                                      img.src,
                                                    );
                                                    setIsUploadingDark(true);

                                                    // Create a temporary URL for preview
                                                    const previewUrl =
                                                      URL.createObjectURL(file);
                                                    form.setValue(
                                                      'headerImageDark',
                                                      previewUrl,
                                                    );

                                                    // Convert file to base64
                                                    const reader =
                                                      new FileReader();
                                                    reader.readAsDataURL(file);
                                                    reader.onload =
                                                      async () => {
                                                        try {
                                                          // Upload image to Zalo
                                                          const result =
                                                            await uploadZnsImageFromUrl(
                                                              supabase,
                                                              oaConfig.id,
                                                              reader.result as string,
                                                            );

                                                          // Save media_id
                                                          form.setValue(
                                                            'headerImageDarkMediaId',
                                                            result.media_id,
                                                          );
                                                          toast.success(
                                                            t(
                                                              'integrations:zns.templates.imageUploaded',
                                                              'Image uploaded successfully',
                                                            ),
                                                          );
                                                        } catch (error) {
                                                          console.error(
                                                            'Error uploading image:',
                                                            error,
                                                          );
                                                          toast.error(
                                                            error instanceof
                                                              Error
                                                              ? error.message
                                                              : t(
                                                                  'integrations:zns.templates.imageUploadError',
                                                                  'Error uploading image',
                                                                ),
                                                          );
                                                          form.setValue(
                                                            'headerImageDark',
                                                            '',
                                                          );
                                                        } finally {
                                                          setIsUploadingDark(
                                                            false,
                                                          );
                                                        }
                                                      };
                                                  };
                                                } catch (error) {
                                                  console.error(
                                                    'Error handling file:',
                                                    error,
                                                  );
                                                  setIsUploadingDark(false);
                                                  toast.error(
                                                    t(
                                                      'integrations:zns.templates.imageUploadError',
                                                      'Error uploading image',
                                                    ),
                                                  );
                                                }
                                              }}
                                            />

                                            {field.value ? (
                                              <div className="relative">
                                                <img
                                                  src={field.value}
                                                  alt="Dark Mode Logo"
                                                  className="h-32 w-full rounded-md bg-gray-800 object-cover"
                                                />
                                                <Button
                                                  type="button"
                                                  variant="destructive"
                                                  size="sm"
                                                  className="absolute top-2 right-2"
                                                  onClick={() => {
                                                    form.setValue(
                                                      'headerImageDark',
                                                      '',
                                                    );
                                                    form.setValue(
                                                      'headerImageDarkMediaId',
                                                      '',
                                                    );
                                                  }}
                                                >
                                                  {t(
                                                    'integrations:zns.templates.removeImage',
                                                    'Remove',
                                                  )}
                                                </Button>
                                              </div>
                                            ) : (
                                              <div
                                                className="border-input flex h-32 cursor-pointer flex-col items-center justify-center rounded-md border border-dashed bg-gray-800 text-center hover:bg-gray-700 hover:text-white"
                                                onClick={() =>
                                                  fileInputDarkRef.current?.click()
                                                }
                                              >
                                                {isUploadingDark ? (
                                                  <div className="flex flex-col items-center justify-center space-y-2">
                                                    <div className="border-primary h-8 w-8 animate-spin rounded-full border-2 border-t-transparent"></div>
                                                    <p className="text-sm text-gray-300">
                                                      {t(
                                                        'integrations:zns.templates.uploading',
                                                        'Uploading...',
                                                      )}
                                                    </p>
                                                  </div>
                                                ) : (
                                                  <>
                                                    <div className="mb-2 rounded-full bg-gray-700 p-2">
                                                      <ImageIcon className="h-6 w-6 text-gray-300" />
                                                    </div>
                                                    <p className="text-center text-sm text-gray-300">
                                                      {t(
                                                        'integrations:zns.templates.uploadDarkLogo',
                                                        'Upload dark mode logo (required)',
                                                      )}
                                                      <br />
                                                      <span className="text-xs">
                                                        {t(
                                                          'integrations:zns.templates.imageRequirements',
                                                          'Định dạng PNG. Kích thước tối thiểu 400x96 px.',
                                                        )}
                                                      </span>
                                                    </p>
                                                  </>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                </div>
                              </div>

                              <FormField
                                control={form.control}
                                name="templateTitle"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>
                                      {t(
                                        'integrations:zns.connect.templates.templateTitle',
                                        'Template Title',
                                      )}
                                    </FormLabel>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder={t(
                                          'integrations:zns.connect.templates.templateTitlePlaceholder',
                                          'Enter template title...',
                                        )}
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      {t(
                                        'integrations:zns.connect.templates.templateTitleDescription',
                                        'The title will appear at the top of your template.',
                                      )}
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="templateContent"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>
                                      {t(
                                        'integrations:zns.connect.templates.templateContent',
                                        'Template Content',
                                      )}
                                    </FormLabel>
                                    <FormControl>
                                      <Textarea
                                        {...field}
                                        className="min-h-[200px] font-mono text-sm"
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      {t(
                                        'integrations:zns.connect.templates.templateContentHelp',
                                        'Use <n> to insert customer name parameter',
                                      )}
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              {/* Table Data Section */}
                              <div className="space-y-4 rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4 shadow-sm">
                                <div className="flex items-center justify-between">
                                  <h4 className="flex items-center gap-2 text-base font-medium text-blue-700">
                                    <MousePointerClick className="h-4 w-4 text-blue-500" />
                                    {t(
                                      'integrations:zns.connect.templates.tableData',
                                      'Table Data',
                                    )}
                                  </h4>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    className="gap-1 border-blue-200 bg-white text-blue-700 hover:bg-blue-50"
                                    onClick={() => {
                                      const currentTableData =
                                        form.getValues('tableData') || [];
                                      form.setValue('tableData', [
                                        ...currentTableData,
                                        {
                                          title: '',
                                          value: '',
                                          row_type: undefined,
                                        },
                                      ]);
                                    }}
                                  >
                                    <PlusCircle className="h-3.5 w-3.5" />
                                    {t(
                                      'integrations:zns.connect.templates.addTableRow',
                                      'Add Row',
                                    )}
                                  </Button>
                                </div>

                                <div className="space-y-3">
                                  {form.watch('tableData')?.map((_, index) => (
                                    <div
                                      key={index}
                                      className="flex items-start gap-2 rounded-md border border-blue-100 bg-white p-4 shadow-sm transition-all hover:shadow-md"
                                    >
                                      <div className="grid flex-1 grid-cols-1 gap-4 md:grid-cols-3">
                                        <div>
                                          <FormLabel className="text-xs font-medium text-blue-700">
                                            {t(
                                              'integrations:zns.connect.templates.tableRowTitle',
                                              'Title',
                                            )}
                                          </FormLabel>
                                          <Input
                                            className="border-blue-100 focus:border-blue-300 focus:ring-blue-200"
                                            value={form.watch(
                                              `tableData.${index}.title`,
                                            )}
                                            onChange={(e) => {
                                              form.setValue(
                                                `tableData.${index}.title`,
                                                e.target.value,
                                              );
                                            }}
                                            placeholder="Mã đơn hàng"
                                          />
                                        </div>

                                        <div>
                                          <FormLabel className="text-xs font-medium text-blue-700">
                                            {t(
                                              'integrations:zns.connect.templates.tableRowValue',
                                              'Value',
                                            )}
                                          </FormLabel>
                                          <Input
                                            className="border-blue-100 font-mono focus:border-blue-300 focus:ring-blue-200"
                                            value={form.watch(
                                              `tableData.${index}.value`,
                                            )}
                                            onChange={(e) => {
                                              form.setValue(
                                                `tableData.${index}.value`,
                                                e.target.value,
                                              );

                                              // Update parameters when table data changes
                                              if (
                                                e.target.value.startsWith(
                                                  '<',
                                                ) &&
                                                e.target.value.endsWith('>')
                                              ) {
                                                // Extract parameter name
                                                const paramName =
                                                  e.target.value.substring(
                                                    1,
                                                    e.target.value.length - 1,
                                                  );
                                                const currentParams =
                                                  form.getValues(
                                                    'parameters',
                                                  ) || [];

                                                // Check if parameter already exists
                                                if (
                                                  !currentParams.some(
                                                    (p) => p.name === paramName,
                                                  )
                                                ) {
                                                  // Add new parameter with appropriate type and sample value
                                                  const paramType =
                                                    paramName === 'name'
                                                      ? '1' // Customer Name
                                                      : paramName.includes(
                                                            'code',
                                                          )
                                                        ? '2' // Code
                                                        : paramName.includes(
                                                              'price',
                                                            ) ||
                                                            paramName.includes(
                                                              'amount',
                                                            )
                                                          ? '3' // Amount
                                                          : paramName.includes(
                                                                'time',
                                                              ) ||
                                                              paramName.includes(
                                                                'date',
                                                              )
                                                            ? '4' // Time
                                                            : paramName.includes(
                                                                  'phone',
                                                                ) ||
                                                                paramName.includes(
                                                                  'contact',
                                                                )
                                                              ? '5' // Contact
                                                              : '5'; // Default to Custom Label

                                                  const sampleValue =
                                                    paramName === 'name'
                                                      ? 'Nguyễn Văn A'
                                                      : paramName.includes(
                                                            'code',
                                                          )
                                                        ? 'ORD-12345'
                                                        : paramName.includes(
                                                              'price',
                                                            ) ||
                                                            paramName.includes(
                                                              'amount',
                                                            )
                                                          ? '100000'
                                                          : paramName.includes(
                                                                'time',
                                                              ) ||
                                                              paramName.includes(
                                                                'date',
                                                              )
                                                            ? '10:00 01/01/2023'
                                                            : paramName.includes(
                                                                  'phone',
                                                                )
                                                              ? '0987654321'
                                                              : `Mẫu ${paramName}`;

                                                  form.setValue('parameters', [
                                                    ...currentParams,
                                                    {
                                                      name: paramName,
                                                      type: paramType,
                                                      sample_value: sampleValue,
                                                    },
                                                  ]);
                                                }
                                              }
                                            }}
                                            placeholder="<order_code>"
                                          />
                                        </div>

                                        <div>
                                          <FormLabel className="text-xs font-medium text-blue-700">
                                            {t(
                                              'integrations:zns.templates.tableRowType',
                                              'Row Type',
                                            )}
                                          </FormLabel>
                                          <Select
                                            value={
                                              form
                                                .watch(
                                                  `tableData.${index}.row_type`,
                                                )
                                                ?.toString() || '0'
                                            }
                                            onValueChange={(value) => {
                                              form.setValue(
                                                `tableData.${index}.row_type`,
                                                value === '0'
                                                  ? undefined
                                                  : Number(value),
                                              );
                                            }}
                                          >
                                            <FormControl>
                                              <SelectTrigger className="w-full border-blue-100 focus:border-blue-300 focus:ring-blue-200">
                                                <SelectValue placeholder="Bình thường" />
                                              </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                              <SelectItem value="0">
                                                {t(
                                                  'integrations:zns.templates.rowTypes.normal',
                                                  'Normal',
                                                )}
                                                <span className="ml-2 text-xs text-gray-500">
                                                  (Bình thường)
                                                </span>
                                              </SelectItem>
                                              <SelectItem
                                                value="1"
                                                className="font-medium"
                                              >
                                                {t(
                                                  'integrations:zns.templates.rowTypes.highlight',
                                                  'Highlight',
                                                )}
                                                <span className="ml-2 text-xs text-blue-500">
                                                  (Nổi bật)
                                                </span>
                                              </SelectItem>
                                            </SelectContent>
                                          </Select>
                                        </div>
                                      </div>

                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        className="mt-5 transition-colors hover:bg-red-50 hover:text-red-600"
                                        onClick={() => {
                                          const currentTableData =
                                            form.getValues('tableData') || [];
                                          form.setValue(
                                            'tableData',
                                            currentTableData.filter(
                                              (_, i) => i !== index,
                                            ),
                                          );
                                        }}
                                      >
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="24"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          className="h-4 w-4 text-red-500"
                                        >
                                          <path d="M3 6h18"></path>
                                          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                          <line
                                            x1="10"
                                            x2="10"
                                            y1="11"
                                            y2="17"
                                          ></line>
                                          <line
                                            x1="14"
                                            x2="14"
                                            y1="11"
                                            y2="17"
                                          ></line>
                                        </svg>
                                      </Button>
                                    </div>
                                  ))}

                                  {(!form.watch('tableData') ||
                                    form.watch('tableData').length === 0) && (
                                    <div className="flex items-center justify-center rounded-md border border-dashed p-4">
                                      <p className="text-muted-foreground text-sm">
                                        {t(
                                          'integrations:zns.templates.noTableData',
                                          'No table data defined. Add rows to display a table in your template.',
                                        )}
                                      </p>
                                    </div>
                                  )}
                                </div>

                                <FormDescription>
                                  {t(
                                    'integrations:zns.templates.tableDataDescription',
                                    'Define table data that will be displayed in your template. You can use parameters in the value field.',
                                  )}
                                </FormDescription>
                              </div>

                              {/* Button Settings */}
                              <Collapsible className="w-full space-y-2">
                                <CollapsibleTrigger className="flex w-full items-center justify-between rounded-md border border-blue-200 bg-blue-50 p-4 transition-colors hover:bg-blue-100">
                                  <div className="flex items-center gap-3">
                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                                      <MousePointerClick className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div className="flex flex-col">
                                      <span className="text-base font-medium text-blue-900">
                                        {t(
                                          'integrations:zns.templates.buttonSettings',
                                          'Nút thao tác',
                                        )}
                                      </span>
                                      <span className="text-sm text-blue-700">
                                        {t(
                                          'integrations:zns.templates.buttonSettingsDescription',
                                          'Chỉ được thêm tối đa 2 nút thao tác',
                                        )}
                                      </span>
                                    </div>
                                  </div>
                                  <ChevronDown className="ui-open:rotate-180 h-5 w-5 text-blue-600 transition-transform" />
                                </CollapsibleTrigger>
                                <CollapsibleContent className="mt-2 space-y-4 rounded-md border border-blue-100 bg-blue-50/50 px-4 pt-4">
                                  {/* Action Buttons */}
                                  <div className="space-y-4">
                                    {/* Button List */}
                                    {form
                                      .watch('buttons')
                                      ?.map((button, index) => (
                                        <div
                                          key={index}
                                          className="rounded-md border p-4"
                                        >
                                          <div className="mb-4 flex items-center justify-between">
                                            <h4 className="text-sm font-medium">
                                              {t(
                                                'integrations:zns.templates.actionButton',
                                                'Nút thao tác',
                                              )}{' '}
                                              {index + 1}
                                            </h4>
                                            <Button
                                              type="button"
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => {
                                                const currentButtons =
                                                  form.getValues('buttons') ||
                                                  [];
                                                form.setValue(
                                                  'buttons',
                                                  currentButtons.filter(
                                                    (_, i) => i !== index,
                                                  ),
                                                );
                                              }}
                                            >
                                              <Trash2 className="h-4 w-4" />
                                            </Button>
                                          </div>

                                          <div className="grid grid-cols-2 gap-4">
                                            <FormField
                                              control={form.control}
                                              name={`buttons.${index}.text`}
                                              render={({ field }) => (
                                                <FormItem>
                                                  <FormLabel>
                                                    {t(
                                                      'integrations:zns.templates.buttonText',
                                                      'Tên nút',
                                                    )}
                                                  </FormLabel>
                                                  <FormControl>
                                                    <Input {...field} />
                                                  </FormControl>
                                                  <FormMessage />
                                                </FormItem>
                                              )}
                                            />

                                            <FormField
                                              control={form.control}
                                              name={`buttons.${index}.actionType`}
                                              render={({ field }) => (
                                                <FormItem>
                                                  <FormLabel>
                                                    {t(
                                                      'integrations:zns.templates.buttonType',
                                                      'Loại nút',
                                                    )}
                                                  </FormLabel>
                                                  <Select
                                                    value={field.value}
                                                    onValueChange={
                                                      field.onChange
                                                    }
                                                  >
                                                    <FormControl>
                                                      <SelectTrigger>
                                                        <SelectValue
                                                          placeholder={t(
                                                            'integrations:zns.templates.selectButtonType',
                                                            'Chọn loại nút',
                                                          )}
                                                        />
                                                      </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent className="max-h-[300px]">
                                                      {buttonTypeOptions.map(
                                                        (category) => (
                                                          <SelectGroup
                                                            key={
                                                              category.category
                                                            }
                                                          >
                                                            <SelectLabel className="mt-2 px-4 py-2 text-xs font-semibold text-gray-500">
                                                              {
                                                                category.category
                                                              }
                                                            </SelectLabel>
                                                            {category.options.map(
                                                              (option) => (
                                                                <SelectItem
                                                                  key={
                                                                    option.id
                                                                  }
                                                                  value={
                                                                    option.id
                                                                  }
                                                                  className="flex flex-col items-start py-2"
                                                                >
                                                                  <div className="flex items-center gap-2">
                                                                    {option.icon ===
                                                                    'phone' ? (
                                                                      <Phone className="h-4 w-4 text-blue-600" />
                                                                    ) : (
                                                                      <Link className="h-4 w-4 text-blue-600" />
                                                                    )}
                                                                    <span className="font-medium">
                                                                      {
                                                                        option.title
                                                                      }
                                                                    </span>
                                                                  </div>
                                                                  <span className="ml-6 text-xs text-gray-500">
                                                                    {
                                                                      option.description
                                                                    }
                                                                  </span>
                                                                </SelectItem>
                                                              ),
                                                            )}
                                                          </SelectGroup>
                                                        ),
                                                      )}
                                                    </SelectContent>
                                                  </Select>
                                                  <FormMessage />
                                                </FormItem>
                                              )}
                                            />
                                          </div>

                                          <FormField
                                            control={form.control}
                                            name={`buttons.${index}.data`}
                                            render={({ field }) => (
                                              <FormItem className="mt-4">
                                                <FormLabel>
                                                  {(() => {
                                                    const actionType =
                                                      form.watch(
                                                        `buttons.${index}.actionType`,
                                                      );

                                                    // Sử dụng enum ZnsButtonType
                                                    switch (
                                                      Number(actionType)
                                                    ) {
                                                      case ZnsButtonType.TO_OA_PAGE:
                                                        return t(
                                                          'integrations:zns.templates.buttonOaId',
                                                          'OA ID',
                                                        );
                                                      case ZnsButtonType.TO_MINI_APP:
                                                        return t(
                                                          'integrations:zns.templates.buttonMiniAppId',
                                                          'Mini App ID',
                                                        );
                                                      case ZnsButtonType.TO_ARTICLE:
                                                        return t(
                                                          'integrations:zns.templates.buttonPostId',
                                                          'Bài viết ID',
                                                        );
                                                      case ZnsButtonType.CALL_PHONE:
                                                        return t(
                                                          'integrations:zns.templates.buttonPhone',
                                                          'Số điện thoại',
                                                        );
                                                      case ZnsButtonType.TO_BUSINESS_PAGE:
                                                      case ZnsButtonType.TO_APP_DOWNLOAD:
                                                      case ZnsButtonType.TO_PRODUCT_PAGE:
                                                      case ZnsButtonType.TO_OTHER_WEB_MINI_APP:
                                                      case ZnsButtonType.TO_OTHER_APP:
                                                      default:
                                                        return t(
                                                          'integrations:zns.templates.buttonUrl',
                                                          'URL',
                                                        );
                                                    }
                                                  })()}
                                                </FormLabel>
                                                <FormControl>
                                                  <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                              </FormItem>
                                            )}
                                          />
                                        </div>
                                      ))}

                                    {/* Add Button */}
                                    {(!form.watch('buttons') ||
                                      form.watch('buttons').length < 2) && (
                                      <div className="mt-4 flex items-center">
                                        <Button
                                          type="button"
                                          variant="outline"
                                          className="flex items-center"
                                          onClick={() => {
                                            const currentButtons =
                                              form.getValues('buttons') || [];
                                            if (currentButtons.length < 2) {
                                              form.setValue('buttons', [
                                                ...currentButtons,
                                                {
                                                  text: '',
                                                  actionType:
                                                    ZnsButtonType.TO_BUSINESS_PAGE.toString(),
                                                  data: '',
                                                },
                                              ]);
                                            }
                                          }}
                                        >
                                          <PlusCircle className="mr-2 h-4 w-4" />
                                          <span>
                                            {t(
                                              'integrations:zns.templates.addButton',
                                              'Thêm nút thao tác',
                                            )}
                                          </span>
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                </CollapsibleContent>
                              </Collapsible>

                              {/* Removed Template Parameters section */}

                              {/* Removed duplicate save button */}
                            </form>
                          </Form>
                        </div>

                        {/* Preview bên phải */}
                        <div className="space-y-6">
                          <div className="flex items-center justify-between rounded-lg border border-indigo-100 bg-gradient-to-r from-indigo-50 to-purple-50 p-4 shadow-sm">
                            <h3 className="flex items-center gap-2 text-lg font-medium text-indigo-700">
                              <Smartphone className="h-5 w-5 text-indigo-500" />
                              {t(
                                'integrations:zns.templates.previewTab',
                                'Live Preview',
                              )}
                            </h3>
                            <Button
                              variant="outline"
                              size="sm"
                              className="gap-1 border-indigo-200 bg-white text-indigo-700 hover:bg-indigo-50"
                              onClick={() => {
                                if (form.getValues('templateContent')) {
                                  openPreviewDialog(previewTemplateId || '');
                                }
                              }}
                            >
                              <Eye className="h-3.5 w-3.5" />
                              {t(
                                'integrations:zns.templates.fullPreview',
                                'Full Preview',
                              )}
                            </Button>
                          </div>
                          <div className="space-y-6 rounded-xl border border-gray-200 bg-gradient-to-b from-gray-50 to-white p-6 shadow-md">
                            <div className="flex justify-center">
                              <div className="w-full max-w-sm">
                                <div className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-lg">
                                  {/* Phone header */}
                                  <div className="flex items-center justify-between bg-blue-600 p-3 text-white">
                                    <div className="flex items-center gap-2">
                                      <Smartphone className="h-4 w-4" />
                                      <span className="text-sm font-medium">
                                        Zalo
                                      </span>
                                    </div>
                                    <div className="text-xs">
                                      {new Date().toLocaleTimeString([], {
                                        hour: '2-digit',
                                        minute: '2-digit',
                                      })}
                                    </div>
                                  </div>

                                  {/* Message container */}
                                  <div className="bg-gray-100 p-4">
                                    <div className="rounded-lg bg-white p-4 shadow-sm">
                                      {/* Template header */}
                                      <div className="mb-3 flex items-center gap-2">
                                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                                          <span className="font-bold text-blue-600">
                                            Z
                                          </span>
                                        </div>
                                        <div>
                                          <div className="font-medium">
                                            Official Account
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            Zalo Notification Service
                                          </div>
                                        </div>
                                      </div>

                                      {/* Template content */}
                                      <div className="space-y-3">
                                        <div className="font-bold">
                                          {form.watch('templateName') ||
                                            'Template Title'}
                                        </div>
                                        <div className="text-sm whitespace-pre-line">
                                          {selectedTemplate &&
                                          previewTemplateData ? (
                                            // Hiển thị dữ liệu mẫu từ template đã chọn
                                            <div
                                              dangerouslySetInnerHTML={{
                                                __html:
                                                  previewTemplateData.html,
                                              }}
                                            />
                                          ) : (
                                            // Hiển thị dữ liệu real-time từ form
                                            <div
                                              dangerouslySetInnerHTML={{
                                                __html: generateRealTimePreview(),
                                              }}
                                            />
                                          )}
                                        </div>

                                        {/* Table data preview */}
                                        {form.watch('tableData') &&
                                          form.watch('tableData').length >
                                            0 && (
                                            <div className="mt-3 overflow-hidden rounded-md border">
                                              <table className="w-full">
                                                <tbody>
                                                  {form
                                                    .watch('tableData')
                                                    .map((row, index) => (
                                                      <tr
                                                        key={index}
                                                        className="border-b last:border-b-0"
                                                      >
                                                        <td className="px-3 py-2 text-sm font-medium">
                                                          {row.title}
                                                        </td>
                                                        <td className="px-3 py-2 text-sm">
                                                          {row.value}
                                                        </td>
                                                      </tr>
                                                    ))}
                                                </tbody>
                                              </table>
                                            </div>
                                          )}

                                        {/* Template footer */}
                                        <div className="mt-2 border-t border-gray-200 pt-2">
                                          <button className="w-full rounded-md bg-blue-50 py-2 text-sm font-medium text-blue-600">
                                            Xem chi tiết
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="flex justify-center">
                              <div className="max-w-md text-center">
                                <p className="text-muted-foreground text-sm">
                                  {t(
                                    'integrations:zns.templates.previewDisclaimer',
                                    'This is a preview of how your template might look. The actual appearance may vary.',
                                  )}
                                </p>
                              </div>
                            </div>

                            {/* Auto-detected Parameters */}
                            <div className="mt-6">
                              {form.watch('parameters') && form.watch('parameters').length > 0 ? (
                                <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                                  <div className="flex items-center gap-2 mb-3">
                                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                    <h4 className="text-sm font-medium text-green-800">
                                      {t(
                                        'integrations:zns.templates.autoDetectedParams',
                                        'Auto-detected Parameters'
                                      )}
                                    </h4>
                                  </div>
                                  <div className="flex flex-wrap gap-2">
                                    {form.watch('parameters').map((param, index) => (
                                      <span
                                        key={index}
                                        className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
                                      >
                                        &lt;{param.name}&gt;
                                      </span>
                                    ))}
                                  </div>
                                  <p className="mt-2 text-xs text-green-700">
                                    {t(
                                      'integrations:zns.templates.autoDetectedParamsNote',
                                      'These parameters will be configured in the next step.'
                                    )}
                                  </p>
                                </div>
                              ) : (
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                  <div className="flex items-center gap-2 mb-2">
                                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                    <h4 className="text-sm font-medium text-blue-800">
                                      {t(
                                        'integrations:zns.templates.noParamsDetected',
                                        'No Parameters Detected'
                                      )}
                                    </h4>
                                  </div>
                                  <p className="text-xs text-blue-700">
                                    {t(
                                      'integrations:zns.templates.noParamsDetectedNote',
                                      'Use <parameter_name> format in your content to add dynamic parameters.'
                                    )}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <Form {...form} onSubmit={form.handleSubmit(onSubmit)}>
                        <div className="space-y-6">
                          <div className="zns-create__step">
                            <h3 className="mb-2 text-lg font-medium">
                              {t(
                                'integrations:zns.templates.submitForReview',
                                'Submit for Review',
                              )}
                            </h3>
                            <p className="text-muted-foreground mb-6 text-sm">
                              {t(
                                'integrations:zns.templates.submitForReviewDescription',
                                'Configure parameters and add review notes to help with accurate approval',
                              )}
                            </p>

                            <div className="step3__params mt-6 mb-6 rounded-md border bg-gray-50 p-4">
                              <div className="mb-4">
                                <div className="flex items-center">
                                  <span className="mr-2 font-medium">
                                    {t(
                                      'integrations:zns.connect.templates.parameters',
                                      'Parameters',
                                    )}
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    title={t(
                                      'integrations:zns.connect.templates.parameterTypesInfo',
                                      'Parameter types (max characters)',
                                    )}
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="h-4 w-4"
                                    >
                                      <circle cx="12" cy="12" r="10"></circle>
                                      <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                      <line
                                        x1="12"
                                        y1="17"
                                        x2="12.01"
                                        y2="17"
                                      ></line>
                                    </svg>
                                  </Button>
                                </div>
                              </div>

                              {/* Parameter headers */}
                              <div className="mb-2 flex">
                                <div className="w-1/3">
                                  <span className="text-xs font-medium text-gray-500">
                                    {t(
                                      'integrations:zns.connect.templates.parameterName',
                                      'Parameter Name',
                                    )}
                                  </span>
                                </div>
                                <div className="w-1/3">
                                  <span className="text-xs font-medium text-gray-500">
                                    {t(
                                      'integrations:zns.connect.templates.parameterType',
                                      'Technical Setting',
                                    )}
                                  </span>
                                </div>
                                <div className="w-1/3">
                                  <span className="text-xs font-medium text-gray-500">
                                    {t(
                                      'integrations:zns.connect.templates.parameterValue',
                                      'Parameter Value',
                                    )}{' '}
                                    *
                                  </span>
                                </div>
                              </div>

                              {/* Parameter rows */}
                              {form.watch('parameters')?.map((param, index) => (
                                <div
                                  key={index}
                                  className="mb-2 flex items-center"
                                >
                                  <div className="w-1/3 pr-2">
                                    <Input
                                      disabled
                                      value={param.name}
                                      className="bg-gray-100"
                                    />
                                  </div>
                                  <div className="w-1/3 px-2">
                                    <Select
                                      value={param.type}
                                      onValueChange={(value) =>
                                        form.setValue(
                                          `parameters.${index}.type`,
                                          value,
                                        )
                                      }
                                    >
                                      <SelectTrigger>
                                        <SelectValue
                                          placeholder={t(
                                            'integrations:zns.connect.templates.selectParameterType',
                                            'Select type',
                                          )}
                                        />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="1">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.customerName',
                                            'Tên khách hàng (30)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="2">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.phoneNumber',
                                            'Số điện thoại (15)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="3">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.address',
                                            'Địa chỉ (200)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="4">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.code',
                                            'Mã số (30)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="5">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.customLabel',
                                            'Nhãn tùy chỉnh (30)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="6">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.transactionStatus',
                                            'Trạng thái giao dịch (30)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="7">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.contactInfo',
                                            'Thông tin liên hệ (50)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="8">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.gender',
                                            'Giới tính / Danh xưng (5)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="9">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.productName',
                                            'Tên sản phẩm / Thương hiệu (200)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="10">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.quantityAmount',
                                            'Số lượng / Số tiền (20)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="11">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.time',
                                            'Thời gian (20)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="12">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.otp',
                                            'OTP (10)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="13">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.url',
                                            'URL (200)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="14">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.currency',
                                            'Tiền tệ (VNĐ) (12)',
                                          )}
                                        </SelectItem>
                                        <SelectItem value="15">
                                          {t(
                                            'integrations:zns.connect.templates.paramTypes.bankTransferNote',
                                            'Bank transfer note (90)',
                                          )}
                                        </SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                  <div className="w-1/3 pl-2">
                                    <Input
                                      value={param.sample_value}
                                      onChange={(e) =>
                                        form.setValue(
                                          `parameters.${index}.sample_value`,
                                          e.target.value,
                                        )
                                      }
                                      placeholder={`${t('integrations:zns.connect.templates.example', 'Example')}: ${param.name === 'name' ? 'Nguyễn Văn A' : param.name}`}
                                    />
                                  </div>
                                </div>
                              ))}
                            </div>

                            {/* Review notes */}
                            <div className="mt-4">
                              <FormField
                                control={form.control}
                                name="templateNote"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>
                                      {t(
                                        'integrations:zns.connect.templates.reviewNotes',
                                        'Review Notes',
                                      )}
                                    </FormLabel>
                                    <FormControl>
                                      <Textarea
                                        {...field}
                                        placeholder={t(
                                          'integrations:zns.connect.templates.reviewNotesPlaceholder',
                                          'Add notes to help with the approval process',
                                        )}
                                        rows={4}
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      {t(
                                        'integrations:zns.connect.templates.reviewNotesDescription',
                                        'Provide additional context to help reviewers understand your template',
                                      )}
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Terms and conditions */}
                            <div className="mt-4">
                              <div className="flex items-start space-x-2">
                                <Checkbox
                                  id="terms"
                                  checked={agreeTerms}
                                  onCheckedChange={(checked) =>
                                    setAgreeTerms(checked === true)
                                  }
                                />
                                <div className="grid gap-1.5 leading-none">
                                  <label
                                    htmlFor="terms"
                                    className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                  >
                                    {t(
                                      'integrations:zns.connect.templates.agreeTerms',
                                      'I have read and agree to the',
                                    )}{' '}
                                    <a
                                      href="https://zalo.cloud/terms"
                                      target="_blank"
                                      rel="noreferrer"
                                      className="text-primary underline"
                                    >
                                      {t(
                                        'integrations:zns.connect.templates.termsAndPolicies',
                                        'Terms and Policies',
                                      )}
                                    </a>
                                  </label>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Form>
                    )}
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    {currentStep === 1 ? (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            router.push(
                              `/home/<USER>/integrations/zns/templates`,
                            )
                          }
                        >
                          {t('common:cancel')}
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          onClick={handleNextStep}
                          disabled={!isReallyConnected}
                          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md transition-all hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg"
                        >
                          {t('common:next')}{' '}
                          <ArrowRight className="ml-1 h-3.5 w-3.5" />
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handlePrevStep}
                          className="flex items-center gap-1 border-blue-200 text-blue-700 hover:bg-blue-50"
                        >
                          <ArrowLeft className="h-3.5 w-3.5" />{' '}
                          {t('common:back')}
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          type="button"
                          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md transition-all hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg"
                          onClick={() => {
                            console.log('Save button clicked');
                            if (!agreeTerms) {
                              toast.error(
                                t(
                                  'integrations:zns.connect.templates.agreeTermsRequired',
                                  'Please agree to the terms and conditions',
                                ),
                              );
                              return;
                            }
                            if (!isReallyConnected) {
                              toast.error(
                                t(
                                  'integrations:zns.connect.templates.notConnected',
                                  'Not Connected',
                                ),
                              );
                              return;
                            }
                            // Get current form values
                            const formValues = form.getValues();
                            console.log(
                              'Form values before submission:',
                              formValues,
                            );

                            // Ensure parameters have valid types (numbers 1-15)
                            if (formValues.parameters) {
                              formValues.parameters = formValues.parameters.map(
                                (param) => ({
                                  ...param,
                                  type:
                                    typeof param.type === 'string'
                                      ? parseInt(param.type, 10)
                                      : param.type,
                                }),
                              );

                              // Filter out any parameters with invalid types
                              formValues.parameters =
                                formValues.parameters.filter((param) => {
                                  const type = Number(param.type);
                                  return (
                                    !isNaN(type) && type >= 1 && type <= 15
                                  );
                                });
                            }

                            // Ensure template note is not empty
                            if (!formValues.templateNote) {
                              formValues.templateNote =
                                'Template for business notifications';
                            }

                            console.log(
                              'Form values after processing:',
                              formValues,
                            );

                            // Call onSubmit with processed values
                            onSubmit(formValues);
                          }}
                          disabled={isSubmitting}
                        >
                          {isSubmitting
                            ? t('common:saving', 'Đang lưu...')
                            : t('common:save', 'Lưu thay đổi')}
                        </Button>
                      </>
                    )}
                  </CardFooter>
                </Card>
              )}
            </>
          )}
        </div>
      </PageBody>

      {/* Preview Template Dialog */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {t(
                'integrations:zns.connect.templates.templatePreview',
                'Template Preview',
              )}
            </DialogTitle>
            <DialogDescription>
              {previewTemplateData?.templateName ||
                findTemplateSample(previewTemplateId || '')?.name ||
                ''}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {isLoadingPreviewTemplate ? (
              <div className="flex items-center justify-center py-8">
                <div className="border-primary h-8 w-8 animate-spin rounded-full border-2 border-t-transparent"></div>
              </div>
            ) : previewTemplateData ? (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <div className="w-full max-w-sm">
                    <div className="bg-card text-card-foreground overflow-hidden rounded-lg border shadow-sm">
                      {/* Phone header */}
                      <div className="flex items-center justify-between bg-blue-600 p-3 text-white">
                        <div className="flex items-center gap-2">
                          <Smartphone className="h-4 w-4" />
                          <span className="text-sm font-medium">Zalo</span>
                        </div>
                        <div className="text-xs">12:34</div>
                      </div>

                      {/* Message container */}
                      <div className="bg-gray-100 p-4">
                        <div className="rounded-lg bg-white p-4 shadow-sm">
                          {/* Template header */}
                          <div className="mb-3 flex items-center gap-2">
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                              <span className="font-bold text-blue-600">Z</span>
                            </div>
                            <div>
                              <div className="font-medium">
                                Official Account
                              </div>
                              <div className="text-xs text-gray-500">
                                Zalo Notification Service
                              </div>
                            </div>
                          </div>

                          {/* Template content */}
                          <div className="space-y-3">
                            <div className="font-bold">
                              {previewTemplateData.templateName}
                            </div>
                            <div className="text-sm whitespace-pre-line">
                              {previewTemplateData.html ? (
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html: previewTemplateData.html,
                                  }}
                                />
                              ) : (
                                previewTemplateData.templateContent || ''
                              )}
                            </div>

                            {/* Template footer */}
                            <div className="mt-2 border-t border-gray-200 pt-2">
                              <button className="w-full rounded-md bg-blue-50 py-2 text-sm font-medium text-blue-600">
                                Xem chi tiết
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center">
                  <div className="max-w-md text-center">
                    <p className="text-muted-foreground text-sm">
                      {t(
                        'integrations:zns.connect.templates.previewDisclaimer',
                        'This is a preview of how your template might look. The actual appearance may vary.',
                      )}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <AlertCircle className="text-muted-foreground h-12 w-12 opacity-50" />
                <p className="text-muted-foreground mt-4 text-center text-sm">
                  {t(
                    'integrations:zns.connect.templates.noPreviewAvailable',
                    'No preview available for this template.',
                  )}
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsPreviewDialogOpen(false)}
            >
              {t('common:close')}
            </Button>
            <Button
              variant="default"
              onClick={async () => {
                if (previewTemplateId) {
                  try {
                    await applyTemplateSample(previewTemplateId);
                    setIsPreviewDialogOpen(false);
                  } catch (error) {
                    console.error('Error applying template:', error);
                    toast.error(
                      t(
                        'integrations:zns.connect.templates.applyError',
                        'Error applying template',
                      ),
                    );
                  }
                }
              }}
              disabled={!previewTemplateId || isUploading}
            >
              {t('integrations:zns.connect.templates.use', 'Use')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
