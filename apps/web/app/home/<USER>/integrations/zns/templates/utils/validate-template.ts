import { ZnsTemplateLayout } from '@kit/zns';





/**
 * <PERSON><PERSON><PERSON> tra cấu trúc template trướ<PERSON> khi upload ảnh
 * @param layout Layout của template
 * @returns Kết quả kiểm tra
 */
export function validateTemplateStructure(layout: ZnsTemplateLayout): { valid: boolean; error?: string } {
  try {
    // Kiểm tra header
    if (!layout.header || !Array.isArray(layout.header.components)) {
      return {
        valid: false,
        error: 'Header must have components array',
      };
    }

    // Kiểm tra body
    if (!layout.body || !Array.isArray(layout.body.components)) {
      return {
        valid: false,
        error: 'Body must have components array',
      };
    }

    // Kiểm tra xem body có ít nhất một component không
    if (layout.body.components.length === 0) {
      return {
        valid: false,
        error: 'Body must have at least one component',
      };
    }

    // Kiểm tra xem có TITLE component không
    const hasTitleComponent = layout.body.components.some(comp => comp.TITLE);
    if (!hasTitleComponent) {
      return {
        valid: false,
        error: 'Body must have a TITLE component',
      };
    }

    // Kiểm tra footer nếu có
    if (layout.footer) {
      if (!Array.isArray(layout.footer.components)) {
        return {
          valid: false,
          error: 'Footer must have components array',
        };
      }

      // Kiểm tra số lượng buttons
      const buttons = layout.footer.components.filter(comp => comp.BUTTONS);
      if (buttons.length > 0) {
        const buttonItems = buttons.reduce((count, comp) => {
          return count + (comp.BUTTONS?.items?.length || 0);
        }, 0);

        if (buttonItems > 3) {
          return {
            valid: false,
            error: 'Footer cannot have more than 3 buttons',
          };
        }
      }
    }

    // Kiểm tra TABLE nếu có
    const tables = layout.body.components.filter(comp => comp.TABLE);
    for (const table of tables) {
      if (!table.TABLE?.rows || !Array.isArray(table.TABLE.rows)) {
        return {
          valid: false,
          error: 'TABLE must have rows array',
        };
      }


      // Kiểm tra từng row
      for (const row of table.TABLE.rows) {
        if (!row.title) {
          return {
            valid: false,
            error: 'TABLE row must have title',
          };
        }
      }
    }

    // Kiểm tra số lượng PARAGRAPH
    const paragraphs = layout.body.components.filter(comp => comp.PARAGRAPH);
    if (paragraphs.length > 4) {
      return {
        valid: false,
        error: 'Body cannot have more than 4 PARAGRAPH components',
      };
    }

    // Validation passed
    return { valid: true };
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Unknown validation error',
    };
  }
}
