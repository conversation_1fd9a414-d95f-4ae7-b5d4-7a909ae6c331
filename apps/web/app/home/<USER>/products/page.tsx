import { Suspense } from 'react';

import Link from 'next/link';

import { Plus } from 'lucide-react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import { CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { PageBody } from '@kit/ui/page';
import { SearchListInput } from '@kit/ui/search-list-input';
import { Trans } from '@kit/ui/trans';

import { loadBranches } from '~/home/<USER>/products/_lib/server/branches.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { checkCanCreateResource } from '~/home/<USER>/_lib/server/resource-access';

import { TeamAccountLayoutPageHeader } from '../_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';
import { ProductsActions } from './_components/products-actions';
import ProductsList from './_components/products-list';
import { loadProducts } from './_lib/server/products-page.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('products:pageTitle');
  return { title };
};

interface PageSearchParams {
  page?: string;
  query?: string;
  error?: string;
  current?: string;
  limit?: string;
}

async function ProductsPage({
  params,
  searchParams,
}: {
  params: Promise<{ account: string }>;
  searchParams: Promise<PageSearchParams>;
}) {
  const client = getSupabaseServerClient();
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);
  const accountSlug = resolvedParams.account;
  const currentPage = resolvedSearchParams.page
    ? parseInt(resolvedSearchParams.page)
    : 1;
  const searchQuery = resolvedSearchParams.query || '';

  // Lấy thông tin lỗi từ URL (nếu có)
  const errorReason = resolvedSearchParams.error || '';
  const errorCurrent = resolvedSearchParams.current ? parseInt(resolvedSearchParams.current) : 0;
  const errorLimit = resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit) : 0;
  const api = createTeamAccountsApi(client);

  try {
    const teamAccount = await api.getTeamAccount(accountSlug);
    if (!teamAccount) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const limit = 10;
    const [{ data: products, total }, { user, account }, branches] =
      await Promise.all([
        loadProducts(client, teamAccount.id, currentPage, searchQuery),
        loadTeamWorkspace(accountSlug),
        loadBranches(teamAccount.id),
      ]);

    if (!account) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    // Kiểm tra quyền đã được thực hiện ở sidebar menu
    // Nếu người dùng có thể truy cập trang này, họ đã có quyền
    const canManageProducts = true;

    // Kiểm tra giới hạn tạo sản phẩm
    const { canCreate, reason, current: resourceCurrent, limit: resourceLimit } = await checkCanCreateResource(account.id, 'products');

    const branchMap = branches.reduce(
      (map, branch) => {
        map[branch.id] = branch.name;
        return map;
      },
      {} as Record<string, string>,
    );

    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="common:routes:products" />}
          description={<AppBreadcrumbs />}
          account={account.slug}
        />
        <PageBody data-testid="products-page">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>
              <SearchListInput
                defaultValue={searchQuery}
                placeholder={<Trans i18nKey="products:search">Search products...</Trans>}
              />
            </CardTitle>
            <If condition={canManageProducts}>
              <ProductsActions
                accountSlug={account.slug}
                canCreate={!errorReason && canCreate}
                reason={errorReason || reason}
                resourceCurrent={errorCurrent || resourceCurrent}
                resourceLimit={errorLimit || resourceLimit}
              />
            </If>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>Loading...</div>}>
              <ProductsList
                products={products.map((product) => ({
                  ...product,
                  branch_products: product.branch_products.map((bp) => ({
                    ...bp,
                    branch_name: branchMap[bp.branch_id] || bp.branch_id,
                  })),
                }))}
                canManage={canManageProducts}
                account={account}
                currentPage={currentPage - 1} // Chuyển về index 0
                limit={limit}
                total={total}
                filters={{ query: searchQuery }}
              />
            </Suspense>
          </CardContent>
        </PageBody>
      </>
    );
  } catch (error) {
    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:generic">
              An error occurred. Please try again.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}

export default ProductsPage;
