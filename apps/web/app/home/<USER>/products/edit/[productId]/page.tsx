import { Suspense } from 'react';

import { redirect } from 'next/navigation';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '../../../_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '../../../_lib/server/team-account-workspace.loader';
import { loadProductById } from '../../_lib/server/products-page.loader';
import { EditProductForm } from './_components/edit-product-form';

interface EditProductPageProps {
  params: Promise<{ account: string; productId: string }>;
}

export default async function EditProductPage({
  params,
}: EditProductPageProps) {
  const { account: accountSlug, productId } = await params;
  const client = getSupabaseServerClient();

  try {
    const { account } = await loadTeamWorkspace(accountSlug);
    if (!account) {
      return redirect(`/home/<USER>/products`);
    }

    const product = await loadProductById(client, account.id, productId);
    if (!product) {
      return redirect(`/home/<USER>/products`);
    }

    return (
      <>
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="products:edit:title">Edit Product</Trans>}
          description={
            <Trans i18nKey="products:edit:description">
              Update your product details
            </Trans>
          }
        />
        <PageBody>
          <Suspense fallback={<div>Loading...</div>}>
            <EditProductForm
              accountId={account.id}
              accountSlug={account.slug}
              product={product}
            />
          </Suspense>
        </PageBody>
      </>
    );
  } catch (error) {
    return redirect(`/home/<USER>/products`);
  }
}
