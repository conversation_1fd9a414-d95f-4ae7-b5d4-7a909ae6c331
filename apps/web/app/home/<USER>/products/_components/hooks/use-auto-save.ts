import { useEffect, useRef } from 'react';

import { UseFormReturn } from 'react-hook-form';

import { ProductFormData } from '../../_lib/types';

interface UseAutoSaveProps {
  form: UseFormReturn<ProductFormData>;
  accountId: string;
  onSave?: () => void;
  interval?: number;
}

export function useAutoSave({
  form,
  accountId,
  onSave,
  interval = 30000, // 30 seconds
}: UseAutoSaveProps) {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const previousData = useRef<string>();

  useEffect(() => {
    const subscription = form.watch((data) => {
      const currentData = JSON.stringify(data);

      // Only save if data has changed
      if (currentData !== previousData.current) {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(async () => {
          try {
            // Save to local storage
            localStorage.setItem(`product_draft_${accountId}`, currentData);

            previousData.current = currentData;
            onSave?.();
          } catch (error) {
            console.error('Error saving draft:', error);
          }
        }, interval);
      }
    });

    return () => {
      subscription.unsubscribe();
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [form, accountId, interval, onSave]);
}
