'use client';

import { useEffect, useState } from 'react';

import { ChevronDown, Store } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card } from '@kit/ui/card';
import { Checkbox } from '@kit/ui/checkbox';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@kit/ui/collapsible';
import { FormControl, FormField, FormItem, FormLabel } from '@kit/ui/form';
import { Switch } from '@kit/ui/switch';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import { loadBranches } from '~/home/<USER>/products/_lib/server/branches.loader';

interface Branch {
  id: string;
  name: string;
  address: string;
  phone: string;
  status: 'active' | 'inactive';
}

interface BranchSectionProps {
  accountId: string;
}

export function BranchSection({ accountId }: BranchSectionProps) {
  const [isOpen, setIsOpen] = useState(true);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { control, watch, setValue } = useFormContext();

  // Set default value for is_global to true when component mounts
  useEffect(() => {
    setValue('is_global', true);
  }, [setValue]);

  const isGlobal = watch('is_global');
  const selectedBranches = watch('selected_branches') || [];

  useEffect(() => {
    async function fetchBranches() {
      try {
        setIsLoading(true);
        const branchData = await loadBranches(accountId);
        setBranches(branchData);

        if (isGlobal) {
          setValue(
            'selected_branches',
            branchData.map((b) => b.id),
          );
        }
      } catch (error) {
        console.error('Failed to load branches:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchBranches();
  }, [accountId, isGlobal, setValue]);

  const handleGlobalToggle = (checked: boolean) => {
    setValue('is_global', checked);
    if (checked) {
      // Khi bật global, chọn tất cả chi nhánh
      setValue(
        'selected_branches',
        branches.map((b) => b.id),
      );
    }
  };

  return (
    <Card className="p-6">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="group flex w-full items-center justify-between p-0"
          >
            <div className="flex items-center gap-2">
              <Store className="text-muted-foreground group-hover:text-primary h-5 w-5 transition-colors" />
              <h3 className="text-lg font-semibold">
                <Trans i18nKey="products:branch:title">
                  Branch Availability
                </Trans>
              </h3>
              {!isGlobal && (
                <Badge variant="secondary" className="text-xs">
                  {selectedBranches.length}/{branches.length}
                </Badge>
              )}
            </div>
            <ChevronDown
              className={`group-hover:text-primary h-4 w-4 transform transition-transform duration-200 ${
                isOpen ? 'rotate-180' : ''
              }`}
            />
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="mt-4 space-y-4">
          <FormField
            control={control}
            name="is_global"
            render={({ field }) => (
              <FormItem>
                <div className="bg-muted/30 hover:bg-muted/50 flex items-center justify-between rounded-lg p-3 transition-colors">
                  <div className="flex flex-col gap-0.5">
                    <FormLabel className="text-sm font-medium">
                      <Trans i18nKey="products:branch:available_everywhere">
                        Available Everywhere
                      </Trans>
                    </FormLabel>
                    <span className="text-muted-foreground text-xs">
                      <Trans i18nKey="products:branch:available_everywhere_description">
                        This product is available at all branches
                      </Trans>
                    </span>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleGlobalToggle}
                      className="data-[state=checked]:bg-primary"
                    />
                  </FormControl>
                </div>
              </FormItem>
            )}
          />

          {!isGlobal && !isLoading && (
            <div className="animate-in fade-in-50 slide-in-from-top-2 duration-200">
              <div className="mb-3 flex items-center justify-between">
                <span className="text-sm font-medium">
                  <Trans i18nKey="products:branch:select_branches">
                    Select Branches
                  </Trans>
                </span>
                <Badge variant="secondary" className="text-xs">
                  {selectedBranches.length}/{branches.length}
                </Badge>
              </div>

              <div className="bg-card space-y-1 rounded-lg border">
                {branches.map((branch) => (
                  <FormField
                    key={branch.id}
                    control={control}
                    name="selected_branches"
                    render={({ field }) => (
                      <FormItem>
                        <label
                          className={cn(
                            'flex w-full cursor-pointer items-start space-x-4 p-3',
                            'hover:bg-muted/50 transition-colors',
                            'border-border/50 border-b last:border-b-0',
                          )}
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(branch.id)}
                              onCheckedChange={(checked) => {
                                const updatedValue = checked
                                  ? [...(field.value || []), branch.id]
                                  : (field.value || []).filter(
                                      (id: string) => id !== branch.id,
                                    );
                                field.onChange(updatedValue);
                              }}
                            />
                          </FormControl>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <p className="text-sm leading-none font-medium">
                                {branch.name}
                              </p>
                              <Badge
                                variant={
                                  branch.status === 'active'
                                    ? 'success'
                                    : 'secondary'
                                }
                                className="text-[10px]"
                              >
                                {branch.status}
                              </Badge>
                            </div>
                            <p className="text-muted-foreground text-xs">
                              {branch.address}
                            </p>
                            {branch.phone && (
                              <p className="text-muted-foreground text-xs">
                                {branch.phone}
                              </p>
                            )}
                          </div>
                        </label>
                      </FormItem>
                    )}
                  />
                ))}
              </div>
            </div>
          )}

          {isLoading && (
            <div className="flex items-center justify-center py-4">
              <span className="text-muted-foreground text-sm">
                <Trans i18nKey="products:branch:loading">Loading branches...</Trans>
              </span>
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
