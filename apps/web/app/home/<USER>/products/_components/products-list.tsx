'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import type { ColumnDef } from '@tanstack/react-table';
import { Edit2, ImageIcon, Trash2, Zap } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { ResponsiveDataTable } from '@kit/ui/responsive-data-table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';
import { formatDate } from '@kit/ui/utils';

import { FormattedPrice } from '~/components/currency/price-display';

import type { Product } from '../_lib/server/products-page.loader';
import DeleteProductButton from './delete-product-button';
import { ProductsPagination } from './products-pagination';

interface ProductsListProps {
  products: Product[];
  canManage: boolean;
  account: { id: string; slug: string };
  currentPage: number;
  limit: number;
  total: number;
  filters?: {
    query?: string;
  };
}

export default function ProductsList({
  products,
  canManage,
  account,
  currentPage,
  limit,
  total,
  filters,
}: ProductsListProps) {
  const router = useRouter();
  const { t } = useTranslation(['products']);

  const columns: ColumnDef<Product>[] = [
    {
      accessorKey: 'image',
      header: t('products:table:image', 'Image'),
      size: 100,
      cell: ({ row }) =>
        row.original.image_url ? (
          <img
            src={row.original.image_url}
            alt={row.original.name}
            className="h-10 w-10 rounded-md object-cover"
          />
        ) : (
          <div className="bg-muted h-10 w-10 rounded-md" />
        ),
    },
    {
      accessorKey: 'name',
      header: t('products:table:name', 'Name'),
      cell: ({ row }) => (
        <Link href={`/home/<USER>/products/edit/${row.original.id}`}>
          <span className="text-blue-600 hover:underline">
            {row.original.name}
          </span>
        </Link>
      ),
    },
    {
      accessorKey: 'type',
      header: t('products:table:type', 'Type'),
      cell: ({ row }) =>
        row.original.type.charAt(0).toUpperCase() + row.original.type.slice(1),
    },
    {
      accessorKey: 'price',
      header: t('products:table:price', 'Price'),
      cell: ({ row }) => {
        const product = row.original;
        const hasFlashSale = !!product.flash_sale;

        return (
          <div className="flex flex-col">
            {hasFlashSale ? (
              <>
                <div className="flex items-center gap-1">
                  <FormattedPrice
                    amount={product.flash_sale.discounted_price}
                  />
                  <Badge variant="destructive" className="text-xs">
                    -{product.flash_sale.discount_percentage}%
                  </Badge>
                </div>
                <FormattedPrice
                  amount={product.price}
                  className="text-xs text-muted-foreground line-through"
                />
              </>
            ) : (
              <FormattedPrice amount={product.price} />
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'flash_sale',
      header: t('products:table:promotion', 'Promotion'),
      size: 150,
      cell: ({ row }) => {
        const product = row.original;
        if (product.flash_sale) {
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1 text-sm">
                    <Zap className="h-4 w-4 text-amber-500" />
                    <span className="font-medium text-amber-600">
                      {product.flash_sale.name}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p className="text-xs">
                      <Trans i18nKey="products:flash_sale_ends">Ends</Trans>:{' '}
                      {formatDate(product.flash_sale.end_time, 'dd/MM/yyyy HH:mm')}
                    </p>
                    <p className="text-xs">
                      <Trans i18nKey="products:flash_sale_discount">Discount</Trans>:{' '}
                      {product.flash_sale.discount_percentage}%
                    </p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        }
        return null;
      },
    },
    {
      accessorKey: 'category_name',
      header: t('products:table:category', 'Category'),
      cell: ({ row }) => row.original.category_name || '-',
    },
    {
      accessorKey: 'stock',
      header: t('products:table:stock', 'Stock'),
      cell: ({ row }) => {
        if (!row.original.track_inventory) {
          return <Trans i18nKey="products:table:notTracked">Not tracked</Trans>;
        }
        if (row.original.inventory.length === 0) {
          return '0';
        }
        const totalStock = row.original.inventory.reduce(
          (sum, inv) => sum + inv.stock,
          0,
        );
        return row.original.product_attributes.length > 0
          ? <Trans i18nKey="products:table:managedPerAttribute">Managed per attribute</Trans>
          : totalStock;
      },
    },
    {
      accessorKey: 'attributes',
      header: t('products:table:attributes', 'Attributes'),
      cell: ({ row }) =>
        row.original.product_attributes.length > 0 ?
          <Trans i18nKey="products:table:yes">Yes</Trans> :
          <Trans i18nKey="products:table:no">No</Trans>,
    },
    {
      accessorKey: 'created_at',
      header: t('products:table:createdAt', 'Created At'),
      cell: ({ row }) => formatDate(row.original.created_at, 'dd/MM/yyyy'),
    },
  ];

  if (canManage) {
    columns.push({
      id: 'actions',
      header: t('products:table:actions', 'Actions'),
      size: 100,
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Link href={`/home/<USER>/products/edit/${row.original.id}`}>
            <Button size="icon" variant="ghost">
              <Edit2 className="h-4 w-4" />
            </Button>
          </Link>
          <DeleteProductButton product={row.original} account={account}>
            <Button size="icon" variant="ghost">
              <Trash2 className="h-4 w-4" />
            </Button>
          </DeleteProductButton>
        </div>
      ),
    });
  }

  return (
    <div className="flex flex-col space-y-6">
      <ResponsiveDataTable
        data={products}
        columns={columns}
        manualPagination={true}
        mobileCardFields={['name', 'price', 'type', 'stock', 'category_name']}
        renderMobileCard={(row) => (
          <div className="p-4">
            <div className="flex items-center gap-4 mb-4">
              {row.original.image_url ? (
                <img
                  src={row.original.image_url}
                  alt={row.original.name}
                  className="h-16 w-16 rounded-xl object-cover shadow-sm"
                />
              ) : (
                <div className="bg-muted h-16 w-16 rounded-xl shadow-sm flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-muted-foreground/50" />
                </div>
              )}
              <div className="flex-1">
                <Link href={`/home/<USER>/products/edit/${row.original.id}`}>
                  <h3 className="font-semibold text-lg text-blue-600 hover:text-blue-800">
                    {row.original.name}
                  </h3>
                </Link>
                <div className="flex items-center mt-1">
                  <span className="text-xs uppercase tracking-wider text-muted-foreground bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
                    {row.original.type.charAt(0).toUpperCase() + row.original.type.slice(1)}
                  </span>
                  {row.original.category_name && (
                    <span className="text-xs text-muted-foreground ml-2">
                      {row.original.category_name}
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800/30 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('products:table:price', 'Price')}</div>
                  <div className="font-semibold text-lg"><FormattedPrice amount={row.original.price} /></div>
                </div>

                <div className="text-right">
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('products:table:stock', 'Stock')}</div>
                  <div className="font-medium">
                    {!row.original.track_inventory ? (
                      <Trans i18nKey="products:table:notTracked">Not tracked</Trans>
                    ) : row.original.inventory.length === 0 ? (
                      '0'
                    ) : row.original.product_attributes.length > 0 ? (
                      <Trans i18nKey="products:table:managedPerAttribute">Managed per attribute</Trans>
                    ) : (
                      row.original.inventory.reduce((sum, inv) => sum + inv.stock, 0)
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('products:table:attributes', 'Attributes')}</div>
                <div className="mt-1">
                  {row.original.product_attributes.length > 0 ? (
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      <Trans i18nKey="products:table:yes">Yes</Trans>
                    </span>
                  ) : (
                    <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                      <Trans i18nKey="products:table:no">No</Trans>
                    </span>
                  )}
                </div>
              </div>
            </div>

            {canManage && (
              <div className="flex justify-end gap-2">
                <Button size="sm" variant="outline" className="rounded-full" asChild>
                  <Link href={`/home/<USER>/products/edit/${row.original.id}`}>
                    <Edit2 className="h-4 w-4 mr-1" />
                    <Trans i18nKey="common:actions:edit" defaults="Edit">Edit</Trans>
                  </Link>
                </Button>
                <DeleteProductButton product={row.original} account={account}>
                  <Button size="sm" variant="outline" className="rounded-full">
                    <Trash2 className="h-4 w-4 mr-1" />
                    <Trans i18nKey="common:actions:delete">Delete</Trans>
                  </Button>
                </DeleteProductButton>
              </div>
            )}
          </div>
        )}
      />
      <ProductsPagination
        currentPage={currentPage}
        limit={limit}
        total={total}
      />
    </div>
  );
}
