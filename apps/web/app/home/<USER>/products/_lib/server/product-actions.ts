'use server';

import { revalidatePath } from 'next/cache';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';
import { checkCanCreateResource, checkCanDeleteResource } from '~/home/<USER>/_lib/server/resource-access';
import { z } from 'zod';
import { enhanceAction } from '@kit/next/actions';

// Schema cho form tạo sản phẩm
const CreateProductSchema = z.object({
  accountId: z.string().uuid(),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be a positive number').optional(),
  // Thêm các trường khác nếu cần
});

/**
 * Server action để tạo sản phẩm mới
 */
export const createProduct = enhanceAction(
  async (data: z.infer<typeof CreateProductSchema>) => {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();

    try {
      // Kiểm tra quyền tạo sản phẩm
      const { canCreate, reason } = await checkCanCreateResource(data.accountId, 'products');

      if (!canCreate) {
        logger.info({
          accountId: data.accountId,
          reason
        }, 'User cannot create product');

        return {
          success: false,
          error: reason
        };
      }

      // Tạo sản phẩm
      const { data: product, error } = await supabase
        .from('products')
        .insert({
          account_id: data.accountId,
          name: data.name,
          description: data.description,
          price: data.price,
          // Thêm các trường khác nếu cần
        })
        .select()
        .single();

      if (error) {
        logger.error({
          accountId: data.accountId,
          error
        }, 'Error creating product');

        return {
          success: false,
          error: 'database_error'
        };
      }

      // Revalidate paths
      revalidatePath(`/home/<USER>/products`);

      return {
        success: true,
        data: product
      };

    } catch (error) {
      logger.error({
        accountId: data.accountId,
        error
      }, 'Error in createProduct action');

      return {
        success: false,
        error: 'unknown_error'
      };
    }
  },
  {
    schema: CreateProductSchema,
  }
);

/**
 * Server action để xóa sản phẩm
 */
export async function deleteProduct(productId: string, accountId: string) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // Kiểm tra quyền xóa sản phẩm
    const { canDelete, reason } = await checkCanDeleteResource(accountId, 'products');

    if (!canDelete) {
      logger.info({
        accountId,
        productId,
        reason
      }, 'User cannot delete product');

      return {
        success: false,
        error: reason
      };
    }

    // Xóa sản phẩm
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', productId)
      .eq('account_id', accountId);

    if (error) {
      logger.error({
        productId,
        accountId,
        error
      }, 'Error deleting product');

      return {
        success: false,
        error: 'database_error'
      };
    }

    // Revalidate paths
    revalidatePath(`/home/<USER>/products`);

    return {
      success: true
    };

  } catch (error) {
    logger.error({
      productId,
      accountId,
      error
    }, 'Error in deleteProduct action');

    return {
      success: false,
      error: 'unknown_error'
    };
  }
}
