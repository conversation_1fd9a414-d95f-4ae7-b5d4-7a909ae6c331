'use server';

import { randomUUID } from 'crypto';
import sharp from 'sharp';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

const PRODUCTS_BUCKET = 'products';

function sanitizeFileName(fileName: string): string {
  return fileName.toLowerCase().replace(/[^a-z0-9.]/g, '-');
}

async function convertToWebp(file: File): Promise<Buffer> {
  const buffer = Buffer.from(await file.arrayBuffer());
  return sharp(buffer).webp({ quality: 80 }).toBuffer();
}

export async function uploadProductImage(
  file: File,
  accountId: string,
  productId?: string,
): Promise<{ url: string; tempPath?: string }> {
  if (!accountId) throw new Error('Account ID is required');

  const supabase = getSupabaseServerClient();
  const sanitizedName = sanitizeFileName(file.name).replace(
    /\.[^/.]+$/,
    '.webp',
  );
  const isTempUpload = !productId;
  const tempFolder = randomUUID();
  const tempPath = isTempUpload
    ? `${tempFolder}/${accountId}/${randomUUID()}-${sanitizedName}`
    : undefined;
  const finalPath = isTempUpload
    ? tempPath
    : `${accountId}/${productId}/${sanitizedName}`;

  console.log('Uploading to bucket:', PRODUCTS_BUCKET, 'Path:', finalPath);

  const webpBuffer = await convertToWebp(file);

  const { error } = await supabase.storage
    .from(PRODUCTS_BUCKET)
    .upload(finalPath!, webpBuffer, {
      contentType: 'image/webp',
      upsert: true,
    });

  if (error) {
    console.error('Upload error:', error);
    throw error;
  }

  const {
    data: { publicUrl },
  } = supabase.storage.from(PRODUCTS_BUCKET).getPublicUrl(finalPath!);

  if (isTempUpload) {
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // Hết hạn sau 24 giờ
    const { error: dbError } = await supabase.from('temp_images').insert({
      temp_path: finalPath,
      url: publicUrl,
      expires_at: expiresAt.toISOString(),
      account_id: accountId,
    });

    if (dbError) {
      console.error('Failed to save temp image metadata:', dbError);
      await supabase.storage.from(PRODUCTS_BUCKET).remove([finalPath!]);
      throw new Error('Failed to save temp image metadata');
    }
  }

  return {
    url: publicUrl,
    tempPath: isTempUpload ? finalPath : undefined,
  };
}

export async function moveTempImage(
  tempPath: string,
  productId: string,
  accountId: string,
): Promise<string> {
  const supabase = getSupabaseServerClient();
  const fileName = tempPath.split('/').pop()!;
  const newPath = `${accountId}/${productId}/${fileName}`;

  const { error } = await supabase.storage
    .from(PRODUCTS_BUCKET)
    .move(tempPath, newPath);

  if (error) {
    console.error('Move error:', error);
    throw error;
  }

  const { error: dbError } = await supabase
    .from('temp_images')
    .update({ is_moved: true })
    .eq('temp_path', tempPath);

  if (dbError) {
    console.error('Failed to update temp image metadata:', dbError);
  }

  const {
    data: { publicUrl },
  } = supabase.storage.from(PRODUCTS_BUCKET).getPublicUrl(newPath);
  return publicUrl;
}

export async function deleteProductImage(url: string) {
  const supabase = getSupabaseServerClient();
  const urlPath = new URL(url).pathname;
  const filePath = urlPath.split('/').slice(2).join('/');
  if (!filePath) return;

  await supabase.from('temp_images').delete().eq('url', url);
  return supabase.storage.from(PRODUCTS_BUCKET).remove([filePath]);
}
