'use client';

import { use<PERSON>allback, useEffect, useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { But<PERSON> } from '@kit/ui/button';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { ImageUploaderAdvance } from '@kit/ui/image-uploader-advance';
import { Input } from '@kit/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { getCategories } from '~/home/<USER>/categories/_lib/server/get-categories';
import { updateCategory } from '~/home/<USER>/categories/_lib/server/update-category';
import {
  deleteCategoryImage,
  uploadCategoryImage,
} from '~/home/<USER>/categories/_lib/server/upload-category-image';

import { categorySchema } from '../_lib/category.schema';
import type { Category } from '../_lib/server/categories-page.loader';

// app/home/<USER>/categories/_components/edit-category-modal.tsx

interface EditCategoryModalProps {
  children: React.ReactNode;
  category: Category;
  account: { id: string; slug: string };
}

export default function EditCategoryModal({
  children,
  category,
  account,
}: EditCategoryModalProps) {
  const [open, setOpen] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [imageUrl, setImageUrl] = useState<string>(category.image_url || '');
  const [tempImagePath, setTempImagePath] = useState<string>('');
  const [tempImages, setTempImages] = useState<
    { tempPath: string; url: string }[]
  >([]);

  const form = useForm({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: category.name,
      description: category.description || '',
      parent_id: category.parent_id || null,
      image_url: category.image_url || '',
    },
  });

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const data = await getCategories(account.id);
        // Exclude the current category and its descendants to prevent circular references
        const filteredCategories = data.filter((cat) => cat.id !== category.id);
        setCategories(filteredCategories);
      } catch (error) {
        toast.error('Failed to load categories');
      }
    };
    if (open) {
      loadCategories();
      // Reset image state when opening the modal
      setImageUrl(category.image_url || '');
      form.setValue('image_url', category.image_url || '');
    }
  }, [open, account.id, category.id, category.image_url, form]);

  // Cleanup temporary images when the modal is closed
  useEffect(() => {
    return () => {
      if (tempImages.length > 0) {
        tempImages.forEach(async ({ url }) => {
          try {
            await deleteCategoryImage(url);
            console.log(`Deleted temp image: ${url}`);
          } catch (error) {
            console.error('Failed to delete temp image:', error);
          }
        });
        setTempImages([]);
      }
    };
  }, [tempImages]);

  const onImageChange = useCallback(
    async (files: File[] | null) => {
      if (!files || files.length === 0) {
        return;
      }

      try {
        const { url, tempPath } = await uploadCategoryImage(
          files[0],
          account.id,
        );
        setImageUrl(url);
        form.setValue('image_url', url);
        if (tempPath) {
          setTempImagePath(tempPath);
          setTempImages((prev) => [...prev, { tempPath, url }]);
        }
      } catch (error) {
        console.error('Failed to upload image:', error);
        toast.error('Failed to upload image');
      }
    },
    [account.id, form],
  );

  const handleRemove = useCallback(
    async (url: string) => {
      try {
        // Only delete if it's a temp image
        const isTemp = tempImages.some((img) => img.url === url);
        if (isTemp) {
          await deleteCategoryImage(url);
        }
        setImageUrl('');
        form.setValue('image_url', '');
        setTempImages((prev) => prev.filter((img) => img.url !== url));
      } catch (error) {
        console.error('Failed to remove image:', error);
      }
    },
    [form, tempImages],
  );

  const onSubmit = form.handleSubmit(async (data) => {
    try {
      // Ensure image_url is properly handled
      const formData = {
        ...data,
        // If image_url is empty string, set it to null
        image_url: data.image_url || null,
        id: category.id,
        accountId: account.id,
        accountSlug: account.slug,
        tempImagePath: tempImagePath,
      };

      await updateCategory(formData);

      setOpen(false);
      setTempImagePath('');
      setTempImages([]);
      toast.success('Category updated successfully');
    } catch (error) {
      console.error('Error updating category:', error);
      toast.error('Failed to update category');
    }
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="categories:edit:title">Edit Category</Trans>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:fields:name">Name</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} data-testid="categories-page-edit-name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parent_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:fields:parent">
                      Parent Category
                    </Trans>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger data-testid="categories-page-edit-parent">
                        <SelectValue placeholder="Select a parent category (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={null}>None</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:fields:description">
                      Description
                    </Trans>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      data-testid="categories-page-edit-description"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="image_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:form:image">
                      Category Image (Optional)
                    </Trans>
                  </FormLabel>
                  <FormDescription className="text-xs">
                    <Trans i18nKey="categories:form:image:help">
                      Upload an image for this category (recommended: 400x400px)
                    </Trans>
                  </FormDescription>
                  <FormControl>
                    <ImageUploaderAdvance
                      value={imageUrl ? [imageUrl] : []}
                      onChange={onImageChange}
                      onRemove={handleRemove}
                      maxFiles={1}
                      accept="image/*"
                      bucket="products"
                      aspectRatio="square"
                      className="hover:border-primary/50 mt-2 w-full rounded-lg border-2 border-dashed transition-colors"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                <Trans i18nKey="common:actions:cancel">Cancel</Trans>
              </Button>
              <Button
                type="submit"
                loading={form.formState.isSubmitting}
                data-testid="categories-page-edit-submit"
              >
                <Trans i18nKey="common:actions:save">Save</Trans>
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
