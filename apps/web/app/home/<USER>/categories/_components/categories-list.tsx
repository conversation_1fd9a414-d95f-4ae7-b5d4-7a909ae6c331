'use client';

import Image from 'next/image';

import type { ColumnDef } from '@tanstack/react-table';
import { Edit2, ImageIcon, Trash2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { ResponsiveDataTable } from '@kit/ui/responsive-data-table';
import { Trans } from '@kit/ui/trans';
import { formatDate } from '@kit/ui/utils';

import type { Category } from '../_lib/server/categories-page.loader';
import { CategoriesPagination } from './categories-pagination';
import DeleteCategoryButton from './delete-category-button';
import EditCategoryModal from './edit-category-modal';

interface CategoriesListProps {
  categories: Category[];
  canManage: boolean;
  account: { id: string; slug: string };
  currentPage: number;
  limit: number;
  total: number;
  filters?: {
    query?: string;
  };
}

export default function CategoriesList({
  categories,
  canManage,
  account,
  currentPage,
  limit,
  total,
  filters,
}: CategoriesListProps) {
  const { t } = useTranslation();
  const columns: ColumnDef<Category>[] = [
    {
      accessorKey: 'image_url',
      header: t('categories:table:image', 'Image'),
      size: 80,
      cell: ({ row }) => {
        const imageUrl = row.original.image_url;
        return (
          <div className="relative flex h-12 w-12 items-center justify-center overflow-hidden rounded-md border">
            {imageUrl ? (
              <Image
                src={imageUrl}
                alt={row.original.name}
                fill
                className="object-cover"
              />
            ) : (
              <div className="bg-muted flex h-full w-full items-center justify-center">
                <ImageIcon className="text-muted-foreground h-6 w-6" />
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'name',
      header: t('categories:table:name', 'Name'),
    },
    {
      accessorKey: 'parent_name',
      header: t('categories:table:parent', 'Parent Category'),
      cell: ({ row }) => {
        return row.original.parent_name || '-';
      },
    },
    {
      accessorKey: 'description',
      header: t('categories:table:description', 'Description'),
      cell: ({ row }) => row.original.description || '-',
    },
    {
      accessorKey: 'product_count',
      header: t('categories:table:products', 'Products'),
    },
    {
      accessorKey: 'created_at',
      header: t('categories:table:createdAt', 'Created At'),
      cell: ({ row }) => formatDate(row.original.created_at, 'dd/MM/yyyy'),
    },
  ];

  if (canManage) {
    columns.push({
      id: 'actions',
      header: t('categories:table:actions', 'Actions'),
      size: 50,
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <EditCategoryModal category={row.original} account={account}>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              data-testid="categories-page-edit-button"
            >
              <Edit2 className="h-4 w-4" />
            </Button>
          </EditCategoryModal>
          <DeleteCategoryButton
            category={row.original}
            disabled={row.original.product_count > 0}
            account={account}
          >
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              data-testid="categories-page-delete-button"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </DeleteCategoryButton>
        </div>
      ),
    });
  }

  return (
    <div className="flex flex-col space-y-6">
      <ResponsiveDataTable
        data={categories}
        columns={columns}
        manualPagination={true}
        rowAttributes={(row) => ({
          'data-testid': 'categories-page-item',
        })}
        mobileCardFields={[
          'name',
          'parent_name',
          'product_count',
          'created_at',
        ]}
        renderMobileCard={(row) => (
          <div className="p-4">
            <div className="mb-4 flex items-center gap-4">
              <div className="relative flex h-16 w-16 items-center justify-center overflow-hidden rounded-xl border shadow-sm">
                {row.original.image_url ? (
                  <Image
                    src={row.original.image_url}
                    alt={row.original.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="bg-muted flex h-full w-full items-center justify-center">
                    <ImageIcon className="text-muted-foreground h-6 w-6" />
                  </div>
                )}
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold">{row.original.name}</h3>
                {row.original.parent_name && (
                  <div className="text-muted-foreground mt-1 text-xs tracking-wider uppercase">
                    {t('categories:table:parent', 'Parent')}:{' '}
                    {row.original.parent_name}
                  </div>
                )}
              </div>
            </div>

            <div className="mb-4 rounded-lg bg-gray-50 p-3 dark:bg-gray-800/30">
              <div className="mb-2 flex items-center justify-between">
                <div>
                  <div className="text-muted-foreground text-xs tracking-wider uppercase">
                    {t('categories:table:products', 'Products')}
                  </div>
                  <div className="text-lg font-semibold">
                    {row.original.product_count}
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-muted-foreground text-xs tracking-wider uppercase">
                    {t('categories:table:createdAt', 'Created At')}
                  </div>
                  <div className="font-medium">
                    {formatDate(row.original.created_at, 'dd/MM/yyyy')}
                  </div>
                </div>
              </div>

              {row.original.description && (
                <div className="mt-3 border-t border-gray-200 pt-3 dark:border-gray-700">
                  <div className="text-muted-foreground text-xs tracking-wider uppercase">
                    {t('categories:table:description', 'Description')}
                  </div>
                  <div className="mt-1 text-sm">{row.original.description}</div>
                </div>
              )}
            </div>

            {canManage && (
              <div className="flex justify-end gap-2">
                <EditCategoryModal category={row.original} account={account}>
                  <Button
                    size="sm"
                    variant="outline"

                    data-testid="categories-page-edit-button"
                  >
                    <Edit2 className="mr-1 h-4 w-4" />
                    <Trans i18nKey="common:actions:edit" defaults="Edit">Edit</Trans>
                  </Button>
                </EditCategoryModal>
                <DeleteCategoryButton
                  category={row.original}
                  disabled={row.original.product_count > 0}
                  account={account}
                >
                  <Button
                    size="sm"
                    variant="outline"

                    data-testid="categories-page-delete-button"
                    disabled={row.original.product_count > 0}
                  >
                    <Trash2 className="mr-1 h-4 w-4" />
                    <Trans i18nKey="common:actions:delete">Delete</Trans>
                  </Button>
                </DeleteCategoryButton>
              </div>
            )}
          </div>
        )}
      />
      <CategoriesPagination
        currentPage={currentPage}
        limit={limit}
        total={total}
      />
    </div>
  );
}
