'use client';

import { useCallback, useEffect, useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { But<PERSON> } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { ImageUploaderAdvance } from '@kit/ui/image-uploader-advance';
import { Input } from '@kit/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { createCategory } from '~/home/<USER>/categories/_lib/server/create-category';
import { getCategories } from '~/home/<USER>/categories/_lib/server/get-categories';
import {
  deleteCategoryImage,
  uploadCategoryImage,
} from '~/home/<USER>/categories/_lib/server/upload-category-image';

import { categorySchema } from '../_lib/category.schema';
import type { Category } from '../_lib/server/categories-page.loader';

interface CreateCategoryModalProps {
  children: React.ReactNode;
}

export default function CreateCategoryModal({
  children,
}: CreateCategoryModalProps) {
  const [open, setOpen] = useState(false);
  const { account } = useTeamAccountWorkspace();
  const [categories, setCategories] = useState<Category[]>([]);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [tempImagePath, setTempImagePath] = useState<string>('');
  const [tempImages, setTempImages] = useState<
    { tempPath: string; url: string }[]
  >([]);

  const form = useForm({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      description: '',
      parent_id: null,
      image_url: '',
    },
  });

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const data = await getCategories(account.id);
        setCategories(data);
      } catch (error) {
        toast.error('Failed to load categories');
      }
    };
    if (open) {
      loadCategories();
    }
  }, [open, account.id]);

  // Cleanup temporary images when the modal is closed
  useEffect(() => {
    return () => {
      if (tempImages.length > 0) {
        tempImages.forEach(async ({ url }) => {
          try {
            await deleteCategoryImage(url);
            console.log(`Deleted temp image: ${url}`);
          } catch (error) {
            console.error('Failed to delete temp image:', error);
          }
        });
        setTempImages([]);
      }
    };
  }, [tempImages]);

  const onImageChange = useCallback(
    async (files: File[] | null) => {
      if (!files || files.length === 0) {
        setImageUrl('');
        form.setValue('image_url', '');
        return;
      }

      try {
        const { url, tempPath } = await uploadCategoryImage(
          files[0],
          account.id,
        );
        setImageUrl(url);
        form.setValue('image_url', url);
        if (tempPath) {
          setTempImagePath(tempPath);
          setTempImages((prev) => [...prev, { tempPath, url }]);
        }
      } catch (error) {
        console.error('Failed to upload image:', error);
        toast.error('Failed to upload image');
      }
    },
    [account.id, form],
  );

  const handleRemove = useCallback(
    async (url: string) => {
      try {
        await deleteCategoryImage(url);
        setImageUrl('');
        form.setValue('image_url', '');
        setTempImages((prev) => prev.filter((img) => img.url !== url));
      } catch (error) {
        console.error('Failed to remove image:', error);
      }
    },
    [form],
  );

  const onSubmit = form.handleSubmit(async (data) => {
    try {
      // Ensure image_url is properly handled
      const formData = {
        ...data,
        // If image_url is empty string, set it to null
        image_url: data.image_url || null,
        accountId: account.id,
        accountSlug: account.slug,
        tempImagePath: tempImagePath,
      };

      await createCategory(formData);

      setOpen(false);
      form.reset();
      setImageUrl('');
      setTempImagePath('');
      setTempImages([]);
      toast.success('Category created successfully');
    } catch (error) {
      console.error('Error creating category:', error);
      toast.error('Failed to create category');
    }
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="categories:create:title">Create Category</Trans>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:form:name">Name</Trans>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      data-testid="categories-page-create-name"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parent_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:form:parent">
                      Parent Category
                    </Trans>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger data-testid="categories-page-create-parent">
                        <SelectValue placeholder="Select a parent category (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={null}>None</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:form:description">
                      Description
                    </Trans>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      data-testid="categories-page-create-description"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="image_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="categories:form:image">
                      Category Image (Optional)
                    </Trans>
                  </FormLabel>
                  <FormDescription className="text-xs">
                    <Trans i18nKey="categories:form:image:help">
                      Upload an image for this category (recommended: 400x400px)
                    </Trans>
                  </FormDescription>
                  <FormControl>
                    <ImageUploaderAdvance
                      value={imageUrl ? [imageUrl] : []}
                      onChange={onImageChange}
                      onRemove={handleRemove}
                      maxFiles={1}
                      accept="image/*"
                      bucket="products"
                      aspectRatio="square"
                      className="hover:border-primary/50 mt-2 w-full rounded-lg border-2 border-dashed transition-colors"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                <Trans i18nKey="common:actions:cancel">Cancel</Trans>
              </Button>
              <Button
                type="submit"
                loading={form.formState.isSubmitting}
                data-testid="categories-page-create-submit"
              >
                <Trans i18nKey="categories:actions:create">Create</Trans>
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
