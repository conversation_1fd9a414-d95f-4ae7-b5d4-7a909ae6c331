'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { CategoryFormData } from '../category.schema';
import { moveTempCategoryImage } from './upload-category-image';

interface CreateCategoryParams extends CategoryFormData {
  accountId: string;
  accountSlug: string;
  tempImagePath?: string;
}

export async function createCategory({
  name,
  description,
  parent_id,
  image_url,
  accountId,
  accountSlug,
  tempImagePath,
}: CreateCategoryParams) {
  const client = getSupabaseServerClient();

  // First insert the category to get the ID
  const { data, error } = await client
    .from('categories')
    .insert({
      name,
      description,
      parent_id,
      image_url,
      account_id: accountId,
    })
    .select()
    .single();

  if (error) {
    throw error;
  }

  // If we have a temporary image, move it to the permanent location
  if (tempImagePath && data.id) {
    try {
      console.log('Moving temp image for category:', data.id, 'Path:', tempImagePath);
      const { url: finalImageUrl } = await moveTempCategoryImage(
        tempImagePath,
        data.id,
        accountId
      );

      console.log('Image moved successfully, updating category with URL:', finalImageUrl);
      // Update the category with the final image URL
      const { error: updateError } = await client
        .from('categories')
        .update({ image_url: finalImageUrl })
        .eq('id', data.id)
        .eq('account_id', accountId);

      if (updateError) {
        console.error('Failed to update category with image URL:', updateError);
      }
    } catch (imageError) {
      console.error('Failed to move temporary image:', imageError);
      // Continue without the image if there's an error
    }
  }

  revalidatePath(`/home/<USER>/categories`);
  return data;
}
