'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { CategoryFormData } from '../category.schema';
import { moveTempCategoryImage } from './upload-category-image';

interface UpdateCategoryParams extends CategoryFormData {
  id: string;
  accountId: string;
  accountSlug: string;
  tempImagePath?: string;
}

export async function updateCategory({
  id,
  name,
  description,
  parent_id,
  image_url,
  accountId,
  accountSlug,
  tempImagePath,
}: UpdateCategoryParams) {
  const client = getSupabaseServerClient();

  // Prepare update data
  const updateData: any = {
    name,
    description,
    parent_id,
  };

  // Only include image_url if it's provided and not a temp image
  if (image_url && !tempImagePath) {
    updateData.image_url = image_url;
  }

  const { error } = await client
    .from('categories')
    .update(updateData)
    .eq('id', id)
    .eq('account_id', accountId);

  if (error) {
    throw error;
  }

  // If we have a temporary image, move it to the permanent location
  if (tempImagePath) {
    try {
      console.log('Moving temp image for category update:', id, 'Path:', tempImagePath);
      const { url: finalImageUrl } = await moveTempCategoryImage(
        tempImagePath,
        id,
        accountId
      );

      console.log('Image moved successfully, updating category with URL:', finalImageUrl);
      // Update the category with the final image URL
      const { error: updateError } = await client
        .from('categories')
        .update({ image_url: finalImageUrl })
        .eq('id', id)
        .eq('account_id', accountId);

      if (updateError) {
        console.error('Failed to update category with image URL:', updateError);
      }
    } catch (imageError) {
      console.error('Failed to move temporary image:', imageError);
      // Continue without the image if there's an error
    }
  }

  revalidatePath(`/home/<USER>/categories`);
}
