'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { deleteCategoryImage } from './upload-category-image';

interface DeleteCategoryParams {
  id: string;
  accountId: string;
  accountSlug: string;
}

export async function deleteCategory({
  id,
  accountId,
  accountSlug,
}: DeleteCategoryParams) {
  const client = getSupabaseServerClient();

  // Check if the category has subcategories
  const { count } = await client
    .from('categories')
    .select('*', { count: 'exact', head: true })
    .eq('parent_id', id);

  if (count && count > 0) {
    throw new Error('Cannot delete category with subcategories');
  }

  // Get the category to find the image URL
  const { data: category } = await client
    .from('categories')
    .select('image_url')
    .eq('id', id)
    .eq('account_id', accountId)
    .single();

  // Delete the category
  const { error } = await client
    .from('categories')
    .delete()
    .eq('id', id)
    .eq('account_id', accountId);

  if (error) {
    throw error;
  }

  // Delete the image if it exists
  if (category?.image_url) {
    try {
      await deleteCategoryImage(category.image_url);
    } catch (imageError) {
      console.error('Failed to delete category image:', imageError);
      // Continue even if image deletion fails
    }
  }

  revalidatePath(`/home/<USER>/categories`);
}
