import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import { Database } from '~/lib/database.types';

export interface ProductItem {
  id: string;
  name: string;
}

export interface CategoryItem {
  id: string;
  name: string;
}

export class ProductCategoryApi {
  constructor(private readonly client: SupabaseClient<Database>) {}

  async getProducts(accountId: string): Promise<ProductItem[]> {
    const { data, error } = await this.client
      .from('products')
      .select('id, name')
      .eq('account_id', accountId)
      .eq('status', 'active')
      .order('name');

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get products');
      throw new Error('Failed to get products');
    }

    return data || [];
  }

  async getCategories(accountId: string): Promise<CategoryItem[]> {
    const { data, error } = await this.client
      .from('categories')
      .select('id, name')
      .eq('account_id', accountId)
      .order('name');

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get categories');
      throw new Error('Failed to get categories');
    }

    return data || [];
  }
}

export function createProductCategoryApi(client: SupabaseClient<Database>): ProductCategoryApi {
  return new ProductCategoryApi(client);
}
