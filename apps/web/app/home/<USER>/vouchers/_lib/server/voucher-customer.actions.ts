import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { z } from 'zod';

import { VoucherCustomerOperation } from '~/lib/types/voucher';

import { createVoucherApi } from './voucher.api';

const VoucherCustomerSchema = z.object({
  voucher_id: z.string().uuid(),
  customer_ids: z.array(z.string().uuid()),
});

export const addCustomersToVoucherAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const api = createVoucherApi(client);

    const ctx = {
      name: 'vouchers.add-customers',
      userId: user.id,
      voucherId: data.voucher_id,
      customerCount: data.customer_ids.length,
    };

    logger.info(ctx, 'Adding customers to voucher');

    try {
      const params: VoucherCustomerOperation = {
        voucher_id: data.voucher_id,
        customer_ids: data.customer_ids,
      };

      const result = await api.addCustomersToVoucher(params);

      if (!result.success) {
        logger.error({ ...ctx, error: result.message }, 'Failed to add customers to voucher');
        return { success: false, error: result.message };
      }

      logger.info({ ...ctx, addedCount: result.added_count }, 'Customers added to voucher successfully');

      revalidatePath(`/home/<USER>/vouchers/${data.voucher_id}`);

      return { success: true, added_count: result.added_count };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to add customers to voucher');
      return { success: false, error: 'Failed to add customers to voucher' };
    }
  },
  {
    schema: VoucherCustomerSchema.extend({
      accountSlug: z.string(),
    }),
  },
);

export const removeCustomersFromVoucherAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const api = createVoucherApi(client);

    const ctx = {
      name: 'vouchers.remove-customers',
      userId: user.id,
      voucherId: data.voucher_id,
      customerCount: data.customer_ids.length,
    };

    logger.info(ctx, 'Removing customers from voucher');

    try {
      const params: VoucherCustomerOperation = {
        voucher_id: data.voucher_id,
        customer_ids: data.customer_ids,
      };

      const result = await api.removeCustomersFromVoucher(params);

      if (!result.success) {
        logger.error({ ...ctx, error: result.message }, 'Failed to remove customers from voucher');
        return { success: false, error: result.message };
      }

      logger.info(
        { ...ctx, removedCount: result.removed_count },
        'Customers removed from voucher successfully',
      );

      revalidatePath(`/home/<USER>/vouchers/${data.voucher_id}`);

      return { success: true, removed_count: result.removed_count };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to remove customers from voucher');
      return { success: false, error: 'Failed to remove customers from voucher' };
    }
  },
  {
    schema: VoucherCustomerSchema.extend({
      accountSlug: z.string(),
    }),
  },
);
