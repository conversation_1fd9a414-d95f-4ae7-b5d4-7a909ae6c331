import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import { Database } from '~/lib/database.types';
import {
  CreateVoucherParams,
  Voucher,
  VoucherCustomerPhoneOperation,
  VoucherWithCustomerPhones,
  VoucherWithRedemptions,
} from '~/lib/types/voucher';

export class VoucherApi {
  constructor(private readonly client: SupabaseClient<Database>) {}

  async getVouchers(accountId: string): Promise<Voucher[]> {
    const { data, error } = await this.client
      .from('vouchers')
      .select('*')
      .eq('account_id', accountId)
      .order('created_at', { ascending: false });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get vouchers');
      throw new Error('Failed to get vouchers');
    }

    return data || [];
  }

  async getVoucherById(id: string): Promise<VoucherWithRedemptions | null> {
    const { data, error } = await this.client
      .from('vouchers')
      .select(
        `
        *,
        redemptions:voucher_redemptions(*)
      `,
      )
      .eq('id', id)
      .single();

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get voucher');
      throw new Error('Failed to get voucher');
    }

    return data;
  }

  async getActiveVouchers(accountId: string): Promise<Voucher[]> {
    const now = new Date().toISOString();

    const { data, error } = await this.client
      .from('vouchers')
      .select('*')
      .eq('account_id', accountId)
      .eq('status', 'active')
      .lte('start_date', now)
      .gte('end_date', now)
      .order('end_date', { ascending: true });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get active vouchers');
      throw new Error('Failed to get active vouchers');
    }

    return data || [];
  }

  async createVoucher(
    accountId: string,
    params: CreateVoucherParams,
  ): Promise<{ id: string; success: boolean; message?: string }> {
    const { data, error } = await this.client.rpc('create_voucher', {
      p_account_id: accountId,
      p_code: params.code,
      p_name: params.name,
      p_description: params.description || null,
      p_discount_type: params.discount_type,
      p_discount_value: params.discount_value,
      p_min_order_value: params.min_order_value || null,
      p_max_discount_value: params.max_discount_value || null,
      p_max_uses: params.max_uses || null,
      p_start_date: params.start_date.toISOString(),
      p_end_date: params.end_date.toISOString(),
      p_is_customer_specific: params.is_customer_specific || false,
      p_usage_limit_per_customer: params.usage_limit_per_customer || null,
      p_first_time_customers_only: params.first_time_customers_only || false,
      p_min_previous_orders: params.min_previous_orders || null,
      p_excluded_product_ids: params.excluded_product_ids || [],
      p_included_product_ids: params.included_product_ids || [],
      p_excluded_category_ids: params.excluded_category_ids || [],
      p_included_category_ids: params.included_category_ids || [],
    });

    if (error || !data?.success) {
      const logger = await getLogger();
      logger.error({ error, data }, 'Failed to create voucher');
      return {
        id: '',
        success: false,
        message: error?.message || data?.message || 'Failed to create voucher',
      };
    }

    return {
      id: data.voucher_id,
      success: true,
    };
  }

  async updateVoucher(
    id: string,
    accountId: string,
    params: {
      code: string;
      name: string;
      description?: string;
      discount_type: 'percentage' | 'fixed';
      discount_value: number;
      min_order_value?: number;
      max_discount_value?: number;
      max_uses?: number;
      start_date: string;
      end_date: string;
      status: 'active' | 'expired' | 'disabled';
      // Advanced restrictions
      is_customer_specific?: boolean;
      usage_limit_per_customer?: number;
      first_time_customers_only?: boolean;
      min_previous_orders?: number;
      excluded_product_ids?: string[];
      included_product_ids?: string[];
      excluded_category_ids?: string[];
      included_category_ids?: string[];
    },
  ): Promise<{ success: boolean; message?: string }> {
    // Validate inputs
    if (params.discount_type === 'percentage' && (params.discount_value <= 0 || params.discount_value > 100)) {
      return { success: false, message: 'Percentage discount must be between 0 and 100' };
    }

    if (new Date(params.end_date) <= new Date(params.start_date)) {
      return { success: false, message: 'End date must be after start date' };
    }

    // Check if code already exists for this account (excluding current voucher)
    const { data: existingVoucher, error: existingError } = await this.client
      .from('vouchers')
      .select('id')
      .eq('account_id', accountId)
      .eq('code', params.code)
      .neq('id', id)
      .single();

    if (existingVoucher) {
      return { success: false, message: 'Voucher code already exists' };
    }

    // Update voucher using the update method
    const { error } = await this.client
      .from('vouchers')
      .update({
        code: params.code,
        name: params.name,
        description: params.description || null,
        discount_type: params.discount_type,
        discount_value: params.discount_value,
        min_order_value: params.min_order_value || null,
        max_discount_value: params.max_discount_value || null,
        max_uses: params.max_uses || null,
        start_date: params.start_date,
        end_date: params.end_date,
        status: params.status,
        // Advanced restrictions
        is_customer_specific: params.is_customer_specific || false,
        usage_limit_per_customer: params.usage_limit_per_customer || null,
        first_time_customers_only: params.first_time_customers_only || false,
        min_previous_orders: params.min_previous_orders || null,
        excluded_product_ids: params.excluded_product_ids || [],
        included_product_ids: params.included_product_ids || [],
        excluded_category_ids: params.excluded_category_ids || [],
        included_category_ids: params.included_category_ids || [],
        updated_at: new Date().toISOString(),
        updated_by: null, // We'll let the database trigger handle this
      })
      .eq('id', id)
      .eq('account_id', accountId);

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to update voucher');
      return {
        success: false,
        message: error.message || 'Failed to update voucher',
      };
    }

    return {
      success: true,
    };
  }

  async updateVoucherStatus(
    id: string,
    status: 'active' | 'expired' | 'disabled',
  ): Promise<boolean> {
    const { error } = await this.client
      .from('vouchers')
      .update({ status })
      .eq('id', id);

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to update voucher status');
      throw new Error('Failed to update voucher status');
    }

    return true;
  }

  async deleteVoucher(
    id: string,
    accountId: string,
  ): Promise<{ success: boolean; message?: string }> {
    const { data, error } = await this.client.rpc('delete_voucher', {
      p_voucher_id: id,
      p_account_id: accountId,
    });

    if (error || !data?.success) {
      const logger = await getLogger();
      logger.error({ error, data }, 'Failed to delete voucher');
      return {
        success: false,
        message: error?.message || data?.message || 'Failed to delete voucher',
      };
    }

    return {
      success: true,
    };
  }

  async applyVoucherToOrder(
    orderId: string,
    voucherCode: string,
    customerPhone?: string,
  ): Promise<{ success: boolean; message: string; discount_amount?: number; new_total?: number }> {
    const { data, error } = await this.client.rpc('apply_voucher_to_order', {
      p_order_id: orderId,
      p_voucher_code: voucherCode,
      p_customer_phone: customerPhone || null,
    });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to apply voucher to order');
      return {
        success: false,
        message: error.message || 'Failed to apply voucher to order',
      };
    }

    return {
      success: data.success,
      message: data.message,
      discount_amount: data.discount_amount,
      new_total: data.new_total,
    };
  }

  async getVoucherWithCustomerPhones(id: string): Promise<VoucherWithCustomerPhones | null> {
    const { data, error } = await this.client
      .from('vouchers')
      .select(
        `
        *,
        customer_phones:voucher_customer_phones(*)
      `,
      )
      .eq('id', id)
      .single();

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get voucher with customer phones');
      throw new Error('Failed to get voucher with customer phones');
    }

    return data;
  }

  async addCustomerPhonesToVoucher(
    params: VoucherCustomerPhoneOperation,
  ): Promise<{ success: boolean; message: string; added_count?: number }> {
    const { data, error } = await this.client.rpc('add_customer_phones_to_voucher', {
      p_voucher_id: params.voucher_id,
      p_phone_numbers: params.phone_numbers,
    });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to add customer phones to voucher');
      return {
        success: false,
        message: error.message || 'Failed to add customer phones to voucher',
      };
    }

    return {
      success: data.success,
      message: data.message,
      added_count: data.added_count,
    };
  }

  async removeCustomerPhonesFromVoucher(
    params: VoucherCustomerPhoneOperation,
  ): Promise<{ success: boolean; message: string; removed_count?: number }> {
    const { data, error } = await this.client.rpc('remove_customer_phones_from_voucher', {
      p_voucher_id: params.voucher_id,
      p_phone_numbers: params.phone_numbers,
    });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to remove customer phones from voucher');
      return {
        success: false,
        message: error.message || 'Failed to remove customer phones from voucher',
      };
    }

    return {
      success: data.success,
      message: data.message,
      removed_count: data.removed_count,
    };
  }
}

export function createVoucherApi(client: SupabaseClient<Database>): VoucherApi {
  return new VoucherApi(client);
}
