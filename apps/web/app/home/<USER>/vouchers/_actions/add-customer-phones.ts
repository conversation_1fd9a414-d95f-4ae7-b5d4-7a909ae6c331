'use server';

import { revalidatePath } from 'next/cache';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function addCustomerPhonesToVoucher(
  voucherId: string,
  phoneNumbers: string[],
  accountSlug: string,
) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  const ctx = {
    name: 'vouchers.add-customer-phones',
    voucherId,
    phoneCount: phoneNumbers.length,
  };

  logger.info(ctx, 'Adding customer phones to voucher');

  try {
    const { data, error } = await client.rpc('add_customer_phones_to_voucher', {
      p_voucher_id: voucherId,
      p_phone_numbers: phoneNumbers,
    });

    if (error || !data?.success) {
      logger.error(
        { ...ctx, error: error?.message || data?.message },
        'Failed to add customer phones to voucher',
      );
      return {
        success: false,
        error: error?.message || data?.message || 'Failed to add customer phones to voucher',
      };
    }

    logger.info(
      { ...ctx, addedCount: data.added_count },
      'Customer phones added to voucher successfully',
    );

    revalidatePath(`/home/<USER>/vouchers/${voucherId}`);

    return { success: true, added_count: data.added_count };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to add customer phones to voucher');
    return { success: false, error: 'Failed to add customer phones to voucher' };
  }
}
