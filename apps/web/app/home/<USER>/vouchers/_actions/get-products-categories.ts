'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createProductCategoryApi } from '../_lib/server/product-category.api';

export async function getProductsAndCategories(accountId: string) {
  try {
    const client = getSupabaseServerClient();
    const api = createProductCategoryApi(client);

    const [products, categories] = await Promise.all([
      api.getProducts(accountId),
      api.getCategories(accountId),
    ]);

    return {
      success: true,
      products,
      categories,
    };
  } catch (error) {
    console.error('Failed to get products and categories', error);
    return {
      success: false,
      products: [],
      categories: [],
      error: 'Failed to get products and categories',
    };
  }
}
