'use client';

import { useEffect, useState } from 'react';

import { formatDateTimeFromObject } from '@kit/ui/utils';
import { Trans } from '@kit/ui/trans';

interface ValidPeriodDisplayProps {
  startDate: Date;
  endDate: Date;
}

export function ValidPeriodDisplay({ startDate, endDate }: ValidPeriodDisplayProps) {
  // Use state to store formatted dates
  const [formattedStartDate, setFormattedStartDate] = useState<string>('');
  const [formattedEndDate, setFormattedEndDate] = useState<string>('');

  // Format dates only on the client side to avoid hydration mismatch
  useEffect(() => {
    setFormattedStartDate(formatDateTimeFromObject(startDate));
    setFormattedEndDate(formatDateTimeFromObject(endDate));
  }, [startDate, endDate]);

  return (
    <span className="text-muted-foreground mt-2 text-xs block">
      {formattedStartDate && formattedEndDate ? (
        <Trans
          i18nKey="vouchers:valid_period"
          values={{
            from: formattedStartDate,
            to: formattedEndDate,
          }}
        >
          Valid from{' '}
          {formattedStartDate}{' '}
          to{' '}
          {formattedEndDate}
        </Trans>
      ) : null}
    </span>
  );
}
