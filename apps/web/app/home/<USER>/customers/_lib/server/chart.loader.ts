import 'server-only';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export type ChartData = {
  labels: string[];
  data: number[];
};

export async function loadCustomerChart(accountId: string) {
  const supabase = getSupabaseServerClient();
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

  const { data: customers, error } = await supabase
    .from('accounts')
    .select('created_at')
    .eq('account_id', accountId)
    .gte('created_at', threeMonthsAgo.toISOString());

  if (error) throw error;

  const monthlyData = new Map<string, number>();

  customers.forEach((customer) => {
    const month = customer.created_at.substring(0, 7); // YYYY-MM
    monthlyData.set(month, (monthlyData.get(month) || 0) + 1);
  });

  const sortedMonths = Array.from(monthlyData.keys()).sort();

  return {
    labels: sortedMonths.map((month) => {
      const [year, monthNum] = month.split('-');
      return new Date(
        parseInt(year),
        parseInt(monthNum) - 1,
      ).toLocaleDateString('en-US', { month: 'short' });
    }),
    data: sortedMonths.map((month) => monthlyData.get(month) || 0),
  };
}
