'use client';

import Link from 'next/link';
import Image from 'next/image';

import type { ColumnDef } from '@tanstack/react-table';
import { Eye, Trash2, User } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { ResponsiveDataTable } from '@kit/ui/responsive-data-table';
import { Trans } from '@kit/ui/trans';
import { formatDate } from '@kit/ui/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';

import { FormattedPrice } from '~/components/currency/price-display';

import type { Customer } from '../_lib/types';
import { CustomersPagination } from './customers-pagination';
import DeleteCustomerButton from './delete-customer-button';

interface CustomerTableProps {
  customers: Customer[];
  canManage?: boolean;
  account: { id: string; slug: string };
  currentPage: number;
  limit: number;
  total: number;
  filters?: {
    query?: string;
  };
}

export default function CustomerTable({
  customers = [],
  canManage = false,
  account,
  currentPage,
  limit,
  total,
  filters = {},
}: CustomerTableProps) {
  const { t } = useTranslation(['customers']);

  const columns: ColumnDef<Customer>[] = [
    {
      accessorKey: 'name',
      header: t('customers:table:name'),
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-9 w-9">
            <AvatarImage src={row.original.avatar_url || undefined} alt={row.original.name} />
            <AvatarFallback className="bg-primary/10">
              <User className="h-4 w-4 text-primary" />
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <Link
              href={`/home/<USER>/customers/${row.original.id}`}
              className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
            >
              {row.original.name}
            </Link>
            {row.original.is_vip && (
              <span className="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800 w-fit mt-1">
                {t('customers:vipBadge')}
              </span>
            )}
          </div>
        </div>
      ),
    },
    { accessorKey: 'email', header: t('customers:table:email') },
    {
      accessorKey: 'phone',
      header: t('customers:table:phone'),
      cell: ({ row }) => row.original.phone || '-',
    },
    {
      accessorKey: 'total_orders',
      header: t('customers:table:orders'),
      cell: ({ row }) => {
        const totalOrders = row.original.total_orders || 0;
        return typeof totalOrders === 'number'
          ? totalOrders.toLocaleString()
          : '0';
      },
    },
    {
      accessorKey: 'total_spent',
      header: t('customers:table:totalSpent'),
      cell: ({ row }) => {
        const totalSpent = row.original.total_spent || 0;
        return <FormattedPrice amount={totalSpent} />;
      },
    },
    {
      accessorKey: 'created_at',
      header: t('customers:table:joinedDate'),
      cell: ({ row }) => formatDate(row.original.created_at, 'dd/MM/yyyy'),
    },
  ];

  if (canManage) {
    columns.push({
      id: 'actions',
      header: t('customers:table:actions'),
      size: 100,
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Button size="sm" variant="ghost" className="h-8 w-8 p-0" asChild>
            <Link href={`/home/<USER>/customers/${row.original.id}`}>
              <Eye className="h-4 w-4" />
            </Link>
          </Button>
          <DeleteCustomerButton customer={row.original} account={account}>
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
              <Trash2 className="h-4 w-4" />
            </Button>
          </DeleteCustomerButton>
        </div>
      ),
    });
  }

  if (!customers) {
    return <div>{t('customers:noData')}</div>;
  }

  return (
    <div className="flex flex-col space-y-6">
      <ResponsiveDataTable
        data={customers}
        columns={columns}
        manualPagination={true}
        mobileCardFields={['name', 'email', 'phone', 'total_orders', 'total_spent']}
        renderMobileCard={(row) => (
          <div className="p-4">
            <div className="flex items-center gap-4 mb-4">
              <Avatar className="h-16 w-16 border shadow-sm">
                <AvatarImage src={row.original.avatar_url || undefined} alt={row.original.name} />
                <AvatarFallback className="bg-primary/10">
                  <User className="h-6 w-6 text-primary" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Link
                    href={`/home/<USER>/customers/${row.original.id}`}
                    className="font-semibold text-lg text-blue-600 hover:text-blue-800"
                  >
                    {row.original.name}
                  </Link>
                  {row.original.is_vip && (
                    <span className="rounded-full bg-yellow-100 px-2 py-0.5 text-xs font-medium text-yellow-800">
                      {t('customers:vipBadge')}
                    </span>
                  )}
                </div>
                <div className="text-sm text-muted-foreground mt-1">{row.original.email}</div>
                {row.original.phone && (
                  <div className="text-sm text-muted-foreground">{row.original.phone}</div>
                )}
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800/30 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('customers:table:orders')}</div>
                  <div className="font-semibold text-lg">{(row.original.total_orders || 0).toLocaleString()}</div>
                </div>

                <div className="text-right">
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('customers:table:totalSpent')}</div>
                  <div className="font-semibold text-lg"><FormattedPrice amount={row.original.total_spent || 0} /></div>
                </div>
              </div>

              <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div>
                  <div className="text-xs uppercase tracking-wider text-muted-foreground">{t('customers:table:joinedDate')}</div>
                  <div className="font-medium">{formatDate(row.original.created_at, 'dd/MM/yyyy')}</div>
                </div>
              </div>
            </div>

            {canManage && (
              <div className="flex justify-end gap-2">
                <Button size="sm" variant="outline" className="rounded-full" asChild>
                  <Link href={`/home/<USER>/customers/${row.original.id}`}>
                    <Eye className="h-4 w-4 mr-1" />
                    <Trans i18nKey="common:actions:view">View</Trans>
                  </Link>
                </Button>
                <DeleteCustomerButton customer={row.original} account={account}>
                  <Button size="sm" variant="outline" className="rounded-full">
                    <Trash2 className="h-4 w-4 mr-1" />
                    <Trans i18nKey="common:actions:delete">Delete</Trans>
                  </Button>
                </DeleteCustomerButton>
              </div>
            )}
          </div>
        )}
      />
      <CustomersPagination
        currentPage={currentPage}
        limit={limit}
        total={total}
      />
    </div>
  );
}
