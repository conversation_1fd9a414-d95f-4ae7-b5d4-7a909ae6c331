'use client';

import { useEffect, useState } from 'react';

import { CartesianGrid, Line, LineChart, XAxis } from 'recharts';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@kit/ui/chart';
import { Trans } from '@kit/ui/trans';

import { getCustomerStats } from '../_lib/server/customer-stats';

interface CustomerChartProps {
  account: string;
}

interface ChartData {
  monthly: Array<{ name: string; value: number }>;
  total: number;
}

interface Stats {
  newCustomers: ChartData;
  customersWithOrders: ChartData;
}

const defaultStats: Stats = {
  newCustomers: { monthly: [], total: 0 },
  customersWithOrders: { monthly: [], total: 0 },
};

export default function CustomerChart({ account }: CustomerChartProps) {
  const [stats, setStats] = useState<Stats>(defaultStats);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadStats() {
      try {
        const data = await getCustomerStats(account);
        setStats(data);
      } catch (error) {
        console.error('Error loading customer stats:', error);
        setStats(defaultStats);
      } finally {
        setLoading(false);
      }
    }

    loadStats();
  }, [account]);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="customers:charts:new">New Customers</Trans>
          </CardTitle>
          <CardDescription>
            <Trans i18nKey="customers:charts:newDescription">
              New customers in the last 8 months
            </Trans>
          </CardDescription>

          <div>
            <Figure>{stats?.newCustomers?.total ?? 0}</Figure>
          </div>
        </CardHeader>

        <CardContent>
          <Chart data={stats?.newCustomers?.monthly ?? []} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="customers:charts:active">Active Customers</Trans>
          </CardTitle>
          <CardDescription>
            <Trans i18nKey="customers:charts:activeDescription">
              Customers with activity in the last 30 days
            </Trans>
          </CardDescription>

          <div>
            <Figure>{stats?.activeCustomers?.total ?? 0}</Figure>
          </div>
        </CardHeader>

        <CardContent>
          <Chart data={stats?.activeCustomers?.monthly ?? []} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="customers:charts:withOrders">
              Customers with Orders
            </Trans>
          </CardTitle>
          <CardDescription>
            <Trans i18nKey="customers:charts:withOrdersDescription">
              Customers who placed orders in the last 30 days
            </Trans>
          </CardDescription>

          <div>
            <Figure>{stats?.customersWithOrders?.total ?? 0}</Figure>
          </div>
        </CardHeader>

        <CardContent>
          <Chart data={stats?.customersWithOrders?.monthly ?? []} />
        </CardContent>
      </Card>
    </div>
  );
}

function Chart(
  props: React.PropsWithChildren<{
    data: Array<{ name: string; value: number }>;
  }>,
) {
  const chartConfig = {
    desktop: {
      label: 'Desktop',
      color: 'var(--chart-1)',
    },
    mobile: {
      label: 'Mobile',
      color: 'var(--chart-2)',
    },
  } satisfies ChartConfig;

  return (
    <ChartContainer config={chartConfig}>
      <LineChart accessibilityLayer data={props.data}>
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey="name"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
        />
        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent hideLabel />}
        />
        <Line
          dataKey="value"
          type="natural"
          stroke="var(--color-desktop)"
          strokeWidth={2}
          dot={false}
        />
      </LineChart>
    </ChartContainer>
  );
}

function Figure({ children }: { children: number }) {
  return <p className="text-3xl font-bold">{children.toLocaleString()}</p>;
}
