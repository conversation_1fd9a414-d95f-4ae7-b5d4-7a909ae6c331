import 'server-only';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

export type CustomerDetail = {
  id: string;
  name: string;
  email: string;
  phone: string | null;
  is_vip: boolean;
  created_at: string;
  account_id: string;
  customer_id?: string;
  avatar_url?: string;
  connected_apps: string[];
  loyalty_cards: Array<{
    name: string;
    slots: number;
    date: string;
  }>;
  labels: string[];
  attributes: Record<string, string>;
  recent_orders: Array<{
    id: string;
    order_code: string;
    date: string;
    amount: number;
    status: string;
    duration: string;
  }>;
  recent_activities: Array<{
    type: string;
    description: string;
    date: string;
    formatted_date: string;
  }>;
};

export async function loadCustomerDetail(
  accountSlug: string,
  customerId: string,
): Promise<{ customer: CustomerDetail | null }> {
  const supabase = getSupabaseServerClient();
  const teamAccountsApi = createTeamAccountsApi(supabase);

  // Get account ID from slug
  const accountData = await teamAccountsApi.getTeamAccount(accountSlug);
  if (!accountData) {
    throw new Error(`Account with slug ${accountSlug} not found`);
  }

  const accountId = accountData.id;

  // Get customer data - customers are personal accounts with primary_owner_user_id = customerId
  const { data: customer, error } = await supabase
    .from('accounts')
    .select('*')
    .eq('id', customerId)
    .eq('is_personal_account', true)
    .single();

  if (error) {
    console.error('Error loading customer:', error);
    return { customer: null };
  }

  if (!customer) {
    return { customer: null };
  }

  // Check if this customer belongs to the account
  const { data: membership, error: membershipError } = await supabase
    .from('accounts_memberships')
    .select('*')
    .eq('user_id', customer.primary_owner_user_id)
    .eq('account_id', accountId)
    .eq('account_role', 'customer')
    .single();

  if (membershipError || !membership) {
    console.error('Customer does not belong to this account');
    return { customer: null };
  }

  const publicData = customer.public_data || {};

  // Get recent orders for this customer
  const { data: recentOrders, error: ordersError } = await supabase
    .from('customer_orders')
    .select('id, order_code, created_at, total_amount, status')
    .eq('customer_id', customer.primary_owner_user_id)
    .eq('account_id', accountId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (ordersError) {
    console.error('Error loading recent orders:', ordersError);
  }

  // Get recent activities for this customer
  const { data: recentActivities, error: activitiesError } = await supabase
    .from('customer_activities')
    .select('action, details, created_at')
    .eq('customer_id', customer.primary_owner_user_id)
    .eq('account_id', accountId)
    .order('created_at', { ascending: false })
    .limit(10);

  if (activitiesError) {
    console.error('Error loading recent activities:', activitiesError);
  }

  // Format recent orders
  const formattedOrders = (recentOrders || []).map(order => {
    const orderDate = new Date(order.created_at);
    return {
      id: order.id,
      order_code: order.order_code || `ORD-${order.id.slice(0, 6)}`,
      date: order.created_at,
      amount: order.total_amount,
      status: order.status,
      duration: ''
    };
  });

  // Format recent activities
  const formattedActivities = (recentActivities || []).map(activity => ({
    type: activity.action,
    description: getActivityDescription(activity),
    date: activity.created_at,
    formatted_date: formatRelativeTime(activity.created_at)
  }));

  return {
    customer: {
      id: customer.id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      is_vip: publicData.is_vip || false,
      created_at: customer.created_at,
      account_id: accountId,
      customer_id: publicData.customer_id,
      avatar_url: publicData.avatar_url || customer.picture_url,
      connected_apps: publicData.connected_apps || [],
      loyalty_cards: publicData.loyalty_cards || [],
      labels: publicData.labels || [],
      attributes: publicData.attributes || {},
      recent_orders: formattedOrders,
      recent_activities: formattedActivities,
    }
  };
}

// Helper function to format activity description
function getActivityDescription(activity: any): string {
  const { action, details } = activity;

  switch (action) {
    case 'account_created':
      return 'Tài khoản được tạo';
    case 'account_login':
      return `Đăng nhập từ ${details?.location || 'unknown'}`;
    case 'profile_updated':
      return 'Cập nhật thông tin cá nhân';
    case 'view_product':
      return `Xem sản phẩm: ${details?.product_name || 'unknown'}`;
    case 'add_to_cart':
      return `Thêm vào giỏ hàng: ${details?.product_name || 'unknown'}`;
    case 'checkout':
      return `Thanh toán ${details?.total_items || 0} sản phẩm`;
    case 'order_placed':
      return `Đặt đơn hàng trị giá ${details?.total_amount || 0}đ`;
    case 'order_review':
      return `Đánh giá đơn hàng: ${details?.rating || 0} sao`;
    default:
      return action;
  }
}

// Helper function to format relative time
function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Vừa xong';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} phút trước`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} giờ trước`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} ngày trước`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months} tháng trước`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years} năm trước`;
  }
}
