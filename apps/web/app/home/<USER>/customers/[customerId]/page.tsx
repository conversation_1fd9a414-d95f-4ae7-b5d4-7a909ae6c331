import { Suspense } from 'react';
import { notFound } from 'next/navigation';

import { Alert, AlertDescription } from '@kit/ui/alert';
import { Breadcrumb } from '@kit/ui/breadcrumb';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { CustomerDetailHeader } from './_components/customer-detail-header';
import { CustomerGeneralInfo } from './_components/customer-general-info';
import { CustomerLoyaltyCards } from './_components/customer-loyalty-cards';
import { CustomerConnectedApps } from './_components/customer-connected-apps';
import { CustomerLabels } from './_components/customer-labels';
import { CustomerAttributes } from './_components/customer-attributes';
import { CustomerRecentOrders } from './_components/customer-recent-orders';
import { CustomerRecentActivity } from './_components/customer-recent-activity';
import { loadCustomerDetail } from './_lib/server/customer-detail.loader';

interface CustomerDetailPageProps {
  params: Promise<{
    account: string;
    customerId: string;
  }>;
}

async function CustomerDetailPage({ params }: CustomerDetailPageProps) {
  await createI18nServerInstance();
  const { account, customerId } = await params;

  try {
    const [{ customer }, { account: workspace }] = await Promise.all([
      loadCustomerDetail(account, customerId),
      loadTeamWorkspace(account),
    ]);

    if (!customer) {
      return notFound();
    }

    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="customers:detail.title">Customer Detail</Trans>}
          description={
            <Breadcrumb
              items={[
                { label: 'Home', href: '/home' },
                { label: 'Customers', href: `/home/<USER>/customers` },
                { label: customer.name },
              ]}
            />
          }
          account={account}
        />

        <PageBody data-testid="customer-detail-page" className="max-w-7xl mx-auto">
          <CustomerDetailHeader customer={customer} />

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
            <div className="lg:col-span-1">
              <CustomerGeneralInfo customer={customer} />
              <CustomerConnectedApps customer={customer} />
              <CustomerLabels customer={customer} />
              <CustomerAttributes customer={customer} />
            </div>
            <div className="lg:col-span-2">
              <CustomerLoyaltyCards customer={customer} />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div className="md:col-span-1">
                  <CustomerRecentOrders customer={customer} />
                </div>
                <div className="md:col-span-1">
                  <CustomerRecentActivity customer={customer} />
                </div>
              </div>
            </div>
          </div>
        </PageBody>
      </>
    );
  } catch (error) {
    console.error('Error loading customer detail page:', error);

    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="customers:detail.title">Customer Detail</Trans>}
          description={
            <Breadcrumb
              items={[
                { label: 'Home', href: '/home' },
                { label: 'Customers', href: `/home/<USER>/customers` },
                { label: 'Error' },
              ]}
            />
          }
          account={account}
        />

        <PageBody data-testid="customer-error-page">
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:loadingFailed">
                Failed to load data. Please try again later.
              </Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }
}

export default withI18n(CustomerDetailPage);
