import { format } from 'date-fns';
import { ChevronRight, Trophy, Star, Award, Calendar, CreditCard, Gift } from 'lucide-react';
import { Trans } from '@kit/ui/trans';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { CustomerDetail } from '../_lib/server/customer-detail.loader';

interface CustomerLoyaltyCardsProps {
  customer: CustomerDetail;
}

export function CustomerLoyaltyCards({ customer }: CustomerLoyaltyCardsProps) {
  const loyaltyCards = customer.loyalty_cards || [];

  // Always show loyalty cards, even if there's no data

  // Mock data for loyalty cards if none exist
  const mockLoyaltyCards = [
    {
      name: 'Thẻ Vàng',
      slots: 70,
      points: 1500,
      level: 'Gold',
      expiry: '31/12/2024',
      date: new Date().toISOString(),
      image: 'https://zalo-miniapp.github.io/zaui-coffee/images/banner-1.jpg'
    },
    {
      name: 'Thẻ Bạc',
      slots: 50,
      points: 850,
      level: 'Silver',
      expiry: '30/09/2024',
      date: new Date().toISOString(),
      image: 'https://zalo-miniapp.github.io/zaui-coffee/images/banner-2.jpg'
    },
    {
      name: 'Thẻ Đồng',
      slots: 18,
      points: 320,
      level: 'Bronze',
      expiry: '31/08/2024',
      date: new Date().toISOString(),
      image: 'https://zalo-miniapp.github.io/zaui-coffee/images/banner-3.jpg'
    }
  ];

  // Always show mock data for this demo
  const displayCards = mockLoyaltyCards;

  return (
    <Card className="mb-6 overflow-hidden border border-gray-200 shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 pb-2">
        <div className="flex items-center">
          <Award className="mr-2 h-5 w-5 text-blue-500" />
          <CardTitle className="text-sm font-bold uppercase tracking-wider text-gray-700">
            <Trans i18nKey="customers:detail.loyaltyCards">THẺ LOYALTY</Trans>
          </CardTitle>
        </div>
        <Button variant="link" size="sm" className="text-blue-600 hover:text-blue-800">
          <Trans i18nKey="common:actions.viewAll">XEM TẤT CẢ</Trans>
          <ChevronRight className="ml-1 h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {displayCards.map((card, index) => {
            // Card styles based on level
            const cardStyles = {
              'Gold': {
                gradient: 'from-amber-500 to-yellow-700',
                iconColor: 'text-yellow-300',
                borderColor: 'border-yellow-400',
                bgColor: 'bg-gradient-to-br from-yellow-50 to-amber-100'
              },
              'Silver': {
                gradient: 'from-slate-400 to-gray-600',
                iconColor: 'text-gray-300',
                borderColor: 'border-gray-300',
                bgColor: 'bg-gradient-to-br from-gray-50 to-slate-200'
              },
              'Bronze': {
                gradient: 'from-amber-700 to-orange-900',
                iconColor: 'text-amber-600',
                borderColor: 'border-amber-600',
                bgColor: 'bg-gradient-to-br from-orange-50 to-amber-200'
              }
            };

            const style = cardStyles[card.level] || {
              gradient: 'from-blue-600 to-indigo-800',
              iconColor: 'text-blue-300',
              borderColor: 'border-blue-400',
              bgColor: 'bg-gradient-to-br from-blue-50 to-indigo-100'
            };

            return (
              <div
                key={`${card.name}-${index}`}
                className={`group relative overflow-hidden rounded-xl ${style.bgColor} border ${style.borderColor} p-0 shadow-md transition-all duration-300 hover:shadow-xl hover:scale-[1.02]`}
              >
                {/* Card header with level badge */}
                <div className={`absolute right-0 top-0 rounded-bl-lg bg-gradient-to-r ${style.gradient} px-3 py-1 text-xs font-bold text-white shadow-md`}>
                  {card.level}
                </div>

                {/* Card content */}
                <div className="p-5">
                  <div className="mb-4 flex items-center">
                    <div className={`mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r ${style.gradient} p-2 shadow-md`}>
                      <Trophy className={`h-6 w-6 ${style.iconColor}`} />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-800">{card.name}</h3>
                      <p className="text-sm text-gray-500">
                        <Trans i18nKey="loyalty:memberSince">Thành viên từ</Trans> {format(new Date(card.date), 'dd/MM/yyyy')}
                      </p>
                    </div>
                  </div>

                  {/* Card details */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600">
                        <Star className="mr-2 h-4 w-4 text-amber-500" />
                        <span className="text-sm font-medium"><Trans i18nKey="loyalty:points">Điểm</Trans>:</span>
                      </div>
                      <span className="text-sm font-bold text-gray-800">{card.points.toLocaleString()}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600">
                        <Gift className="mr-2 h-4 w-4 text-pink-500" />
                        <span className="text-sm font-medium"><Trans i18nKey="loyalty:slots">Ưu đãi</Trans>:</span>
                      </div>
                      <span className="text-sm font-bold text-gray-800">{card.slots}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600">
                        <Calendar className="mr-2 h-4 w-4 text-blue-500" />
                        <span className="text-sm font-medium"><Trans i18nKey="loyalty:expiry">Hết hạn</Trans>:</span>
                      </div>
                      <span className="text-sm font-bold text-gray-800">{card.expiry}</span>
                    </div>
                  </div>

                  {/* Card footer */}
                  <div className="mt-4 pt-3 text-center">
                    <Button
                      variant="outline"
                      size="sm"
                      className={`w-full border-2 ${style.borderColor} font-medium ${card.level === 'Gold' ? 'hover:bg-amber-500' : card.level === 'Silver' ? 'hover:bg-gray-500' : card.level === 'Bronze' ? 'hover:bg-amber-700' : 'hover:bg-blue-500'} hover:text-white`}
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      <Trans i18nKey="loyalty:useCard">Sử dụng thẻ</Trans>
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
