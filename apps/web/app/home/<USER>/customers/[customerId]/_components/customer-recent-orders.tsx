'use client';

import Link from 'next/link';
import { useParams } from 'next/navigation';
import { ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { FormattedPrice } from '../../../../../../components/currency/price-display';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { Trans } from '@kit/ui/trans';
import { formatDate } from '@kit/ui/utils';

import { CustomerDetail } from '../_lib/server/customer-detail.loader';

interface CustomerRecentOrdersProps {
  customer: CustomerDetail;
}

export function CustomerRecentOrders({ customer }: CustomerRecentOrdersProps) {
  const { t } = useTranslation();
  const { account } = useParams<{ account: string }>();
  const orders = customer.recent_orders || [];

  // Always use real data from customer
  const displayOrders = orders;

  if (displayOrders.length === 0) {
    return null;
  }

  // Function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'ongoing':
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'closed':
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'on hold':
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'canceled':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'delivered':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Function to translate status
  const translateStatus = (status: string) => {
    const key = `orders:status.${status.toLowerCase().replace(' ', '_')}`;
    return t(key, { defaultValue: status });
  };

  return (
    <Card className="mb-6 h-full overflow-hidden border border-gray-200 shadow-sm">
      <CardHeader className="border-b border-gray-100 bg-gray-50 pb-2">
        <CardTitle className="text-sm font-bold tracking-wider text-gray-700 uppercase">
          <Trans i18nKey="customers:detail.recentOrders">
            ĐƠN HÀNG GẦN ĐÂY
          </Trans>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="py-3">
                  <Trans i18nKey="orders:table:orderCode">Mã đơn hàng</Trans>
                </TableHead>
                <TableHead className="py-3">
                  <Trans i18nKey="orders:table:total">Tổng tiền</Trans>
                </TableHead>
                <TableHead className="py-3">
                  <Trans i18nKey="orders:table:status">Trạng thái</Trans>
                </TableHead>
                <TableHead className="py-3">
                  <Trans i18nKey="orders:table:createdAt">Ngày tạo</Trans>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayOrders.map((order) => {
                const orderDate = new Date(order.date);
                const statusColor = getStatusColor(order.status);

                return (
                  <TableRow key={order.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">
                      <Link
                        href={`/home/<USER>/orders/${order.id}`}
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {order.order_code}
                      </Link>
                    </TableCell>
                    <TableCell className="font-medium text-gray-900">
                      <FormattedPrice amount={order.amount} />
                    </TableCell>
                    <TableCell>
                      <span
                        className={`inline-block rounded-full px-2 py-1 text-xs font-medium ${statusColor}`}
                      >
                        {translateStatus(order.status)}
                      </span>
                    </TableCell>
                    <TableCell className="text-gray-600">
                      {order.date ? formatDate(order.date, 'dd/MM/yyyy HH:mm') : ''}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        <div className="border-t border-gray-100 p-4 text-center">
          <Button
            variant="link"
            className="text-blue-600 hover:text-blue-800"
            asChild
          >
            <Link href={`/home/<USER>/orders?customer_id=${customer.id}`}>
              <Trans i18nKey="customers:detail.seeAllDeals">
                XEM TẤT CẢ GIAO DỊCH
              </Trans>
              <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
