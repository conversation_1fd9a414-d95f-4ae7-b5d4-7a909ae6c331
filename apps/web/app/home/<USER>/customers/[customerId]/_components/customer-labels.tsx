import { Trans } from '@kit/ui/trans';
import { Badge } from '@kit/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { CustomerDetail } from '../_lib/server/customer-detail.loader';

interface CustomerLabelsProps {
  customer: CustomerDetail;
}

export function CustomerLabels({ customer }: CustomerLabelsProps) {
  const labels = customer.labels || [];

  // Mock data for labels if none exist
  const mockLabels = ['Web Design', 'Code Review', 'Figma', 'Product Development', 'Workflow', 'AI'];

  // Use real data if available, otherwise use mock data
  const displayLabels = labels.length > 0 ? labels : mockLabels;

  if (displayLabels.length === 0) {
    return null;
  }

  // Generate a color for each label based on its name
  const getLabelColor = (label: string) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-yellow-100 text-yellow-800',
      'bg-red-100 text-red-800',
      'bg-purple-100 text-purple-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800',
      'bg-gray-100 text-gray-800'
    ];

    // Use a simple hash function to determine the color
    let hash = 0;
    for (let i = 0; i < label.length; i++) {
      hash = label.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <Card className="mb-6 overflow-hidden border border-gray-200 shadow-sm">
      <CardHeader className="border-b border-gray-100 bg-gray-50 pb-2">
        <CardTitle className="text-sm font-bold uppercase tracking-wider text-gray-700">
          <Trans i18nKey="customers:detail.labels">NHÃN</Trans>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="flex flex-wrap gap-2">
          {displayLabels.map((label) => (
            <Badge
              key={label}
              variant="outline"
              className={`rounded-md px-2 py-1 font-normal ${getLabelColor(label)}`}
            >
              {label}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
