'use client';

import { useEffect, useState } from 'react';

import dynamic from 'next/dynamic';

import { useTranslation } from 'react-i18next';
import type { CallBackProps, Step } from 'react-joyride';
import { STATUS } from 'react-joyride';

// Dynamically import Joyride with no SSR to avoid hydration issues
const Joyride = dynamic(() => import('react-joyride'), { ssr: false });

interface PersonalTourProps {
  isFirstLogin?: boolean;
  onComplete?: () => void;
  onSkip?: () => void;
}

export function PersonalTour({
  isFirstLogin = false,
  onComplete,
  onSkip,
}: PersonalTourProps) {
  const [run, setRun] = useState(false);

  // Set run state on client side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setRun(isFirstLogin);
    }
  }, [isFirstLogin]);
  const [steps, setSteps] = useState<Step[]>([]);
  const { t } = useTranslation();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Define tour steps
    const tourSteps: Step[] = [
      {
        target: 'body',
        content: t(
          'tour:welcome.title',
          "Welcome to MinApp! Let's get you started with a quick tour.",
        ),
        placement: 'center',
        disableBeacon: true,
      },
      {
        target: '[data-tour="create-team"]',
        content: t(
          'tour:createTeam.title',
          'Create your first business to start managing your products and customers.',
        ),
        placement: 'bottom',
      },
      {
        target: '[data-tour="loading"]',
        content: t('tour:loading.title', "We're setting up your business..."),
        placement: 'center',
        disableBeacon: true,
      },
      {
        target: '[data-tour="team-welcome"]',
        content: t(
          'tour:teamWelcome.title',
          'Welcome to your new business dashboard! Here you can manage everything.',
        ),
        placement: 'center',
        disableBeacon: true,
      },
      {
        target: '[data-tour="product-peek"]',
        content: t(
          'tour:productPeek.title',
          "Let's add your first product to get started.",
        ),
        placement: 'bottom',
      },
    ];

    setSteps(tourSteps);
  }, [t]);

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, index } = data;

    // Handle tour completion
    if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
      setRun(false);

      if (status === STATUS.FINISHED && onComplete) {
        onComplete();
      }

      if (status === STATUS.SKIPPED && onSkip) {
        onSkip();
      }
    }

    // Handle specific step actions
    if (data.action === 'next' && index === 1) {
      // After clicking next on the create team step, show loading step
      document
        .querySelector('[data-tour="create-team"]')
        ?.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    }
  };

  return (
    <Joyride
      callback={handleJoyrideCallback}
      continuous
      hideCloseButton
      run={run}
      scrollToFirstStep
      showProgress
      showSkipButton
      steps={steps}
      disableOverlayClose
      disableCloseOnEsc
      floaterProps={{
        styles: {
          floater: {},
        }
      }}
      tooltipProps={{
        'data-test': 'personal-tour-joyride'
      }}
      styles={{
        options: {
          zIndex: 10000,
          primaryColor: '#2563eb',
        },
        tooltipContainer: {
          textAlign: 'left',
        },
        buttonNext: {
          backgroundColor: '#2563eb',
        },
        buttonBack: {
          marginRight: 10,
        },
      }}
      locale={{
        back: t('tour:back', 'Back'),
        close: t('tour:close', 'Close'),
        last: t('tour:finish', 'Finish'),
        next: t('tour:next', 'Next'),
        skip: t('tour:skip', 'Skip'),
      }}
    />
  );
}

export function useTour() {
  const [showTour, setShowTour] = useState(false);

  const startTour = () => setShowTour(true);
  const endTour = () => setShowTour(false);

  return {
    showTour,
    startTour,
    endTour,
  };
}
