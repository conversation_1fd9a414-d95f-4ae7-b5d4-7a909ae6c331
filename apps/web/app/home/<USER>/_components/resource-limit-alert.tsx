'use client';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Trans } from '@kit/ui/trans';
import { AlertCircle, AlertTriangle, Info } from 'lucide-react';

interface ResourceLimitAlertProps {
  resourceType: string;
  reason: string;
  currentCount?: number;
  maxAllowed?: number;
}

/**
 * Component hiển thị thông báo khi người dùng đạt giới hạn tài nguyên
 */
export function ResourceLimitAlert({
  resourceType,
  reason,
  currentCount,
  maxAllowed
}: ResourceLimitAlertProps) {
  // Format tên tài nguyên
  const formatResourceName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  // Xác định variant và icon dựa trên lý do
  let variant: 'default' | 'destructive' | 'warning' = 'default';
  let Icon = Info;

  switch (reason) {
    case 'limit_reached':
      variant = 'destructive';
      Icon = AlertCircle;
      break;
    case 'no_active_subscription':
    case 'no_permission':
      variant = 'warning';
      Icon = AlertTriangle;
      break;
    default:
      variant = 'default';
      Icon = Info;
  }

  // Xác định tiêu đề và mô tả dựa trên lý do
  let title = '';
  let description = '';

  switch (reason) {
    case 'limit_reached':
      title = `${formatResourceName(resourceType)} Limit Reached`;
      description = `You have reached your ${resourceType} limit (${currentCount}/${maxAllowed}). Upgrade your plan to create more ${resourceType}.`;
      break;
    case 'no_active_subscription':
      title = 'Subscription Required';
      description = `You need an active subscription to create ${resourceType}.`;
      break;
    case 'no_permission':
      title = 'Permission Required';
      description = `You don't have permission to create ${resourceType}.`;
      break;
    case 'unknown_error':
      title = 'Error Checking Limits';
      description = `There was an error checking your ${resourceType} limits. Please try again later.`;
      break;
    default:
      title = 'Access Denied';
      description = '';
  }

  return (
    <Alert
      variant={variant}
      data-testid={`${reason === 'limit_reached' ? 'resource-limit-alert' :
                     reason === 'no_active_subscription' ? 'subscription-required-alert' :
                     reason === 'no_permission' ? 'permission-required-alert' :
                     'resource-access-alert'}`}
    >
      <Icon className="h-4 w-4" />
      <AlertTitle>
        <Trans i18nKey={`resources:${reason}.title`} defaults={title} />
      </AlertTitle>
      <AlertDescription>
        <Trans
          i18nKey={`resources:${reason}.description`}
          values={{
            resourceType,
            current: currentCount,
            max: maxAllowed
          }}
          defaults={description}
        />
      </AlertDescription>
    </Alert>
  );
}
