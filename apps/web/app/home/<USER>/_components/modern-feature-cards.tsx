'use client';

import { motion } from 'framer-motion';
import { Trans } from '@kit/ui/trans';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';

export function ModernFeatureCards() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="grid gap-6 md:grid-cols-3"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div key="feature-manage" variants={item}>
        <Card className="group overflow-hidden border-none shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg dark:bg-gray-800/50 dark:hover:bg-gray-800/80">
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="mr-4 rounded-full bg-blue-100 p-2 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 3v18h18"/>
                <path d="m19 9-5 5-4-4-3 3"/>
              </svg>
            </div>
            <CardTitle className="text-lg font-medium transition-colors group-hover:text-blue-600 dark:group-hover:text-blue-400">
              <Trans i18nKey="home:features.manage">Manage Your Business</Trans>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              <Trans i18nKey="home:features.manageDescription">
                Create products, manage orders, and track your business performance
              </Trans>
            </p>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div key="feature-collaborate" variants={item}>
        <Card className="group overflow-hidden border-none shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg dark:bg-gray-800/50 dark:hover:bg-gray-800/80">
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="mr-4 rounded-full bg-indigo-100 p-2 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </div>
            <CardTitle className="text-lg font-medium transition-colors group-hover:text-indigo-600 dark:group-hover:text-indigo-400">
              <Trans i18nKey="home:features.collaborate">Collaborate with Team</Trans>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              <Trans i18nKey="home:features.collaborateDescription">
                Invite team members and assign roles to work together efficiently
              </Trans>
            </p>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div key="feature-grow" variants={item}>
        <Card className="group overflow-hidden border-none shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg dark:bg-gray-800/50 dark:hover:bg-gray-800/80">
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="mr-4 rounded-full bg-purple-100 p-2 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 20V10"/>
                <path d="m18 20-6-6-6 6"/>
                <path d="M18 4H6"/>
                <path d="M18 8H6"/>
              </svg>
            </div>
            <CardTitle className="text-lg font-medium transition-colors group-hover:text-purple-600 dark:group-hover:text-purple-400">
              <Trans i18nKey="home:features.grow">Grow Your Business</Trans>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              <Trans i18nKey="home:features.growDescription">
                Use analytics and insights to make data-driven decisions
              </Trans>
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
