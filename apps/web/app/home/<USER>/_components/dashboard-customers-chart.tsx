'use client';

import { useState } from 'react';
import { BarChart, CartesianGrid, XAxis, YAxis, Bar, ResponsiveContainer, Tooltip, Cell, Legend } from 'recharts';
import { Users } from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@kit/ui/chart';
import { Trans } from '@kit/ui/trans';

interface CustomersChartProps {
  data: Array<{
    name: string;
    customers: number;
  }>;
  total: number;
}

export function CustomersChart({ data, total }: CustomersChartProps) {
  // Thêm màu sắc cho từng mini app
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe', '#00C49F'];

  // Sắp xếp dữ liệu theo số lượng khách hàng giảm dần
  const sortedData = [...data].sort((a, b) => b.customers - a.customers);

  const chartConfig = {
    customers: {
      label: <Trans i18nKey="dashboard:customers">Khách hàng</Trans>,
    },
    name: {
      label: <Trans i18nKey="dashboard:miniAppName">Tên Mini App</Trans>,
      color: 'var(--chart-1)',
    },
  } satisfies ChartConfig;

  return (
    <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <Trans i18nKey="dashboard:customersTitle">Khách hàng theo Mini App</Trans>
            </CardTitle>
            <CardDescription className="mt-1">
              <Trans i18nKey="dashboard:customersDescription">
                Tổng số khách hàng: {total.toLocaleString()}
              </Trans>
            </CardDescription>
          </div>
          <div className="text-3xl font-bold tracking-tight">{total.toLocaleString()}</div>
        </div>
      </CardHeader>

      <CardContent className="px-2 sm:p-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-72 w-full"
        >
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={sortedData}
              layout="vertical"
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid horizontal={true} vertical={false} strokeDasharray="3 3" />
              <XAxis
                type="number"
                tickLine={true}
                axisLine={true}
                tickFormatter={(value) => value.toLocaleString()}
              />
              <YAxis
                dataKey="name"
                type="category"
                tickLine={false}
                axisLine={false}
                width={120}
                tickFormatter={(value) => {
                  // Giới hạn độ dài tên mini app
                  return value.length > 15 ? value.substring(0, 15) + '...' : value;
                }}
                style={{ fontSize: '12px' }}
              />
              <Tooltip
                content={
                  <ChartTooltipContent
                    className="w-[180px] shadow-lg border-none bg-white/90 backdrop-blur-sm dark:bg-slate-800/90"
                    nameKey="customers"
                  />
                }
                cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
              />
              <Bar
                dataKey="customers"
                radius={[0, 4, 4, 0]}
                animationDuration={1500}
                barSize={24}
              >
                {sortedData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Thêm chú thích màu sắc */}
        <div className="mt-4 flex flex-wrap gap-4 justify-center">
          {sortedData.map((entry, index) => (
            <div key={`legend-${index}`} className="flex items-center gap-2">
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: COLORS[index % COLORS.length] }}
              />
              <span className="text-xs text-muted-foreground">{entry.name}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
