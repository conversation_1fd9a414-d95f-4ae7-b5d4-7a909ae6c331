'use client';

import { useState } from 'react';

import { Trans } from '@kit/ui/trans';

import { HomeAddAccountButton } from './home-add-account-button';
import { ModernDashboardOverview } from './modern-dashboard-overview';
import { ModernQuickActions } from './modern-quick-actions';
import { ModernRecentActivity } from './modern-recent-activity';
import { ModernTeamCards } from './modern-team-cards';

interface OwnerWithTeamsProps {
  ownedTeams: Array<{
    id: string | null;
    name: string | null;
    picture_url: string | null;
    role: string | null;
    slug: string | null;
    value: string | null; // Added for navigation
    is_personal_account?: boolean;
    subscription_status?: string | null;
    isOwner?: boolean;
    permissions?: string[];
    canCreateTeam?: boolean;
  }>;
  memberTeams: Array<{
    account: {
      id: string | null;
      name: string | null;
      picture_url: string | null;
      role: string | null;
      slug: string | null;
      value: string | null; // Added for navigation
      is_personal_account?: boolean;
      subscription_status?: string | null;
      permissions?: string[];
      canCreateTeam?: boolean;
    };
    role: string;
  }>;
}

export function OwnerWithTeams({
  ownedTeams,
  memberTeams,
}: OwnerWithTeamsProps) {
  const [isAddingAccount, setIsAddingAccount] = useState(false);

  // Use the original data structure without modifications
  const allTeams = [
    ...ownedTeams.map((team) => ({
      ...team,
      isOwner: true,
      role: 'owner',
    })),
    ...memberTeams.map(({ account, role }) => ({
      ...account,
      isOwner: false,
      role,
      permissions: account.permissions,
    })),
  ];
  console.log(allTeams);
  // Sử dụng thuộc tính canCreateTeam đã được kiểm tra trên server
  const canCreateTeam = allTeams.some((team) => team.canCreateTeam);

  console.log('All Teams in OwnerWithTeams:', allTeams);

  return (
    <div className="space-y-8" data-testid="owner-with-teams">
      {/* Dashboard Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold tracking-tight">
          <Trans i18nKey="home:dashboard.title">Your Dashboard</Trans>
        </h2>
        <p className="text-muted-foreground">
          <Trans i18nKey="home:dashboard.subtitle">
            Manage your businesses and personal settings
          </Trans>
        </p>
      </div>

      {/* Dashboard Overview Stats */}
      <ModernDashboardOverview
        ownedTeamsCount={ownedTeams.length}
        memberTeamsCount={memberTeams.length}
        totalTeams={allTeams.length}
      />

      {/* Teams Section */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">
          <Trans i18nKey="home:teams.title">Your Businesses</Trans>
        </h3>
        <HomeAddAccountButton
          isOpen={isAddingAccount}
          setIsOpen={setIsAddingAccount}
          canCreateTeam={canCreateTeam}
        />
      </div>

      {/* Modern Team Cards */}
      <ModernTeamCards teams={allTeams} />

      {/* Two Column Layout for Quick Actions and Recent Activity */}
      <div className="grid gap-6 md:grid-cols-12">
        <div className="md:col-span-4">
          <ModernQuickActions canCreateTeam={canCreateTeam} />
        </div>

        <div className="md:col-span-8">
          <ModernRecentActivity />
        </div>
      </div>
    </div>
  );
}
