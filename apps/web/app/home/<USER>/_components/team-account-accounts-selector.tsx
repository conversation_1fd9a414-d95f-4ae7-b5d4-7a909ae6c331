'use client';

import { useContext, useMemo } from 'react';

import { useRouter } from 'next/navigation';

import { AccountSelector } from '@kit/accounts/account-selector';
import { SidebarContext } from '@kit/ui/shadcn-sidebar';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

import featureFlagsConfig from '~/config/feature-flags.config';
import pathsConfig from '~/config/paths.config';

// Features will be set dynamically based on permissions

export function TeamAccountAccountsSelector(params: {
  selectedAccount: string;
  userId: string;

  accounts: Array<{
    label: string | null;
    value: string | null;
    image: string | null;
  }>;
}) {
  const router = useRouter();
  const ctx = useContext(SidebarContext);
  const { account } = useTeamAccountWorkspace();

  // Check if the user has the teams.manage permission
  const canCreateTeam = useMemo(() => {
    if (!account || !account.permissions) return false;
    return account.permissions.includes('teams.manage');
  }, [account]);

  // Set features based on permissions
  const features = {
    enableTeamCreation: featureFlagsConfig.enableTeamCreation && canCreateTeam,
  };

  return (
    <AccountSelector
      selectedAccount={params.selectedAccount}
      accounts={params.accounts}
      userId={params.userId}
      collapsed={!ctx?.open}
      features={features}
      onAccountChange={(value) => {
        const path = value
          ? pathsConfig.app.accountHome.replace('[account]', value)
          : pathsConfig.app.home;

        router.replace(path);
      }}
    />
  );
}
