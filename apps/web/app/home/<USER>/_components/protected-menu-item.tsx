'use client';

import { useParams } from 'next/navigation';
import { useMenuAccess } from '../_lib/hooks/use-menu-access';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks';

interface ProtectedMenuItemProps {
  permission: string; // Tên quyền đầy đủ cần kiểm tra (orders.manage, billing.manage, v.v.)
  children: React.ReactNode;
}

/**
 * Component wrapper cho menu item để kiểm tra quyền truy cập
 * Chỉ hiển thị menu item nếu người dùng có quyền truy cập
 */
export function ProtectedMenuItem({ permission, children }: ProtectedMenuItemProps) {
  const { account } = useParams();
  const { workspace, user } = useTeamAccountWorkspace();
  const accountId = workspace?.account?.id || '';
  const userId = user?.id || '';

  // Kiểm tra quyền truy cập
  const { canAccess, isLoading } = useMenuAccess(accountId, permission, userId);

  // Nếu đang tải hoặc không có quyền truy cập, không hiển thị menu item
  if (isLoading || !canAccess) {
    return null;
  }

  // Nếu có quyền truy cập, hiển thị menu item
  return <>{children}</>;
}
