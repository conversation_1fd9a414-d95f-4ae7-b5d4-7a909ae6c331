import { z } from 'zod';

import { NavigationConfigSchema } from '@kit/ui/navigation-schema';

import { AccessControlledSidebarNavigation } from './access-controlled-sidebar-navigation';

export function TeamAccountLayoutSidebarNavigation({
  config,
  userId,
  accountId,
}: React.PropsWithChildren<{
  config: z.infer<typeof NavigationConfigSchema>;
  userId: string;
  accountId: string;
}>) {
  return (
    <AccessControlledSidebarNavigation
      config={config}
      userId={userId}
      accountId={accountId}
    />
  );
}
