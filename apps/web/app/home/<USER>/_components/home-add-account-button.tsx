'use client';

import { useState } from 'react';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { ModernCreateTeamForm } from './modern-create-team-form';

interface HomeAddAccountButtonProps {
  className?: string;
  onSuccess?: (teamSlug: string) => void;
  isOpen?: boolean;
  setIsOpen?: (isOpen: boolean) => void;
  canCreateTeam?: boolean;
}

export function HomeAddAccountButton({
  className,
  onSuccess,
  isOpen: externalIsOpen,
  setIsOpen: externalSetIsOpen,
  canCreateTeam = true
}: HomeAddAccountButtonProps) {
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  // Use external state if provided, otherwise use internal state
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const setIsOpen = externalSetIsOpen || setInternalIsOpen;

  // If canCreateTeam is false, don't render anything
  if (!canCreateTeam) {
    return null;
  }

  return (
    <>
      <Button
        className={className}
        onClick={() => setIsOpen(true)}
        data-testid="create-business-button"
      >
        <Trans i18nKey={'account:createTeamButtonLabel'} />
      </Button>

      <ModernCreateTeamForm
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        onSuccess={onSuccess}
      />
    </>
  );
}
