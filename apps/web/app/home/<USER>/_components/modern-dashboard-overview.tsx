'use client';

import { motion } from 'framer-motion';
import { Trans } from '@kit/ui/trans';
import { Card, CardContent } from '@kit/ui/card';
import { Building, Users, Activity, Clock } from 'lucide-react';

interface ModernDashboardOverviewProps {
  ownedTeamsCount: number;
  memberTeamsCount: number;
  totalTeams: number;
}

export function ModernDashboardOverview({
  ownedTeamsCount,
  memberTeamsCount,
  totalTeams
}: ModernDashboardOverviewProps) {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div key="stat-total" variants={item}>
        <Card className="border-none bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md dark:from-blue-950 dark:to-indigo-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                  <Trans i18nKey="home:stats.totalBusinesses">Total Businesses</Trans>
                </p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">{totalTeams}</h3>
              </div>
              <div className="rounded-full bg-blue-100 p-3 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
                <Building className="h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div key="stat-owned" variants={item}>
        <Card className="border-none bg-gradient-to-br from-indigo-50 to-purple-50 shadow-md dark:from-indigo-950 dark:to-purple-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-indigo-600 dark:text-indigo-400">
                  <Trans i18nKey="home:stats.ownedBusinesses">Owned Businesses</Trans>
                </p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">{ownedTeamsCount}</h3>
              </div>
              <div className="rounded-full bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400">
                <Users className="h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div key="stat-member" variants={item}>
        <Card className="border-none bg-gradient-to-br from-purple-50 to-pink-50 shadow-md dark:from-purple-950 dark:to-pink-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                  <Trans i18nKey="home:stats.memberBusinesses">Member Businesses</Trans>
                </p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">{memberTeamsCount}</h3>
              </div>
              <div className="rounded-full bg-purple-100 p-3 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
                <Activity className="h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div key="stat-active" variants={item}>
        <Card className="border-none bg-gradient-to-br from-pink-50 to-red-50 shadow-md dark:from-pink-950 dark:to-red-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-pink-600 dark:text-pink-400">
                  <Trans i18nKey="home:stats.lastActive">Last Active</Trans>
                </p>
                <h3 className="mt-1 text-lg font-bold text-gray-900 dark:text-white">
                  <Trans i18nKey="home:stats.today">Today</Trans>
                </h3>
              </div>
              <div className="rounded-full bg-pink-100 p-3 text-pink-600 dark:bg-pink-900/30 dark:text-pink-400">
                <Clock className="h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
