'use client';

import { useState } from 'react';
import { BarChart, CartesianGrid, XAxis, YAxis, Bar, ResponsiveContainer, Tooltip, Legend } from 'recharts';
import { Users, UserPlus, UserCheck } from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@kit/ui/chart';
import { Trans } from '@kit/ui/trans';

interface VisitorsChartProps {
  data: Array<{
    date: string;
    desktop: number;
    mobile: number;
    tablet: number;
  }>;
  total: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
}

export function VisitorsChart({ data, total }: VisitorsChartProps) {
  // Transform the data to focus on customer types instead of device types
  const transformedData = data.map(item => {
    const date = new Date(item.date);
    const formattedDate = date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
    });

    return {
      date: item.date,
      formattedDate,
      newCustomers: Math.round(item.mobile * 0.3), // Approximately 30% of mobile users are new customers
      returningCustomers: Math.round(item.mobile * 0.7), // Approximately 70% of mobile users are returning customers
    };
  });

  const chartConfig = {
    visitors: {
      label: <Trans i18nKey="dashboard:visitors">Lượt truy cập</Trans>,
    },
    newCustomers: {
      label: <Trans i18nKey="dashboard:newCustomers">Khách hàng mới</Trans>,
      color: '#8884d8',
    },
    returningCustomers: {
      label: <Trans i18nKey="dashboard:returningCustomers">Khách hàng quay lại</Trans>,
      color: '#82ca9d',
    },
  } satisfies ChartConfig;

  // Calculate totals for the new metrics
  const customerTotals = {
    newCustomers: Math.round(total.mobile * 0.3),
    returningCustomers: Math.round(total.mobile * 0.7),
  };

  return (
    <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <Trans i18nKey="dashboard:customerActivityTitle">Hoạt động khách hàng</Trans>
            </CardTitle>
            <CardDescription className="mt-1">
              <Trans i18nKey="dashboard:customerActivityDescription">
                Phân tích khách hàng trong 30 ngày qua
              </Trans>
            </CardDescription>
          </div>
          <div className="flex gap-4">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 rounded-full bg-[#8884d8]" />
              <span className="text-xs text-muted-foreground">
                <Trans i18nKey="dashboard:newCustomers">Khách hàng mới</Trans>
              </span>
            </div>
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 rounded-full bg-[#82ca9d]" />
              <span className="text-xs text-muted-foreground">
                <Trans i18nKey="dashboard:returningCustomers">Khách hàng quay lại</Trans>
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-2 sm:p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-4 flex items-center justify-between border border-purple-100 dark:border-purple-900/30">
            <div className="flex items-center gap-3">
              <div className="rounded-full p-2 bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
                <UserPlus className="h-5 w-5" />
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">
                  <Trans i18nKey="dashboard:newCustomers">Khách hàng mới</Trans>
                </div>
                <div className="text-2xl font-bold">
                  {customerTotals.newCustomers.toLocaleString()}
                </div>
              </div>
            </div>
            <div className="text-xs font-medium px-2 py-1 rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
              30%
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 flex items-center justify-between border border-green-100 dark:border-green-900/30">
            <div className="flex items-center gap-3">
              <div className="rounded-full p-2 bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
                <UserCheck className="h-5 w-5" />
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">
                  <Trans i18nKey="dashboard:returningCustomers">Khách hàng quay lại</Trans>
                </div>
                <div className="text-2xl font-bold">
                  {customerTotals.returningCustomers.toLocaleString()}
                </div>
              </div>
            </div>
            <div className="text-xs font-medium px-2 py-1 rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
              70%
            </div>
          </div>
        </div>

        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-72 w-full"
        >
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={transformedData}
              margin={{ top: 10, right: 30, left: 0, bottom: 10 }}
            >
              <CartesianGrid vertical={false} strokeDasharray="3 3" />
              <XAxis
                dataKey="formattedDate"
                tickLine={false}
                axisLine={true}
                tickMargin={8}
                minTickGap={32}
              />
              <YAxis
                tickLine={true}
                axisLine={true}
                tickFormatter={(value) => value.toLocaleString()}
              />
              <Tooltip
                content={
                  <ChartTooltipContent
                    className="w-[180px] shadow-lg border-none bg-white/90 backdrop-blur-sm dark:bg-slate-800/90"
                    nameKey="visitors"
                    labelFormatter={(value) => {
                      return new Date(value).toLocaleDateString('vi-VN', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                      });
                    }}
                  />
                }
                cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
              />
              <Bar
                name={chartConfig.newCustomers.label}
                dataKey="newCustomers"
                fill={chartConfig.newCustomers.color}
                radius={[4, 4, 0, 0]}
                animationDuration={1500}
                barSize={20}
                stackId="a"
              />
              <Bar
                name={chartConfig.returningCustomers.label}
                dataKey="returningCustomers"
                fill={chartConfig.returningCustomers.color}
                radius={[4, 4, 0, 0]}
                animationDuration={1500}
                barSize={20}
                stackId="a"
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
