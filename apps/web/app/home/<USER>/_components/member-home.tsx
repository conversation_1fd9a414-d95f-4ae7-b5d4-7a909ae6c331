'use client';

import { useState } from 'react';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import featureFlagsConfig from '~/config/feature-flags.config';

import { HomeAddAccountButton } from './home-add-account-button';
import { ModernDashboardOverview } from './modern-dashboard-overview';
import { ModernQuickActions } from './modern-quick-actions';
import { ModernTeamCards } from './modern-team-cards';

interface MemberHomeProps {
  memberTeams: Array<{
    account: {
      id: string | null;
      name: string | null;
      picture_url: string | null;
      role: string | null;
      slug: string | null;
      value: string | null; // Added for navigation
      is_personal_account?: boolean;
      subscription_status?: string | null;
      permissions?: string[];
      canCreateTeam?: boolean;
    };
    role: string;
  }>;
  pendingInvitations?: Array<any>; // Type would be defined based on your invitations structure
}

export function MemberHome({
  memberTeams,
  pendingInvitations = [],
}: MemberHomeProps) {
  const [isAddingAccount, setIsAddingAccount] = useState(false);

  // Transform the data structure to match what ModernTeamCards expects
  const teams = memberTeams.map(({ account, role }) => {
    // Handle both possible data structures
    if (account.account) {
      // Handle nested account structure
      return {
        id: account.account.id,
        name: account.account.name,
        picture_url: account.account.picture_url,
        role: role || account.role,
        slug: account.account.slug,
        value: account.account.value,
        isOwner: false,
        permissions: account.account.permissions,
      };
    } else {
      // Handle flat account structure
      return {
        ...account,
        isOwner: false,
        role: role || account.role,
      };
    }
  });

  // Sử dụng thuộc tính canCreateTeam đã được kiểm tra trên server
  const canCreateTeam = teams.some((team) => team.canCreateTeam);

  console.log('Teams in MemberHome:', teams);

  return (
    <div className="space-y-8" data-testid="member-home">
      {/* Dashboard Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold tracking-tight">
          <Trans i18nKey="home:dashboard.title">Your Dashboard</Trans>
        </h2>
        <p className="text-muted-foreground">
          <Trans i18nKey="home:dashboard.subtitle">
            Manage your businesses and personal settings
          </Trans>
        </p>
      </div>

      {/* Dashboard Overview Stats */}
      <ModernDashboardOverview
        ownedTeamsCount={0}
        memberTeamsCount={memberTeams.length}
        totalTeams={memberTeams.length}
      />

      {/* Teams Section */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">
          <Trans i18nKey="home:teams.title">Your Businesses</Trans>
        </h3>
        {featureFlagsConfig.enableTeamCreation && (
          <HomeAddAccountButton
            isOpen={isAddingAccount}
            setIsOpen={setIsAddingAccount}
            canCreateTeam={canCreateTeam}
          />
        )}
      </div>

      {/* Modern Team Cards */}
      {teams.length > 0 ? (
        <ModernTeamCards teams={teams} />
      ) : (
        <div className="rounded-xl bg-gray-50 p-8 text-center dark:bg-gray-800/50">
          <p className="text-muted-foreground mb-4">
            <Trans i18nKey="home:teams.noTeams">
              You are not a member of any business yet
            </Trans>
          </p>

          <p className="text-sm text-blue-600 dark:text-blue-400">
            {canCreateTeam ? (
              <Trans i18nKey="home:teams.createToJoin">
                Create a business or wait for an invitation to join one
              </Trans>
            ) : (
              <Trans i18nKey="home:teams.waitForInvitation">
                Wait for an invitation to join a business
              </Trans>
            )}
          </p>
        </div>
      )}

      {/* Two Column Layout for Quick Actions and Pending Invitations */}
      <div className="grid gap-6 md:grid-cols-12">
        <div className="md:col-span-4">
          <ModernQuickActions canCreateTeam={canCreateTeam} />
        </div>

        <div className="md:col-span-8">
          <div className="rounded-xl bg-white p-6 shadow-md dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-semibold">
              <Trans i18nKey="home:pendingInvitations.title">
                Pending Invitations
              </Trans>
            </h3>

            <div className="space-y-4">
              {pendingInvitations.length > 0 ? (
                pendingInvitations.map((invitation: any) => (
                  <div
                    key={invitation.id}
                    className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-700/50"
                  >
                    <div>
                      <p className="font-medium">{invitation.team_name}</p>
                      <p className="text-muted-foreground text-sm">
                        <Trans
                          i18nKey="home:pendingInvitations.invitedBy"
                          values={{ name: invitation.invited_by }}
                        >
                          Invited by {{ name: invitation.invited_by }}
                        </Trans>
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-200 bg-red-50 text-red-600 hover:bg-red-100 dark:border-red-900 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/40"
                      >
                        <Trans i18nKey="home:pendingInvitations.decline">
                          Decline
                        </Trans>
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white"
                      >
                        <Trans i18nKey="home:pendingInvitations.accept">
                          Accept
                        </Trans>
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="mb-3 rounded-full bg-gray-100 p-3 text-gray-400 dark:bg-gray-800 dark:text-gray-500">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="3" y="5" width="18" height="14" rx="2" />
                      <polyline points="3 7 12 13 21 7" />
                    </svg>
                  </div>
                  <p className="text-muted-foreground">
                    <Trans i18nKey="home:pendingInvitations.empty">
                      No pending invitations
                    </Trans>
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
