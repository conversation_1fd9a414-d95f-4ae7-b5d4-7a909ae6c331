import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';

import { createCorsResponse } from '~/lib/cors';

const VoucherQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1).optional(),
  limit: z.coerce.number().min(1).max(50).default(10).optional(),
  status: z.enum(['active', 'expired', 'disabled']).optional(),
});

/**
 * @swagger
 * /api/vouchers:
 *   get:
 *     summary: Get vouchers with pagination and filtering
 *     description: Retrieves a list of vouchers with optional filtering by status
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, expired, disabled]
 *         description: Filter vouchers by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: Number of items per page
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: List of vouchers
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       500:
 *         description: Internal server error
 */
export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      const {
        page = 1,
        limit = 10,
        status,
      } = VoucherQuerySchema.parse({
        page: searchParams.get('page') ?? '1',
        limit: searchParams.get('limit') ?? '10',
        status: searchParams.get('status'),
      });

      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return createCorsResponse(
          request,
          { success: false, error: 'No account_id found in token' },
          401,
        );
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;
      const now = new Date().toISOString();

      let query = supabase
        .from('vouchers')
        .select(
          `
          id,
          code,
          name,
          description,
          discount_type,
          discount_value,
          min_order_value,
          max_discount_value,
          max_uses,
          uses_count,
          start_date,
          end_date,
          status,
          created_at,
          updated_at
          `,
          { count: 'exact' },
        )
        .eq('account_id', account_id)
        .order('created_at', { ascending: false })
        .range(from, to);

      // Filter by status if provided
      if (status) {
        query = query.eq('status', status);
      }

      // If status is not provided, show active vouchers by default
      // Active means status='active' AND current time is between start_date and end_date
      if (!status) {
        query = query
          .eq('status', 'active')
          .lte('start_date', now)
          .gte('end_date', now);
      }

      const { data, error, count } = await query;

      if (error) {
        throw new Error(error.message || 'Database query failed');
      }

      // Process the data to format it nicely
      const vouchers = (data || []).map((voucher) => {
        // Check if voucher is active
        const currentTime = new Date();
        const startDate = new Date(voucher.start_date);
        const endDate = new Date(voucher.end_date);
        const isActive =
          voucher.status === 'active' &&
          currentTime >= startDate &&
          currentTime <= endDate;

        // Check if voucher has reached max uses
        const hasReachedMaxUses =
          voucher.max_uses !== null &&
          voucher.uses_count >= voucher.max_uses;

        // Calculate time remaining
        const timeRemaining = isActive ? Math.max(0, endDate.getTime() - currentTime.getTime()) : 0;
        const daysRemaining = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
        const hoursRemaining = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

        return {
          id: voucher.id,
          code: voucher.code,
          name: voucher.name,
          description: voucher.description,
          discount_type: voucher.discount_type,
          discount_value: voucher.discount_value,
          min_order_value: voucher.min_order_value,
          max_discount_value: voucher.max_discount_value,
          max_uses: voucher.max_uses,
          uses_count: voucher.uses_count,
          start_date: voucher.start_date,
          end_date: voucher.end_date,
          status: voucher.status,
          created_at: voucher.created_at,
          updated_at: voucher.updated_at,
          is_active: isActive,
          has_reached_max_uses: hasReachedMaxUses,
          time_remaining: {
            milliseconds: timeRemaining,
            days: daysRemaining,
            hours: hoursRemaining,
            formatted: `${daysRemaining}d ${hoursRemaining}h`,
          },
        };
      });

      return createCorsResponse(
        request,
        {
          success: true,
          data: vouchers,
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages: count ? Math.ceil(count / limit) : 0,
          },
        },
        200,
      );
    } catch (error: any) {
      if (error instanceof z.ZodError) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: true },
);
