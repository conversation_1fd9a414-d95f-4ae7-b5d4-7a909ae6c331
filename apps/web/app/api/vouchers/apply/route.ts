import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';

import { createCorsResponse } from '~/lib/cors';

const ApplyVoucherSchema = z.object({
  code: z.string().min(1),
  order_id: z.string().uuid(),
});

/**
 * @swagger
 * /api/vouchers/apply:
 *   post:
 *     summary: Apply a voucher to an order
 *     description: Applies a voucher code to an existing order
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - order_id
 *             properties:
 *               code:
 *                 type: string
 *                 description: The voucher code to apply
 *               order_id:
 *                 type: string
 *                 format: uuid
 *                 description: The ID of the order to apply the voucher to
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Voucher application result
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       500:
 *         description: Internal server error
 */
export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { code, order_id } = ApplyVoucherSchema.parse(body);

      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return createCorsResponse(
          request,
          { success: false, error: 'No account_id found in token' },
          401,
        );
      }

      // Call the RPC function to apply the voucher
      const { data, error } = await supabase.rpc('apply_voucher_to_order', {
        p_order_id: order_id,
        p_voucher_code: code.toUpperCase(),
      });

      if (error) {
        throw new Error(error.message || 'Failed to apply voucher');
      }

      if (!data.success) {
        return createCorsResponse(
          request,
          { 
            success: false, 
            error: data.message || 'Failed to apply voucher' 
          },
          200,
        );
      }

      return createCorsResponse(
        request,
        {
          success: true,
          data: {
            message: data.message,
            discount_amount: data.discount_amount,
            new_total: data.new_total,
          },
        },
        200,
      );
    } catch (error: any) {
      if (error instanceof z.ZodError) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: true },
);
