import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';

import { createCorsResponse } from '~/lib/cors';

// Schema cho GET
const OrdersGetQuerySchema = z.object({
  query: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val === '' || val === 'null' ? undefined : val)),
  status: z.preprocess(
    (val) => {
      if (typeof val !== 'string' || val === '' || val === 'null')
        return undefined;
      return val;
    },
    z.enum(['pending', 'processing', 'completed', 'cancelled']).optional(),
  ),
  startDate: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val === '' || val === 'null' ? undefined : val)),
  endDate: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val === '' || val === 'null' ? undefined : val)),
  page: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val === '' || val === 'null' || !val ? '1' : val))
    .pipe(z.coerce.number().min(1).default(1)),
  limit: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val === '' || val === 'null' || !val ? '10' : val))
    .pipe(z.coerce.number().min(1).max(50).default(10)),
});

// Schema cho item trong POST
const OrderItemSchema = z.object({
  product_id: z.string().uuid(),
  attribute_id: z.string().uuid().optional().nullable(),
  quantity: z.number().min(1),
  price: z.number().min(0),
  original_price: z.number().min(0).optional(),
  flash_sale_id: z.string().uuid().optional().nullable(),
  discount_percentage: z.number().min(0).max(100).optional(),
});

// Schema cho POST
const OrdersPostSchema = z
  .object({
    items: z.array(OrderItemSchema).min(1, 'At least one item is required'),
    branch_id: z.string().uuid().optional().nullable(),
    subtotal: z.number().min(0),
    discount: z.number().min(0).default(0),
    total_amount: z.number().min(0),
    payment_method: z.enum(['cash', 'card', 'transfer']),
    status: z
      .enum(['pending', 'processing', 'completed', 'cancelled'])
      .optional(),
    voucher_code: z.string().optional().nullable(),
    voucher_id: z.string().uuid().optional().nullable(),
    voucher_discount: z.number().min(0).optional(),
    metadata: z.record(z.any()).optional(),
    webhook_url: z.string().url().optional(),
  })
  .refine(
    (data) => {
      const itemsTotal = data.items.reduce(
        (sum, item) => sum + item.quantity * item.price,
        0,
      );
      return (
        data.subtotal === itemsTotal &&
        data.total_amount === itemsTotal - data.discount
      );
    },
    {
      message:
        'Subtotal must equal sum of items, and total_amount must equal subtotal minus discount',
      path: ['total_amount'],
    },
  );

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      const {
        query: searchQuery,
        status,
        startDate,
        endDate,
        page = 1,
        limit = 10,
      } = OrdersGetQuerySchema.parse({
        query: searchParams.get('query'),
        status: searchParams.get('status'),
        startDate: searchParams.get('startDate'),
        endDate: searchParams.get('endDate'),
        page: searchParams.get('page'),
        limit: searchParams.get('limit'),
      });

      // Get account_id from user metadata or from the token claims
      let account_id = user.user_metadata?.account_id;

      // If not found in user_metadata, try to get from app_metadata
      if (!account_id && user.app_metadata?.account_id) {
        account_id = user.app_metadata.account_id;
      }

      // If still not found, check if there's a team_id in the token
      if (!account_id && user.user_metadata?.team_id) {
        account_id = user.user_metadata.team_id;
      }

      // For test purposes, if we're in a test environment and still don't have an account_id,
      // try to get the first account the user has access to
      if (!account_id && process.env.NODE_ENV === 'test') {
        const { data: memberships } = await supabase
          .from('accounts_memberships')
          .select('account_id')
          .eq('user_id', user.sub)
          .limit(1);

        if (memberships && memberships.length > 0) {
          account_id = memberships[0].account_id;
        }
      }

      if (!account_id) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'No account_id found in token or user memberships',
          },
          401,
        );
      }

      const customer_id = user.sub;

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      let dbQuery = supabase
        .from('customer_orders')
        .select(
          `
          id,
          customer_id,
          branch_id,
          customer_order_items(
            product_id,
            quantity,
            price,
            original_price,
            flash_sale_id,
            discount_percentage,
            product_attributes!customer_order_items_attribute_id_fkey(name, value),
            products!customer_order_items_product_id_fkey(id, name)
          ),
          subtotal,
          discount_amount,
          total_amount,
          status,
          payment_method,
          voucher_id,
          vouchers(id, code, name, discount_type, discount_value),
          created_at
          `,
          { count: 'exact' },
        )
        .eq('account_id', account_id)
        .eq('customer_id', customer_id)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (searchQuery) {
        dbQuery = dbQuery.or(
          `id.ilike.%${searchQuery}%,customer_order_items.products.name.ilike.%${searchQuery}%`,
        );
      }
      if (status) {
        dbQuery = dbQuery.eq('status', status);
      }
      if (startDate) {
        dbQuery = dbQuery.gte('created_at', startDate);
      }
      if (endDate) {
        dbQuery = dbQuery.lte('created_at', endDate);
      }

      const { data, error, count } = await dbQuery;

      if (error) throw new Error(error.message || 'Database query failed');

      const orders = (data || []).map((order) => ({
        id: order.id,
        branch_id: order.branch_id,
        items: order.customer_order_items.map((item: any) => ({
          product_id: item.product_id,
          product_name: item.products?.name || '',
          quantity: item.quantity,
          price: item.price,
          original_price: item.original_price || item.price,
          discount_percentage: item.discount_percentage || 0,
          flash_sale_id: item.flash_sale_id,
          attributes: item.product_attributes
            ? [
                {
                  name: item.product_attributes.name,
                  value: item.product_attributes.value,
                },
              ]
            : [],
        })),
        subtotal: order.subtotal || order.total_amount,
        discount_amount: order.discount_amount || 0,
        total_amount: order.total_amount,
        status: order.status,
        payment_method: order.payment_method,
        voucher: order.voucher_id
          ? {
              id: order.voucher_id,
              code: order.vouchers?.code,
              name: order.vouchers?.name,
              discount_type: order.vouchers?.discount_type,
              discount_value: order.vouchers?.discount_value,
            }
          : null,
        created_at: order.created_at,
      }));

      return createCorsResponse(
        request,
        {
          success: true,
          data: orders,
          pagination: {
            page,
            limit,
            total: count || 0,
            total_pages: count ? Math.ceil(count / limit) : 0,
          },
        },
        200,
      );
    } catch (error: any) {
      const logger = await getLogger();

      if (error instanceof z.ZodError) {
        logger.warn(
          { error: error.errors },
          'Invalid request parameters in GET /api/orders',
        );
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      // Log the error for debugging
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          user_id: user?.sub,
          user_metadata: user?.user_metadata,
          app_metadata: user?.app_metadata,
        },
        'Error in GET /api/orders',
      );

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
          // For test environments, include more details to help with debugging
          debug:
            process.env.NODE_ENV === 'test'
              ? {
                  user_id: user?.sub,
                  has_user_metadata: !!user?.user_metadata,
                  has_app_metadata: !!user?.app_metadata,
                }
              : undefined,
        },
        500,
      );
    }
  },
  { auth: true },
);

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    const logger = await getLogger();

    try {
      const body = await request.json();
      const validatedData = OrdersPostSchema.parse(body);

      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return createCorsResponse(
          request,
          { success: false, error: 'No account_id found in token' },
          401,
        );
      }

      const customer_id = user.sub;

      const items = validatedData.items.map((item) => ({
        product_id: item.product_id,
        attribute_id: item.attribute_id || null,
        quantity: item.quantity,
        price: item.price,
        original_price: item.original_price || item.price,
        flash_sale_id: item.flash_sale_id || null,
        discount_percentage: item.discount_percentage || null,
      }));

      // Sử dụng hàm createOrderWithEvents để tạo đơn hàng và phát sự kiện
      const { data, error } = await import('~/app/home/<USER>/orders/_lib/server/order-events')
        .then(module => module.createOrderWithEvents({
          account_id,
          customer_id,
          branch_id: validatedData.branch_id || null,
          items,
          subtotal: validatedData.subtotal,
          discount: validatedData.discount,
          total_amount: validatedData.total_amount,
          payment_method: validatedData.payment_method,
          status: validatedData.status || 'pending',
          voucher_code: validatedData.voucher_code || null,
          voucher_id: validatedData.voucher_id || null,
          voucher_discount: validatedData.voucher_discount || null,
          metadata: validatedData.metadata || null,
          webhook_url: validatedData.webhook_url || null,
          team_account_id: account_id, // Sử dụng account_id làm team_account_id
        }));

      if (error || !data || data.status === 'error') {
        const message =
          data?.message || error?.message || 'Failed to create order';

        // Phân tích message để trả về mã lỗi cụ thể
        if (message.includes('Insufficient stock')) {
          return createCorsResponse(
            request,
            {
              success: false,
              error: 'INSUFFICIENT_STOCK',
              message: 'One or more products have insufficient stock',
              details: message,
            },
            400,
          );
        }

        if (message.includes('Product not active')) {
          return createCorsResponse(
            request,
            {
              success: false,
              error: 'PRODUCT_NOT_ACTIVE',
              message:
                'One or more products are not active at the specified branch',
              details: message,
            },
            400,
          );
        }

        return createCorsResponse(
          request,
          { success: false, error: message },
          500,
        );
      }

      logger.info(
        {
          account_id,
          customer_id,
          order_id: data.order_id,
          webhook_url: validatedData.webhook_url,
        },
        'Order created successfully',
      );

      return createCorsResponse(
        request,
        {
          success: true,
          message: 'Order created successfully',
          order_id: data.order_id,
        },
        201,
      );
    } catch (error: any) {
      logger.error(
        { error: error.message, stack: error.stack },
        'Error in POST /api/orders',
      );

      if (error instanceof z.ZodError) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          400,
        );
      }

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Failed to create order',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: true },
);
