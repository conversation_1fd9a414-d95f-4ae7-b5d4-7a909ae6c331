import { NextRequest, NextResponse } from 'next/server';

import { ZNS_EVENT_TYPES } from '@kit/zns';

export async function GET(request: NextRequest) {
  try {
    // Simulate the module structure used in CreateMappingDialog
    const eventTypesByModule = {
      orders: [
        ZNS_EVENT_TYPES.find(t => t.id === 'order_confirmation')!,
        ZNS_EVENT_TYPES.find(t => t.id === 'payment_confirmation')!,
        ZNS_EVENT_TYPES.find(t => t.id === 'shipping_notification')!,
      ],
      customer_service: [
        ZNS_EVENT_TYPES.find(t => t.id === 'customer_support')!,
        ZNS_EVENT_TYPES.find(t => t.id === 'appointment_reminder')!,
      ],
      marketing: [
        ZNS_EVENT_TYPES.find(t => t.id === 'product_promotion')!,
        ZNS_EVENT_TYPES.find(t => t.id === 'general_notification')!,
      ],
    };

    const modules = [
      {
        id: 'orders',
        name: '<PERSON>ơn hàng',
        description: 'Sự kiện liên quan đến đơn hàng và thanh toán',
        icon: '📦',
        events: eventTypesByModule.orders,
      },
      {
        id: 'customer_service',
        name: 'Chăm sóc khách hàng',
        description: 'Sự kiện hỗ trợ và chăm sóc khách hàng',
        icon: '🎧',
        events: eventTypesByModule.customer_service,
      },
      {
        id: 'marketing',
        name: 'Marketing',
        description: 'Sự kiện khuyến mãi và tiếp thị',
        icon: '🎉',
        events: eventTypesByModule.marketing,
      },
    ];

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Mapping Dialog - Event Types</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
          .module-card { border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; margin: 12px 0; background: #f9f9f9; }
          .module-header { display: flex; align-items: center; gap: 12px; margin-bottom: 12px; }
          .module-icon { font-size: 24px; }
          .module-name { font-size: 18px; font-weight: bold; margin: 0; }
          .module-description { color: #666; font-size: 14px; margin: 0; }
          .events-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 12px; }
          .event-card { background: white; border: 1px solid #ddd; border-radius: 6px; padding: 12px; }
          .event-header { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; }
          .event-icon { font-size: 20px; }
          .event-name { font-weight: bold; margin: 0; }
          .event-description { color: #666; font-size: 13px; margin: 0; }
          .comparison { background: #e8f5e8; border: 1px solid #4caf50; border-radius: 8px; padding: 16px; margin: 20px 0; }
          .comparison h3 { color: #2e7d32; margin-top: 0; }
          .highlight { background: #fff3cd; padding: 2px 6px; border-radius: 3px; font-weight: bold; }
          .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
          .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🔗 ZNS Mapping Dialog - Event Types</h1>
          <p>Kiểm tra tính đồng nhất giữa Template Creation và Mapping Creation</p>
        </div>

        <div class="section success">
          <h2>✅ Đồng nhất hoàn thành!</h2>
          <p>Event types trong <strong>Mapping Creation</strong> đã được cập nhật để sử dụng cùng <code>ZNS_EVENT_TYPES</code> với <strong>Template Creation</strong>.</p>
          
          <h3>🔄 Thay đổi chính:</h3>
          <ul>
            <li><strong>Trước:</strong> Module riêng biệt (orders, education, marketing) với events khác nhau</li>
            <li><strong>Sau:</strong> Sử dụng <span class="highlight">ZNS_EVENT_TYPES</span> được nhóm theo module</li>
            <li><strong>Kết quả:</strong> Đồng nhất 100% giữa Template Creation và Mapping Creation</li>
          </ul>
        </div>

        <div class="section">
          <h2>📋 Cấu trúc Module mới</h2>
          ${modules.map(module => `
            <div class="module-card">
              <div class="module-header">
                <span class="module-icon">${module.icon}</span>
                <div>
                  <h3 class="module-name">${module.name}</h3>
                  <p class="module-description">${module.description}</p>
                </div>
              </div>
              
              <div class="events-grid">
                ${module.events.map(event => `
                  <div class="event-card">
                    <div class="event-header">
                      <span class="event-icon">${event.icon}</span>
                      <h4 class="event-name">${event.name}</h4>
                    </div>
                    <p class="event-description">${event.description}</p>
                    <div style="font-size: 12px; color: #888; margin-top: 8px;">
                      <strong>ID:</strong> ${event.id}
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>

        <div class="section info">
          <h2>🎯 Workflow đồng nhất</h2>
          
          <h3>1. Template Creation:</h3>
          <ol>
            <li>User chọn <strong>Event Type</strong> từ dropdown (7 options)</li>
            <li>Xem key suggestions cho event type đã chọn</li>
            <li>Tạo template với event_type được lưu vào database</li>
          </ol>

          <h3>2. Mapping Creation:</h3>
          <ol>
            <li>User chọn <strong>Module</strong> (Đơn hàng, Chăm sóc khách hàng, Marketing)</li>
            <li>Chọn <strong>Event Type</strong> từ cùng danh sách ZNS_EVENT_TYPES</li>
            <li>Chọn template (có thể filter theo event_type)</li>
            <li>Parameter mapping tự động dựa trên key suggestions</li>
          </ol>

          <h3>3. Tính nhất quán:</h3>
          <ul>
            <li>✅ Cùng event types (order_confirmation, payment_confirmation, etc.)</li>
            <li>✅ Cùng icon và description</li>
            <li>✅ Cùng key suggestions</li>
            <li>✅ Cùng parameter mapping logic</li>
          </ul>
        </div>

        <div class="section">
          <h2>📊 Thống kê</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #007bff;">${modules.length}</div>
              <div style="font-size: 14px; color: #666;">Modules</div>
            </div>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #28a745;">${modules.reduce((total, module) => total + module.events.length, 0)}</div>
              <div style="font-size: 14px; color: #666;">Event Types</div>
            </div>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${ZNS_EVENT_TYPES.length}</div>
              <div style="font-size: 14px; color: #666;">Total ZNS Events</div>
            </div>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #dc3545;">100%</div>
              <div style="font-size: 14px; color: #666;">Consistency</div>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>🔧 Để test</h2>
          <ol>
            <li><strong>Template Creation:</strong> <code>/home/<USER>/integrations/zns/templates/create</code></li>
            <li><strong>Mapping Creation:</strong> <code>/home/<USER>/integrations/zns/mappings</code> → Click "Tạo mapping mới"</li>
            <li><strong>Kiểm tra:</strong> Event types trong cả 2 nơi phải giống nhau</li>
          </ol>
        </div>

        <div class="comparison">
          <h3>🎉 Kết quả</h3>
          <p>Hệ thống ZNS đã có <strong>tính đồng nhất hoàn toàn</strong> giữa Template Creation và Mapping Creation:</p>
          <ul>
            <li>✅ Cùng event types từ ZNS_EVENT_TYPES</li>
            <li>✅ Cùng key suggestions</li>
            <li>✅ Cùng parameter mapping logic</li>
            <li>✅ User experience nhất quán</li>
          </ul>
        </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error: any) {
    console.error('Error in test mapping dialog API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
