import { NextRequest, NextResponse } from 'next/server';

import { ZNS_EVENT_TYPES, getKeySuggestionsForEventType } from '@kit/zns';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const eventTypeId = searchParams.get('eventType');

    // Nếu có eventType, hiển thị chi tiết
    if (eventTypeId) {
      const eventType = ZNS_EVENT_TYPES.find(type => type.id === eventTypeId);
      const keySuggestions = getKeySuggestionsForEventType(eventTypeId);

      if (!eventType) {
        return NextResponse.json({ error: 'Event type not found' }, { status: 404 });
      }

      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>ZNS Event Type: ${eventType.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
            .key-card { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 12px; margin: 8px 0; }
            .key-name { font-family: monospace; background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-weight: bold; }
            .badge { padding: 2px 6px; border-radius: 4px; font-size: 12px; font-weight: bold; }
            .badge-type { background: #e3f2fd; color: #1976d2; }
            .example { background: #fff3e0; border-left: 4px solid #ff9800; padding: 12px; margin: 12px 0; font-family: monospace; }
            .back-link { display: inline-block; margin-bottom: 20px; color: #007bff; text-decoration: none; }
            .back-link:hover { text-decoration: underline; }
          </style>
        </head>
        <body>
          <a href="/api/zns/test-event-types" class="back-link">← Quay lại danh sách</a>
          
          <div class="header">
            <h1>${eventType.icon} ${eventType.name}</h1>
            <p>${eventType.description}</p>
          </div>

          <div class="section">
            <h2>📝 Ví dụ template</h2>
            <div class="example">
              ${eventType.example}
            </div>
          </div>

          <div class="section">
            <h2>🔑 Key được khuyến nghị (${keySuggestions.length})</h2>
            ${keySuggestions.map(key => `
              <div class="key-card">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                  <span class="key-name">&lt;${key.key}&gt;</span>
                  <span class="badge badge-type">${key.paramType}</span>
                </div>
                <div style="margin-bottom: 4px;"><strong>${key.description}</strong></div>
                <div style="font-size: 14px; color: #666;">
                  <strong>Ví dụ:</strong> ${key.example}
                </div>
                <div style="font-size: 14px; color: #666;">
                  <strong>Đường dẫn:</strong> <code>${key.path}</code>
                </div>
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h2>📊 Thông tin chi tiết</h2>
            <ul>
              <li><strong>ID:</strong> ${eventType.id}</li>
              <li><strong>Categories:</strong> ${eventType.categories.join(', ')}</li>
              <li><strong>Suggested keys:</strong> ${eventType.suggestedKeys.length}</li>
              <li><strong>Available keys:</strong> ${keySuggestions.length}</li>
            </ul>
          </div>
        </body>
        </html>
      `;

      return new Response(html, {
        headers: { 'Content-Type': 'text/html' },
      });
    }

    // Hiển thị danh sách tất cả event types
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Event Types</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
          .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s; }
          .card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.15); }
          .card-title { display: flex; align-items: center; gap: 10px; margin-bottom: 10px; }
          .card-icon { font-size: 24px; }
          .card-name { font-size: 18px; font-weight: bold; margin: 0; }
          .card-description { color: #666; margin-bottom: 15px; }
          .card-stats { display: flex; gap: 15px; margin-bottom: 15px; }
          .stat { background: #f8f9fa; padding: 8px 12px; border-radius: 4px; font-size: 14px; }
          .card-link { display: inline-block; background: #007bff; color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; font-size: 14px; }
          .card-link:hover { background: #0056b3; }
          .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎯 ZNS Event Types</h1>
          <p>Danh sách các loại sự kiện được hỗ trợ cho ZNS templates với key suggestions</p>
        </div>

        <div class="summary">
          <h2>📊 Tổng quan</h2>
          <ul>
            <li><strong>Tổng số event types:</strong> ${ZNS_EVENT_TYPES.length}</li>
            <li><strong>Tổng số key suggestions:</strong> ${ZNS_EVENT_TYPES.reduce((total, type) => total + type.suggestedKeys.length, 0)}</li>
            <li><strong>Categories:</strong> ${[...new Set(ZNS_EVENT_TYPES.flatMap(type => type.categories))].join(', ')}</li>
          </ul>
        </div>

        <div class="grid">
          ${ZNS_EVENT_TYPES.map(eventType => {
            const keySuggestions = getKeySuggestionsForEventType(eventType.id);
            return `
              <div class="card">
                <div class="card-title">
                  <span class="card-icon">${eventType.icon}</span>
                  <h3 class="card-name">${eventType.name}</h3>
                </div>
                <p class="card-description">${eventType.description}</p>
                <div class="card-stats">
                  <div class="stat">
                    <strong>${eventType.suggestedKeys.length}</strong> keys
                  </div>
                  <div class="stat">
                    <strong>${eventType.categories.length}</strong> categories
                  </div>
                </div>
                <a href="/api/zns/test-event-types?eventType=${eventType.id}" class="card-link">
                  Xem chi tiết →
                </a>
              </div>
            `;
          }).join('')}
        </div>

        <div class="summary">
          <h2>🔧 Cách sử dụng</h2>
          <ol>
            <li>User chọn event type trong form tạo template</li>
            <li>Hệ thống hiển thị key suggestions tương ứng</li>
            <li>User sử dụng key trong template với format <code>&lt;key_name&gt;</code></li>
            <li>Khi tạo mapping, key này sẽ được tự động map</li>
          </ol>
        </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error: any) {
    console.error('Error in test event types API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
