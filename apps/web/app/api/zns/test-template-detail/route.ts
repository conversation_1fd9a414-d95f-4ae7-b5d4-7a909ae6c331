import { NextRequest, NextResponse } from 'next/server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getZnsTemplateDetail } from '@kit/zns';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountSlug = searchParams.get('accountSlug');
    const templateId = searchParams.get('templateId');

    console.log('Test template detail API called with:', { accountSlug, templateId });

    if (!accountSlug || !templateId) {
      return NextResponse.json(
        { error: 'Account slug and template ID are required' },
        { status: 400 }
      );
    }

    // Create Supabase admin client for database queries
    const adminSupabase = getSupabaseServerAdminClient();
    // Create regular client for ZNS API calls
    const supabase = getSupabaseServerClient();

    // Get account by slug
    const { data: account, error: accountError } = await adminSupabase
      .from('accounts')
      .select('id')
      .eq('slug', accountSlug)
      .single();

    if (!account) {
      return NextResponse.json(
        { error: 'Account not found', details: accountError },
        { status: 404 }
      );
    }

    console.log('Found account:', { slug: accountSlug, id: account.id });

    // Get template from database
    const { data: dbTemplate, error: dbError } = await adminSupabase
      .from('zns_templates')
      .select('*')
      .eq('account_id', account.id)
      .eq('template_id', templateId)
      .maybeSingle();

    console.log('Database template:', { found: !!dbTemplate, template: dbTemplate, error: dbError });

    // Get ZNS integration
    const { data: integration, error: integrationError } = await adminSupabase
      .from('integrations')
      .select('*')
      .eq('account_id', account.id)
      .eq('type', 'zalo')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    console.log('Integration:', { found: !!integration, error: integrationError });

    let zaloTemplateDetail = null;
    if (integration?.metadata?.oa_config_id) {
      try {
        // Get OA config
        const { data: oaConfig, error: oaError } = await adminSupabase
          .from('oa_configurations')
          .select('*')
          .eq('id', integration.metadata.oa_config_id)
          .single();

        console.log('OA Config:', { found: !!oaConfig, error: oaError });

        if (oaConfig) {
          // Get template detail from Zalo API
          zaloTemplateDetail = await getZnsTemplateDetail(
            supabase,
            oaConfig.id,
            templateId,
          );
          console.log('Zalo template detail:', { found: !!zaloTemplateDetail });
        }
      } catch (error) {
        console.error('Error fetching Zalo template detail:', error);
      }
    }

    return NextResponse.json({
      accountSlug,
      templateId,
      account,
      dbTemplate,
      integration,
      zaloTemplateDetail,
      canShowEnableButton: !!dbTemplate,
    });
  } catch (error: any) {
    console.error('Error in test template detail API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
