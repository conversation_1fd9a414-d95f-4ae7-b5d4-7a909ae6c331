import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Import ZNS functions
    const { 
      ZNS_DEFAULT_KEYS,
      getZnsKeyConfig,
      categorizeTemplateParams,
      generateAutoMapping,
    } = await import('@kit/zns');

    // Test parameters
    const testParams = [
      { name: 'customer_name', type: '1' },
      { name: 'order_id', type: '4' },
      { name: 'bank_name', type: '15' },
      { name: 'custom_field', type: '13' },
    ];

    // Test each parameter
    const results = testParams.map(param => {
      const config = getZnsKeyConfig(param.name);
      return {
        name: param.name,
        type: param.type,
        inConfig: !!config,
        autoPath: config?.path || null,
        description: config?.description || 'Custom parameter',
      };
    });

    // Test categorization
    const { autoParams, manualParams } = categorizeTemplateParams(testParams);

    // Test auto mapping
    const autoMapping = generateAutoMapping(testParams);

    return NextResponse.json({
      success: true,
      data: {
        totalDefaultKeys: ZNS_DEFAULT_KEYS.length,
        testParams: results,
        categorization: {
          auto: autoParams.length,
          manual: manualParams.length,
        },
        autoMapping,
        autoParamsDetails: autoParams.map(({ param, config }) => ({
          name: param.name,
          path: config.path,
          description: config.description,
        })),
        manualParamsDetails: manualParams.map(param => ({
          name: param.name,
          type: param.type,
        })),
      },
    });
  } catch (error: any) {
    console.error('Error in simple mapping test:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Internal server error',
        stack: error.stack,
      },
      { status: 500 }
    );
  }
}
