import { NextRequest, NextResponse } from 'next/server';

import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getZnsTemplateList } from '@kit/zns';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountSlug = searchParams.get('accountSlug');

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Create Supabase admin client
    const adminSupabase = getSupabaseServerAdminClient();

    // Get account by slug
    const { data: account, error: accountError } = await adminSupabase
      .from('accounts')
      .select('id')
      .eq('slug', accountSlug)
      .single();

    if (!account) {
      return NextResponse.json(
        { error: 'Account not found', details: accountError },
        { status: 404 }
      );
    }

    // Get integration
    const { data: integration, error: integrationError } = await adminSupabase
      .from('integrations')
      .select('*')
      .eq('account_id', account.id)
      .eq('type', 'zalo')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (!integration) {
      return NextResponse.json({
        error: 'No Zalo integration found',
        details: integrationError,
      });
    }

    // Get OA config
    const { data: oaConfig, error: oaError } = await adminSupabase
      .from('oa_configurations')
      .select('*')
      .eq('id', integration.metadata.oa_config_id)
      .single();

    if (!oaConfig) {
      return NextResponse.json({
        error: 'OA config not found',
        details: oaError,
      });
    }

    // Get templates from database
    const { data: localTemplates, error: localError } = await adminSupabase
      .from('zns_templates')
      .select('*')
      .eq('account_id', account.id);

    console.log('Local templates:', localTemplates?.length || 0);

    // Get templates from Zalo API
    let zaloTemplates = null;
    try {
      const result = await getZnsTemplateList(
        adminSupabase,
        oaConfig.id,
        0,
        10,
        undefined,
      );
      zaloTemplates = result.data;
      console.log('Zalo templates:', zaloTemplates?.length || 0);
    } catch (error) {
      console.error('Error fetching Zalo templates:', error);
    }

    // Merge data
    const mergedTemplates = zaloTemplates?.map((zaloTemplate) => {
      const localTemplate = localTemplates?.find(
        (local) => String(local.template_id) === String(zaloTemplate.templateId)
      );
      
      return {
        templateId: zaloTemplate.templateId,
        templateName: zaloTemplate.templateName,
        status: zaloTemplate.status,
        // Add database fields
        enabled: localTemplate?.enabled || false,
        dbStatus: localTemplate?.status || null,
        eventType: localTemplate?.event_type || null,
        dbId: localTemplate?.id || null,
        hasLocalData: !!localTemplate,
      };
    }) || [];

    return NextResponse.json({
      accountSlug,
      accountId: account.id,
      localTemplatesCount: localTemplates?.length || 0,
      zaloTemplatesCount: zaloTemplates?.length || 0,
      mergedTemplatesCount: mergedTemplates.length,
      localTemplates: localTemplates?.map(t => ({
        id: t.id,
        template_id: t.template_id,
        template_name: t.template_name,
        enabled: t.enabled,
        status: t.status,
      })),
      mergedTemplates,
    });
  } catch (error: any) {
    console.error('Error in test merge API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
