import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Mapping UI Debug Guide</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .step { border: 1px solid #ddd; border-radius: 8px; padding: 16px; margin: 12px 0; background: #f9f9f9; }
          .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 12px; }
          .code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 8px; font-family: monospace; margin: 8px 0; }
          .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
          .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
          .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
          .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
          .screenshot { border: 2px solid #ddd; border-radius: 8px; padding: 16px; margin: 12px 0; background: #f8f9fa; }
          .console-log { background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 4px; font-family: monospace; margin: 8px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🐛 ZNS Mapping UI Debug Guide</h1>
          <p>Hướng dẫn debug tại sao parameter mapping không hiển thị trong modal</p>
        </div>

        <div class="step">
          <h2><span class="step-number">1</span>Mở Browser Console</h2>
          <p>Trước khi test, mở Developer Tools:</p>
          <div class="code">
            <strong>Chrome/Edge:</strong> F12 hoặc Ctrl+Shift+I<br>
            <strong>Firefox:</strong> F12 hoặc Ctrl+Shift+K<br>
            <strong>Safari:</strong> Cmd+Option+I
          </div>
          <p>Chuyển sang tab <strong>Console</strong> để xem logs.</p>
        </div>

        <div class="step">
          <h2><span class="step-number">2</span>Mở Mapping Dialog</h2>
          <p>Truy cập trang mappings và mở dialog:</p>
          <div class="code">
            1. Vào: http://localhost:3000/home/<USER>/integrations/zns/mappings<br>
            2. Click nút "Tạo mapping mới"<br>
            3. Modal sẽ mở ra
          </div>
        </div>

        <div class="step">
          <h2><span class="step-number">3</span>Chọn Module và Event Type</h2>
          <p>Thực hiện các bước sau và quan sát console:</p>
          <div class="code">
            1. Chọn Module: "Đơn hàng"<br>
            2. Chọn Event Type: "Xác nhận đơn hàng"<br>
            3. Xem console có logs không
          </div>
        </div>

        <div class="step">
          <h2><span class="step-number">4</span>Chọn Template</h2>
          <p>Đây là bước quan trọng nhất:</p>
          <div class="code">
            1. Chọn một template từ dropdown<br>
            2. Ngay lập tức check console logs<br>
            3. Tìm logs bắt đầu với "🔍 handleTemplateChange"
          </div>
          
          <div class="info">
            <h3>Expected Console Logs:</h3>
            <div class="console-log">
🔍 handleTemplateChange called with templateId: abc-123<br>
📋 Found template: {id: "abc-123", template_name: "...", metadata: {...}}<br>
📋 Template metadata: {params: [...]}<br>
📋 Template params: [{name: "customer_name", type: "1"}, ...]<br>
✅ Template has params, processing...<br>
🔄 Categorization result: {autoParams: 2, manualParams: 1, ...}<br>
🎯 Setting state: {autoParameterConfigs: 2, manualParameterConfigs: 1}<br>
🔗 Auto mapping generated: {customer_name: "customer.name", ...}
            </div>
          </div>
        </div>

        <div class="step">
          <h2><span class="step-number">5</span>Kiểm tra Debug Info</h2>
          <p>Sau khi chọn template, tìm debug info box trong modal:</p>
          <div class="screenshot">
            <h3>🔍 Debug Info Box</h3>
            <p>Sẽ hiển thị thông tin như:</p>
            <div class="code">
              Selected Template: Tên template<br>
              Template ID: abc-123<br>
              Auto Parameters: 2<br>
              Manual Parameters: 1<br>
              Has Metadata: Yes<br>
              Has Params: Yes<br>
              Params Count: 3<br>
              Params: ["customer_name", "order_id", "custom_field"]
            </div>
          </div>
        </div>

        <div class="step">
          <h2><span class="step-number">6</span>Troubleshooting</h2>
          
          <div class="error">
            <h3>❌ Nếu không thấy logs "🔍 handleTemplateChange":</h3>
            <ul>
              <li>Template dropdown không hoạt động</li>
              <li>Event handler không được bind</li>
              <li>Check lỗi JavaScript trong console</li>
            </ul>
          </div>

          <div class="warning">
            <h3>⚠️ Nếu thấy logs "❌ Template has no params":</h3>
            <ul>
              <li>Template không có metadata.params</li>
              <li>Cần tạo template mới với parameters</li>
              <li>Hoặc template structure không đúng format</li>
            </ul>
          </div>

          <div class="warning">
            <h3>⚠️ Nếu Debug Info hiển thị "Auto Parameters: 0, Manual Parameters: 0":</h3>
            <ul>
              <li>Template có params nhưng categorization thất bại</li>
              <li>Check import ZNS functions</li>
              <li>Check ZNS_DEFAULT_KEYS có load được không</li>
            </ul>
          </div>

          <div class="success">
            <h3>✅ Nếu Debug Info hiển thị đúng nhưng không thấy Parameter Mapping UI:</h3>
            <ul>
              <li>State đã được set nhưng UI không render</li>
              <li>Check condition: autoParameters.length > 0 || manualParameters.length > 0</li>
              <li>Check React re-render</li>
            </ul>
          </div>
        </div>

        <div class="step">
          <h2><span class="step-number">7</span>Expected UI Result</h2>
          <p>Nếu mọi thứ hoạt động đúng, bạn sẽ thấy:</p>
          
          <div class="screenshot">
            <h3>🎯 Parameter Mapping Section</h3>
            <div class="code">
              <strong>🤖 Auto-mapped Parameters (2)</strong><br>
              ├── customer_name → customer.name (disabled input)<br>
              └── order_id → order.id (disabled input)<br>
              <br>
              <strong>✋ Manual Parameters (1)</strong><br>
              └── custom_field → [editable input field]
            </div>
          </div>
        </div>

        <div class="step">
          <h2><span class="step-number">8</span>Common Issues & Solutions</h2>
          
          <div class="info">
            <h3>🔧 Issue: Template dropdown trống</h3>
            <p><strong>Solution:</strong> Tạo template mới với parameters trong template creation page</p>
          </div>

          <div class="info">
            <h3>🔧 Issue: Template không có params</h3>
            <p><strong>Solution:</strong> Template cần có metadata.params structure:</p>
            <div class="code">
{<br>
  "params": [<br>
    {"name": "customer_name", "type": "1"},<br>
    {"name": "order_id", "type": "4"},<br>
    {"name": "custom_field", "type": "13"}<br>
  ]<br>
}
            </div>
          </div>

          <div class="info">
            <h3>🔧 Issue: ZNS functions import error</h3>
            <p><strong>Solution:</strong> Check console for import errors, restart dev server</p>
          </div>

          <div class="info">
            <h3>🔧 Issue: State không update</h3>
            <p><strong>Solution:</strong> Check React DevTools, verify useState hooks</p>
          </div>
        </div>

        <div class="step success">
          <h2><span class="step-number">9</span>Success Criteria</h2>
          <p>Mapping dialog hoạt động đúng khi:</p>
          <ul>
            <li>✅ Console logs hiển thị đầy đủ</li>
            <li>✅ Debug info box hiển thị đúng số parameters</li>
            <li>✅ Parameter Mapping section xuất hiện</li>
            <li>✅ Auto parameters disabled, Manual parameters editable</li>
            <li>✅ Validation hoạt động khi submit</li>
          </ul>
        </div>

        <div class="step">
          <h2><span class="step-number">10</span>Next Steps</h2>
          <p>Sau khi debug xong:</p>
          <ol>
            <li>Remove debug logs và debug info box</li>
            <li>Test end-to-end workflow</li>
            <li>Verify parameter mapping được lưu đúng</li>
            <li>Test validation với manual parameters</li>
          </ol>
        </div>

        <div class="step info">
          <h2>📞 Need Help?</h2>
          <p>Nếu vẫn gặp vấn đề, cung cấp thông tin sau:</p>
          <ul>
            <li>Console logs đầy đủ</li>
            <li>Debug info box content</li>
            <li>Template structure (từ database)</li>
            <li>Browser và version</li>
          </ul>
        </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error: any) {
    console.error('Error in debug mapping UI:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
