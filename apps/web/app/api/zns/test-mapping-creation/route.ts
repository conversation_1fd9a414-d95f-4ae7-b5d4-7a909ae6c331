import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    console.log('🧪 Test mapping creation received body:', body);

    // Check required fields
    const requiredFields = ['name', 'template_id', 'module', 'event_type', 'account_id'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields',
        missingFields,
        receivedFields: Object.keys(body),
        body,
      }, { status: 400 });
    }

    // Validate account_id format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const isValidUUID = uuidRegex.test(body.account_id);

    return NextResponse.json({
      success: true,
      message: 'Test mapping creation validation passed',
      data: {
        receivedFields: Object.keys(body),
        requiredFields,
        missingFields,
        accountIdValid: isValidUUID,
        body,
      },
    });
  } catch (error: any) {
    console.error('Error in test mapping creation:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error',
      stack: error.stack,
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Test ZNS Mapping Creation</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background-color: #f0fdf4; border-color: #22c55e; }
        .error { background-color: #fef2f2; border-color: #ef4444; }
        .code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 8px; font-family: monospace; margin: 8px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        #result { margin-top: 20px; padding: 15px; border-radius: 8px; }
        .json { background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 4px; overflow-x: auto; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🧪 Test ZNS Mapping Creation</h1>
        <p>Test API validation để debug account_id error</p>
      </div>

      <div class="section">
        <h2>🔧 Test Cases</h2>
        <p>Click các button dưới để test different scenarios:</p>
        
        <button onclick="testValidRequest()">✅ Valid Request</button>
        <button onclick="testMissingAccountId()">❌ Missing account_id</button>
        <button onclick="testInvalidUUID()">❌ Invalid UUID</button>
        <button onclick="testEmptyBody()">❌ Empty Body</button>
      </div>

      <div id="result"></div>

      <script>
        async function makeRequest(data, testName) {
          const resultDiv = document.getElementById('result');
          resultDiv.innerHTML = '<p>🔄 Testing: ' + testName + '...</p>';
          
          try {
            const response = await fetch('/api/zns/test-mapping-creation', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            resultDiv.className = 'section ' + (result.success ? 'success' : 'error');
            resultDiv.innerHTML = \`
              <h3>\${result.success ? '✅' : '❌'} \${testName}</h3>
              <p><strong>Status:</strong> \${response.status}</p>
              <p><strong>Success:</strong> \${result.success}</p>
              \${result.error ? '<p><strong>Error:</strong> ' + result.error + '</p>' : ''}
              \${result.missingFields ? '<p><strong>Missing Fields:</strong> ' + result.missingFields.join(', ') + '</p>' : ''}
              <div class="json">
                <pre>\${JSON.stringify(result, null, 2)}</pre>
              </div>
            \`;
          } catch (error) {
            resultDiv.className = 'section error';
            resultDiv.innerHTML = \`
              <h3>❌ \${testName} - Network Error</h3>
              <p><strong>Error:</strong> \${error.message}</p>
            \`;
          }
        }

        function testValidRequest() {
          const data = {
            name: 'Test Mapping',
            description: 'Test description',
            template_id: '123e4567-e89b-12d3-a456-************',
            module: 'orders',
            event_type: 'order_confirmation',
            parameter_mapping: {
              customer_name: 'customer.name',
              order_id: 'order.id'
            },
            recipient_path: 'customer.phone',
            account_id: '123e4567-e89b-12d3-a456-************'
          };
          makeRequest(data, 'Valid Request');
        }

        function testMissingAccountId() {
          const data = {
            name: 'Test Mapping',
            template_id: '123e4567-e89b-12d3-a456-************',
            module: 'orders',
            event_type: 'order_confirmation',
            parameter_mapping: {}
            // Missing account_id
          };
          makeRequest(data, 'Missing account_id');
        }

        function testInvalidUUID() {
          const data = {
            name: 'Test Mapping',
            template_id: '123e4567-e89b-12d3-a456-************',
            module: 'orders',
            event_type: 'order_confirmation',
            parameter_mapping: {},
            account_id: 'invalid-uuid-format'
          };
          makeRequest(data, 'Invalid UUID');
        }

        function testEmptyBody() {
          makeRequest({}, 'Empty Body');
        }
      </script>

      <div class="section">
        <h2>📋 Expected Behavior</h2>
        <ul>
          <li><strong>Valid Request:</strong> Should return success with validation details</li>
          <li><strong>Missing account_id:</strong> Should return error with missing fields</li>
          <li><strong>Invalid UUID:</strong> Should return success but accountIdValid: false</li>
          <li><strong>Empty Body:</strong> Should return error with all missing fields</li>
        </ul>
      </div>

      <div class="section">
        <h2>🎯 Debug Steps</h2>
        <ol>
          <li>Test với valid request để đảm bảo API hoạt động</li>
          <li>Kiểm tra account_id có được gửi đúng format không</li>
          <li>Verify UUID validation</li>
          <li>Check console logs trong browser và server</li>
        </ol>
      </div>
    </body>
    </html>
  `;

  return new Response(html, {
    headers: { 'Content-Type': 'text/html' },
  });
}
