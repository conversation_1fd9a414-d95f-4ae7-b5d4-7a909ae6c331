import { NextRequest, NextResponse } from 'next/server';

import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');

    console.log('Fix templates API called with:', { accountId });

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Create Supabase admin client to bypass RLS
    const supabase = getSupabaseServerAdminClient();

    // First, try to find account by slug if accountId is not a UUID
    let realAccountId = accountId;
    if (!accountId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      const { data: account, error: accountError } = await supabase
        .from('accounts')
        .select('id')
        .eq('slug', accountId)
        .single();
      
      if (account) {
        realAccountId = account.id;
        console.log('Found account by slug:', { slug: accountId, id: realAccountId });
      } else {
        console.log('Account not found by slug:', { slug: accountId, error: accountError });
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 404 }
        );
      }
    }

    // Fetch all templates with null status
    const { data: templatesWithNullStatus, error: fetchError } = await supabase
      .from('zns_templates')
      .select('*')
      .eq('account_id', realAccountId)
      .is('status', null);

    if (fetchError) {
      console.error('Error fetching templates:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch templates' },
        { status: 500 }
      );
    }

    console.log('Templates with null status:', templatesWithNullStatus?.length || 0);

    // Update each template to have PENDING_REVIEW status (default for newly created templates)
    const updates = [];
    for (const template of templatesWithNullStatus || []) {
      const { error: updateError } = await supabase
        .from('zns_templates')
        .update({ 
          status: 'PENDING_REVIEW',
          updated_at: new Date().toISOString()
        })
        .eq('id', template.id);

      if (updateError) {
        console.error('Error updating template:', template.id, updateError);
        updates.push({ id: template.id, success: false, error: updateError.message });
      } else {
        console.log('Updated template:', template.id);
        updates.push({ id: template.id, success: true });
      }
    }

    return NextResponse.json({
      message: 'Templates fixed successfully',
      accountId: realAccountId,
      templatesFound: templatesWithNullStatus?.length || 0,
      updates,
    });
  } catch (error: any) {
    console.error('Error in fix templates API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
