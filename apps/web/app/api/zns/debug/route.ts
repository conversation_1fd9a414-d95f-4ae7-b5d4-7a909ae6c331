import { NextRequest, NextResponse } from 'next/server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const templateId = searchParams.get('templateId');

    console.log('Debug API called with:', { accountId, templateId });

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Create Supabase admin client to bypass RLS
    const supabase = getSupabaseServerAdminClient();

    // First, try to find account by slug if accountId is not a UUID
    let realAccountId = accountId;
    if (!accountId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      const { data: account, error: accountError } = await supabase
        .from('accounts')
        .select('id')
        .eq('slug', accountId)
        .single();

      if (account) {
        realAccountId = account.id;
        console.log('Found account by slug:', { slug: accountId, id: realAccountId });
      } else {
        console.log('Account not found by slug:', { slug: accountId, error: accountError });
      }
    }

    // Fetch all templates for the account
    const { data: allTemplates, error: allError } = await supabase
      .from('zns_templates')
      .select('*')
      .eq('account_id', realAccountId);

    console.log('All templates for account:', {
      accountId,
      count: allTemplates?.length || 0,
      templates: allTemplates?.map(t => ({
        id: t.id,
        template_id: t.template_id,
        template_name: t.template_name,
        enabled: t.enabled,
        status: t.status,
        created_at: t.created_at
      }))
    });

    if (templateId) {
      // Fetch specific template
      const { data: specificTemplate, error: specificError } = await supabase
        .from('zns_templates')
        .select('*')
        .eq('account_id', realAccountId)
        .eq('template_id', templateId)
        .maybeSingle();

      console.log('Specific template:', {
        templateId,
        found: !!specificTemplate,
        template: specificTemplate,
        error: specificError
      });

      return NextResponse.json({
        accountId,
        templateId,
        allTemplates: allTemplates || [],
        specificTemplate,
        allError,
        specificError,
      });
    }

    return NextResponse.json({
      accountId,
      allTemplates: allTemplates || [],
      allError,
    });
  } catch (error: any) {
    console.error('Error in debug API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
