import { NextRequest, NextResponse } from 'next/server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

export async function GET(
  request: NextRequest,
  { params }: { params: { templateId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const templateId = params.templateId;

    if (!accountId || !templateId) {
      return NextResponse.json(
        { error: 'Account ID and template ID are required' },
        { status: 400 }
      );
    }

    // Create Supabase admin client to bypass RLS for template queries
    const supabase = getSupabaseServerAdminClient();

    // Fetch template from database
    const { data: template, error } = await supabase
      .from('zns_templates')
      .select('*')
      .eq('account_id', accountId)
      .eq('template_id', templateId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching template:', error);
      return NextResponse.json(
        { error: 'Failed to fetch template' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: template,
      error: 0,
      message: template ? 'Template found' : 'Template not found',
    });
  } catch (error: any) {
    console.error('Error in GET /api/zns/templates/[templateId]:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { templateId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const templateId = params.templateId;
    const body = await request.json();

    if (!accountId || !templateId) {
      return NextResponse.json(
        { error: 'Account ID and template ID are required' },
        { status: 400 }
      );
    }

    // Create Supabase admin client to bypass RLS for template updates
    const supabase = getSupabaseServerAdminClient();

    // Update template in database
    const { data: template, error } = await supabase
      .from('zns_templates')
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq('account_id', accountId)
      .eq('template_id', templateId)
      .select()
      .single();

    if (error) {
      console.error('Error updating template:', error);
      return NextResponse.json(
        { error: 'Failed to update template' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: template,
      error: 0,
      message: 'Template updated successfully',
    });
  } catch (error: any) {
    console.error('Error in PATCH /api/zns/templates/[templateId]:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
