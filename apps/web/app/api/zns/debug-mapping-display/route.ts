import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Debug ZNS Mapping Display</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
          .success { background-color: #f0fdf4; border-color: #22c55e; }
          .error { background-color: #fef2f2; border-color: #ef4444; }
          .warning { background-color: #fff3cd; border-color: #ffeaa7; }
          .info { background-color: #d1ecf1; border-color: #bee5eb; }
          .code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 8px; font-family: monospace; margin: 8px 0; }
          button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
          button:hover { background: #0056b3; }
          #result { margin-top: 20px; padding: 15px; border-radius: 8px; }
          .json { background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 4px; overflow-x: auto; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🐛 Debug ZNS Mapping Display</h1>
          <p>Debug tại sao mapping được tạo thành công nhưng không hiển thị</p>
        </div>

        <div class="section info">
          <h2>🔍 Vấn đề hiện tại</h2>
          <ol>
            <li><strong>Mapping được tạo thành công</strong> - API trả về success</li>
            <li><strong>Mapping Overview không hiển thị</strong> - UI không show mapping mới</li>
            <li><strong>Error trong console</strong>: Cannot read properties of undefined (reading 'find')</li>
          </ol>
        </div>

        <div class="section warning">
          <h2>⚠️ Nguyên nhân có thể</h2>
          <ul>
            <li><strong>Module mismatch</strong>: Mapping được tạo với module khác với filter hiện tại</li>
            <li><strong>Query cache</strong>: React Query chưa invalidate cache</li>
            <li><strong>Event type mismatch</strong>: Event type không match với ZNS_EVENT_TYPES mới</li>
            <li><strong>Database structure</strong>: Mapping được lưu nhưng structure không đúng</li>
          </ul>
        </div>

        <div class="section">
          <h2>🧪 Debug Steps</h2>
          <button onclick="checkMappingsAPI()">1. Check Mappings API</button>
          <button onclick="checkConsole()">2. Check Console Logs</button>
          <button onclick="checkDatabase()">3. Check Database</button>
          <button onclick="checkModules()">4. Check Module Structure</button>
        </div>

        <div id="result"></div>

        <script>
          async function checkMappingsAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 Checking mappings API...</p>';
            
            try {
              // Test với account ID giả định
              const testAccountId = '123e4567-e89b-12d3-a456-************';
              const response = await fetch(\`/api/zns/mappings?accountId=\${testAccountId}\`);
              const result = await response.json();
              
              resultDiv.className = 'section ' + (response.ok ? 'success' : 'error');
              resultDiv.innerHTML = \`
                <h3>\${response.ok ? '✅' : '❌'} Mappings API Test</h3>
                <p><strong>Status:</strong> \${response.status}</p>
                <p><strong>Account ID:</strong> \${testAccountId}</p>
                <p><strong>Total Mappings:</strong> \${result.data?.length || 0}</p>
                \${result.data?.length > 0 ? \`
                  <h4>📋 Mappings Found:</h4>
                  \${result.data.map(m => \`
                    <div class="code">
                      <strong>Name:</strong> \${m.name}<br>
                      <strong>Module:</strong> \${m.module}<br>
                      <strong>Event Type:</strong> \${m.event_type}<br>
                      <strong>Enabled:</strong> \${m.enabled}<br>
                      <strong>Template:</strong> \${m.template?.template_name || 'N/A'}
                    </div>
                  \`).join('')}
                \` : '<p>No mappings found</p>'}
                <div class="json">
                  <pre>\${JSON.stringify(result, null, 2)}</pre>
                </div>
              \`;
            } catch (error) {
              resultDiv.className = 'section error';
              resultDiv.innerHTML = \`
                <h3>❌ API Error</h3>
                <p><strong>Error:</strong> \${error.message}</p>
              \`;
            }
          }

          function checkConsole() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'section info';
            resultDiv.innerHTML = \`
              <h3>🔍 Console Debug Instructions</h3>
              <ol>
                <li><strong>Mở browser console</strong> (F12)</li>
                <li><strong>Vào mappings page</strong>: /home/<USER>/integrations/zns/mappings</li>
                <li><strong>Tìm logs sau:</strong></li>
              </ol>
              
              <h4>Expected Console Logs:</h4>
              <div class="code">
🔍 Fetching mappings for: {selectedModule: "orders", accountId: "..."}
📋 Mappings response: {data: [...]}
🎯 Filtering mappings: {totalMappings: X, selectedModule: "orders", mappings: [...]}
✅ Filtered mappings: Y
              </div>

              <h4>🚨 Nếu không thấy logs:</h4>
              <ul>
                <li>Query không được trigger</li>
                <li>Account ID undefined</li>
                <li>Component không mount</li>
              </ul>

              <h4>🚨 Nếu totalMappings > 0 nhưng filteredMappings = 0:</h4>
              <ul>
                <li>Module mismatch: mapping.module ≠ selectedModule</li>
                <li>Mapping được tạo với module khác</li>
              </ul>
            \`;
          }

          function checkDatabase() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'section warning';
            resultDiv.innerHTML = \`
              <h3>🗄️ Database Check Instructions</h3>
              
              <h4>1. Check zns_mappings table:</h4>
              <div class="code">
SELECT id, name, module, event_type, enabled, created_at 
FROM zns_mappings 
ORDER BY created_at DESC 
LIMIT 5;
              </div>

              <h4>2. Check recent mappings:</h4>
              <div class="code">
SELECT * FROM zns_mappings 
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC;
              </div>

              <h4>3. Check module values:</h4>
              <div class="code">
SELECT DISTINCT module FROM zns_mappings;
              </div>

              <h4>🎯 Expected Results:</h4>
              <ul>
                <li><strong>Module values:</strong> orders, customer_service, marketing</li>
                <li><strong>Event types:</strong> order_confirmation, payment_confirmation, etc.</li>
                <li><strong>Recent mapping:</strong> Mapping vừa tạo phải có trong list</li>
              </ul>
            \`;
          }

          function checkModules() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'section info';
            resultDiv.innerHTML = \`
              <h3>🔧 Module Structure Check</h3>
              
              <h4>📦 Current Module Structure:</h4>
              <div class="code">
modules = [
  {
    id: 'orders',
    name: 'Đơn hàng', 
    events: [order_confirmation, payment_confirmation, shipping_notification]
  },
  {
    id: 'customer_service',
    name: 'Chăm sóc khách hàng',
    events: [customer_support, appointment_reminder]
  },
  {
    id: 'marketing', 
    name: 'Marketing',
    events: [product_promotion, general_notification]
  }
]
              </div>

              <h4>🚨 Potential Issues:</h4>
              <ul>
                <li><strong>Module ID mismatch:</strong> Form gửi 'orders' nhưng filter expect 'orders'</li>
                <li><strong>Event type mismatch:</strong> Event type không có trong module.events</li>
                <li><strong>ZNS_EVENT_TYPES undefined:</strong> Import failed</li>
              </ul>

              <h4>✅ Solutions:</h4>
              <ol>
                <li>Verify module IDs match between create và display</li>
                <li>Check ZNS_EVENT_TYPES import</li>
                <li>Ensure event types are consistent</li>
                <li>Check selectedModule default value</li>
              </ol>
            \`;
          }
        </script>

        <div class="section success">
          <h2>🎯 Quick Fix Checklist</h2>
          <ol>
            <li>✅ <strong>Fixed handleTabChange error</strong> → handleModuleChange</li>
            <li>✅ <strong>Fixed module.events.find error</strong> → modules.find(m => m.id === mapping.module)</li>
            <li>✅ <strong>Updated to ZNS_EVENT_TYPES</strong> → Consistent event types</li>
            <li>⏳ <strong>Debug mapping display</strong> → Check console logs</li>
            <li>⏳ <strong>Verify query invalidation</strong> → React Query cache</li>
          </ol>
        </div>

        <div class="section">
          <h2>🔄 Next Steps</h2>
          <ol>
            <li>Run debug tests above</li>
            <li>Check browser console logs</li>
            <li>Verify mapping creation in database</li>
            <li>Check module/event type consistency</li>
            <li>Test query invalidation after creation</li>
          </ol>
        </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error: any) {
    console.error('Error in debug mapping display:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
