import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountSlug = searchParams.get('accountSlug');

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Simulate merged templates data
    const mergedTemplates = [
      {
        templateId: 436882,
        templateName: "Thông báo học phí test",
        status: "PENDING_REVIEW",
        templateTag: 1,
        templateQuality: "HIGH",
        // Database fields
        enabled: false,
        dbStatus: "PENDING_REVIEW",
        eventType: null,
        dbId: "d65e9afa-c186-4d96-b044-2fa940d2cffa",
        hasLocalData: true,
      },
      {
        templateId: 436876,
        templateName: "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> đóng học phí",
        status: "PENDING_REVIEW",
        templateTag: 1,
        templateQuality: "HIGH",
        // Database fields
        enabled: false,
        dbStatus: "PENDING_REVIEW",
        eventType: null,
        dbId: "********-ce52-474d-9289-340cd0a577ca",
        hasLocalData: true,
      },
      {
        templateId: 436872,
        templateName: "Xác nhận đóng học phí",
        status: "PENDING_REVIEW",
        templateTag: 1,
        templateQuality: "HIGH",
        // Database fields
        enabled: true, // This one is enabled
        dbStatus: "PENDING_REVIEW",
        eventType: null,
        dbId: "f73648c3-38eb-4df5-921e-0e32428b3e78",
        hasLocalData: true,
      },
      {
        templateId: 436871,
        templateName: "Xác nhận đóng học phí",
        status: "PENDING_REVIEW",
        templateTag: 1,
        templateQuality: "MEDIUM",
        // Database fields - not in database
        enabled: false,
        dbStatus: null,
        eventType: null,
        dbId: null,
        hasLocalData: false,
      },
    ];

    // Generate HTML table to test
    const tableRows = mergedTemplates.map(template => {
      let enabledBadge = '';
      if (!template.dbId) {
        enabledBadge = '<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Not Saved</span>';
      } else if (template.enabled) {
        enabledBadge = '<span class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">✓ Enabled</span>';
      } else {
        enabledBadge = '<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">✗ Disabled</span>';
      }

      return `
        <tr class="border-b">
          <td class="p-2 font-mono text-xs">${template.templateId}</td>
          <td class="p-2">${template.templateName}</td>
          <td class="p-2">
            <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded">${template.status}</span>
          </td>
          <td class="p-2">${enabledBadge}</td>
          <td class="p-2">
            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">Transaction</span>
          </td>
          <td class="p-2">
            <span class="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">${template.templateQuality}</span>
          </td>
          <td class="p-2 text-right">
            <button class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">View</button>
          </td>
        </tr>
      `;
    }).join('');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Templates Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          table { width: 100%; border-collapse: collapse; }
          th { background-color: #f5f5f5; padding: 12px; text-align: left; border-bottom: 2px solid #ddd; }
          td { padding: 8px; }
          .highlight { background-color: #fff3cd; }
        </style>
      </head>
      <body>
        <h1>ZNS Templates List Test</h1>
        <p>Account: <strong>${accountSlug}</strong></p>
        <p>This is a test page to verify the "Enabled" column functionality.</p>
        
        <table>
          <thead>
            <tr>
              <th>Template ID</th>
              <th>Template Name</th>
              <th>Status</th>
              <th class="highlight">Enabled</th>
              <th>Tag</th>
              <th>Quality</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
        
        <h2>Data Summary</h2>
        <ul>
          <li><strong>Total templates:</strong> ${mergedTemplates.length}</li>
          <li><strong>Enabled templates:</strong> ${mergedTemplates.filter(t => t.enabled).length}</li>
          <li><strong>Templates in database:</strong> ${mergedTemplates.filter(t => t.hasLocalData).length}</li>
          <li><strong>Templates not saved:</strong> ${mergedTemplates.filter(t => !t.hasLocalData).length}</li>
        </ul>
        
        <h2>Expected Behavior</h2>
        <ul>
          <li>Template 436872 should show "✓ Enabled" (green)</li>
          <li>Templates 436882, 436876 should show "✗ Disabled" (gray)</li>
          <li>Template 436871 should show "Not Saved" (gray)</li>
        </ul>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error: any) {
    console.error('Error in test list page API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
