import { NextRequest, NextResponse } from 'next/server';

// Import trực tiếp từ zns package
import {
  ZNS_DEFAULT_KEYS,
  getZnsKeyConfig,
  isDefaultKey,
  generateAutoMapping,
  categorizeTemplateParams,
  validateParameterMapping as validateParams,
  getKeysByCategory as getZnsKeysByCategory,
} from '@kit/zns';

export async function GET(request: NextRequest) {
  try {
    // Test parameters từ template thực tế
    const testTemplateParams = [
      { name: 'customer_name', type: '1' },
      { name: 'customer_phone', type: '2' },
      { name: 'order_id', type: '4' },
      { name: 'order_total', type: '5' },
      { name: 'bank_name', type: '15' }, // Manual parameter
      { name: 'support_message', type: '13' }, // Manual parameter
      { name: 'custom_field_xyz', type: '13' }, // Manual parameter
    ];

    // Test categorization với config mới
    const categorizedParams = testTemplateParams.map(param => {
      const config = getZnsKeyConfig(param.name);
      return {
        original: param,
        isInConfig: !!config,
        config: config,
      };
    });

    // Test auto mapping
    const autoMapping = generateAutoMapping(testTemplateParams);

    // Test categorization
    const { autoParams, manualParams } = categorizeTemplateParams(testTemplateParams);

    // Test validation với mapping đầy đủ
    const completeMapping = {
      ...autoMapping,
      bank_name: 'Vietcombank',
      support_message: 'Liên hệ hotline 1900-1234',
      custom_field_xyz: 'Custom value',
    };

    const validationComplete = validateParams(testTemplateParams, completeMapping);

    // Test validation với mapping thiếu
    const incompleteMapping = {
      ...autoMapping,
      bank_name: 'Vietcombank',
      // Thiếu support_message và custom_field_xyz
    };

    const validationIncomplete = validateParams(testTemplateParams, incompleteMapping);

    // Test default keys
    const allDefaultKeys = ZNS_DEFAULT_KEYS;
    const customerKeys = getZnsKeysByCategory('customer');
    const orderKeys = getZnsKeysByCategory('order');

    // Generate HTML để hiển thị kết quả
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS New Parameter Mapping Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
          .auto { background-color: #f0f9ff; border-color: #0ea5e9; }
          .manual { background-color: #fff7ed; border-color: #f97316; }
          .success { background-color: #f0fdf4; border-color: #22c55e; }
          .error { background-color: #fef2f2; border-color: #ef4444; }
          .config { background-color: #f8fafc; border-color: #64748b; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
          th { background-color: #f5f5f5; }
          .badge { padding: 2px 6px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .badge-auto { background-color: #dcfce7; color: #166534; }
          .badge-manual { background-color: #fed7aa; color: #9a3412; }
          pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
          .highlight { background-color: #fef3c7; padding: 2px 4px; border-radius: 3px; }
        </style>
      </head>
      <body>
        <h1>🧪 ZNS New Parameter Mapping Test</h1>
        <p><strong>Sử dụng config mặc định từ logic gửi ZNS</strong></p>

        <div class="section config">
          <h2>📋 Default Keys Configuration</h2>
          <p>Tổng số key mặc định: <strong>${allDefaultKeys.length}</strong></p>

          <h3>Customer Keys (${customerKeys.length})</h3>
          <ul>
            ${customerKeys.map(key => `
              <li><code>${key.key}</code> → <code>${key.path}</code> (${key.description})</li>
            `).join('')}
          </ul>

          <h3>Order Keys (${orderKeys.length})</h3>
          <ul>
            ${orderKeys.map(key => `
              <li><code>${key.key}</code> → <code>${key.path}</code> (${key.description})</li>
            `).join('')}
          </ul>
        </div>

        <div class="section">
          <h2>📋 Test Template Parameters</h2>
          <table>
            <thead>
              <tr>
                <th>Parameter Name</th>
                <th>Type</th>
                <th>In Default Config?</th>
                <th>Auto Mapped</th>
                <th>Auto Path</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              ${categorizedParams.map(({ original, isInConfig, config }) => `
                <tr>
                  <td><strong>${original.name}</strong></td>
                  <td>${original.type}</td>
                  <td>
                    ${isInConfig ?
                      '<span class="highlight">✅ Yes</span>' :
                      '<span style="color: #dc2626;">❌ No</span>'
                    }
                  </td>
                  <td>
                    <span class="badge ${isInConfig ? 'badge-auto' : 'badge-manual'}">
                      ${isInConfig ? 'Auto' : 'Manual'}
                    </span>
                  </td>
                  <td>${config?.path || '-'}</td>
                  <td>${config?.description || 'Tham số tùy chỉnh'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <div class="section auto">
          <h2>🤖 Auto-mapped Parameters (${autoParams.length})</h2>
          <p>Các tham số này có trong config mặc định và sẽ được tự động ánh xạ:</p>
          <ul>
            ${autoParams.map(({ param, config }) => `
              <li><strong>${param.name}</strong> → <code>${config.path}</code> (${config.description})</li>
            `).join('')}
          </ul>
        </div>

        <div class="section manual">
          <h2>✋ Manual Parameters (${manualParams.length})</h2>
          <p>Các tham số này KHÔNG có trong config mặc định và cần người dùng cấu hình thủ công:</p>
          <ul>
            ${manualParams.map(param => `
              <li><strong>${param.name}</strong> - Tham số tùy chỉnh</li>
            `).join('')}
          </ul>
        </div>

        <div class="section">
          <h2>🔗 Auto Mapping Result</h2>
          <pre>${JSON.stringify(autoMapping, null, 2)}</pre>
        </div>

        <div class="section success">
          <h2>✅ Validation - Complete Mapping</h2>
          <p><strong>Status:</strong> ${validationComplete.isValid ? 'Valid' : 'Invalid'}</p>
          <p><strong>Mapping:</strong></p>
          <pre>${JSON.stringify(completeMapping, null, 2)}</pre>
          ${validationComplete.errors.length > 0 ? `
            <p><strong>Errors:</strong></p>
            <ul>${validationComplete.errors.map(error => `<li>${error}</li>`).join('')}</ul>
          ` : '<p>✅ No errors</p>'}
        </div>

        <div class="section error">
          <h2>❌ Validation - Incomplete Mapping</h2>
          <p><strong>Status:</strong> ${validationIncomplete.isValid ? 'Valid' : 'Invalid'}</p>
          <p><strong>Mapping:</strong></p>
          <pre>${JSON.stringify(incompleteMapping, null, 2)}</pre>
          ${validationIncomplete.errors.length > 0 ? `
            <p><strong>Errors:</strong></p>
            <ul>${validationIncomplete.errors.map(error => `<li style="color: #dc2626;">${error}</li>`).join('')}</ul>
          ` : '<p>✅ No errors</p>'}
        </div>

        <div class="section">
          <h2>📊 Summary</h2>
          <ul>
            <li><strong>Total parameters:</strong> ${testTemplateParams.length}</li>
            <li><strong>Auto-mapped (in config):</strong> ${autoParams.length}</li>
            <li><strong>Manual (not in config):</strong> ${manualParams.length}</li>
            <li><strong>Default keys available:</strong> ${allDefaultKeys.length}</li>
          </ul>
        </div>

        <div class="section">
          <h2>🎯 Key Improvements</h2>
          <ul>
            <li>✅ <strong>Config-driven</strong>: Auto/Manual dựa trên config mặc định từ logic gửi ZNS</li>
            <li>✅ <strong>Consistent</strong>: Logic mapping và logic gửi tin sử dụng cùng config</li>
            <li>✅ <strong>Maintainable</strong>: Chỉ cần update config ở một nơi</li>
            <li>✅ <strong>User-friendly</strong>: Template creation có thể hiển thị key documentation</li>
            <li>✅ <strong>Predictable</strong>: User biết chính xác key nào sẽ auto, key nào manual</li>
          </ul>
        </div>

        <div class="section">
          <h2>🔧 Next Steps</h2>
          <ol>
            <li>Update CreateMappingDialog để sử dụng logic mới</li>
            <li>Thêm ZnsKeyDocumentation vào template creation</li>
            <li>Update logic gửi ZNS để sử dụng cùng config</li>
            <li>Test end-to-end workflow</li>
          </ol>
        </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error: any) {
    console.error('Error in test new parameter mapping API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
