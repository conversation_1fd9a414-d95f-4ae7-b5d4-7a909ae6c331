import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    // Read the send page file
    const sendPagePath = path.join(process.cwd(), 'apps/web/app/home/<USER>/integrations/zns/send/page.tsx');
    const sendPageContent = fs.readFileSync(sendPagePath, 'utf-8');

    // Read the integrations.json file
    const integrationsPath = path.join(process.cwd(), 'apps/web/public/locales/vi/integrations.json');
    const integrationsContent = fs.readFileSync(integrationsPath, 'utf-8');
    const translations = JSON.parse(integrationsContent);

    // Extract all translation keys from send page
    const keyRegex = /t\('integrations:zns\.send\.([^']+)'/g;
    const foundKeys = new Set<string>();
    let match;

    while ((match = keyRegex.exec(sendPageContent)) !== null) {
      foundKeys.add(match[1]);
    }

    // Check which keys exist in translations
    const existingKeys = new Set<string>();
    const missingKeys = new Set<string>();

    foundKeys.forEach(key => {
      const keyPath = key.split('.');
      let current = translations.zns?.send;
      
      for (const part of keyPath) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part];
        } else {
          current = undefined;
          break;
        }
      }

      if (current !== undefined) {
        existingKeys.add(key);
      } else {
        missingKeys.add(key);
      }
    });

    // Get all available keys in translations
    const getAllKeys = (obj: any, prefix = ''): string[] => {
      const keys: string[] = [];
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        if (typeof value === 'object' && value !== null) {
          keys.push(...getAllKeys(value, fullKey));
        } else {
          keys.push(fullKey);
        }
      }
      return keys;
    };

    const availableKeys = translations.zns?.send ? getAllKeys(translations.zns.send) : [];

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Send Page Translation Keys Check</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
          .success { background-color: #f0fdf4; border-color: #22c55e; }
          .error { background-color: #fef2f2; border-color: #ef4444; }
          .warning { background-color: #fff3cd; border-color: #ffeaa7; }
          .info { background-color: #d1ecf1; border-color: #bee5eb; }
          .key-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin: 10px 0; }
          .key-item { background: #f8f9fa; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 14px; }
          .missing { background: #fee; border-left: 4px solid #e53e3e; }
          .existing { background: #efe; border-left: 4px solid #38a169; }
          .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin: 16px 0; }
          .stat-card { background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: center; border: 1px solid #e9ecef; }
          .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
          .stat-label { font-size: 14px; color: #666; margin-top: 4px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🌐 ZNS Send Page Translation Keys Check</h1>
          <p>Kiểm tra tính đầy đủ của các key bản dịch trong trang Send ZNS</p>
        </div>

        <div class="stats">
          <div class="stat-card">
            <div class="stat-number">${foundKeys.size}</div>
            <div class="stat-label">Total Keys Found</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" style="color: #22c55e;">${existingKeys.size}</div>
            <div class="stat-label">Existing Keys</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" style="color: #ef4444;">${missingKeys.size}</div>
            <div class="stat-label">Missing Keys</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" style="color: #8b5cf6;">${availableKeys.length}</div>
            <div class="stat-label">Available Keys</div>
          </div>
        </div>

        ${missingKeys.size > 0 ? `
        <div class="section error">
          <h2>❌ Missing Translation Keys</h2>
          <p>Các key này được sử dụng trong code nhưng chưa có bản dịch:</p>
          <div class="key-list">
            ${Array.from(missingKeys).map(key => `
              <div class="key-item missing">integrations:zns.send.${key}</div>
            `).join('')}
          </div>
          
          <h3>🔧 JSON cần thêm vào integrations.json:</h3>
          <pre style="background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 4px; overflow-x: auto;">
{
  "zns": {
    "send": {
${Array.from(missingKeys).map(key => `      "${key}": "Bản dịch cho ${key}"`).join(',\n')}
    }
  }
}
          </pre>
        </div>
        ` : `
        <div class="section success">
          <h2>✅ All Translation Keys Found</h2>
          <p>Tất cả các key được sử dụng trong code đều đã có bản dịch!</p>
        </div>
        `}

        <div class="section ${existingKeys.size > 0 ? 'success' : 'warning'}">
          <h2>✅ Existing Translation Keys</h2>
          <p>Các key này đã có bản dịch:</p>
          <div class="key-list">
            ${Array.from(existingKeys).map(key => `
              <div class="key-item existing">integrations:zns.send.${key}</div>
            `).join('')}
          </div>
        </div>

        <div class="section info">
          <h2>📋 All Available Translation Keys</h2>
          <p>Tất cả các key có sẵn trong integrations.json:</p>
          <div class="key-list">
            ${availableKeys.map(key => `
              <div class="key-item">integrations:zns.send.${key}</div>
            `).join('')}
          </div>
        </div>

        <div class="section">
          <h2>🔍 Analysis</h2>
          <ul>
            <li><strong>Coverage:</strong> ${existingKeys.size}/${foundKeys.size} keys (${Math.round((existingKeys.size / foundKeys.size) * 100)}%)</li>
            <li><strong>Status:</strong> ${missingKeys.size === 0 ? '✅ Complete' : `❌ ${missingKeys.size} keys missing`}</li>
            <li><strong>Extra Keys:</strong> ${availableKeys.length - existingKeys.size} unused keys available</li>
          </ul>
        </div>

        <div class="section">
          <h2>📝 Key Usage Examples</h2>
          <h3>In React Component:</h3>
          <pre style="background: #f8f9fa; padding: 12px; border-radius: 4px; overflow-x: auto;">
const { t } = useTranslation(['integrations']);

// Usage examples:
{t('integrations:zns.send.title')}
{t('integrations:zns.send.templateId')}
{t('integrations:zns.send.sendingModes.normal')}
          </pre>

          <h3>In JSON File:</h3>
          <pre style="background: #f8f9fa; padding: 12px; border-radius: 4px; overflow-x: auto;">
{
  "zns": {
    "send": {
      "title": "Gửi tin ZNS",
      "templateId": "ID mẫu tin",
      "sendingModes": {
        "normal": "Gửi thường",
        "exceedQuota": "Gửi vượt hạn mức",
        "development": "Chế độ phát triển"
      }
    }
  }
}
          </pre>
        </div>

        ${missingKeys.size === 0 ? `
        <div class="section success">
          <h2>🎉 Kết luận</h2>
          <p><strong>Trang Send ZNS đã có đầy đủ bản dịch!</strong></p>
          <ul>
            <li>✅ Tất cả ${foundKeys.size} keys đều đã có bản dịch</li>
            <li>✅ Không có key nào bị thiếu</li>
            <li>✅ Trang sẵn sàng cho production</li>
          </ul>
        </div>
        ` : `
        <div class="section error">
          <h2>⚠️ Action Required</h2>
          <p><strong>Cần thêm ${missingKeys.size} key bản dịch!</strong></p>
          <ol>
            <li>Copy JSON code ở trên</li>
            <li>Thêm vào file <code>apps/web/public/locales/vi/integrations.json</code></li>
            <li>Cập nhật bản dịch phù hợp</li>
            <li>Test lại trang Send</li>
          </ol>
        </div>
        `}
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error: any) {
    console.error('Error checking translations:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Internal server error',
        stack: error.stack,
      },
      { status: 500 }
    );
  }
}
