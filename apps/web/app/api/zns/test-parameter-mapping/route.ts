import { NextRequest, NextResponse } from 'next/server';

import {
  categorizeParameter,
  generateAutoParameterMapping,
  getManualParameters,
  getAutoMappedParameters,
  validateParameterMapping,
} from '../../../home/<USER>/integrations/zns/_lib/parameter-mapping';

export async function GET(request: NextRequest) {
  try {
    // Test parameters từ template thực tế
    const testTemplateParams = [
      { name: 'customer_name', type: '1' },
      { name: 'phone', type: '2' },
      { name: 'order_id', type: '4' },
      { name: 'total_amount', type: '5' },
      { name: 'bank_name', type: '15' },
      { name: 'support_phone', type: '2' },
      { name: 'custom_field', type: '13' },
    ];

    // Test categorization
    const categorizedParams = testTemplateParams.map(param => ({
      original: param,
      categorized: categorizeParameter(param.name),
    }));

    // Test auto mapping
    const autoMapping = generateAutoParameterMapping(testTemplateParams);

    // Test manual parameters
    const manualParams = getManualParameters(testTemplateParams);

    // Test auto parameters
    const autoParams = getAutoMappedParameters(testTemplateParams);

    // Test validation với mapping đầy đủ
    const completeMapping = {
      ...autoMapping,
      bank_name: 'Vietcombank',
      support_phone: '1900-1234',
      custom_field: 'Custom value',
    };

    const validationComplete = validateParameterMapping(testTemplateParams, completeMapping);

    // Test validation với mapping thiếu
    const incompleteMapping = {
      ...autoMapping,
      bank_name: 'Vietcombank',
      // Thiếu support_phone và custom_field
    };

    const validationIncomplete = validateParameterMapping(testTemplateParams, incompleteMapping);

    // Generate HTML để hiển thị kết quả
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Parameter Mapping Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
          .auto { background-color: #f0f9ff; border-color: #0ea5e9; }
          .manual { background-color: #fff7ed; border-color: #f97316; }
          .success { background-color: #f0fdf4; border-color: #22c55e; }
          .error { background-color: #fef2f2; border-color: #ef4444; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
          th { background-color: #f5f5f5; }
          .badge { padding: 2px 6px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .badge-auto { background-color: #dcfce7; color: #166534; }
          .badge-manual { background-color: #fed7aa; color: #9a3412; }
          pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        </style>
      </head>
      <body>
        <h1>🧪 ZNS Parameter Mapping Test</h1>
        
        <div class="section">
          <h2>📋 Test Template Parameters</h2>
          <table>
            <thead>
              <tr>
                <th>Parameter Name</th>
                <th>Type</th>
                <th>Category</th>
                <th>Auto Mapped</th>
                <th>Auto Path</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              ${categorizedParams.map(({ original, categorized }) => `
                <tr>
                  <td><strong>${original.name}</strong></td>
                  <td>${original.type}</td>
                  <td>${categorized?.category || 'unknown'}</td>
                  <td>
                    <span class="badge ${categorized?.isAutoMapped ? 'badge-auto' : 'badge-manual'}">
                      ${categorized?.isAutoMapped ? 'Auto' : 'Manual'}
                    </span>
                  </td>
                  <td>${categorized?.autoPath || '-'}</td>
                  <td>${categorized?.description || '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <div class="section auto">
          <h2>🤖 Auto-mapped Parameters (${autoParams.length})</h2>
          <p>Các tham số này sẽ được tự động ánh xạ từ dữ liệu sự kiện:</p>
          <ul>
            ${autoParams.map(param => `
              <li><strong>${param.name}</strong> → <code>${param.autoPath}</code> (${param.description})</li>
            `).join('')}
          </ul>
        </div>

        <div class="section manual">
          <h2>✋ Manual Parameters (${manualParams.length})</h2>
          <p>Các tham số này cần người dùng cấu hình thủ công:</p>
          <ul>
            ${manualParams.map(param => `
              <li><strong>${param.name}</strong> - ${param.description} 
                ${param.placeholder ? `<em>(ví dụ: ${param.placeholder})</em>` : ''}
              </li>
            `).join('')}
          </ul>
        </div>

        <div class="section">
          <h2>🔗 Auto Mapping Result</h2>
          <pre>${JSON.stringify(autoMapping, null, 2)}</pre>
        </div>

        <div class="section success">
          <h2>✅ Validation - Complete Mapping</h2>
          <p><strong>Status:</strong> ${validationComplete.isValid ? 'Valid' : 'Invalid'}</p>
          <p><strong>Mapping:</strong></p>
          <pre>${JSON.stringify(completeMapping, null, 2)}</pre>
          ${validationComplete.errors.length > 0 ? `
            <p><strong>Errors:</strong></p>
            <ul>${validationComplete.errors.map(error => `<li>${error}</li>`).join('')}</ul>
          ` : '<p>✅ No errors</p>'}
        </div>

        <div class="section error">
          <h2>❌ Validation - Incomplete Mapping</h2>
          <p><strong>Status:</strong> ${validationIncomplete.isValid ? 'Valid' : 'Invalid'}</p>
          <p><strong>Mapping:</strong></p>
          <pre>${JSON.stringify(incompleteMapping, null, 2)}</pre>
          ${validationIncomplete.errors.length > 0 ? `
            <p><strong>Errors:</strong></p>
            <ul>${validationIncomplete.errors.map(error => `<li style="color: #dc2626;">${error}</li>`).join('')}</ul>
          ` : '<p>✅ No errors</p>'}
        </div>

        <div class="section">
          <h2>📊 Summary</h2>
          <ul>
            <li><strong>Total parameters:</strong> ${testTemplateParams.length}</li>
            <li><strong>Auto-mapped:</strong> ${autoParams.length}</li>
            <li><strong>Manual:</strong> ${manualParams.length}</li>
            <li><strong>Auto mapping paths:</strong> ${Object.keys(autoMapping).length}</li>
          </ul>
        </div>

        <div class="section">
          <h2>🎯 Expected Behavior in UI</h2>
          <ul>
            <li>✅ <strong>customer_name, phone, order_id, total_amount</strong> sẽ hiển thị trong section "Tự động ánh xạ" (màu xanh, disabled input)</li>
            <li>⚠️ <strong>bank_name, support_phone, custom_field</strong> sẽ hiển thị trong section "Cần cấu hình thủ công" (màu cam, editable input)</li>
            <li>🔒 User không thể edit các auto parameters</li>
            <li>✏️ User phải điền đầy đủ các manual parameters mới submit được</li>
          </ul>
        </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error: any) {
    console.error('Error in test parameter mapping API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
