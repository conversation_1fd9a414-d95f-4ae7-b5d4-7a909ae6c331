import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Simulate parameter mapping UI
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Parameter Mapping UI Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .container { max-width: 800px; margin: 0 auto; }
          .section { margin: 20px 0; padding: 20px; border-radius: 8px; }
          .auto-section { background-color: #f0f9ff; border: 2px solid #0ea5e9; }
          .manual-section { background-color: #fff7ed; border: 2px solid #f97316; }
          .summary-section { background-color: #eff6ff; border: 2px solid #3b82f6; }
          .param-row { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin: 12px 0; padding: 12px; border-radius: 6px; }
          .auto-row { background-color: #dcfce7; border: 1px solid #16a34a; }
          .manual-row { background-color: #fed7aa; border: 1px solid #ea580c; }
          .param-label { display: flex; align-items: center; gap: 8px; font-weight: 600; }
          .param-input { display: flex; align-items: center; gap: 8px; }
          .input { padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 4px; width: 100%; }
          .input:disabled { background-color: #f3f4f6; color: #6b7280; }
          .badge { padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; }
          .badge-auto { background-color: #dcfce7; color: #166534; }
          .badge-manual { background-color: #fed7aa; color: #9a3412; }
          .badge-type { background-color: #e0e7ff; color: #3730a3; }
          .icon { width: 16px; height: 16px; display: inline-block; }
          .icon-lock { color: #16a34a; }
          .icon-settings { color: #ea580c; }
          .icon-check { color: #059669; }
          .icon-alert { color: #d97706; }
          .description { font-size: 12px; color: #6b7280; margin-top: 4px; grid-column: span 2; }
          h2 { margin: 0 0 16px 0; display: flex; align-items: center; gap: 8px; }
          h3 { margin: 0 0 12px 0; display: flex; align-items: center; gap: 8px; }
          .summary-list { margin: 0; padding-left: 20px; }
          .summary-list li { margin: 4px 0; }
          .button { padding: 12px 24px; background-color: #3b82f6; color: white; border: none; border-radius: 6px; font-weight: 600; cursor: pointer; }
          .button:hover { background-color: #2563eb; }
          .button:disabled { background-color: #9ca3af; cursor: not-allowed; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🔗 ZNS Parameter Mapping UI Demo</h1>
          <p>Đây là demo UI cho tính năng parameter mapping thông minh trong tạo ZNS mapping.</p>

          <div class="section">
            <h2>⚙️ Parameter Mapping</h2>
            
            <!-- Auto-mapped Parameters -->
            <div class="auto-section">
              <h3>
                <span class="icon icon-check">✅</span>
                Tự động ánh xạ (4)
              </h3>
              
              <div class="param-row auto-row">
                <div class="param-label">
                  <span class="icon icon-lock">🔒</span>
                  <strong>customer_name</strong>
                  <span class="badge badge-type">1</span>
                </div>
                <div class="param-input">
                  <input type="text" class="input" value="customer.name" disabled>
                  <span class="badge badge-auto">Auto</span>
                </div>
                <div class="description">Tên khách hàng</div>
              </div>

              <div class="param-row auto-row">
                <div class="param-label">
                  <span class="icon icon-lock">🔒</span>
                  <strong>phone</strong>
                  <span class="badge badge-type">2</span>
                </div>
                <div class="param-input">
                  <input type="text" class="input" value="customer.phone" disabled>
                  <span class="badge badge-auto">Auto</span>
                </div>
                <div class="description">Số điện thoại khách hàng</div>
              </div>

              <div class="param-row auto-row">
                <div class="param-label">
                  <span class="icon icon-lock">🔒</span>
                  <strong>order_id</strong>
                  <span class="badge badge-type">4</span>
                </div>
                <div class="param-input">
                  <input type="text" class="input" value="order.id" disabled>
                  <span class="badge badge-auto">Auto</span>
                </div>
                <div class="description">Mã đơn hàng</div>
              </div>

              <div class="param-row auto-row">
                <div class="param-label">
                  <span class="icon icon-lock">🔒</span>
                  <strong>total_amount</strong>
                  <span class="badge badge-type">5</span>
                </div>
                <div class="param-input">
                  <input type="text" class="input" value="order.total" disabled>
                  <span class="badge badge-auto">Auto</span>
                </div>
                <div class="description">Tổng tiền</div>
              </div>
            </div>

            <!-- Manual Parameters -->
            <div class="manual-section">
              <h3>
                <span class="icon icon-alert">⚠️</span>
                Cần cấu hình thủ công (3)
              </h3>
              
              <div class="param-row manual-row">
                <div class="param-label">
                  <span class="icon icon-settings">⚙️</span>
                  <strong>bank_name</strong>
                  <span class="badge badge-type">15</span>
                </div>
                <div class="param-input">
                  <input type="text" class="input" placeholder="Vietcombank" value="Vietcombank">
                  <span class="badge badge-manual">Manual</span>
                </div>
                <div class="description">Tên ngân hàng</div>
              </div>

              <div class="param-row manual-row">
                <div class="param-label">
                  <span class="icon icon-settings">⚙️</span>
                  <strong>support_phone</strong>
                  <span class="badge badge-type">2</span>
                </div>
                <div class="param-input">
                  <input type="text" class="input" placeholder="1900-xxxx" value="">
                  <span class="badge badge-manual">Manual</span>
                </div>
                <div class="description">Số điện thoại hỗ trợ</div>
              </div>

              <div class="param-row manual-row">
                <div class="param-label">
                  <span class="icon icon-settings">⚙️</span>
                  <strong>custom_field</strong>
                  <span class="badge badge-type">13</span>
                </div>
                <div class="param-input">
                  <input type="text" class="input" placeholder="Nhập giá trị cho custom_field" value="">
                  <span class="badge badge-manual">Manual</span>
                </div>
                <div class="description">Tham số tùy chỉnh: custom_field</div>
              </div>
            </div>

            <!-- Summary -->
            <div class="summary-section">
              <h3>
                <span class="icon icon-check">✅</span>
                Tóm tắt
              </h3>
              <ul class="summary-list">
                <li><strong>4</strong> tham số được tự động ánh xạ</li>
                <li><strong>3</strong> tham số cần cấu hình thủ công</li>
                <li>Các tham số tự động sẽ được lấy từ dữ liệu sự kiện</li>
                <li>Các tham số thủ công sử dụng giá trị cố định bạn nhập</li>
              </ul>
            </div>

            <div style="margin-top: 24px; text-align: right;">
              <button class="button" onclick="validateAndSubmit()">
                Tạo Mapping
              </button>
            </div>
          </div>

          <div class="section">
            <h2>🎯 Tính năng chính</h2>
            <ul>
              <li>✅ <strong>Tự động phân loại parameters</strong>: Auto vs Manual dựa trên tên parameter</li>
              <li>🔒 <strong>Khóa auto parameters</strong>: User không thể edit, tránh lỗi</li>
              <li>✏️ <strong>Chỉ cho edit manual parameters</strong>: Những field thực sự cần config</li>
              <li>🔍 <strong>Validation thông minh</strong>: Kiểm tra manual parameters đã điền đủ chưa</li>
              <li>🎨 <strong>UI trực quan</strong>: Màu sắc phân biệt rõ ràng auto vs manual</li>
              <li>📝 <strong>Mô tả chi tiết</strong>: Giải thích từng parameter để user hiểu</li>
            </ul>
          </div>

          <div class="section">
            <h2>🚀 Workflow</h2>
            <ol>
              <li><strong>Chọn template</strong> → Hệ thống tự động phân loại parameters</li>
              <li><strong>Auto parameters</strong> → Hiển thị disabled, đã có sẵn mapping path</li>
              <li><strong>Manual parameters</strong> → User cần điền giá trị cố định</li>
              <li><strong>Validation</strong> → Kiểm tra manual parameters đã đủ chưa</li>
              <li><strong>Submit</strong> → Tạo mapping với đầy đủ parameter mapping</li>
            </ol>
          </div>
        </div>

        <script>
          function validateAndSubmit() {
            const manualInputs = document.querySelectorAll('.manual-row input:not([disabled])');
            const emptyFields = [];
            
            manualInputs.forEach(input => {
              if (!input.value.trim()) {
                emptyFields.push(input.placeholder || 'Unknown field');
              }
            });
            
            if (emptyFields.length > 0) {
              alert('Vui lòng điền đầy đủ các tham số thủ công:\\n' + emptyFields.join('\\n'));
              return;
            }
            
            alert('✅ Validation thành công! Mapping sẽ được tạo với:\\n\\n' +
                  'Auto parameters: 4\\n' +
                  'Manual parameters: 3\\n' +
                  'Tất cả đã được cấu hình đầy đủ.');
          }
        </script>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error: any) {
    console.error('Error in test mapping UI API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
