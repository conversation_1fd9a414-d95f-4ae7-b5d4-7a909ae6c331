import { NextRequest, NextResponse } from 'next/server';

import { 
  ZNS_DEFAULT_KEYS,
  getZnsKeyConfig,
  categorizeTemplateParams,
  generateAutoMapping,
} from '@kit/zns';

export async function GET(request: NextRequest) {
  try {
    // Simulate template parameters từ template thực tế
    const testTemplateParams = [
      { name: 'customer_name', type: '1' },      // Có trong ZNS_DEFAULT_KEYS → Auto
      { name: 'customer_phone', type: '2' },     // Có trong ZNS_DEFAULT_KEYS → Auto
      { name: 'order_id', type: '4' },           // C<PERSON> trong ZNS_DEFAULT_KEYS → Auto
      { name: 'order_total', type: '5' },        // Có trong ZNS_DEFAULT_KEYS → Auto
      { name: 'bank_name', type: '15' },         // KHÔNG có trong ZNS_DEFAULT_KEYS → Manual
      { name: 'support_message', type: '13' },   // KHÔNG có trong ZNS_DEFAULT_KEYS → Manual
      { name: 'custom_field_xyz', type: '13' },  // KHÔNG có trong ZNS_DEFAULT_KEYS → Manual
    ];

    // Test categorization sử dụng ZNS config
    const { autoParams, manualParams } = categorizeTemplateParams(testTemplateParams);

    // Test auto mapping
    const autoMapping = generateAutoMapping(testTemplateParams);

    // Chuyển đổi sang ParameterConfig format như trong UI
    const autoParameterConfigs = autoParams.map(({ param, config }) => ({
      name: param.name,
      type: config.paramType,
      isAutoMapped: true,
      autoPath: config.path,
      description: config.description,
      category: config.category,
      example: config.example,
    }));

    const manualParameterConfigs = manualParams.map(param => ({
      name: param.name,
      type: param.type || '13',
      isAutoMapped: false,
      description: `Tham số tùy chỉnh: ${param.name}`,
      placeholder: `Nhập giá trị cho ${param.name}`,
      category: 'custom',
    }));

    // Test validation
    const completeMapping = {
      ...autoMapping,
      bank_name: 'Vietcombank',
      support_message: 'Liên hệ hotline 1900-1234',
      custom_field_xyz: 'Custom value',
    };

    const incompleteMapping = {
      ...autoMapping,
      bank_name: 'Vietcombank',
      // Thiếu support_message và custom_field_xyz
    };

    // Validation logic
    const validateMapping = (mapping: Record<string, string>) => {
      const errors: string[] = [];
      manualParams.forEach(param => {
        if (!mapping[param.name] || mapping[param.name].trim() === '') {
          errors.push(`Tham số "${param.name}" cần được cấu hình`);
        }
      });
      return { isValid: errors.length === 0, errors };
    };

    const validationComplete = validateMapping(completeMapping);
    const validationIncomplete = validateMapping(incompleteMapping);

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Mapping Parameters Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
          .auto { background-color: #f0f9ff; border-color: #0ea5e9; }
          .manual { background-color: #fff7ed; border-color: #f97316; }
          .success { background-color: #f0fdf4; border-color: #22c55e; }
          .error { background-color: #fef2f2; border-color: #ef4444; }
          .config { background-color: #f8fafc; border-color: #64748b; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
          th { background-color: #f5f5f5; }
          .badge { padding: 2px 6px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .badge-auto { background-color: #dcfce7; color: #166534; }
          .badge-manual { background-color: #fed7aa; color: #9a3412; }
          pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
          .highlight { background-color: #fef3c7; padding: 2px 4px; border-radius: 3px; font-weight: bold; }
          .param-card { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 12px; margin: 8px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🧪 ZNS Mapping Parameters Test</h1>
          <p><strong>Kiểm tra logic phân loại parameters dựa trên ZNS_DEFAULT_KEYS</strong></p>
        </div>

        <div class="section config">
          <h2>📋 Test Template Parameters</h2>
          <table>
            <thead>
              <tr>
                <th>Parameter Name</th>
                <th>Type</th>
                <th>In ZNS_DEFAULT_KEYS?</th>
                <th>Category</th>
                <th>Auto Path</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              ${testTemplateParams.map(param => {
                const config = getZnsKeyConfig(param.name);
                const isInConfig = !!config;
                return `
                  <tr>
                    <td><strong>${param.name}</strong></td>
                    <td>${param.type}</td>
                    <td>
                      ${isInConfig ? 
                        '<span class="highlight">✅ Yes</span>' : 
                        '<span style="color: #dc2626;">❌ No</span>'
                      }
                    </td>
                    <td>
                      <span class="badge ${isInConfig ? 'badge-auto' : 'badge-manual'}">
                        ${isInConfig ? 'Auto' : 'Manual'}
                      </span>
                    </td>
                    <td>${config?.path || '-'}</td>
                    <td>${config?.description || 'Tham số tùy chỉnh'}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>
        </div>

        <div class="section auto">
          <h2>🤖 Auto Parameters (${autoParameterConfigs.length})</h2>
          <p>Các parameters có trong <code>ZNS_DEFAULT_KEYS</code> sẽ được tự động map:</p>
          ${autoParameterConfigs.map(param => `
            <div class="param-card" style="border-color: #22c55e; background-color: #f0fdf4;">
              <div style="display: flex; justify-content: between; align-items: center;">
                <div>
                  <strong>${param.name}</strong> 
                  <span class="badge badge-auto">${param.type}</span>
                </div>
                <code style="background: #dcfce7; padding: 4px 8px; border-radius: 4px;">${param.autoPath}</code>
              </div>
              <div style="font-size: 14px; color: #166534; margin-top: 4px;">
                ${param.description} (ví dụ: ${param.example})
              </div>
            </div>
          `).join('')}
        </div>

        <div class="section manual">
          <h2>✋ Manual Parameters (${manualParameterConfigs.length})</h2>
          <p>Các parameters KHÔNG có trong <code>ZNS_DEFAULT_KEYS</code> cần user config:</p>
          ${manualParameterConfigs.map(param => `
            <div class="param-card" style="border-color: #f97316; background-color: #fff7ed;">
              <div>
                <strong>${param.name}</strong> 
                <span class="badge badge-manual">${param.type}</span>
              </div>
              <div style="font-size: 14px; color: #9a3412; margin-top: 4px;">
                ${param.description}
              </div>
              <div style="font-size: 12px; color: #ea580c; margin-top: 4px;">
                Placeholder: "${param.placeholder}"
              </div>
            </div>
          `).join('')}
        </div>

        <div class="section">
          <h2>🔗 Auto Mapping Result</h2>
          <pre>${JSON.stringify(autoMapping, null, 2)}</pre>
        </div>

        <div class="section success">
          <h2>✅ Validation - Complete Mapping</h2>
          <p><strong>Status:</strong> ${validationComplete.isValid ? 'Valid' : 'Invalid'}</p>
          <p><strong>Mapping:</strong></p>
          <pre>${JSON.stringify(completeMapping, null, 2)}</pre>
          ${validationComplete.errors.length > 0 ? `
            <p><strong>Errors:</strong></p>
            <ul>${validationComplete.errors.map(error => `<li>${error}</li>`).join('')}</ul>
          ` : '<p>✅ No errors</p>'}
        </div>

        <div class="section error">
          <h2>❌ Validation - Incomplete Mapping</h2>
          <p><strong>Status:</strong> ${validationIncomplete.isValid ? 'Valid' : 'Invalid'}</p>
          <p><strong>Mapping:</strong></p>
          <pre>${JSON.stringify(incompleteMapping, null, 2)}</pre>
          ${validationIncomplete.errors.length > 0 ? `
            <p><strong>Errors:</strong></p>
            <ul>${validationIncomplete.errors.map(error => `<li style="color: #dc2626;">${error}</li>`).join('')}</ul>
          ` : '<p>✅ No errors</p>'}
        </div>

        <div class="section">
          <h2>📊 Summary</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #007bff;">${testTemplateParams.length}</div>
              <div style="font-size: 14px; color: #666;">Total Parameters</div>
            </div>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #28a745;">${autoParameterConfigs.length}</div>
              <div style="font-size: 14px; color: #666;">Auto (in config)</div>
            </div>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${manualParameterConfigs.length}</div>
              <div style="font-size: 14px; color: #666;">Manual (not in config)</div>
            </div>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #dc3545;">${ZNS_DEFAULT_KEYS.length}</div>
              <div style="font-size: 14px; color: #666;">Available Keys</div>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>🎯 Logic hoạt động</h2>
          <ol>
            <li><strong>Template có parameters</strong> → Gọi <code>categorizeTemplateParams()</code></li>
            <li><strong>Check từng parameter</strong> → <code>getZnsKeyConfig(param.name)</code></li>
            <li><strong>Có trong ZNS_DEFAULT_KEYS</strong> → Auto parameter với path sẵn</li>
            <li><strong>Không có trong config</strong> → Manual parameter cần user điền</li>
            <li><strong>Auto mapping</strong> → <code>generateAutoMapping()</code> tạo mapping cho auto params</li>
            <li><strong>Validation</strong> → Chỉ check manual parameters đã điền chưa</li>
          </ol>
        </div>

        <div class="section success">
          <h2>🎉 Kết quả</h2>
          <p>Logic phân loại parameters đã hoạt động đúng theo ZNS_DEFAULT_KEYS:</p>
          <ul>
            <li>✅ <strong>4 auto parameters</strong>: customer_name, customer_phone, order_id, order_total</li>
            <li>⚠️ <strong>3 manual parameters</strong>: bank_name, support_message, custom_field_xyz</li>
            <li>🔒 <strong>Auto parameters</strong>: Disabled input, hiển thị path sẵn</li>
            <li>✏️ <strong>Manual parameters</strong>: Editable input, cần user điền</li>
            <li>✅ <strong>Validation</strong>: Chỉ check manual parameters</li>
          </ul>
        </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error: any) {
    console.error('Error in test mapping parameters API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
