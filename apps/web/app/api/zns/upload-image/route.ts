import { NextRequest, NextResponse } from 'next/server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { uploadZnsImage, uploadZnsImageFromUrl } from '@kit/zns';

export async function POST(request: NextRequest) {
  try {
    // Check content type to determine how to parse the request
    const contentType = request.headers.get('content-type') || '';

    let oaConfigId: string;
    let file: File | null = null;
    let imageUrl: string | null = null;
    let type: string | null = null;

    // Parse request based on content type
    if (contentType.includes('multipart/form-data')) {
      // Handle form data (direct file upload)
      const formData = await request.formData();
      file = formData.get('file') as File;
      oaConfigId = formData.get('oaConfigId') as string;
    } else {
      // Handle JSON data (URL-based upload)
      const jsonData = await request.json();
      oaConfigId = jsonData.oaConfigId;
      imageUrl = jsonData.imageUrl;
      type = jsonData.type;
    }

    // Validate required parameters
    if ((!file && !imageUrl) || !oaConfigId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 },
      );
    }

    // Create Supabase client
    const supabase = getSupabaseServerClient();

    let result;

    // Process based on upload type
    if (file) {
      // Handle direct file upload
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      result = await uploadZnsImage(supabase, oaConfigId, buffer, file.name);
    } else if (imageUrl) {
      // Handle URL-based upload
      result = await uploadZnsImageFromUrl(supabase, oaConfigId, imageUrl);
    }

    // Return the response
    return NextResponse.json({
      data: result,
      error: 0,
      message: 'Success',
    });
  } catch (error: any) {
    console.error('Error uploading image:', error);
    return NextResponse.json(
      {
        error: error.message,
        details: error.response?.data || 'Unknown error',
      },
      { status: 500 },
    );
  }
}
