import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Simulate optimized modal UI
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>ZNS Modal UI Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
          .modal-overlay { position: fixed; inset: 0; background-color: rgba(0, 0, 0, 0.8); display: flex; align-items: center; justify-content: center; z-index: 50; }
          .modal-content { 
            background: white; 
            border-radius: 8px; 
            width: 90%; 
            max-width: 900px; 
            max-height: 90vh; 
            overflow-y: auto; 
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          }
          .modal-header { padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; }
          .modal-body { padding: 24px; }
          .section { margin-bottom: 24px; }
          .section-title { font-size: 18px; font-weight: 600; margin-bottom: 16px; }
          .form-group { margin-bottom: 16px; }
          .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
          .label { display: block; font-size: 14px; font-weight: 500; margin-bottom: 4px; }
          .input, .textarea, .select { 
            width: 100%; 
            padding: 8px 12px; 
            border: 1px solid #d1d5db; 
            border-radius: 6px; 
            font-size: 14px;
          }
          .textarea { resize: vertical; min-height: 60px; }
          .auto-param { 
            display: flex; 
            align-items: center; 
            justify-content: between; 
            padding: 8px 12px; 
            background-color: #f0f9ff; 
            border: 1px solid #0ea5e9; 
            border-radius: 6px; 
            margin-bottom: 8px;
          }
          .manual-param { 
            padding: 12px; 
            background-color: #fff7ed; 
            border: 1px solid #f97316; 
            border-radius: 6px; 
            margin-bottom: 12px;
          }
          .badge { 
            padding: 2px 8px; 
            border-radius: 12px; 
            font-size: 12px; 
            font-weight: 600;
          }
          .badge-auto { background-color: #dcfce7; color: #166534; }
          .badge-manual { background-color: #fed7aa; color: #9a3412; }
          .badge-type { background-color: #e0e7ff; color: #3730a3; }
          .summary { 
            padding: 12px; 
            background-color: #eff6ff; 
            border: 1px solid #3b82f6; 
            border-radius: 6px; 
            font-size: 12px;
          }
          .button { 
            padding: 12px 24px; 
            background-color: #3b82f6; 
            color: white; 
            border: none; 
            border-radius: 6px; 
            font-weight: 600; 
            cursor: pointer;
          }
          .button:hover { background-color: #2563eb; }
          .modal-footer { 
            padding: 16px 24px; 
            border-top: 1px solid #e5e7eb; 
            display: flex; 
            justify-content: flex-end;
          }
          .scroll-area { max-height: 200px; overflow-y: auto; }
          .close-btn { 
            position: absolute; 
            top: 16px; 
            right: 16px; 
            background: none; 
            border: none; 
            font-size: 24px; 
            cursor: pointer;
          }
        </style>
      </head>
      <body>
        <h1>🔧 ZNS Modal UI Test - Optimized</h1>
        <p>Test modal với kích thước và scroll được tối ưu.</p>
        
        <button onclick="openModal()" class="button">Mở Modal "Tạo mapping mới"</button>

        <!-- Modal -->
        <div id="modal" class="modal-overlay" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <button class="close-btn" onclick="closeModal()">&times;</button>
              <h2>Tạo mapping mới</h2>
              <p style="color: #6b7280; margin: 8px 0 0 0;">Cấu hình cách sử dụng mẫu tin ZNS với một sự kiện cụ thể</p>
            </div>
            
            <div class="modal-body">
              <!-- Basic Information -->
              <div class="section">
                <h3 class="section-title">Thông tin cơ bản</h3>
                <div class="form-group">
                  <label class="label">Tên</label>
                  <input type="text" class="input" placeholder="Order confirmation notification" value="Thông báo xác nhận đơn hàng">
                </div>
                <div class="form-group">
                  <label class="label">Mô tả</label>
                  <textarea class="textarea" placeholder="Send notification when a new order is created">Gửi thông báo khi có đơn hàng mới được tạo</textarea>
                </div>
              </div>

              <!-- Event Configuration -->
              <div class="section">
                <h3 class="section-title">Cấu hình sự kiện</h3>
                <div class="form-row">
                  <div class="form-group">
                    <label class="label">Module</label>
                    <select class="select">
                      <option>Orders</option>
                      <option>Customers</option>
                      <option>Payments</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label class="label">Loại sự kiện</label>
                    <select class="select">
                      <option>Order Created</option>
                      <option>Order Updated</option>
                      <option>Order Completed</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Template Configuration -->
              <div class="section">
                <h3 class="section-title">Cấu hình template</h3>
                <div class="form-row">
                  <div class="form-group">
                    <label class="label">Mẫu tin</label>
                    <select class="select">
                      <option>Xác nhận đóng học phí</option>
                      <option>Thông báo học phí test</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label class="label">Đường dẫn đến số điện thoại</label>
                    <input type="text" class="input" placeholder="customer.phone" value="customer.phone">
                    <small style="color: #6b7280; font-size: 12px;">Đường dẫn đến số điện thoại trong dữ liệu sự kiện</small>
                  </div>
                </div>
              </div>

              <!-- Parameter Mapping -->
              <div class="section">
                <h3 class="section-title">⚙️ Parameter Mapping</h3>
                
                <!-- Auto Parameters -->
                <div style="margin-bottom: 20px;">
                  <h4 style="color: #059669; font-size: 14px; font-weight: 600; margin-bottom: 12px;">
                    ✅ Tự động ánh xạ (4)
                  </h4>
                  <div class="scroll-area">
                    <div class="auto-param">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <span>🔒</span>
                        <strong>customer_name</strong>
                        <span class="badge badge-type">1</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <code style="background: #dcfce7; color: #166534; padding: 2px 6px; border-radius: 4px;">customer.name</code>
                        <span class="badge badge-auto">Auto</span>
                      </div>
                    </div>
                    <div class="auto-param">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <span>🔒</span>
                        <strong>phone</strong>
                        <span class="badge badge-type">2</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <code style="background: #dcfce7; color: #166534; padding: 2px 6px; border-radius: 4px;">customer.phone</code>
                        <span class="badge badge-auto">Auto</span>
                      </div>
                    </div>
                    <div class="auto-param">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <span>🔒</span>
                        <strong>order_id</strong>
                        <span class="badge badge-type">4</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <code style="background: #dcfce7; color: #166534; padding: 2px 6px; border-radius: 4px;">order.id</code>
                        <span class="badge badge-auto">Auto</span>
                      </div>
                    </div>
                    <div class="auto-param">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <span>🔒</span>
                        <strong>total_amount</strong>
                        <span class="badge badge-type">5</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <code style="background: #dcfce7; color: #166534; padding: 2px 6px; border-radius: 4px;">order.total</code>
                        <span class="badge badge-auto">Auto</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Manual Parameters -->
                <div style="margin-bottom: 20px;">
                  <h4 style="color: #d97706; font-size: 14px; font-weight: 600; margin-bottom: 12px;">
                    ⚠️ Cần cấu hình thủ công (2)
                  </h4>
                  <div class="manual-param">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                      <span>⚙️</span>
                      <strong>bank_name</strong>
                      <span class="badge badge-type">15</span>
                      <span class="badge badge-manual">Manual</span>
                    </div>
                    <input type="text" class="input" placeholder="Vietcombank" value="Vietcombank" style="margin-bottom: 4px;">
                    <small style="color: #d97706;">Tên ngân hàng</small>
                  </div>
                  <div class="manual-param">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                      <span>⚙️</span>
                      <strong>support_phone</strong>
                      <span class="badge badge-type">2</span>
                      <span class="badge badge-manual">Manual</span>
                    </div>
                    <input type="text" class="input" placeholder="1900-xxxx" value="1900-1234" style="margin-bottom: 4px;">
                    <small style="color: #d97706;">Số điện thoại hỗ trợ</small>
                  </div>
                </div>

                <!-- Summary -->
                <div class="summary">
                  <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span>✅</span>
                    <strong style="color: #1d4ed8;">Tóm tắt</strong>
                  </div>
                  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; color: #1d4ed8;">
                    <p>• <strong>4</strong> tham số tự động</p>
                    <p>• <strong>2</strong> tham số thủ công</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="modal-footer">
              <button class="button">
                ⚙️ Tạo Mapping
              </button>
            </div>
          </div>
        </div>

        <div style="margin-top: 40px;">
          <h2>🎯 Cải tiến đã thực hiện</h2>
          <ul>
            <li>✅ <strong>Kích thước modal</strong>: Tăng từ 600px lên 900px</li>
            <li>✅ <strong>Chiều cao modal</strong>: Giới hạn 90vh để không tràn màn hình</li>
            <li>✅ <strong>Scroll</strong>: Thêm overflow-y-auto cho toàn bộ modal</li>
            <li>✅ <strong>Layout tối ưu</strong>: Chia thành sections rõ ràng</li>
            <li>✅ <strong>Form 2 cột</strong>: Event và Template config dùng grid 2 cột</li>
            <li>✅ <strong>Parameter mapping gọn</strong>: Auto params có scroll riêng</li>
            <li>✅ <strong>Manual params</strong>: Layout dọc, dễ nhìn hơn</li>
            <li>✅ <strong>Summary thu gọn</strong>: Chỉ hiển thị thông tin cần thiết</li>
          </ul>
        </div>

        <script>
          function openModal() {
            document.getElementById('modal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
          }
          
          function closeModal() {
            document.getElementById('modal').style.display = 'none';
            document.body.style.overflow = 'auto';
          }
          
          // Close modal when clicking overlay
          document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
              closeModal();
            }
          });
        </script>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error: any) {
    console.error('Error in test modal UI API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
