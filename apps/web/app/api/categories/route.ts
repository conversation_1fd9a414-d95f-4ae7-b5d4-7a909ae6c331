import { NextResponse } from 'next/server';

import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';

const CategoriesGetQuerySchema = z.object({
  search: z.string().optional().nullable(),
  parent_id: z.string().uuid('Invalid parent ID').optional().nullable(),
});

export const GET = enhanceRouteHandler(
  async function ({ request, supabase, user }) {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      // Parse and validate query params
      const { search, parent_id } = CategoriesGetQuerySchema.parse({
        search: searchParams.get('search') || undefined,
        parent_id: searchParams.get('parent_id') || undefined,
      });

      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return NextResponse.json(
          { error: 'No account_id found in token' },
          { status: 401 },
        );
      }

      // Build query without pagination
      let query = supabase
        .from('categories')
        .select(
          `
          id,
          name,
          description,
          parent_id,
          created_at,
          updated_at
          `,
        )
        .eq('account_id', account_id)
        .order('name', { ascending: true });

      // Apply filters only if they exist
      if (search) {
        query = query.ilike('name', `%${search}%`);
      }
      if (parent_id) {
        query = query.eq('parent_id', parent_id);
      }

      const { data, error } = await query;

      if (error) throw error;

      return NextResponse.json({
        success: true,
        data: data || [],
      });
    } catch (error) {
      console.error('Error in GET /api/categories:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 },
        );
      }

      return NextResponse.json(
        { success: false, error: 'Internal server error' },
        { status: 500 },
      );
    }
  },
  { auth: true },
);
