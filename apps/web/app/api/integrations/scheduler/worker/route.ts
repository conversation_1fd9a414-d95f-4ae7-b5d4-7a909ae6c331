import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { SyncWorker } from '~/lib/integrations/scheduler/sync-worker';

/**
 * API endpoint để chạy worker
 * POST /api/integrations/scheduler/worker
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();

  try {
    // Kiểm tra API key
    const apiKey = request.headers.get('x-api-key');
    const expectedApiKey = process.env.INTEGRATION_WORKER_API_KEY;

    if (!apiKey || apiKey !== expectedApiKey) {
      logger.warn('Invalid API key for worker');
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Invalid API key' },
        { status: 401 }
      );
    }

    // Tạo và chạy worker
    const worker = new SyncWorker();
    await worker.run();

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error({ error }, 'Error running worker');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}
