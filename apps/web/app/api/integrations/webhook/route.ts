import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { createConnectorFromIntegrationId } from '~/lib/integrations/connectors/connector-factory';
import crypto from 'crypto';

/**
 * API endpoint để nhận webhook từ bên thứ 3
 * POST /api/integrations/webhook/:platform/:integrationId
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { platform: string; integrationId: string } }
) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy thông tin từ URL
    const { platform, integrationId } = params;

    if (!platform || !integrationId) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'platform and integrationId are required' },
        { status: 400 }
      );
    }

    // L<PERSON>y thông tin tích hợp
    const { data: integration, error: fetchError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('type', platform)
      .single();

    if (fetchError || !integration) {
      logger.error({ error: fetchError, platform, integrationId }, 'Integration not found');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Kiểm tra trạng thái tích hợp
    if (integration.status !== 'connected' || !integration.enabled) {
      logger.warn({ integrationId }, 'Integration is not connected or disabled');
      return NextResponse.json(
        { error: 'Integration unavailable', message: 'Integration is not connected or disabled' },
        { status: 400 }
      );
    }

    // Lấy dữ liệu webhook
    const payload = await request.json();

    // Xác thực webhook signature nếu có
    if (integration.webhook_secret) {
      const signature = request.headers.get('x-webhook-signature') || '';
      const isValid = verifyWebhookSignature(
        JSON.stringify(payload),
        signature,
        integration.webhook_secret
      );

      if (!isValid) {
        logger.warn({ integrationId }, 'Invalid webhook signature');
        return NextResponse.json(
          { error: 'Unauthorized', message: 'Invalid webhook signature' },
          { status: 401 }
        );
      }
    }

    // Tạo connector
    const connector = await createConnectorFromIntegrationId(integrationId);
    if (!connector) {
      logger.error({ integrationId }, 'Failed to create connector');
      return NextResponse.json(
        { error: 'Connector error', message: 'Failed to create connector' },
        { status: 500 }
      );
    }

    // Xử lý webhook
    await connector.handleWebhook(payload);

    // Lưu webhook vào database
    await supabase.from('integration_webhooks').insert({
      integration_id: integrationId,
      payload,
      processed_at: new Date().toISOString(),
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error({ error }, 'Error handling webhook');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * Xác thực webhook signature
 * @param payload Dữ liệu webhook
 * @param signature Chữ ký webhook
 * @param secret Secret key
 */
function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const hmac = crypto.createHmac('sha256', secret);
    const digest = hmac.update(payload).digest('hex');
    return crypto.timingSafeEqual(
      Buffer.from(digest),
      Buffer.from(signature)
    );
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
}
