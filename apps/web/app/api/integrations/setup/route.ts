import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { getConnectorFactory } from '~/lib/integrations/connectors/connector-factory';
import { checkResourceAccess } from '~/lib/check-resource-access';
import { encrypt } from '~/lib/integrations/utils/crypto';

/**
 * API endpoint để thiết lập tích hợp
 * POST /api/integrations/setup
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy dữ liệu từ request
    const { accountId, platform, credentials, config, name, description } = await request.json();

    // <PERSON><PERSON>m tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'create');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, platform }, 'Access denied to create integration');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Kiểm tra xung đột exclusive resources
    if (config?.exclusive_resources) {
      for (const [resource, assignedPlatform] of Object.entries(config.exclusive_resources)) {
        if (!assignedPlatform) continue;

        // Kiểm tra xem resource này đã được tích hợp với platform khác chưa
        const { data: existingIntegrations, error: fetchError } = await supabase
          .from('integrations')
          .select('id, type, config')
          .eq('account_id', accountId)
          .eq('enabled', true)
          .neq('type', platform);

        if (fetchError) {
          logger.error({ error: fetchError }, 'Error fetching existing integrations');
          return NextResponse.json(
            { error: 'Database error', message: 'Failed to check existing integrations' },
            { status: 500 }
          );
        }

        // Kiểm tra xung đột
        for (const integration of existingIntegrations || []) {
          const exclusiveResources = integration.config?.exclusive_resources || {};
          if (exclusiveResources[resource] === integration.type) {
            return NextResponse.json(
              {
                error: 'Resource conflict',
                message: `Resource "${resource}" is already integrated with "${integration.type}"`,
                conflictingIntegration: integration.id,
              },
              { status: 409 }
            );
          }
        }
      }
    }

    // Xác thực với platform
    const factory = getConnectorFactory();
    const connector = await factory.createConnector(platform, credentials);

    if (!connector) {
      logger.error({ platform }, 'Unsupported platform');
      return NextResponse.json(
        { error: 'Unsupported platform', message: `Platform "${platform}" is not supported` },
        { status: 400 }
      );
    }

    const isAuthenticated = await connector.authenticate(credentials);
    if (!isAuthenticated) {
      logger.error({ platform, credentials }, 'Authentication failed');
      return NextResponse.json(
        { error: 'Authentication failed', message: 'Failed to authenticate with the platform' },
        { status: 401 }
      );
    }

    // Mã hóa credentials
    const encryptedCredentials = encrypt(credentials);

    // Lưu thông tin tích hợp vào database
    const { data: integration, error: insertError } = await supabase
      .from('integrations')
      .insert({
        account_id: accountId,
        type: platform,
        name: name || `${platform.charAt(0).toUpperCase() + platform.slice(1)} Integration`,
        description: description || `Integration with ${platform}`,
        credentials: encryptedCredentials,
        config,
        status: 'connected',
        enabled: true,
      })
      .select()
      .single();

    if (insertError) {
      logger.error({ error: insertError }, 'Error inserting integration');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to save integration' },
        { status: 500 }
      );
    }

    logger.info({ accountId, platform, integrationId: integration.id }, 'Integration setup successful');

    return NextResponse.json({
      success: true,
      integration: {
        id: integration.id,
        type: integration.type,
        name: integration.name,
        status: integration.status,
      },
    });
  } catch (error: any) {
    logger.error({ error }, 'Error setting up integration');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * API endpoint để lấy thông tin tích hợp
 * GET /api/integrations/setup?accountId=xxx&platform=yyy
 */
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const platform = searchParams.get('platform');
    const integrationId = searchParams.get('integrationId');

    if (!accountId) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId is required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'view');
    if (!accessCheck.allowed) {
      logger.warn({ accountId }, 'Access denied to view integration');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Xây dựng query
    let query = supabase
      .from('integrations')
      .select('id, type, name, description, status, enabled, config, last_sync_at, error_message, updated_at')
      .eq('account_id', accountId);

    if (platform) {
      query = query.eq('type', platform);
    }

    if (integrationId) {
      query = query.eq('id', integrationId);
    }

    // Thực hiện query
    const { data: integrations, error } = await query;

    if (error) {
      logger.error({ error }, 'Error fetching integrations');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to fetch integrations' },
        { status: 500 }
      );
    }

    return NextResponse.json({ integrations });
  } catch (error: any) {
    logger.error({ error }, 'Error getting integrations');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * API endpoint để cập nhật tích hợp
 * PATCH /api/integrations/setup
 */
export async function PATCH(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy dữ liệu từ request
    const { accountId, integrationId, credentials, config, name, description, enabled } = await request.json();

    if (!accountId || !integrationId) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId and integrationId are required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'manage');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to update integration');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Lấy thông tin tích hợp hiện tại
    const { data: integration, error: fetchError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (fetchError || !integration) {
      logger.error({ error: fetchError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (enabled !== undefined) updateData.enabled = enabled;
    if (config !== undefined) updateData.config = config;

    // Nếu có credentials mới, xác thực và mã hóa
    if (credentials) {
      const factory = getConnectorFactory();
      const connector = await factory.createConnector(integration.type, credentials);

      if (!connector) {
        logger.error({ platform: integration.type }, 'Unsupported platform');
        return NextResponse.json(
          { error: 'Unsupported platform', message: `Platform "${integration.type}" is not supported` },
          { status: 400 }
        );
      }

      const isAuthenticated = await connector.authenticate(credentials);
      if (!isAuthenticated) {
        logger.error({ platform: integration.type, credentials }, 'Authentication failed');
        return NextResponse.json(
          { error: 'Authentication failed', message: 'Failed to authenticate with the platform' },
          { status: 401 }
        );
      }

      // Mã hóa credentials
      updateData.credentials = encrypt(credentials);
      updateData.status = 'connected';
      updateData.error_message = null;
    }

    // Cập nhật tích hợp
    const { data: updatedIntegration, error: updateError } = await supabase
      .from('integrations')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', integrationId)
      .select()
      .single();

    if (updateError) {
      logger.error({ error: updateError }, 'Error updating integration');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to update integration' },
        { status: 500 }
      );
    }

    logger.info({ accountId, integrationId }, 'Integration updated successfully');

    return NextResponse.json({
      success: true,
      integration: {
        id: updatedIntegration.id,
        type: updatedIntegration.type,
        name: updatedIntegration.name,
        status: updatedIntegration.status,
        enabled: updatedIntegration.enabled,
      },
    });
  } catch (error: any) {
    logger.error({ error }, 'Error updating integration');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * API endpoint để xóa tích hợp
 * DELETE /api/integrations/setup?accountId=xxx&integrationId=yyy
 */
export async function DELETE(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const integrationId = searchParams.get('integrationId');

    if (!accountId || !integrationId) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId and integrationId are required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'manage');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to delete integration');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Xóa tích hợp
    const { error } = await supabase
      .from('integrations')
      .delete()
      .eq('id', integrationId)
      .eq('account_id', accountId);

    if (error) {
      logger.error({ error }, 'Error deleting integration');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to delete integration' },
        { status: 500 }
      );
    }

    logger.info({ accountId, integrationId }, 'Integration deleted successfully');

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error({ error }, 'Error deleting integration');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}
