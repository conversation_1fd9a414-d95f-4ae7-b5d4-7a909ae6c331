import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { createConnectorFromIntegrationId } from '~/lib/integrations/connectors/connector-factory';
import { checkResourceAccess } from '~/lib/check-resource-access';

/**
 * API endpoint để xem trước dữ liệu sau khi áp dụng mapping
 * POST /api/integrations/preview
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy dữ liệu từ request
    const { accountId, integrationId, resourceType, mappings } = await request.json();

    if (!accountId || !integrationId || !resourceType || !mappings) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId, integrationId, resourceType, and mappings are required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'view');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to preview data');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Kiểm tra integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (integrationError || !integration) {
      logger.error({ error: integrationError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Tạo connector
    const connector = await createConnectorFromIntegrationId(integrationId);
    if (!connector) {
      logger.error({ integrationId }, 'Failed to create connector');
      return NextResponse.json(
        { error: 'Connector error', message: 'Failed to create connector' },
        { status: 500 }
      );
    }

    // Lấy dữ liệu mẫu từ connector
    try {
      const data = await connector.getData({
        resourceType,
        limit: 1,
      });

      if (!data || data.length === 0) {
        // Nếu không có dữ liệu thật, sử dụng dữ liệu mẫu
        const sampleData = getSampleData(integration.type, resourceType);

        // Áp dụng mapping
        const mappedData = applyMapping(sampleData, mappings);

        return NextResponse.json({
          preview: mappedData,
          is_sample: true,
        });
      }

      // Áp dụng mapping
      const mappedData = applyMapping(data[0], mappings);

      return NextResponse.json({
        preview: mappedData,
        is_sample: false,
      });
    } catch (error: any) {
      logger.error({ error, integrationId, resourceType }, 'Error getting data for preview');

      // Nếu có lỗi, sử dụng dữ liệu mẫu
      const sampleData = getSampleData(integration.type, resourceType);

      // Áp dụng mapping
      const mappedData = applyMapping(sampleData, mappings);

      return NextResponse.json({
        preview: mappedData,
        is_sample: true,
        error: error.message,
      });
    }
  } catch (error: any) {
    logger.error({ error }, 'Error in preview endpoint');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * Áp dụng mapping cho dữ liệu
 * @param data Dữ liệu gốc
 * @param mappings Mapping
 */
function applyMapping(data: any, mappings: any[]): any {
  const result: Record<string, any> = {};

  // Nếu không có mapping, trả về dữ liệu gốc
  if (!mappings || mappings.length === 0) {
    return data;
  }

  // Áp dụng từng mapping
  for (const mapping of mappings) {
    const { source_field, target_field, transform_function } = mapping;

    // Lấy giá trị từ source field
    let value = data[source_field];

    // Áp dụng hàm biến đổi nếu có
    if (transform_function && value !== undefined) {
      try {
        // Thực thi hàm biến đổi (cẩn thận với eval!)
        // Trong môi trường production, nên sử dụng cách an toàn hơn
        const transformFn = new Function('value', transform_function);
        value = transformFn(value);
      } catch (error) {
        console.error(
          `Error applying transform function for ${source_field}:`,
          error
        );
      }
    }

    // Gán giá trị cho target field
    if (value !== undefined) {
      result[target_field] = value;
    }
  }

  // Thêm id và account_id nếu chưa có
  if (!result.id && data.id) {
    result.id = data.id;
  }

  // Thêm các trường bắt buộc khác
  result.account_id = 'sample-account-id';

  return result;
}

/**
 * Lấy dữ liệu mẫu cho platform và resource type
 * @param platform Platform
 * @param resourceType Resource type
 */
function getSampleData(platform: string, resourceType: string): any {
  switch (platform) {
    case 'ipos':
      switch (resourceType) {
        case 'products':
          return {
            id: 'sample-product-id',
            name: 'Sample Product',
            ta_price: 100000,
            ots_price: 120000,
            sort: 1,
            store_id: 123,
            store_item_id: 'PROD001',
            type_id: 'TYPE001',
            image_url: 'https://example.com/image.jpg',
            description: 'This is a sample product description',
            status: 'ACTIVE',
            update_at: new Date().toISOString(),
          };
        case 'orders':
          return {
            id: 'sample-order-id',
            order_code: 'ORD001',
            customer_id: 'CUST001',
            customer_name: 'John Doe',
            customer_phone: '0123456789',
            customer_email: '<EMAIL>',
            total_amount: 250000,
            discount_amount: 20000,
            tax_amount: 10000,
            status: 'COMPLETED',
            payment_status: 'PAID',
            payment_method: 'CASH',
            note: 'Sample order note',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            items: [
              {
                id: 'ITEM001',
                product_id: 'PROD001',
                product_name: 'Sample Product',
                quantity: 2,
                price: 100000,
                total_amount: 200000,
              },
            ],
          };
        case 'customers':
          return {
            id: 'sample-customer-id',
            name: 'John Doe',
            phone: '0123456789',
            email: '<EMAIL>',
            address: '123 Sample Street',
            city: 'Sample City',
            province: 'Sample Province',
            country: 'Vietnam',
            postal_code: '10000',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
        default:
          return {};
      }
    case 'sapo':
      switch (resourceType) {
        case 'products':
          return {
            id: 'sample-product-id',
            name: 'Sample Product',
            description: 'This is a sample product description',
            price: 100000,
            compare_at_price: 120000,
            sku: 'SKU001',
            barcode: '1234567890',
            image_url: 'https://example.com/image.jpg',
            category_id: 'CAT001',
            status: 'active',
            stock_quantity: 100,
            weight: 1.5,
            dimensions: { length: 10, width: 5, height: 2 },
            metadata: { color: 'red', size: 'M' },
          };
        case 'orders':
          return {
            id: 'sample-order-id',
            customer_id: 'CUST001',
            order_number: 'ORD001',
            total_amount: 250000,
            discount_amount: 20000,
            tax_amount: 10000,
            status: 'completed',
            payment_method: 'cash',
            payment_status: 'paid',
            shipping_address: {
              name: 'John Doe',
              address: '123 Sample Street',
              city: 'Sample City',
              province: 'Sample Province',
              country: 'Vietnam',
              postal_code: '10000',
            },
            billing_address: {
              name: 'John Doe',
              address: '123 Sample Street',
              city: 'Sample City',
              province: 'Sample Province',
              country: 'Vietnam',
              postal_code: '10000',
            },
            notes: 'Sample order note',
            metadata: { source: 'web' },
          };
        case 'customers':
          return {
            id: 'sample-customer-id',
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '0123456789',
            address: '123 Sample Street',
            city: 'Sample City',
            province: 'Sample Province',
            country: 'Vietnam',
            postal_code: '10000',
            metadata: { group: 'regular' },
          };
        default:
          return {};
      }
    case 'misa-eshop':
      switch (resourceType) {
        case 'products':
          return {
            id: 'sample-product-id',
            code: 'PROD001',
            name: 'Sample Product',
            description: 'This is a sample product description',
            price: 100000,
            cost_price: 80000,
            sku: 'SKU001',
            barcode: '1234567890',
            image_url: 'https://example.com/image.jpg',
            category_id: 'CAT001',
            category_name: 'Sample Category',
            status: 'active',
            stock_quantity: 100,
            weight: 1.5,
            unit: 'piece',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
        case 'orders':
          return {
            id: 'sample-order-id',
            code: 'ORD001',
            customer_id: 'CUST001',
            customer_name: 'John Doe',
            customer_phone: '0123456789',
            customer_email: '<EMAIL>',
            total_amount: 250000,
            discount_amount: 20000,
            tax_amount: 10000,
            status: 'completed',
            payment_status: 'paid',
            payment_method: 'cash',
            shipping_address: {
              name: 'John Doe',
              address: '123 Sample Street',
              city: 'Sample City',
              district: 'Sample District',
              ward: 'Sample Ward',
              country: 'Vietnam',
              postal_code: '10000',
            },
            billing_address: {
              name: 'John Doe',
              address: '123 Sample Street',
              city: 'Sample City',
              district: 'Sample District',
              ward: 'Sample Ward',
              country: 'Vietnam',
              postal_code: '10000',
            },
            note: 'Sample order note',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
        case 'customers':
          return {
            id: 'sample-customer-id',
            code: 'CUST001',
            name: 'John Doe',
            phone: '0123456789',
            email: '<EMAIL>',
            address: '123 Sample Street',
            city: 'Sample City',
            district: 'Sample District',
            ward: 'Sample Ward',
            country: 'Vietnam',
            postal_code: '10000',
            group_id: 'GROUP001',
            group_name: 'Regular Customers',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
        default:
          return {};
      }
    case 'kiotviet':
      switch (resourceType) {
        case 'products':
          return {
            id: 'sample-product-id',
            code: 'PROD001',
            name: 'Sample Product',
            fullName: 'Sample Product Full Name',
            categoryId: 'CAT001',
            categoryName: 'Sample Category',
            basePrice: 100000,
            price: 120000,
            description: 'This is a sample product description',
            unit: 'piece',
            barcode: '1234567890',
            images: ['https://example.com/image.jpg'],
            attributes: { color: 'red', size: 'M' },
            inventories: [{ branchId: 'BR001', quantity: 100 }],
            isActive: true,
          };
        case 'orders':
          return {
            id: 'sample-order-id',
            code: 'ORD001',
            purchaseDate: new Date().toISOString(),
            branchId: 'BR001',
            branchName: 'Sample Branch',
            customerId: 'CUST001',
            customerName: 'John Doe',
            customerCode: 'C001',
            total: 250000,
            discount: 20000,
            status: 1,
            statusValue: 'Completed',
            description: 'Sample order description',
            orderDetails: [
              {
                productId: 'PROD001',
                productName: 'Sample Product',
                quantity: 2,
                price: 100000,
                total: 200000,
              },
            ],
            payments: [
              {
                method: 'Cash',
                amount: 250000,
              },
            ],
          };
        case 'customers':
          return {
            id: 'sample-customer-id',
            code: 'C001',
            name: 'John Doe',
            contactNumber: '0123456789',
            email: '<EMAIL>',
            address: '123 Sample Street',
            birthDate: '1990-01-01',
            gender: 1,
            rewardPoint: 100,
            debt: 0,
            groups: ['Regular'],
          };
        default:
          return {};
      }
    default:
      return {};
  }
}
