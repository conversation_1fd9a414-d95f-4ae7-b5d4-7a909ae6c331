import { NextRequest } from 'next/server';
import { POST, GET, PATCH, DELETE } from '../setup/route';

// Mock dependencies
jest.mock('@kit/shared/logger', () => ({
  getLogger: jest.fn().mockResolvedValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  }),
}));

jest.mock('@kit/supabase/server-client', () => ({
  getSupabaseServerClient: jest.fn().mockReturnValue({
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
  }),
}));

jest.mock('~/lib/check-resource-access', () => ({
  checkResourceAccess: jest.fn().mockResolvedValue({ allowed: true }),
}));

jest.mock('~/lib/integrations/connectors/connector-factory', () => ({
  getConnectorFactory: jest.fn().mockReturnValue({
    createConnector: jest.fn().mockResolvedValue({
      authenticate: jest.fn().mockResolvedValue(true),
    }),
  }),
}));

jest.mock('~/lib/integrations/utils/crypto', () => ({
  encrypt: jest.fn().mockReturnValue('encrypted_credentials'),
  decrypt: jest.fn().mockReturnValue({ key: 'value' }),
}));

describe('Setup API Endpoints', () => {
  let mockRequest: NextRequest;
  let mockJson: jest.Mock;
  let mockSupabase: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock request
    mockJson = jest.fn();
    mockRequest = {
      json: mockJson,
      nextUrl: {
        searchParams: new URLSearchParams(),
      },
    } as unknown as NextRequest;

    // Mock Supabase response
    mockSupabase = require('@kit/supabase/server-client').getSupabaseServerClient();
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: { id: 'integration_id', type: 'kiotviet' },
      error: null,
    });
    mockSupabase.from().insert().select().single.mockResolvedValue({
      data: { id: 'integration_id', type: 'kiotviet' },
      error: null,
    });
    mockSupabase.from().update().eq().mockResolvedValue({
      data: { id: 'integration_id', type: 'kiotviet' },
      error: null,
    });
    mockSupabase.from().delete().eq().mockResolvedValue({
      error: null,
    });
    mockSupabase.from().select().eq().mockResolvedValue({
      data: [{ id: 'integration_id', type: 'kiotviet' }],
      error: null,
    });
  });

  describe('POST /api/integrations/setup', () => {
    test('should create a new integration', async () => {
      // Mock request data
      mockJson.mockResolvedValue({
        accountId: 'account_id',
        platform: 'kiotviet',
        credentials: { client_id: 'test' },
        name: 'KiotViet Integration',
        description: 'Test integration',
        config: { exclusive_resources: { products: 'kiotviet' } },
      });

      // Call the endpoint
      const response = await POST(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.integration).toBeDefined();
      expect(mockSupabase.from().insert).toHaveBeenCalled();
    });

    test('should return error if authentication fails', async () => {
      // Mock request data
      mockJson.mockResolvedValue({
        accountId: 'account_id',
        platform: 'kiotviet',
        credentials: { client_id: 'test' },
        name: 'KiotViet Integration',
      });

      // Mock authentication failure
      require('~/lib/integrations/connectors/connector-factory')
        .getConnectorFactory()
        .createConnector()
        .authenticate.mockResolvedValue(false);

      // Call the endpoint
      const response = await POST(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Authentication failed');
    });
  });

  describe('GET /api/integrations/setup', () => {
    test('should get integrations', async () => {
      // Mock URL parameters
      mockRequest.nextUrl.searchParams.append('accountId', 'account_id');

      // Call the endpoint
      const response = await GET(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.integrations).toBeDefined();
      expect(mockSupabase.from().select).toHaveBeenCalled();
    });

    test('should return error if accountId is missing', async () => {
      // Call the endpoint without accountId
      const response = await GET(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Missing parameter');
    });
  });

  describe('PATCH /api/integrations/setup', () => {
    test('should update an integration', async () => {
      // Mock request data
      mockJson.mockResolvedValue({
        accountId: 'account_id',
        integrationId: 'integration_id',
        name: 'Updated Integration',
      });

      // Call the endpoint
      const response = await PATCH(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(mockSupabase.from().update).toHaveBeenCalled();
    });

    test('should return error if integrationId is missing', async () => {
      // Mock request data without integrationId
      mockJson.mockResolvedValue({
        accountId: 'account_id',
        name: 'Updated Integration',
      });

      // Call the endpoint
      const response = await PATCH(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Missing parameter');
    });
  });

  describe('DELETE /api/integrations/setup', () => {
    test('should delete an integration', async () => {
      // Mock URL parameters
      mockRequest.nextUrl.searchParams.append('accountId', 'account_id');
      mockRequest.nextUrl.searchParams.append('integrationId', 'integration_id');

      // Call the endpoint
      const response = await DELETE(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(mockSupabase.from().delete).toHaveBeenCalled();
    });

    test('should return error if parameters are missing', async () => {
      // Call the endpoint without parameters
      const response = await DELETE(mockRequest);
      const responseData = await response.json();

      // Assertions
      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Missing parameter');
    });
  });
});
