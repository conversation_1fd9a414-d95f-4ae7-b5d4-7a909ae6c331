import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { checkResourceAccess } from '~/lib/check-resource-access';

/**
 * API endpoint để lấy mappings
 * GET /api/integrations/mapping?integrationId=xxx&resourceType=yyy
 */
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const integrationId = searchParams.get('integrationId');
    const resourceType = searchParams.get('resourceType');

    if (!integrationId || !resourceType) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'integrationId and resourceType are required' },
        { status: 400 }
      );
    }

    // Lấy thông tin integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('account_id')
      .eq('id', integrationId)
      .single();

    if (integrationError || !integration) {
      logger.error({ error: integrationError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(integration.account_id, 'integrations', 'view');
    if (!accessCheck.allowed) {
      logger.warn({ integrationId }, 'Access denied to view mappings');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Lấy mappings
    const { data: mappings, error: mappingsError } = await supabase
      .from('integration_mappings')
      .select('*')
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType)
      .eq('is_active', true);

    if (mappingsError) {
      logger.error({ error: mappingsError }, 'Error fetching mappings');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to fetch mappings' },
        { status: 500 }
      );
    }

    return NextResponse.json({ mappings });
  } catch (error: any) {
    logger.error({ error }, 'Error getting mappings');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * API endpoint để lưu mappings
 * POST /api/integrations/mapping
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy dữ liệu từ request
    const { accountId, integrationId, resourceType, mappings } = await request.json();

    if (!accountId || !integrationId || !resourceType || !mappings) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId, integrationId, resourceType, and mappings are required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'manage');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to manage mappings');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Kiểm tra integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (integrationError || !integration) {
      logger.error({ error: integrationError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Vô hiệu hóa tất cả mappings hiện tại
    const { error: disableError } = await supabase
      .from('integration_mappings')
      .update({ is_active: false })
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType);

    if (disableError) {
      logger.error({ error: disableError }, 'Error disabling existing mappings');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to update mappings' },
        { status: 500 }
      );
    }

    // Thêm mappings mới
    const newMappings = mappings.map((mapping: any, index: number) => ({
      integration_id: integrationId,
      resource_type: resourceType,
      source_field: mapping.source_field,
      target_field: mapping.target_field,
      transform_function: mapping.transform_function || null,
      is_required: mapping.is_required || false,
      sort_order: index,
      is_active: true,
    }));

    const { error: insertError } = await supabase
      .from('integration_mappings')
      .insert(newMappings);

    if (insertError) {
      logger.error({ error: insertError }, 'Error inserting mappings');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to save mappings' },
        { status: 500 }
      );
    }

    logger.info({ integrationId, resourceType }, 'Mappings saved successfully');
    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error({ error }, 'Error saving mappings');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}
