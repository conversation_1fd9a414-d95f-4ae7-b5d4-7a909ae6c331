import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { checkResourceAccess } from '~/lib/check-resource-access';
import { getScheduler } from '~/lib/integrations/jobs/scheduler';

/**
 * API endpoint để cập nhật lịch trình đồng bộ
 * POST /api/integrations/schedule
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // L<PERSON>y dữ li<PERSON>u từ request
    const { accountId, integrationId, resourceType, schedule } = await request.json();

    if (!accountId || !integrationId || !resourceType || !schedule) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId, integrationId, resourceType, and schedule are required' },
        { status: 400 }
      );
    }

    // Ki<PERSON>m tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'manage');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to manage integration schedule');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Kiểm tra integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id, config')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (integrationError || !integration) {
      logger.error({ error: integrationError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Cập nhật config
    const config = integration.config || {};
    config.sync_schedules = config.sync_schedules || {};
    config.sync_schedules[resourceType] = schedule;

    // Cập nhật integration
    const { error: updateError } = await supabase
      .from('integrations')
      .update({ config })
      .eq('id', integrationId)
      .eq('account_id', accountId);

    if (updateError) {
      logger.error({ error: updateError }, 'Error updating integration schedule');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to update integration schedule' },
        { status: 500 }
      );
    }

    // Cập nhật lịch trình
    const scheduler = getScheduler();
    await scheduler.updateSchedule(integrationId);

    logger.info({ integrationId, resourceType }, 'Integration schedule updated');

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error({ error }, 'Error updating integration schedule');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * API endpoint để lấy lịch trình đồng bộ
 * GET /api/integrations/schedule?accountId=xxx&integrationId=yyy
 */
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const integrationId = searchParams.get('integrationId');

    if (!accountId || !integrationId) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId and integrationId are required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'view');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to view integration schedule');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Lấy thông tin integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id, config')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (integrationError || !integration) {
      logger.error({ error: integrationError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Lấy lịch trình
    const config = integration.config || {};
    const schedules = config.sync_schedules || {};

    return NextResponse.json({ schedules });
  } catch (error: any) {
    logger.error({ error }, 'Error getting integration schedule');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * API endpoint để xóa lịch trình đồng bộ
 * DELETE /api/integrations/schedule?accountId=xxx&integrationId=yyy&resourceType=zzz
 */
export async function DELETE(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const integrationId = searchParams.get('integrationId');
    const resourceType = searchParams.get('resourceType');

    if (!accountId || !integrationId || !resourceType) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId, integrationId, and resourceType are required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'manage');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to delete integration schedule');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Kiểm tra integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id, config')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (integrationError || !integration) {
      logger.error({ error: integrationError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Cập nhật config
    const config = integration.config || {};
    if (config.sync_schedules && config.sync_schedules[resourceType]) {
      delete config.sync_schedules[resourceType];
    }

    // Cập nhật integration
    const { error: updateError } = await supabase
      .from('integrations')
      .update({ config })
      .eq('id', integrationId)
      .eq('account_id', accountId);

    if (updateError) {
      logger.error({ error: updateError }, 'Error updating integration schedule');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to delete integration schedule' },
        { status: 500 }
      );
    }

    // Dừng lịch trình
    const scheduler = getScheduler();
    scheduler.stopJob(integrationId, resourceType);

    logger.info({ integrationId, resourceType }, 'Integration schedule deleted');

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error({ error }, 'Error deleting integration schedule');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}
