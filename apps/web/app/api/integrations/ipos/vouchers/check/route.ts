import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/logger';
import { prisma } from '@kit/db';
import { IPOSConnector } from '@/lib/integrations/connectors/ipos/ipos-connector';

const logger = getLogger({ service: 'api-ipos-vouchers-check' });

/**
 * API bắc cầu để kiểm tra voucher từ iPOS
 * @param request Request
 */
export async function GET(request: NextRequest) {
  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const voucherCode = searchParams.get('voucherCode');
    const posId = searchParams.get('posId');
    const buyerInfo = searchParams.get('buyerInfo');

    // Kiểm tra tham số bắt buộc
    if (!accountId || !voucherCode || !posId || !buyerInfo) {
      return NextResponse.json(
        { success: false, message: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Lấy thông tin tích hợp iPOS
    const integration = await prisma.integrations.findFirst({
      where: {
        account_id: accountId,
        platform: 'ipos',
        status: 'active'
      }
    });

    if (!integration) {
      return NextResponse.json(
        { success: false, message: 'No active iPOS integration found' },
        { status: 404 }
      );
    }

    // Lấy cấu hình từ integration
    const config = integration.config as any;
    const credentials = {
      access_token: config.access_token,
      pos_parent: config.pos_parent,
      pos_id: config.pos_id || posId,
      baseUrl: config.baseUrl || 'https://api.foodbook.vn'
    };

    // Khởi tạo connector
    const connector = new IPOSConnector(credentials, logger);

    // Gọi API iPOS để kiểm tra voucher
    const response = await connector.checkVoucher(voucherCode, posId, buyerInfo);

    // Trả về kết quả
    return NextResponse.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    logger.error({ error }, 'Error checking voucher from iPOS');
    return NextResponse.json(
      { success: false, message: (error as Error).message },
      { status: 500 }
    );
  }
}
