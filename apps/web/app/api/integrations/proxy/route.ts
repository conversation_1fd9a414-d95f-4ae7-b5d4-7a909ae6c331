import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { createConnectorFromIntegrationId } from '~/lib/integrations/connectors/connector-factory';
import { checkResourceAccess } from '~/lib/check-resource-access';

/**
 * API endpoint để bắc cầu API đến bên thứ 3
 * POST /api/integrations/proxy
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy dữ liệu từ request
    const { accountId, integrationId, resourceType, action, payload } = await request.json();

    if (!accountId || !integrationId || !resourceType || !action) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId, integrationId, resourceType, and action are required' },
        { status: 400 }
      );
    }

    // Ki<PERSON>m tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'use');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to use integration proxy');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Lấy thông tin tích hợp
    const { data: integration, error: fetchError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (fetchError || !integration) {
      logger.error({ error: fetchError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Kiểm tra trạng thái tích hợp
    if (integration.status !== 'connected' || !integration.enabled) {
      logger.warn({ integrationId }, 'Integration is not connected or disabled');
      return NextResponse.json(
        { error: 'Integration unavailable', message: 'Integration is not connected or disabled' },
        { status: 400 }
      );
    }

    // Tạo connector
    const connector = await createConnectorFromIntegrationId(integrationId);
    if (!connector) {
      logger.error({ integrationId }, 'Failed to create connector');
      return NextResponse.json(
        { error: 'Connector error', message: 'Failed to create connector' },
        { status: 500 }
      );
    }

    // Gọi API bên thứ 3
    try {
      const result = await connector.proxyRequest(resourceType, action, payload);

      // Lưu lịch sử gọi API
      await supabase.from('integration_proxy_logs').insert({
        integration_id: integrationId,
        resource_type: resourceType,
        action,
        request_payload: payload,
        response_payload: result,
        created_by: payload.userId,
      });

      return NextResponse.json({
        success: true,
        result,
      });
    } catch (error: any) {
      logger.error({ error, integrationId, resourceType, action }, 'Error proxying request');

      // Lưu lịch sử gọi API
      await supabase.from('integration_proxy_logs').insert({
        integration_id: integrationId,
        resource_type: resourceType,
        action,
        request_payload: payload,
        error_message: error.message,
        created_by: payload.userId,
      });

      return NextResponse.json(
        { error: 'Proxy error', message: error.message },
        { status: 500 }
      );
    }
  } catch (error: any) {
    logger.error({ error }, 'Error in proxy endpoint');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}
