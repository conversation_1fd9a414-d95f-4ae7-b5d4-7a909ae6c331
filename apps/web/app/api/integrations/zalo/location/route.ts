import { NextResponse } from 'next/server';

import axios from 'axios';
import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';

// Schema for request body
const LocationRequestSchema = z.object({
  code: z.string(), // Token from Zalo getLocation API
  access_token: z.string(), // Access token from Zalo SDK
  theme_id: z.string().uuid().optional(), // Theme ID is optional
});

// Sử dụng enhanceRouteHandler để xử lý xác thực
export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const logger = await getLogger();

      // Parse request body
      const body = await request.json();
      const { code, access_token, theme_id } =
        LocationRequestSchema.parse(body);

      // Get account_id from user metadata
      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return NextResponse.json(
          { success: false, error: 'No account_id found in token' },
          { status: 401 },
        );
      }

      // Get OA configuration
      let oaConfig;

      if (theme_id) {
        // If theme_id is provided, try to get OA configuration from the theme
        const { data: accountTheme, error: themeError } = await supabase
          .from('account_themes')
          .select('oa_config_id')
          .eq('id', theme_id)
          .single();

        if (!themeError && accountTheme?.oa_config_id) {
          // Get OA configuration by ID
          const { data: themeOaConfig, error: oaError } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('id', accountTheme.oa_config_id)
            .single();

          if (!oaError && themeOaConfig?.app_id) {
            oaConfig = themeOaConfig;
          }
        }
      }

      // If no OA configuration found from theme, try to get the default one
      if (!oaConfig) {
        // First try to get the system default OA configuration
        const { data: defaultOaConfig, error: defaultOaError } = await supabase
          .from('oa_configurations')
          .select('*')
          .eq('account_id', account_id)
          .eq('is_system_default', true)
          .single();

        if (!defaultOaError && defaultOaConfig?.app_id) {
          oaConfig = defaultOaConfig;
        } else {
          // Fallback to any OA configuration for the account
          const { data: accountOaConfig, error: oaError } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('account_id', account_id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          if (oaError || !accountOaConfig?.app_id) {
            logger.error(
              { account_id, error: oaError },
              'Failed to get OA configuration',
            );
            return NextResponse.json(
              { success: false, error: 'OA configuration not found' },
              { status: 404 },
            );
          }

          oaConfig = accountOaConfig;
        }
      }

      // Get location using the Zalo API
      const locationResponse = await axios.get(
        'https://graph.zalo.me/v2.0/me/info',
        {
          headers: {
            access_token: access_token,
            code: code,
            secret_key: oaConfig.secret_key,
          },
        },
      );

      if (
        locationResponse.status !== 200 ||
        locationResponse.data.error !== 0
      ) {
        logger.error(
          { account_id, response: locationResponse.data },
          'Failed to get location from Zalo',
        );
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to get location',
            details: locationResponse.data,
          },
          { status: 500 },
        );
      }

      const locationData = locationResponse.data.data;

      if (!locationData || !locationData.latitude || !locationData.longitude) {
        logger.error(
          { account_id, response: locationResponse.data },
          'Location data not available',
        );
        return NextResponse.json(
          {
            success: false,
            error: 'Location data not available',
            details: locationResponse.data,
          },
          { status: 400 },
        );
      }

      // Không cập nhật user metadata với thông tin vị trí
      // Chỉ trả về thông tin vị trí cho client

      // Return the location data
      return NextResponse.json({
        success: true,
        data: {
          location: {
            latitude: locationData.latitude,
            longitude: locationData.longitude,
            provider: locationData.provider,
            timestamp: locationData.timestamp
          }
        },
      });
    } catch (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Error getting location from Zalo');

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 },
        );
      }

      return NextResponse.json(
        { success: false, error: 'Internal server error' },
        { status: 500 },
      );
    }
  },
  { auth: true },
);
