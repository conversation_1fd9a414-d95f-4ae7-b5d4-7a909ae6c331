import { NextRequest, NextResponse } from 'next/server';

import { getLogger } from '@kit/shared/logger';
import { isUUID } from '@kit/shared/uuid';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getAccessTokenFromCode, getZaloOfficialAccount } from '@kit/zns';

/**
 * API endpoint để nhận webhook từ bên thứ 3 và xử lý callback OAuth
 * GET /api/integrations/zalo/callback - Xử lý OAuth callback
 * POST /api/integrations/zalo/callback - Xử lý webhook events
 */

// Xử lý OAuth callback
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerAdminClient();

  try {
    // Lấy code và state từ query params
    const searchParams = request.nextUrl.searchParams;
    console.log('searchParams', searchParams);
    const code = searchParams.get('code');
    const oaId = searchParams.get('oa_id');
    const state = searchParams.get('state');

    // Lấy thông tin từ state
    let accountId = '';
    let oaConfigId = null;
    let returnPath = null;

    try {
      // Giải mã state
      const stateData = JSON.parse(decodeURIComponent(state || '{}'));
      accountId = stateData.accountId || '';
      oaConfigId = stateData.oaConfigId || null;
      returnPath = stateData.returnPath || null;

      logger.info(
        { accountId, oaConfigId, returnPath },
        'Successfully parsed state data',
      );
    } catch (error) {
      // Nếu state không phải JSON, sử dụng nó như accountId (cho tương thích ngược)
      accountId = state || '';
      logger.warn(
        { state, error },
        'Failed to parse state as JSON, using as accountId for backward compatibility',
      );
    }

    if (!code || !accountId) {
      logger.error({ code, accountId }, 'Missing code or state in callback');
      return NextResponse.redirect(
        new URL('/error?message=Missing+required+parameters', request.url),
      );
    }

    // Kiểm tra xem accountId có phải là UUID không
    let realAccountId = accountId;

    if (!isUUID(accountId)) {
      // Nếu là slug, lấy UUID tương ứng
      const { data: account, error: accountError } = await supabase
        .from('accounts')
        .select('id')
        .eq('slug', accountId)
        .single();

      if (accountError || !account) {
        logger.error(
          { accountId, error: accountError },
          'Failed to get account by slug',
        );
        return NextResponse.redirect(
          new URL('/error?message=Account+not+found', request.url),
        );
      }

      realAccountId = account.id;
    }

    // Lấy thông tin OA configuration
    let finalOaConfig;

    // Nếu có oaConfigId từ state, sử dụng nó để lấy OA configuration
    if (oaConfigId) {
      const { data: existingOaConfig, error: oaError } = await supabase
        .from('oa_configurations')
        .select('*')
        .eq('id', oaConfigId)
        .single();

      if (oaError || !existingOaConfig) {
        logger.error(
          { accountId, oaConfigId, error: oaError },
          'Failed to get OA configuration by ID',
        );
      } else {
        finalOaConfig = existingOaConfig;
        logger.info(
          { accountId, oaConfigId },
          'Found existing OA configuration by ID',
        );
      }
    }

    // Nếu không có oaConfigId hoặc không tìm thấy, tìm theo account_id
    if (!finalOaConfig) {
      const { data: accountOaConfig, error: oaError } = await supabase
        .from('oa_configurations')
        .select('*')
        .eq('account_id', realAccountId)
        .maybeSingle();

      if (oaError) {
        logger.error(
          { accountId, error: oaError },
          'Failed to get OA configuration by account_id',
        );
      } else if (accountOaConfig) {
        finalOaConfig = accountOaConfig;
        logger.info(
          { accountId },
          'Found existing OA configuration by account_id',
        );
      }
    }

    // Nếu không tìm thấy OA configuration, trả về lỗi
    if (!finalOaConfig) {
      logger.error(
        { accountId, oaConfigId },
        'No existing OA configuration found. Webhook requires an existing OA configuration.',
      );

      return NextResponse.redirect(
        new URL(
          '/error?message=No+existing+OA+configuration+found',
          request.url,
        ),
      );
    }

    // // Lấy code verifier từ metadata
    // const codeVerifier = finalOaConfig.oa_metadata?.code_verifier;
    //
    // if (!codeVerifier) {
    //   logger.error({ accountId }, 'Code verifier not found');
    //   return NextResponse.redirect(
    //     new URL('/error?message=Authentication+failed', request.url),
    //   );
    // }

    // Trao đổi code lấy access token sử dụng hàm từ package zns
    let tokenInfo;
    try {
      tokenInfo = await getAccessTokenFromCode(
        finalOaConfig.app_id,
        finalOaConfig.secret_key,
        code,
        // codeVerifier,
      );

      logger.info(
        { accountId, tokenInfo },
        'Successfully exchanged code for token',
      );
    } catch (error: any) {
      logger.error(
        {
          accountId,
          error: error.message,
          code,
          app_id: finalOaConfig.app_id,
        },
        'Failed to exchange code for token',
      );

      return NextResponse.redirect(
        new URL(
          `/error?message=Failed+to+connect+Zalo+OA&error=${encodeURIComponent(error.message)}`,
          request.url,
        ),
      );
    }

    // Lấy thông tin OA sử dụng hàm từ packages/zns
    let oaInfo = null;

    try {
      // Lấy thông tin OA (chứa cả thông tin người dùng)
      const oaResponse = await getZaloOfficialAccount(tokenInfo.accessToken);
      oaInfo = oaResponse.data;
      logger.info({ oaInfo }, 'Successfully fetched Zalo OA information');
    } catch (error: any) {
      logger.warn(
        { error: error.message },
        'Failed to fetch Zalo OA information, continuing without OA info',
      );
    }

    // Sử dụng thông tin token từ tokenInfo
    const {
      accessToken: access_token,
      refreshToken: refresh_token,
      expiresIn: expires_in,
    } = tokenInfo;

    // Tính thời gian hết hạn
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + (expires_in || 90000)); // Mặc định 25 giờ nếu không có expiresIn

    // Cập nhật OA configuration
    const { error: updateError } = await supabase
      .from('oa_configurations')
      .update({
        access_token,
        refresh_token,
        token_expires_at: expiresAt.toISOString(),
        oa_id: oaId || finalOaConfig.oa_id,
        oa_metadata: {
          ...finalOaConfig.oa_metadata,
          last_connected: new Date().toISOString(),
          connected_by: accountId,
          token_validated: true,
          user_info: oaInfo || null, // Thêm thông tin OA vào metadata
          token_info: {
            expires_in,
            connected_at: new Date().toISOString(),
            expires_at: expiresAt.toISOString(),
          },
        },
      })
      .eq('id', finalOaConfig.id);

    if (updateError) {
      logger.error(
        { accountId, error: updateError },
        'Failed to update tokens',
      );
      return NextResponse.redirect(
        new URL('/error?message=Failed+to+save+connection', request.url),
      );
    }

    // Cập nhật ZNS integration
    const { error: integrationError } = await supabase
      .from('integrations')
      .upsert({
        account_id: realAccountId,
        type: 'zalo',
        name: 'Zalo Notification Service',
        status: 'connected',
        enabled: true,
        metadata: {
          oa_config_id: finalOaConfig.id,
          oa_id: oaId || finalOaConfig.oa_id,
        },
      });

    if (integrationError) {
      logger.error(
        { accountId, error: integrationError },
        'Failed to update integration',
      );
      // Tiếp tục vì đã lưu được token
    }

    // Lấy slug của account nếu có
    let accountSlug = accountId;
    // Sử dụng lại biến isUUID đã khai báo ở trên
    const isAccountIdUUID =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        accountId,
      );

    if (isAccountIdUUID) {
      // Nếu accountId là UUID, lấy slug tương ứng
      const { data: account } = await supabase
        .from('accounts')
        .select('slug')
        .eq('id', accountId)
        .single();

      if (account?.slug) {
        accountSlug = account.slug;
      }
    }

    // Xác định trang chuyển hướng dựa trên returnPath hoặc loại OA configuration
    let redirectUrl: string;

    if (returnPath) {
      // Nếu có returnPath, chuyển hướng về trang đó
      logger.info({ returnPath }, 'Redirecting to return path');
      redirectUrl = `${returnPath}?success=true`;
    } else {
      redirectUrl = `/home/<USER>/integrations?success=true`;
    }

    return NextResponse.redirect(new URL(redirectUrl, request.url));
  } catch (error: any) {
    logger.error(
      { error: error.message, stack: error.stack },
      'Error processing Zalo OA callback',
    );
    return NextResponse.redirect(
      new URL('/error?message=Connection+failed', request.url),
    );
  }
}

// Xử lý webhook events
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerAdminClient();

  try {
    // Lấy dữ liệu webhook từ request body
    const webhookData = await request.json();

    // Log dữ liệu webhook để debug
    logger.info({ webhookData }, 'Received Zalo webhook');

    // Xử lý các loại sự kiện khác nhau
    const eventName = webhookData.event_name;
    const eventData = webhookData.event_data;

    // Lưu lại webhook event vào database để theo dõi
    await supabase.from('webhook_events').insert({
      provider: 'zalo',
      event_type: eventName,
      payload: webhookData,
      status: 'received',
    });

    // Trả về 200 OK để Zalo biết webhook đã được nhận
    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error(
      { error: error.message, stack: error.stack },
      'Error processing Zalo webhook',
    );

    // Vẫn trả về 200 OK để Zalo không gửi lại webhook
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
    });
  }
}
