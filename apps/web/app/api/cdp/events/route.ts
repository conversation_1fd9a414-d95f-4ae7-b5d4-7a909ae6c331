import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { events, batch = false } = body;

      if (!events) {
        return NextResponse.json(
          {
            success: false,
            error: 'Events data is required',
            code: 'MISSING_EVENTS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Event Processor Service
      const eventProcessor = cdp.getService('eventProcessor');

      let results;

      if (batch && Array.isArray(events)) {
        // Process multiple events
        results = await eventProcessor.processBatchEvents(events);
      } else if (Array.isArray(events)) {
        // Process each event individually
        results = await Promise.all(
          events.map(event => eventProcessor.processEvent(event))
        );
      } else {
        // Process single event
        results = await eventProcessor.processEvent(events);
      }

      return NextResponse.json({
        success: true,
        data: results,
        processed: Array.isArray(results) ? results.length : 1
      });

    } catch (error) {
      console.error('CDP Events API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const customerId = url.searchParams.get('customerId');
      const eventType = url.searchParams.get('eventType');
      const limit = parseInt(url.searchParams.get('limit') || '100');
      const offset = parseInt(url.searchParams.get('offset') || '0');

      // Build query
      let query = supabase
        .from('analytics_events')
        .select('*')
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (customerId) {
        query = query.eq('customer_profile_id', customerId);
      }

      if (eventType) {
        query = query.eq('event_type', eventType);
      }

      const { data: events, error, count } = await query;

      if (error) {
        throw new Error(error.message);
      }

      return NextResponse.json({
        success: true,
        data: events || [],
        total: count || 0,
        limit,
        offset
      });

    } catch (error) {
      console.error('CDP Events Get API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
