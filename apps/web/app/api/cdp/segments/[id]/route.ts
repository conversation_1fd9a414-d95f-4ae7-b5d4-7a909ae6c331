import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

interface RouteParams {
  params: Promise<{ id: string }>;
}

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user, params }: { request: NextRequest; supabase: any; user: any; params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Segmentation Service
      const segmentationService = cdp.getService('segmentation');

      // Get segment
      const segment = await segmentationService.getSegment(id);

      return NextResponse.json({
        success: true,
        data: segment
      });

    } catch (error) {
      console.error('CDP Segment API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const PUT = enhanceRouteHandler(
  async ({ request, supabase, user, params }: { request: NextRequest; supabase: any; user: any; params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Segmentation Service
      const segmentationService = cdp.getService('segmentation');

      // Update segment
      const segment = await segmentationService.updateSegment(id, body);

      return NextResponse.json({
        success: true,
        data: segment
      });

    } catch (error) {
      console.error('CDP Segment API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const DELETE = enhanceRouteHandler(
  async ({ request, supabase, user, params }: { request: NextRequest; supabase: any; user: any; params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Segmentation Service
      const segmentationService = cdp.getService('segmentation');

      // Delete segment
      await segmentationService.deleteSegment(id);

      return NextResponse.json({
        success: true,
        message: 'Segment deleted successfully'
      });

    } catch (error) {
      console.error('CDP Segment API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
