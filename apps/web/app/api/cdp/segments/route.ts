import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { name, description, criteria, type = 'dynamic', color, icon, tags } = body;

      if (!name || !criteria) {
        return NextResponse.json(
          {
            success: false,
            error: 'Name and criteria are required',
            code: 'MISSING_REQUIRED_FIELDS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Segmentation Service
      const segmentationService = cdp.getService('segmentation');

      // Create segment
      const segment = await segmentationService.createSegment(
        user.user_metadata?.account_id || user.id,
        {
          name,
          description,
          criteria,
          type,
          color: color || '#3B82F6',
          icon: icon || 'users',
          is_active: true,
          tags: tags || []
        }
      );

      return NextResponse.json({
        success: true,
        data: segment
      });

    } catch (error) {
      console.error('CDP Segments API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const limit = parseInt(url.searchParams.get('limit') || '50');
      const offset = parseInt(url.searchParams.get('offset') || '0');
      const isActive = url.searchParams.get('isActive');
      const type = url.searchParams.get('type') as 'static' | 'dynamic' | undefined;

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Segmentation Service
      const segmentationService = cdp.getService('segmentation');

      // List segments
      const result = await segmentationService.listSegments(
        user.user_metadata?.account_id || user.id,
        {
          limit,
          offset,
          isActive: isActive ? isActive === 'true' : undefined,
          type
        }
      );

      return NextResponse.json({
        success: true,
        data: result.segments,
        total: result.total,
        limit,
        offset
      });

    } catch (error) {
      console.error('CDP Segments API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
