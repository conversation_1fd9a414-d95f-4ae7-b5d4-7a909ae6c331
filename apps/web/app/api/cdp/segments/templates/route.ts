import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const category = url.searchParams.get('category');

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Segmentation Service
      const segmentationService = cdp.getService('segmentation');

      // Get segment templates
      const templates = await segmentationService.getSegmentTemplates(category || undefined);

      return NextResponse.json({
        success: true,
        data: templates
      });

    } catch (error) {
      console.error('CDP Segment Templates API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { templateId, variables } = body;

      if (!templateId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Template ID is required',
            code: 'MISSING_TEMPLATE_ID'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Segmentation Service
      const segmentationService = cdp.getService('segmentation');

      // Get template
      const templates = await segmentationService.getSegmentTemplates();
      const template = templates.find(t => t.id === templateId);

      if (!template) {
        return NextResponse.json(
          {
            success: false,
            error: 'Template not found',
            code: 'TEMPLATE_NOT_FOUND'
          },
          { status: 404 }
        );
      }

      // Apply variables to template criteria
      let criteria = template.criteria;
      if (variables && template.variables) {
        criteria = this.applyVariablesToCriteria(criteria, variables, template.variables);
      }

      // Create segment from template
      const segment = await segmentationService.createSegment(
        user.user_metadata?.account_id || user.id,
        {
          name: template.name,
          description: template.description,
          criteria,
          type: 'dynamic',
          is_active: true,
          tags: [...template.tags, 'from-template']
        }
      );

      return NextResponse.json({
        success: true,
        data: segment
      });

    } catch (error) {
      console.error('CDP Segment Templates API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

// Helper function to apply variables to criteria
function applyVariablesToCriteria(criteria: any, variables: Record<string, any>, templateVariables: any[]): any {
  const appliedCriteria = JSON.parse(JSON.stringify(criteria));
  
  // Replace variable placeholders in conditions
  if (appliedCriteria.conditions) {
    appliedCriteria.conditions = appliedCriteria.conditions.map((condition: any) => {
      const templateVar = templateVariables.find(v => condition.value === `{{${v.name}}}`);
      if (templateVar && variables[templateVar.name] !== undefined) {
        return {
          ...condition,
          value: variables[templateVar.name]
        };
      }
      return condition;
    });
  }
  
  return appliedCriteria;
}
