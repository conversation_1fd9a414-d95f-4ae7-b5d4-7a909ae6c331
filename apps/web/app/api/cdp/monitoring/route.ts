import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const type = url.searchParams.get('type') || 'overview';

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Performance Monitor Service
      const performanceMonitor = cdp.getService('performanceMonitor');

      let data;

      switch (type) {
        case 'overview':
          data = await performanceMonitor.getSystemOverview();
          break;
        
        case 'metrics':
          data = await performanceMonitor.getRealTimeMetrics();
          break;
        
        case 'alerts':
          data = await performanceMonitor.getActiveAlerts();
          break;
        
        case 'health':
          // Get health status from all services
          const healthData = await Promise.all([
            cdp.getService('profile').getHealthStatus(),
            cdp.getService('segmentation').getHealthStatus(),
            cdp.getService('journey').getHealthStatus(),
            cdp.getService('eventProcessor').getHealthStatus(),
            cdp.getService('messageQueue').getHealthStatus(),
            performanceMonitor.getHealthStatus(),
            cdp.getService('realTimeAnalytics').getHealthStatus()
          ]);

          data = {
            overall: healthData.every(h => h.status === 'healthy') ? 'healthy' : 'unhealthy',
            services: {
              profile: healthData[0],
              segmentation: healthData[1],
              journey: healthData[2],
              eventProcessor: healthData[3],
              messageQueue: healthData[4],
              performanceMonitor: healthData[5],
              realTimeAnalytics: healthData[6]
            },
            timestamp: new Date().toISOString()
          };
          break;
        
        default:
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid monitoring type',
              code: 'INVALID_TYPE'
            },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data,
        type
      });

    } catch (error) {
      console.error('CDP Monitoring API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { action, data: actionData } = body;

      if (!action) {
        return NextResponse.json(
          {
            success: false,
            error: 'Action is required',
            code: 'MISSING_ACTION'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Performance Monitor Service
      const performanceMonitor = cdp.getService('performanceMonitor');

      let result;

      switch (action) {
        case 'recordMetric':
          if (!actionData.metric) {
            return NextResponse.json(
              {
                success: false,
                error: 'Metric data is required',
                code: 'MISSING_METRIC'
              },
              { status: 400 }
            );
          }
          
          await performanceMonitor.recordMetric({
            ...actionData.metric,
            timestamp: new Date()
          });
          
          result = { message: 'Metric recorded successfully' };
          break;
        
        case 'recordBatchMetrics':
          if (!actionData.metrics || !Array.isArray(actionData.metrics)) {
            return NextResponse.json(
              {
                success: false,
                error: 'Metrics array is required',
                code: 'MISSING_METRICS'
              },
              { status: 400 }
            );
          }
          
          const metricsWithTimestamp = actionData.metrics.map((metric: any) => ({
            ...metric,
            timestamp: new Date()
          }));
          
          await performanceMonitor.recordBatchMetrics(metricsWithTimestamp);
          
          result = { 
            message: 'Batch metrics recorded successfully',
            count: metricsWithTimestamp.length
          };
          break;
        
        case 'setThreshold':
          if (!actionData.threshold) {
            return NextResponse.json(
              {
                success: false,
                error: 'Threshold data is required',
                code: 'MISSING_THRESHOLD'
              },
              { status: 400 }
            );
          }
          
          await performanceMonitor.setThreshold(actionData.threshold);
          
          result = { message: 'Threshold set successfully' };
          break;
        
        case 'clearAlert':
          if (!actionData.alertId) {
            return NextResponse.json(
              {
                success: false,
                error: 'Alert ID is required',
                code: 'MISSING_ALERT_ID'
              },
              { status: 400 }
            );
          }
          
          await performanceMonitor.clearAlert(actionData.alertId);
          
          result = { message: 'Alert cleared successfully' };
          break;
        
        default:
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid action',
              code: 'INVALID_ACTION'
            },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data: result,
        action
      });

    } catch (error) {
      console.error('CDP Monitoring Action API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
