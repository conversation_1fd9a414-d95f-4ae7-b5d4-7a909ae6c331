import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { 
  getCustomerSegments, 
  createCustomerSegment,
  updateCustomerSegment,
  deleteCustomerSegment,
  CreateCustomerSegmentData 
} from '~/home/<USER>/cdp/_lib/server/customer-segments-api';

export async function GET(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const accountSlug = searchParams.get('account');
    const page = parseInt(searchParams.get('page') || '1');
    const searchQuery = searchParams.get('search') || '';
    const filter = searchParams.get('filter') || 'all';
    const pageSize = parseInt(searchParams.get('pageSize') || '20');

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Load customer segments
    const result = await getCustomerSegments(
      client,
      teamAccount.id,
      page,
      searchQuery,
      filter,
      pageSize
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error in GET /api/cdp/customer-segments:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const body = await request.json();
    const { accountSlug, ...segmentData } = body as { 
      accountSlug: string 
    } & CreateCustomerSegmentData;

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!segmentData.name || !segmentData.description || !segmentData.type) {
      return NextResponse.json(
        { error: 'Name, description, and type are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Create customer segment
    const segment = await createCustomerSegment(
      client,
      teamAccount.id,
      segmentData
    );

    return NextResponse.json({
      success: true,
      data: segment,
    });
  } catch (error) {
    console.error('Error in POST /api/cdp/customer-segments:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const body = await request.json();
    const { accountSlug, segmentId, ...updates } = body as { 
      accountSlug: string;
      segmentId: string;
    } & Partial<CreateCustomerSegmentData>;

    if (!accountSlug || !segmentId) {
      return NextResponse.json(
        { error: 'Account slug and segment ID are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Update customer segment
    const segment = await updateCustomerSegment(
      client,
      teamAccount.id,
      segmentId,
      updates
    );

    return NextResponse.json({
      success: true,
      data: segment,
    });
  } catch (error) {
    console.error('Error in PUT /api/cdp/customer-segments:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const searchParams = request.nextUrl.searchParams;
    const accountSlug = searchParams.get('account');
    const segmentId = searchParams.get('segmentId');

    if (!accountSlug || !segmentId) {
      return NextResponse.json(
        { error: 'Account slug and segment ID are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Delete customer segment
    await deleteCustomerSegment(
      client,
      teamAccount.id,
      segmentId
    );

    return NextResponse.json({
      success: true,
      message: 'Customer segment deleted successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/cdp/customer-segments:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
