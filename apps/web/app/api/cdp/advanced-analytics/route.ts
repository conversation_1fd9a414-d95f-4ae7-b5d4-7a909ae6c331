import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const service = url.searchParams.get('service');
      const action = url.searchParams.get('action');

      if (!service || !action) {
        return NextResponse.json(
          {
            success: false,
            error: 'Service and action parameters are required',
            code: 'MISSING_PARAMETERS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      let data;

      switch (service) {
        case 'journey':
          const journeyService = cdp.getService('journeyAnalytics');
          
          switch (action) {
            case 'analyze':
              const startDate = url.searchParams.get('startDate');
              const endDate = url.searchParams.get('endDate');
              const visualizationType = url.searchParams.get('visualizationType') || 'sankey';
              
              if (!startDate || !endDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await journeyService.analyzeJourneys({
                time_range: { start: new Date(startDate), end: new Date(endDate) },
                visualization_type: visualizationType as any
              });
              break;
            
            case 'customer-journey':
              const customerId = url.searchParams.get('customerId');
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              
              data = await journeyService.getCustomerJourney(customerId);
              break;
            
            case 'flows':
              const flowStartDate = url.searchParams.get('startDate');
              const flowEndDate = url.searchParams.get('endDate');
              
              if (!flowStartDate || !flowEndDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await journeyService.getJourneyFlows({
                start: new Date(flowStartDate),
                end: new Date(flowEndDate)
              });
              break;
            
            case 'common-paths':
              const pathStartDate = url.searchParams.get('startDate');
              const pathEndDate = url.searchParams.get('endDate');
              const minSupport = parseFloat(url.searchParams.get('minSupport') || '0.01');
              
              if (!pathStartDate || !pathEndDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await journeyService.getCommonPaths(
                { start: new Date(pathStartDate), end: new Date(pathEndDate) },
                minSupport
              );
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid journey action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'cohort':
          const cohortService = cdp.getService('advancedCohortAnalysis');
          
          switch (action) {
            case 'analyze':
              const cohortId = url.searchParams.get('cohortId');
              if (!cohortId) {
                return NextResponse.json(
                  { success: false, error: 'Cohort ID required', code: 'MISSING_COHORT_ID' },
                  { status: 400 }
                );
              }
              
              data = await cohortService.analyzeCohort(cohortId);
              break;
            
            case 'retention-matrix':
              const matrixCohortId = url.searchParams.get('cohortId');
              if (!matrixCohortId) {
                return NextResponse.json(
                  { success: false, error: 'Cohort ID required', code: 'MISSING_COHORT_ID' },
                  { status: 400 }
                );
              }
              
              data = await cohortService.generateRetentionMatrix(matrixCohortId);
              break;
            
            case 'compare':
              const cohortIdA = url.searchParams.get('cohortIdA');
              const cohortIdB = url.searchParams.get('cohortIdB');
              
              if (!cohortIdA || !cohortIdB) {
                return NextResponse.json(
                  { success: false, error: 'Both cohort IDs required for comparison', code: 'MISSING_COHORT_IDS' },
                  { status: 400 }
                );
              }
              
              data = await cohortService.compareCohorts(cohortIdA, cohortIdB);
              break;
            
            case 'trends':
              const trendStartDate = url.searchParams.get('startDate');
              const trendEndDate = url.searchParams.get('endDate');
              const metric = url.searchParams.get('metric') || 'retention';
              
              if (!trendStartDate || !trendEndDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await cohortService.getCohortTrends(
                { start: new Date(trendStartDate), end: new Date(trendEndDate) },
                metric as any
              );
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid cohort action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'attribution':
          const attributionService = cdp.getService('attributionModeling');
          
          switch (action) {
            case 'analyze':
              const attrStartDate = url.searchParams.get('startDate');
              const attrEndDate = url.searchParams.get('endDate');
              const modelId = url.searchParams.get('modelId') || 'last_touch';
              
              if (!attrStartDate || !attrEndDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await attributionService.analyzeAttribution(
                { start: new Date(attrStartDate), end: new Date(attrEndDate) },
                modelId
              );
              break;
            
            case 'compare-models':
              const compareStartDate = url.searchParams.get('startDate');
              const compareEndDate = url.searchParams.get('endDate');
              const modelIds = url.searchParams.get('modelIds')?.split(',') || ['first_touch', 'last_touch'];
              
              if (!compareStartDate || !compareEndDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await attributionService.compareModels(
                { start: new Date(compareStartDate), end: new Date(compareEndDate) },
                modelIds
              );
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid attribution action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'funnel':
          const funnelService = cdp.getService('funnelOptimization');
          
          switch (action) {
            case 'analyze':
              const funnelId = url.searchParams.get('funnelId');
              const funnelStartDate = url.searchParams.get('startDate');
              const funnelEndDate = url.searchParams.get('endDate');
              
              if (!funnelId || !funnelStartDate || !funnelEndDate) {
                return NextResponse.json(
                  { success: false, error: 'Funnel ID, start and end dates required', code: 'MISSING_PARAMETERS' },
                  { status: 400 }
                );
              }
              
              data = await funnelService.analyzeFunnel(
                funnelId,
                { start: new Date(funnelStartDate), end: new Date(funnelEndDate) }
              );
              break;
            
            case 'compare':
              const funnelIdA = url.searchParams.get('funnelIdA');
              const funnelIdB = url.searchParams.get('funnelIdB');
              const compareStartDate = url.searchParams.get('startDate');
              const compareEndDate = url.searchParams.get('endDate');
              
              if (!funnelIdA || !funnelIdB || !compareStartDate || !compareEndDate) {
                return NextResponse.json(
                  { success: false, error: 'Both funnel IDs and date range required', code: 'MISSING_PARAMETERS' },
                  { status: 400 }
                );
              }
              
              data = await funnelService.compareFunnels(
                funnelIdA,
                funnelIdB,
                { start: new Date(compareStartDate), end: new Date(compareEndDate) }
              );
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid funnel action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid service', code: 'INVALID_SERVICE' },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data,
        service,
        action
      });

    } catch (error) {
      console.error('CDP Advanced Analytics API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { service, action, data: requestData } = body;

      if (!service || !action) {
        return NextResponse.json(
          {
            success: false,
            error: 'Service and action are required',
            code: 'MISSING_PARAMETERS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      let result;

      switch (service) {
        case 'cohort':
          const cohortService = cdp.getService('advancedCohortAnalysis');
          
          switch (action) {
            case 'create':
              if (!requestData.definition) {
                return NextResponse.json(
                  { success: false, error: 'Cohort definition required', code: 'MISSING_DATA' },
                  { status: 400 }
                );
              }
              result = await cohortService.createCohortDefinition(requestData.definition);
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid cohort action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'funnel':
          const funnelService = cdp.getService('funnelOptimization');
          
          switch (action) {
            case 'create':
              if (!requestData.definition) {
                return NextResponse.json(
                  { success: false, error: 'Funnel definition required', code: 'MISSING_DATA' },
                  { status: 400 }
                );
              }
              result = await funnelService.createFunnel(requestData.definition);
              break;
            
            case 'create-experiment':
              if (!requestData.experiment) {
                return NextResponse.json(
                  { success: false, error: 'Experiment data required', code: 'MISSING_DATA' },
                  { status: 400 }
                );
              }
              result = await funnelService.createExperiment(requestData.experiment);
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid funnel action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid service', code: 'INVALID_SERVICE' },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data: result,
        service,
        action
      });

    } catch (error) {
      console.error('CDP Advanced Analytics POST API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
