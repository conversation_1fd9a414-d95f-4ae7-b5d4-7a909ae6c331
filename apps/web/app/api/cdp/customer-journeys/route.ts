import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { 
  getCustomerJourneys, 
  createCustomerJourney,
  updateCustomerJourney,
  deleteCustomerJourney,
  publishCustomerJourney,
  pauseCustomerJourney,
  CreateCustomerJourneyData 
} from '~/home/<USER>/cdp/_lib/server/customer-journeys-api';

export async function GET(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const accountSlug = searchParams.get('account');
    const page = parseInt(searchParams.get('page') || '1');
    const searchQuery = searchParams.get('search') || '';
    const filter = searchParams.get('filter') || 'all';
    const pageSize = parseInt(searchParams.get('pageSize') || '20');

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Load customer journeys
    const result = await getCustomerJourneys(
      client,
      teamAccount.id,
      page,
      searchQuery,
      filter,
      pageSize
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error in GET /api/cdp/customer-journeys:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const body = await request.json();
    const { accountSlug, ...journeyData } = body as { 
      accountSlug: string 
    } & CreateCustomerJourneyData;

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!journeyData.name || !journeyData.description || !journeyData.trigger_type) {
      return NextResponse.json(
        { error: 'Name, description, and trigger type are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Create customer journey
    const journey = await createCustomerJourney(
      client,
      teamAccount.id,
      journeyData
    );

    return NextResponse.json({
      success: true,
      data: journey,
    });
  } catch (error) {
    console.error('Error in POST /api/cdp/customer-journeys:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const body = await request.json();
    const { accountSlug, journeyId, action, ...updates } = body as { 
      accountSlug: string;
      journeyId: string;
      action?: 'publish' | 'pause';
    } & Partial<CreateCustomerJourneyData & { status: string }>;

    if (!accountSlug || !journeyId) {
      return NextResponse.json(
        { error: 'Account slug and journey ID are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    let journey;
    
    // Handle special actions
    if (action === 'publish') {
      journey = await publishCustomerJourney(client, teamAccount.id, journeyId);
    } else if (action === 'pause') {
      journey = await pauseCustomerJourney(client, teamAccount.id, journeyId);
    } else {
      // Regular update
      journey = await updateCustomerJourney(
        client,
        teamAccount.id,
        journeyId,
        updates
      );
    }

    return NextResponse.json({
      success: true,
      data: journey,
    });
  } catch (error) {
    console.error('Error in PUT /api/cdp/customer-journeys:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const searchParams = request.nextUrl.searchParams;
    const accountSlug = searchParams.get('account');
    const journeyId = searchParams.get('journeyId');

    if (!accountSlug || !journeyId) {
      return NextResponse.json(
        { error: 'Account slug and journey ID are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Delete customer journey
    await deleteCustomerJourney(
      client,
      teamAccount.id,
      journeyId
    );

    return NextResponse.json({
      success: true,
      message: 'Customer journey deleted successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/cdp/customer-journeys:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
