import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { name, description, trigger_type, trigger_config, steps, tags } = body;

      if (!name || !trigger_type || !steps) {
        return NextResponse.json(
          {
            success: false,
            error: 'Name, trigger_type, and steps are required',
            code: 'MISSING_REQUIRED_FIELDS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Journey Service
      const journeyService = cdp.getService('journey');

      // Create journey
      const journey = await journeyService.createJourney(
        user.user_metadata?.account_id || user.id,
        {
          name,
          description,
          trigger_type,
          trigger_config: trigger_config || {},
          steps,
          is_active: false,
          is_draft: true,
          tags: tags || []
        }
      );

      return NextResponse.json({
        success: true,
        data: journey
      });

    } catch (error) {
      console.error('CDP Journeys API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const limit = parseInt(url.searchParams.get('limit') || '50');
      const offset = parseInt(url.searchParams.get('offset') || '0');
      const isActive = url.searchParams.get('isActive');
      const isDraft = url.searchParams.get('isDraft');

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Journey Service
      const journeyService = cdp.getService('journey');

      // List journeys
      const result = await journeyService.listJourneys(
        user.user_metadata?.account_id || user.id,
        {
          limit,
          offset,
          isActive: isActive ? isActive === 'true' : undefined,
          isDraft: isDraft ? isDraft === 'true' : undefined
        }
      );

      return NextResponse.json({
        success: true,
        data: result.journeys,
        total: result.total,
        limit,
        offset
      });

    } catch (error) {
      console.error('CDP Journeys API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
