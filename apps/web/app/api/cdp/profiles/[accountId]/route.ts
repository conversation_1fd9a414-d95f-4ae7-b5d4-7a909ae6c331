import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(
  request: NextRequest,
  { params }: { params: { accountId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { accountId } = params;
    const { searchParams } = new URL(request.url);

    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const filter = searchParams.get('filter') || 'all';
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('customer_profiles')
      .select(`
        id,
        email,
        first_name,
        last_name,
        phone,
        avatar_url,
        created_at,
        last_active_at,
        total_orders,
        total_spent,
        avg_order_value,
        engagement_score,
        churn_risk_score,
        value_tier,
        tags,
        metadata
      `, { count: 'exact' })
      .eq('account_id', accountId);

    // Apply search filter
    if (search) {
      query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Apply value tier filter
    if (filter !== 'all') {
      switch (filter) {
        case 'highValue':
          query = query.eq('value_tier', 'high');
          break;
        case 'mediumValue':
          query = query.eq('value_tier', 'medium');
          break;
        case 'lowValue':
          query = query.eq('value_tier', 'low');
          break;
        case 'atRisk':
          query = query.gte('churn_risk_score', 0.7);
          break;
        case 'newCustomers':
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          query = query.gte('created_at', thirtyDaysAgo.toISOString());
          break;
      }
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: profiles, error, count } = await query;

    if (error) {
      // If table doesn't exist, return sample data
      if (error.message.includes('relation "customer_profiles" does not exist')) {
        return NextResponse.json({
          profiles: generateSampleProfiles(limit),
          total: 100,
          page,
          limit,
          totalPages: Math.ceil(100 / limit),
          fallback: true
        });
      }
      throw error;
    }

    // Transform data
    const transformedProfiles = profiles?.map(profile => ({
      ...profile,
      created_at: new Date(profile.created_at),
      last_active_at: profile.last_active_at ? new Date(profile.last_active_at) : null,
      engagement_score: profile.engagement_score || Math.random(),
      churn_risk_score: profile.churn_risk_score || Math.random(),
      value_tier: profile.value_tier || calculateValueTier(profile.total_spent || 0),
      tags: profile.tags || [],
      metadata: profile.metadata || {}
    })) || [];

    return NextResponse.json({
      profiles: transformedProfiles,
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    });

  } catch (error) {
    console.error('Error fetching customer profiles:', error);
    
    // Return sample data as fallback
    return NextResponse.json({
      profiles: generateSampleProfiles(50),
      total: 100,
      page: 1,
      limit: 50,
      totalPages: 2,
      fallback: true
    });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { accountId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { accountId } = params;
    const body = await request.json();

    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create new customer profile
    const { data, error } = await supabase
      .from('customer_profiles')
      .insert({
        account_id: accountId,
        email: body.email,
        first_name: body.first_name,
        last_name: body.last_name,
        phone: body.phone,
        avatar_url: body.avatar_url,
        total_orders: body.total_orders || 0,
        total_spent: body.total_spent || 0,
        avg_order_value: body.avg_order_value || 0,
        engagement_score: body.engagement_score || 0,
        churn_risk_score: body.churn_risk_score || 0,
        value_tier: body.value_tier || calculateValueTier(body.total_spent || 0),
        tags: body.tags || [],
        metadata: body.metadata || {},
        created_at: new Date().toISOString(),
        last_active_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({ success: true, profile: data });

  } catch (error) {
    console.error('Error creating customer profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function calculateValueTier(totalSpent: number): 'high' | 'medium' | 'low' {
  if (totalSpent > 5000000) return 'high'; // 5M VND
  if (totalSpent > 1000000) return 'medium'; // 1M VND
  return 'low';
}

function generateSampleProfiles(count: number) {
  const profiles = [];
  const firstNames = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng'];
  const lastNames = ['Văn An', 'Thị Bình', 'Minh Châu', 'Thị Dung', 'Văn Em', 'Thị Phương', 'Minh Giang', 'Thị Hoa'];

  for (let i = 0; i < count; i++) {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const totalSpent = Math.random() * 10000000; // Up to 10M VND
    const totalOrders = Math.floor(Math.random() * 50) + 1;
    
    profiles.push({
      id: `customer_${i + 1}`,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase().replace(/\s+/g, '')}@example.com`,
      first_name: firstName,
      last_name: lastName,
      phone: `+84${Math.floor(Math.random() * 1000000000)}`,
      avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${firstName}${lastName}`,
      created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      last_active_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      total_orders: totalOrders,
      total_spent: Math.round(totalSpent),
      avg_order_value: Math.round(totalSpent / totalOrders),
      engagement_score: Math.random(),
      churn_risk_score: Math.random(),
      value_tier: calculateValueTier(totalSpent),
      tags: ['sample', 'generated'],
      metadata: { 
        source: 'sample_data',
        location: ['Hà Nội', 'TP.HCM', 'Đà Nẵng', 'Cần Thơ'][Math.floor(Math.random() * 4)],
        preferred_language: 'vi'
      }
    });
  }

  return profiles;
}
