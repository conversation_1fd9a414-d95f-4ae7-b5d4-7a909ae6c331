import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';
import { extractIdentities } from '@kit/cdp/utils';

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { identities: rawIdentities, ...profileData } = body;

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Profile Service
      const profileService = cdp.getService('profile');

      // Extract or use provided identities
      const identities = rawIdentities || extractIdentities({
        email: profileData.primary_email,
        phone: profileData.primary_phone,
        user_id: profileData.primary_user_id || user.id,
        source: 'api'
      });

      // Create or update profile
      const profile = await profileService.createOrUpdateProfile(
        user.user_metadata?.account_id || user.id,
        identities,
        profileData
      );

      return NextResponse.json({
        success: true,
        data: profile
      });

    } catch (error) {
      console.error('CDP Profile API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const profileId = url.searchParams.get('id');
      const search = url.searchParams.get('search');
      const limit = parseInt(url.searchParams.get('limit') || '50');
      const offset = parseInt(url.searchParams.get('offset') || '0');

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Profile Service
      const profileService = cdp.getService('profile');

      if (profileId) {
        // Get specific profile
        const profile = await profileService.getProfile(profileId);
        return NextResponse.json({
          success: true,
          data: profile
        });
      } else {
        // Search profiles
        const searchCriteria: Record<string, any> = {};
        
        if (search) {
          // Simple search implementation - in production, you'd want more sophisticated search
          searchCriteria.primary_email = search;
        }

        const result = await profileService.searchProfiles(
          user.user_metadata?.account_id || user.id,
          searchCriteria,
          limit,
          offset
        );

        return NextResponse.json({
          success: true,
          data: result.profiles,
          total: result.total,
          limit,
          offset
        });
      }

    } catch (error) {
      console.error('CDP Profile API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const PUT = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { profileId, identities: newIdentities, ...updateData } = body;

      if (!profileId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Profile ID is required',
            code: 'MISSING_PROFILE_ID'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Profile Service
      const profileService = cdp.getService('profile');

      // Update profile
      const profile = await profileService.updateProfile(
        profileId,
        updateData,
        newIdentities
      );

      return NextResponse.json({
        success: true,
        data: profile
      });

    } catch (error) {
      console.error('CDP Profile API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const DELETE = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const profileId = url.searchParams.get('id');

      if (!profileId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Profile ID is required',
            code: 'MISSING_PROFILE_ID'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Profile Service
      const profileService = cdp.getService('profile');

      // Delete profile
      await profileService.deleteProfile(profileId);

      return NextResponse.json({
        success: true,
        message: 'Profile deleted successfully'
      });

    } catch (error) {
      console.error('CDP Profile API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
