import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const service = url.searchParams.get('service');
      const action = url.searchParams.get('action');
      const customerId = url.searchParams.get('customerId');

      if (!service || !action) {
        return NextResponse.json(
          {
            success: false,
            error: 'Service and action parameters are required',
            code: 'MISSING_PARAMETERS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      let data;

      switch (service) {
        case 'predictive':
          const predictiveService = cdp.getService('predictiveAnalytics');
          
          switch (action) {
            case 'churn':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              data = await predictiveService.predictChurnRisk(customerId);
              break;
            
            case 'ltv':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              data = await predictiveService.predictLifetimeValue(customerId);
              break;
            
            case 'engagement':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              data = await predictiveService.predictEngagement(customerId);
              break;
            
            case 'performance':
              const modelType = url.searchParams.get('modelType') || 'churn_prediction';
              data = await predictiveService.getModelPerformance(modelType);
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid predictive action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'recommendations':
          const recommendationService = cdp.getService('recommendationEngine');
          
          switch (action) {
            case 'get':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              
              const context = url.searchParams.get('context') || 'homepage';
              const limit = parseInt(url.searchParams.get('limit') || '5');
              
              data = await recommendationService.getRecommendations({
                customer_id: customerId,
                context: context as any,
                limit
              });
              break;
            
            case 'next-best-action':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              data = await recommendationService.getNextBestAction(customerId);
              break;
            
            case 'analytics':
              const startDate = url.searchParams.get('startDate');
              const endDate = url.searchParams.get('endDate');
              
              if (!startDate || !endDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await recommendationService.getRecommendationAnalytics({
                start: new Date(startDate),
                end: new Date(endDate)
              });
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid recommendation action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'segmentation':
          const segmentationService = cdp.getService('autoSegmentation');
          
          switch (action) {
            case 'segments':
              data = await segmentationService.getAutoSegments();
              break;
            
            case 'customer-cluster':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              data = await segmentationService.getCustomerCluster(customerId);
              break;
            
            case 'insights':
              const segmentId = url.searchParams.get('segmentId');
              if (!segmentId) {
                return NextResponse.json(
                  { success: false, error: 'Segment ID required', code: 'MISSING_SEGMENT_ID' },
                  { status: 400 }
                );
              }
              data = await segmentationService.getSegmentInsights(segmentId);
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid segmentation action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'personalization':
          const personalizationService = cdp.getService('contentPersonalization');
          
          switch (action) {
            case 'email':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              
              const emailType = url.searchParams.get('emailType') || 'promotional';
              data = await personalizationService.personalizeEmail(
                customerId,
                emailType as any
              );
              break;
            
            case 'web':
              if (!customerId) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID required', code: 'MISSING_CUSTOMER_ID' },
                  { status: 400 }
                );
              }
              
              const pageType = url.searchParams.get('pageType') || 'homepage';
              data = await personalizationService.personalizeWebContent(
                customerId,
                pageType as any
              );
              break;
            
            case 'performance':
              const startDate = url.searchParams.get('startDate');
              const endDate = url.searchParams.get('endDate');
              
              if (!startDate || !endDate) {
                return NextResponse.json(
                  { success: false, error: 'Start and end dates required', code: 'MISSING_DATE_RANGE' },
                  { status: 400 }
                );
              }
              
              data = await personalizationService.getContentPerformance({
                start: new Date(startDate),
                end: new Date(endDate)
              });
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid personalization action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid service', code: 'INVALID_SERVICE' },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data,
        service,
        action
      });

    } catch (error) {
      console.error('CDP AI API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { service, action, data: requestData } = body;

      if (!service || !action) {
        return NextResponse.json(
          {
            success: false,
            error: 'Service and action are required',
            code: 'MISSING_PARAMETERS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      let result;

      switch (service) {
        case 'predictive':
          const predictiveService = cdp.getService('predictiveAnalytics');
          
          switch (action) {
            case 'batch-predictions':
              if (!requestData.customerIds || !requestData.modelType) {
                return NextResponse.json(
                  { success: false, error: 'Customer IDs and model type required', code: 'MISSING_DATA' },
                  { status: 400 }
                );
              }
              result = await predictiveService.getBatchPredictions(
                requestData.customerIds,
                requestData.modelType
              );
              break;
            
            case 'train-model':
              if (!requestData.modelType || !requestData.trainingData) {
                return NextResponse.json(
                  { success: false, error: 'Model type and training data required', code: 'MISSING_DATA' },
                  { status: 400 }
                );
              }
              result = await predictiveService.trainModel(
                requestData.modelType,
                requestData.trainingData
              );
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid predictive action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'recommendations':
          const recommendationService = cdp.getService('recommendationEngine');
          
          switch (action) {
            case 'record-interaction':
              if (!requestData.customerId || !requestData.recommendationId || !requestData.interactionType) {
                return NextResponse.json(
                  { success: false, error: 'Customer ID, recommendation ID, and interaction type required', code: 'MISSING_DATA' },
                  { status: 400 }
                );
              }
              
              await recommendationService.recordInteraction(
                requestData.customerId,
                requestData.recommendationId,
                requestData.interactionType,
                requestData.metadata
              );
              
              result = { message: 'Interaction recorded successfully' };
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid recommendation action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'segmentation':
          const segmentationService = cdp.getService('autoSegmentation');
          
          switch (action) {
            case 'discover':
              const algorithm = requestData.algorithm || 'behavioral';
              const parameters = requestData.parameters || {};
              
              result = await segmentationService.discoverSegments(algorithm, parameters);
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid segmentation action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        case 'personalization':
          const personalizationService = cdp.getService('contentPersonalization');
          
          switch (action) {
            case 'create-template':
              if (!requestData.template) {
                return NextResponse.json(
                  { success: false, error: 'Template data required', code: 'MISSING_DATA' },
                  { status: 400 }
                );
              }
              result = await personalizationService.createTemplate(requestData.template);
              break;
            
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid personalization action', code: 'INVALID_ACTION' },
                { status: 400 }
              );
          }
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid service', code: 'INVALID_SERVICE' },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data: result,
        service,
        action
      });

    } catch (error) {
      console.error('CDP AI POST API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
