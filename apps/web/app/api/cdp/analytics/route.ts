import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const type = url.searchParams.get('type') || 'dashboard';

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Real-time Analytics Service
      const analyticsService = cdp.getService('realTimeAnalytics');

      let data;

      switch (type) {
        case 'dashboard':
          data = await analyticsService.getRealTimeDashboard();
          break;
        
        case 'overview':
          data = await analyticsService.getSystemOverview();
          break;
        
        default:
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid analytics type',
              code: 'INVALID_TYPE'
            },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data,
        type
      });

    } catch (error) {
      console.error('CDP Analytics API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { type, query } = body;

      if (!type || !query) {
        return NextResponse.json(
          {
            success: false,
            error: 'Type and query are required',
            code: 'MISSING_REQUIRED_FIELDS'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      // Get Real-time Analytics Service
      const analyticsService = cdp.getService('realTimeAnalytics');

      let data;

      switch (type) {
        case 'query':
          data = await analyticsService.executeQuery(query);
          break;
        
        case 'funnel':
          data = await analyticsService.getFunnelAnalysis(
            query.steps,
            query.timeRange,
            query.filters
          );
          break;
        
        case 'cohort':
          data = await analyticsService.getCohortAnalysis(
            query.cohortType,
            query.metric,
            query.periods
          );
          break;
        
        case 'attribution':
          data = await analyticsService.getAttributionAnalysis(
            query.conversionEvent,
            query.touchpointEvents,
            query.timeRange,
            query.attributionModel
          );
          break;
        
        case 'journey':
          data = await analyticsService.getCustomerJourneyAnalytics(query.customerId);
          break;
        
        default:
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid query type',
              code: 'INVALID_QUERY_TYPE'
            },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data,
        type,
        query
      });

    } catch (error) {
      console.error('CDP Analytics Query API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
