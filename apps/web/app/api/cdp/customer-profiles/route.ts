import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { 
  getCustomerProfiles, 
  createCustomerProfile,
  updateCustomerProfile,
  deleteCustomerProfile,
  CreateCustomerProfileData 
} from '~/home/<USER>/cdp/_lib/server/customer-profiles-api';

export async function GET(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const accountSlug = searchParams.get('account');
    const page = parseInt(searchParams.get('page') || '1');
    const searchQuery = searchParams.get('search') || '';
    const filter = searchParams.get('filter') || 'all';
    const pageSize = parseInt(searchParams.get('pageSize') || '20');

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Load customer profiles
    const result = await getCustomerProfiles(
      client,
      teamAccount.id,
      page,
      searchQuery,
      filter,
      pageSize
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error in GET /api/cdp/customer-profiles:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const body = await request.json();
    const { accountSlug, ...profileData } = body as { 
      accountSlug: string 
    } & CreateCustomerProfileData;

    if (!accountSlug) {
      return NextResponse.json(
        { error: 'Account slug is required' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!profileData.first_name || !profileData.last_name || !profileData.email) {
      return NextResponse.json(
        { error: 'First name, last name, and email are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Create customer profile
    const profile = await createCustomerProfile(
      client,
      teamAccount.id,
      profileData
    );

    return NextResponse.json({
      success: true,
      data: profile,
    });
  } catch (error) {
    console.error('Error in POST /api/cdp/customer-profiles:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const body = await request.json();
    const { accountSlug, profileId, ...updates } = body as { 
      accountSlug: string;
      profileId: string;
    } & Partial<CreateCustomerProfileData>;

    if (!accountSlug || !profileId) {
      return NextResponse.json(
        { error: 'Account slug and profile ID are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Update customer profile
    const profile = await updateCustomerProfile(
      client,
      teamAccount.id,
      profileId,
      updates
    );

    return NextResponse.json({
      success: true,
      data: profile,
    });
  } catch (error) {
    console.error('Error in PUT /api/cdp/customer-profiles:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const client = getSupabaseServerClient();
    const api = createTeamAccountsApi(client);
    
    const searchParams = request.nextUrl.searchParams;
    const accountSlug = searchParams.get('account');
    const profileId = searchParams.get('profileId');

    if (!accountSlug || !profileId) {
      return NextResponse.json(
        { error: 'Account slug and profile ID are required' },
        { status: 400 }
      );
    }

    // Get team account
    const teamAccount = await api.getTeamAccount(accountSlug);
    
    // Delete customer profile
    await deleteCustomerProfile(
      client,
      teamAccount.id,
      profileId
    );

    return NextResponse.json({
      success: true,
      message: 'Customer profile deleted successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/cdp/customer-profiles:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
