import { NextRequest, NextResponse } from 'next/server';
import { enhanceRouteHand<PERSON> } from '@kit/next/routes';
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';

export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const action = url.searchParams.get('action');
      const integrationId = url.searchParams.get('integrationId');

      if (!action) {
        return NextResponse.json(
          {
            success: false,
            error: 'Action parameter is required',
            code: 'MISSING_ACTION'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      const integrationManager = cdp.getService('integrationManager');
      if (!integrationManager) {
        return NextResponse.json(
          {
            success: false,
            error: 'Integration Manager not available',
            code: 'SERVICE_NOT_AVAILABLE'
          },
          { status: 503 }
        );
      }

      let data;

      switch (action) {
        case 'list':
          // Get all integrations
          const integrations = integrationManager.getAllIntegrations();
          data = integrations.map(integration => ({
            config: integration.getConfig(),
            status: integration.getStatus()
          }));
          break;

        case 'available':
          // Get available integration types
          data = integrationManager.getAvailableIntegrations();
          break;

        case 'health':
          if (integrationId) {
            // Get specific integration health
            data = integrationManager.getIntegrationHealth(integrationId);
          } else {
            // Get all integration health
            data = integrationManager.getAllIntegrationHealth();
          }
          break;

        case 'schedules':
          if (integrationId) {
            // Get specific integration schedule
            data = integrationManager.getSyncSchedule(integrationId);
          } else {
            // Get all sync schedules
            const integrations = integrationManager.getAllIntegrations();
            data = integrations.map(integration => ({
              integration_id: integration.getConfig().id,
              schedule: integrationManager.getSyncSchedule(integration.getConfig().id)
            }));
          }
          break;

        case 'sync-status':
          if (!integrationId) {
            return NextResponse.json(
              { success: false, error: 'Integration ID required', code: 'MISSING_INTEGRATION_ID' },
              { status: 400 }
            );
          }

          const integration = integrationManager.getIntegration(integrationId);
          if (!integration) {
            return NextResponse.json(
              { success: false, error: 'Integration not found', code: 'INTEGRATION_NOT_FOUND' },
              { status: 404 }
            );
          }

          data = integration.getStatus();
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action', code: 'INVALID_ACTION' },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data,
        action
      });

    } catch (error) {
      console.error('CDP Integrations API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { action, data: requestData } = body;

      if (!action) {
        return NextResponse.json(
          {
            success: false,
            error: 'Action is required',
            code: 'MISSING_ACTION'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      const integrationManager = cdp.getService('integrationManager');
      if (!integrationManager) {
        return NextResponse.json(
          {
            success: false,
            error: 'Integration Manager not available',
            code: 'SERVICE_NOT_AVAILABLE'
          },
          { status: 503 }
        );
      }

      let result;

      switch (action) {
        case 'create':
          if (!requestData.type || !requestData.config || !requestData.syncConfig) {
            return NextResponse.json(
              { success: false, error: 'Type, config, and syncConfig are required', code: 'MISSING_DATA' },
              { status: 400 }
            );
          }

          result = await integrationManager.createIntegration(
            requestData.type,
            requestData.config,
            requestData.syncConfig
          );
          break;

        case 'update':
          if (!requestData.integrationId || !requestData.updates) {
            return NextResponse.json(
              { success: false, error: 'Integration ID and updates are required', code: 'MISSING_DATA' },
              { status: 400 }
            );
          }

          result = await integrationManager.updateIntegration(
            requestData.integrationId,
            requestData.updates
          );
          break;

        case 'delete':
          if (!requestData.integrationId) {
            return NextResponse.json(
              { success: false, error: 'Integration ID is required', code: 'MISSING_INTEGRATION_ID' },
              { status: 400 }
            );
          }

          await integrationManager.deleteIntegration(requestData.integrationId);
          result = { message: 'Integration deleted successfully' };
          break;

        case 'sync':
          if (!requestData.integrationId) {
            return NextResponse.json(
              { success: false, error: 'Integration ID is required', code: 'MISSING_INTEGRATION_ID' },
              { status: 400 }
            );
          }

          result = await integrationManager.syncIntegration(
            requestData.integrationId,
            requestData.direction
          );
          break;

        case 'update-schedule':
          if (!requestData.integrationId || !requestData.scheduleUpdates) {
            return NextResponse.json(
              { success: false, error: 'Integration ID and schedule updates are required', code: 'MISSING_DATA' },
              { status: 400 }
            );
          }

          result = await integrationManager.updateSyncSchedule(
            requestData.integrationId,
            requestData.scheduleUpdates
          );
          break;

        case 'webhook':
          if (!requestData.integrationId || !requestData.payload) {
            return NextResponse.json(
              { success: false, error: 'Integration ID and payload are required', code: 'MISSING_DATA' },
              { status: 400 }
            );
          }

          await integrationManager.handleWebhook(
            requestData.integrationId,
            requestData.payload
          );
          result = { message: 'Webhook processed successfully' };
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action', code: 'INVALID_ACTION' },
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data: result,
        action
      });

    } catch (error) {
      console.error('CDP Integrations POST API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);

export const DELETE = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const integrationId = url.searchParams.get('integrationId');

      if (!integrationId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Integration ID is required',
            code: 'MISSING_INTEGRATION_ID'
          },
          { status: 400 }
        );
      }

      // Initialize CDP Manager
      const cdp = createCDPManager(defaultCDPConfig, supabase);
      await cdp.initialize();

      const integrationManager = cdp.getService('integrationManager');
      if (!integrationManager) {
        return NextResponse.json(
          {
            success: false,
            error: 'Integration Manager not available',
            code: 'SERVICE_NOT_AVAILABLE'
          },
          { status: 503 }
        );
      }

      await integrationManager.deleteIntegration(integrationId);

      return NextResponse.json({
        success: true,
        message: 'Integration deleted successfully'
      });

    } catch (error) {
      console.error('CDP Integrations DELETE API Error:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
        },
        { status: 500 }
      );
    }
  },
  { auth: true }
);
