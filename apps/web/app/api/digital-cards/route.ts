import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

import { createCorsResponse } from '~/lib/cors';

// Schema for query parameters
const DigitalCardQuerySchema = z.object({
  user_id: z.string().optional(),
  theme_id: z.string().nullable().optional(), // Optional - only used when requesting business-specific card
});

/**
 * GET /api/digital-cards
 * Returns digital card information for a user across all businesses
 * If theme_id is provided, returns business-specific card details
 */

export const GET = enhanceRouteHandler(
  async ({ request, user }) => {
    const logger = await getLogger();
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;
      // Parse and validate query params
      const { theme_id } = DigitalCardQuerySchema.parse({
        user_id: user?.id,
        theme_id: searchParams.get('theme_id'),
      });

      // For now, return mockup data
      // In the future, this would query the blockchain or a database

      // Generate a random card ID that looks like a blockchain hash
      const cardId = `0x${Array.from({ length: 40 }, () =>
        Math.floor(Math.random() * 16).toString(16),
      ).join('')}`;

      // Generate random total points between 1000 and 20000
      const totalPoints = Math.floor(Math.random() * 19000) + 1000;

      // Generate random tier based on total points
      let tier = 'Bronze';
      if (totalPoints > 10000) tier = 'Platinum';
      else if (totalPoints > 5000) tier = 'Gold';
      else if (totalPoints > 2000) tier = 'Silver';

      // Generate random business cards (3-5 businesses)
      const businessCount = Math.floor(Math.random() * 3) + 3;
      const businessCards = Array.from({ length: businessCount }, (_, i) => {
        const businessPoints = Math.floor(Math.random() * 2000) + 100;
        let businessTier = 'Bronze';
        if (businessPoints > 1000) businessTier = 'Gold';
        else if (businessPoints > 500) businessTier = 'Silver';

        return {
          business_id: `business-${i + 1}`,
          business_name: `Cửa hàng ${String.fromCharCode(65 + i)}`, // Shop A, Shop B, etc.
          logo_url: '',
          points: businessPoints,
          tier: businessTier,
          last_transaction: new Date(
            Date.now() - i * 24 * 60 * 60 * 1000,
          ).toISOString(),
        };
      });

      // Generate random transactions across all businesses
      const transactions = Array.from({ length: 8 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);

        const amount = Math.floor(Math.random() * 500000) + 50000;
        const pointsEarned = Math.floor(amount / 10000);
        const businessIndex = Math.floor(Math.random() * businessCount);

        return {
          id: `tx-${Date.now()}-${i}`,
          date: date.toISOString(),
          type: Math.random() > 0.3 ? 'purchase' : 'reward',
          amount,
          points_earned: pointsEarned,
          business_name: `Cửa hàng ${String.fromCharCode(65 + businessIndex)}`,
          business_id: `business-${businessIndex + 1}`,
          description:
            Math.random() > 0.3
              ? 'Mua hàng tại cửa hàng'
              : 'Điểm thưởng khách hàng thân thiết',
        };
      });

      // Generate random global rewards
      const globalRewards = [
        {
          id: 'reward-global-1',
          name: 'Giảm 100.000đ cho đơn hàng tiếp theo',
          points_required: 1000,
          expires_at: new Date(
            Date.now() + 30 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          image_url: '',
          is_global: true,
        },
        {
          id: 'reward-global-2',
          name: 'Miễn phí vận chuyển toàn nền tảng',
          points_required: 2000,
          expires_at: new Date(
            Date.now() + 60 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          image_url: '',
          is_global: true,
        },
      ];

      // Generate random business-specific rewards
      const businessRewards = businessCards.flatMap((business, i) => {
        return [
          {
            id: `reward-${business.business_id}-1`,
            business_id: business.business_id,
            business_name: business.business_name,
            name: `Giảm 50.000đ tại ${business.business_name}`,
            points_required: 500,
            expires_at: new Date(
              Date.now() + (30 + i * 5) * 24 * 60 * 60 * 1000,
            ).toISOString(),
            image_url: ``,
            is_global: false,
          },
          {
            id: `reward-${business.business_id}-2`,
            business_id: business.business_id,
            business_name: business.business_name,
            name: `Giảm 15% tại ${business.business_name}`,
            points_required: 800,
            expires_at: new Date(
              Date.now() + (45 + i * 5) * 24 * 60 * 60 * 1000,
            ).toISOString(),
            image_url: ``,
            is_global: false,
          },
        ];
      });

      // Combine global and business rewards
      const allRewards = [...globalRewards, ...businessRewards];

      // If theme_id is provided, return business-specific card
      if (theme_id) {
        const supabase = getSupabaseServerAdminClient();

        // Get the account theme
        const { data: theme } = await supabase
          .from('account_themes')
          .select('account_id')
          .eq('id', theme_id)
          .single();

        if (theme) {
          // Get the account details
          const { data: account } = await supabase
            .from('accounts')
            .select('id, name, logo_url')
            .eq('id', theme.account_id)
            .single();

          if (account) {
            // Find or create a business card for this specific business
            let businessCard = businessCards.find(
              (b) => b.business_id === account.id,
            );

            if (!businessCard) {
              // Create a new business card if not found
              const businessPoints = Math.floor(Math.random() * 2000) + 100;
              let businessTier = 'Bronze';
              if (businessPoints > 1000) businessTier = 'Gold';
              else if (businessPoints > 500) businessTier = 'Silver';

              businessCard = {
                business_id: account.id,
                business_name: account.name,
                logo_url:
                  account.picture_url ||
                  `${process.env.NEXT_PUBLIC_SITE_URL || ''}/images/minapp-logo.png`,
                points: businessPoints,
                tier: businessTier,
                last_transaction: new Date().toISOString(),
              };
            }

            // Filter transactions for this business
            const businessTransactions = transactions.filter(
              (tx) => tx.business_id === businessCard.business_id,
            );

            // Filter rewards for this business
            const businessSpecificRewards = allRewards.filter(
              (r) => r.is_global || r.business_id === businessCard.business_id,
            );

            return createCorsResponse(
              request,
              {
                success: true,
                data: {
                  card_id: cardId,
                  user_id: user?.id || 'guest-user',
                  business: {
                    id: businessCard.business_id,
                    name: businessCard.business_name,
                    logo: businessCard.logo_url,
                  },
                  points: {
                    current: businessCard.points,
                    lifetime:
                      businessCard.points + Math.floor(Math.random() * 1000),
                    tier: businessCard.tier,
                    next_tier:
                      businessCard.tier === 'Gold'
                        ? 'Platinum'
                        : businessCard.tier === 'Silver'
                          ? 'Gold'
                          : 'Silver',
                    points_to_next_tier:
                      businessCard.tier === 'Gold'
                        ? 1000 - (businessCard.points % 1000)
                        : businessCard.tier === 'Silver'
                          ? 500 - (businessCard.points % 500)
                          : 500 - businessCard.points,
                  },
                  transactions:
                    businessTransactions.length > 0
                      ? businessTransactions
                      : [
                          {
                            id: `tx-${Date.now()}-1`,
                            date: new Date().toISOString(),
                            type: 'purchase',
                            amount: 250000,
                            points_earned: 25,
                            business_name: businessCard.business_name,
                            business_id: businessCard.business_id,
                            description: 'Mua hàng tại cửa hàng',
                          },
                        ],
                  rewards: businessSpecificRewards,
                  blockchain_info: {
                    network: 'Ethereum',
                    contract_address:
                      '******************************************',
                    token_id: Math.floor(Math.random() * 10000),
                    last_updated: new Date().toISOString(),
                  },
                },
              },
              200,
            );
          }
        }
      }

      // Return global card information (not specific to any business)
      return createCorsResponse(
        request,
        {
          success: true,
          data: {
            card_id: cardId,
            user_id: user?.id || 'guest-user',
            points: {
              total: totalPoints,
              lifetime: totalPoints + Math.floor(Math.random() * 5000),
              tier: tier,
              next_tier:
                tier === 'Platinum'
                  ? null
                  : tier === 'Gold'
                    ? 'Platinum'
                    : tier === 'Silver'
                      ? 'Gold'
                      : 'Silver',
              points_to_next_tier:
                tier === 'Platinum'
                  ? 0
                  : tier === 'Gold'
                    ? 10000 - totalPoints
                    : tier === 'Silver'
                      ? 5000 - totalPoints
                      : 2000 - totalPoints,
            },
            business_cards: businessCards,
            transactions: transactions,
            rewards: allRewards,
            blockchain_info: {
              network: 'Ethereum',
              contract_address: '******************************************',
              token_id: Math.floor(Math.random() * 10000),
              last_updated: new Date().toISOString(),
            },
          },
        },
        200,
      );
    } catch (error: any) {
      logger.error({ error: error.message }, 'Error in digital cards API');

      if (error instanceof z.ZodError) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: true },
);
