import { NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getLogger } from '@kit/shared/logger';

/**
 * API route để đồng bộ lại counter
 * C<PERSON> thể được gọi bởi cron job hoặc webhook
 */
export async function POST(request: Request) {
  const logger = await getLogger();
  
  try {
    // Kiểm tra API key nếu cần
    const authHeader = request.headers.get('authorization');
    const apiKey = process.env.RESOURCE_SYNC_API_KEY;
    
    if (apiKey && (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== apiKey)) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    const supabase = getSupabaseServerAdminClient();
    
    // Gọi function để refresh usage stats
    const { error } = await supabase.rpc('refresh_usage_stats');
    
    if (error) {
      logger.error({ error }, 'Error syncing resource usage');
      return new Response('Error syncing resource usage', { status: 500 });
    }
    
    logger.info('Resource usage synced successfully');
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error({ error }, 'Error in sync resource usage endpoint');
    return new Response('Internal Server Error', { status: 500 });
  }
}

/**
 * API route để đồng bộ lại counter (GET method)
 * Chỉ dùng cho testing, không nên sử dụng trong production
 */
export async function GET(request: Request) {
  // Chỉ cho phép trong môi trường development
  if (process.env.NODE_ENV !== 'development') {
    return new Response('Method not allowed', { status: 405 });
  }
  
  return POST(request);
}
