import { NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getLogger } from '@kit/shared/logger';
import { refreshZnsToken } from '@kit/zns/utils';

export async function POST(request: Request) {
  const logger = await getLogger();
  
  try {
    // Kiểm tra API key nếu cần
    const authHeader = request.headers.get('authorization');
    const apiKey = process.env.CRON_API_KEY;
    
    if (apiKey && (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== apiKey)) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    const supabase = getSupabaseServerAdminClient();
    
    // L<PERSON>y danh sách OA configurations có token sắp hết hạn
    // (còn hạn nhưng sắp hết - trong vòng 24 giờ tới)
    const { data: oaConfigs, error } = await supabase
      .from('oa_configurations')
      .select('*')
      .lt('token_expires_at', new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString())
      .gt('token_expires_at', new Date().toISOString());
    
    if (error) {
      logger.error({ error }, 'Error fetching OA configurations');
      return new Response('Error fetching OA configurations', { status: 500 });
    }
    
    if (!oaConfigs || oaConfigs.length === 0) {
      return NextResponse.json({ success: true, message: 'No tokens to refresh' });
    }
    
    // Refresh token cho từng OA configuration
    const results = await Promise.allSettled(
      oaConfigs.map(async (oaConfig) => {
        try {
          await refreshZnsToken(supabase, oaConfig);
          return { id: oaConfig.id, success: true };
        } catch (error: any) {
          logger.error({ oaConfigId: oaConfig.id, error: error.message }, 'Failed to refresh token');
          return { id: oaConfig.id, success: false, error: error.message };
        }
      })
    );
    
    // Tổng hợp kết quả
    const successful = results.filter((result) => result.status === 'fulfilled' && (result.value as any).success).length;
    const failed = results.filter((result) => result.status === 'rejected' || !(result.value as any).success).length;
    
    logger.info({ successful, failed, total: oaConfigs.length }, 'Refreshed ZNS tokens');
    
    return NextResponse.json({
      success: true,
      refreshed: successful,
      failed,
      total: oaConfigs.length
    });
  } catch (error: any) {
    logger.error({ error: error.message }, 'Error in refresh ZNS tokens endpoint');
    return new Response('Internal Server Error', { status: 500 });
  }
}

// Cho phép gọi GET trong môi trường development để dễ test
export async function GET(request: Request) {
  if (process.env.NODE_ENV !== 'development') {
    return new Response('Method not allowed', { status: 405 });
  }
  
  return POST(request);
}
