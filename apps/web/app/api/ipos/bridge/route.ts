import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';
import { createIPOSConnector } from '~/app/home/<USER>/integrations/_lib/server/ipos-connector-server';

/**
 * API Bridge cho iPOS
 * Cho phép mobile app truy cập dữ liệu từ iPOS ngay cả khi hệ thống không có API tương ứng
 */

// Cache cho các API call
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = {
  SHORT: 60 * 1000, // 1 phút
  MEDIUM: 5 * 60 * 1000, // 5 phút
  LONG: 30 * 60 * 1000, // 30 phút
};

/**
 * Lấy dữ liệu từ cache hoặc gọi API iPOS
 */
async function getFromCacheOrAPI(
  cacheKey: string,
  ttl: number,
  apiCall: () => Promise<any>,
) {
  const now = Date.now();
  const cachedData = cache.get(cacheKey);

  // Nếu có dữ liệu trong cache và chưa hết hạn
  if (cachedData && now - cachedData.timestamp < ttl) {
    return cachedData.data;
  }

  // Gọi API
  const data = await apiCall();

  // Lưu vào cache
  cache.set(cacheKey, { data, timestamp: now });

  return data;
}

/**
 * API Bridge cho iPOS
 */
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra xác thực
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 },
      );
    }

    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const endpoint = searchParams.get('endpoint');
    const resourceType = searchParams.get('resourceType');
    const id = searchParams.get('id');
    const limit = searchParams.get('limit');
    const page = searchParams.get('page');

    // Kiểm tra tham số bắt buộc
    if (!accountId || !endpoint) {
      return NextResponse.json(
        { error: 'Missing required parameters: accountId, endpoint' },
        { status: 400 },
      );
    }

    // Lấy integration ID từ accountId
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id')
      .eq('account_id', accountId)
      .eq('type', 'ipos')
      .single();

    if (integrationError || !integration) {
      return NextResponse.json(
        { error: 'iPOS integration not found for this account' },
        { status: 404 },
      );
    }

    // Tạo iPOS connector
    const connector = await createIPOSConnector(integration.id);

    // Xử lý các endpoint khác nhau
    let data;
    const cacheKey = `${accountId}:${endpoint}:${resourceType}:${id}:${limit}:${page}`;

    switch (endpoint) {
      case 'products':
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.MEDIUM,
          () => connector.getProducts({ limit: limit ? parseInt(limit) : 10, page: page ? parseInt(page) : 1 }),
        );
        break;

      case 'product':
        if (!id) {
          return NextResponse.json(
            { error: 'Missing required parameter: id' },
            { status: 400 },
          );
        }
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.MEDIUM,
          () => connector.getProductById(id),
        );
        break;

      case 'orders':
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.SHORT,
          () => connector.getOrders({ limit: limit ? parseInt(limit) : 10, page: page ? parseInt(page) : 1 }),
        );
        break;

      case 'order':
        if (!id) {
          return NextResponse.json(
            { error: 'Missing required parameter: id' },
            { status: 400 },
          );
        }
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.SHORT,
          () => connector.getOrderById(id),
        );
        break;

      case 'customers':
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.MEDIUM,
          () => connector.getCustomers({ limit: limit ? parseInt(limit) : 10, page: page ? parseInt(page) : 1 }),
        );
        break;

      case 'customer':
        if (!id) {
          return NextResponse.json(
            { error: 'Missing required parameter: id' },
            { status: 400 },
          );
        }
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.MEDIUM,
          () => connector.getCustomerById(id),
        );
        break;

      case 'transactions':
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.SHORT,
          () => connector.getTransactions({ limit: limit ? parseInt(limit) : 10, page: page ? parseInt(page) : 1 }),
        );
        break;

      case 'reports':
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.LONG,
          () => connector.getReports({ type: resourceType }),
        );
        break;

      case 'tables':
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.SHORT,
          () => connector.getTables(),
        );
        break;

      case 'inventory':
        data = await getFromCacheOrAPI(
          cacheKey,
          CACHE_TTL.SHORT,
          () => connector.getInventory({ productId: id }),
        );
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid endpoint' },
          { status: 400 },
        );
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    logger.error({ error }, 'Error in iPOS API Bridge');
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * API Bridge cho iPOS (POST)
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra xác thực
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 },
      );
    }

    // Lấy dữ liệu từ body
    const body = await request.json();
    const { accountId, endpoint, data: requestData } = body;

    // Kiểm tra tham số bắt buộc
    if (!accountId || !endpoint || !requestData) {
      return NextResponse.json(
        { error: 'Missing required parameters: accountId, endpoint, data' },
        { status: 400 },
      );
    }

    // Lấy integration ID từ accountId
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id')
      .eq('account_id', accountId)
      .eq('type', 'ipos')
      .single();

    if (integrationError || !integration) {
      return NextResponse.json(
        { error: 'iPOS integration not found for this account' },
        { status: 404 },
      );
    }

    // Tạo iPOS connector
    const connector = await createIPOSConnector(integration.id);

    // Xử lý các endpoint khác nhau
    let data;

    switch (endpoint) {
      case 'createOrder':
        data = await connector.createOrder(requestData);
        break;

      case 'updateOrder':
        data = await connector.updateOrder(requestData.id, requestData);
        break;

      case 'createCustomer':
        data = await connector.createCustomer(requestData);
        break;

      case 'updateCustomer':
        data = await connector.updateCustomer(requestData.id, requestData);
        break;

      case 'reserveTable':
        data = await connector.reserveTable(requestData);
        break;

      case 'payment':
        data = await connector.processPayment(requestData);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid endpoint' },
          { status: 400 },
        );
    }

    // Xóa cache liên quan
    const cachePrefix = `${accountId}:${endpoint}`;
    for (const key of cache.keys()) {
      if (key.startsWith(cachePrefix)) {
        cache.delete(key);
      }
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    logger.error({ error }, 'Error in iPOS API Bridge');
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 },
    );
  }
}
