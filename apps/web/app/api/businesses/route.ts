import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

import { createCorsResponse } from '~/lib/cors';

// Schema for query parameters
const BusinessesQuerySchema = z.object({
  search: z.string().optional(),
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val) : 1)),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val) : 10)),
  category: z.string().nullable().optional(),
});

/**
 * GET /api/businesses
 * Returns a list of businesses with their active themes
 */
export const GET = enhanceRouteHandler(
  async ({ request }) => {
    const logger = await getLogger();
    const supabase = getSupabaseServerAdminClient();

    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      // Parse and validate query params
      const { search, page, limit, category } = BusinessesQuerySchema.parse({
        search: searchParams.get('search'),
        page: searchParams.get('page'),
        limit: searchParams.get('limit'),
        category: searchParams.get('category'),
      });

      // Calculate pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      // Build query for accounts with active themes
      let query = supabase
        .from('accounts')
        .select(
          `
          id,
          name,
          slug,
          picture_url,
          themes:account_themes(
            id,
            name,
            mini_app_id,
            config
          )
        `,
          { count: 'exact' },
        )
        .eq('themes.is_active', true)
        .not('is_personal_account', 'eq', true) // Exclude personal accounts
        .range(from, to)
        .order('name', { ascending: true });

      // Apply search filter if provided
      if (search && search.trim() !== '') {
        // Sanitize search input to prevent SQL injection
        const sanitizedSearch = search.replace(/[%_]/g, '\\$&');
        query = query.ilike('name', `%${sanitizedSearch}%`);
      }

      // Apply category filter if provided
      if (category) {
        // Join with categories table if needed
        // This would require additional database structure
      }

      const { data, count, error } = await query;

      if (error) {
        logger.error(
          { error, search, page, limit, category },
          'Error fetching businesses',
        );
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Failed to fetch businesses',
            details: error.message,
            code: error.code,
          },
          500,
        );
      }

      // Transform data to include only businesses with active themes
      const businesses = data
        .filter((account) => account.themes && account.themes.length > 0)
        .map((account) => ({
          id: account.id,
          name: account.name,
          slug: account.slug,
          logo_url:
            account.themes[0]?.config?.logoUrl || account.picture_url || '',
          theme_id: account.themes[0]?.id,
          mini_app_id: account.themes[0]?.mini_app_id,
        }));

      return createCorsResponse(
        request,
        {
          success: true,
          data: businesses,
          pagination: {
            page,
            limit,
            total: count || 0,
            total_pages: count ? Math.ceil(count / limit) : 0,
          },
        },
        200,
      );
    } catch (error: any) {
      logger.error({ error: error.message }, 'Error in businesses API');

      if (error instanceof z.ZodError) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: false },
);
