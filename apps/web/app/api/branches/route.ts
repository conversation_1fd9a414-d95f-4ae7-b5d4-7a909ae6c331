import { NextResponse } from 'next/server';

import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';

import type { Branch } from './_lib/types';

const BranchesGetQuerySchema = z.object({
  search: z.string().optional().nullable(),
});

export const GET = enhanceRouteHandler(
  async function ({ request, user, supabase }) {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      const { search } = BranchesGetQuerySchema.parse({
        search: searchParams.get('search') || undefined,
      });

      const account_id = (user as any)?.user_metadata?.account_id;
      if (!account_id) {
        return NextResponse.json(
          { error: 'No account_id found in token' },
          { status: 401 },
        );
      }
      let query = supabase
        .from('branches')
        .select(
          `
          id,
          name,
          address,
          phone,
          location,
          is_active,
          account_id
          `,
        )
        .eq('account_id', account_id)
        .eq('is_active', true)
        .order('name', { ascending: true });
      if (search) {
        query = query.ilike('name', `%${search}%`);
      }

      const { data, error } = await query;
      if (error) throw error;

      const branches: Partial<Branch>[] = (data || []).map((branch) => {
        // Xử lý tọa độ GPS từ location nếu có
        let gpsCoordinates = null;
        if (branch.location) {
          // Kiểm tra nếu location có chứa tọa độ GPS (định dạng: số, số)
          const gpsMatch = branch.location.match(/(-?\d+\.\d+)\s*,\s*(-?\d+\.\d+)/);
          if (gpsMatch) {
            gpsCoordinates = {
              lat: parseFloat(gpsMatch[1]),
              lng: parseFloat(gpsMatch[2])
            };
          }
        }

        return {
          id: branch.id,
          name: branch.name,
          address: branch.address,
          phone: branch.phone,
          location: branch.location,
          gps_coordinates: gpsCoordinates,
          is_active: branch.is_active,
          account_id: branch.account_id,
        };
      });

      return NextResponse.json({
        data: branches || [],
      });
    } catch (error) {
      console.error('Error in GET /api/branches:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request parameters', details: error.errors },
          { status: 400 },
        );
      }

      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 },
      );
    }
  },
  { auth: true },
);
