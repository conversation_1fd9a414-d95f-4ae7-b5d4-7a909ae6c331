import { z } from 'zod';

export const BranchQuerySchema = z.object({
  account_id: z.string().uuid('Invalid account ID'),
  status: z.enum(['active', 'inactive']).optional(),
  search: z.string().optional(),
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
});

export const BranchCreateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  address: z.string().min(1, 'Address is required'),
  phone: z.string().optional(),
  location: z.string().optional(),
  account_id: z.string().uuid('Invalid account ID'),
});

export const BranchUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  address: z.string().min(1, 'Address is required').optional(),
  phone: z.string().optional(),
  location: z.string().optional(),
  is_active: z.boolean().optional(),
});
