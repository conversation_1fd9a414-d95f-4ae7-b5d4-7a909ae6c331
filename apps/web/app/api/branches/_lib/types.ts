export interface GpsCoordinates {
  lat: number;
  lng: number;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  phone: string | null;
  location?: string;
  gps_coordinates?: GpsCoordinates | null;
  is_active: boolean;
  account_id: string;
  created_at: string;
  updated_at: string;
  orders: number;
  revenues: number;
}

export interface BranchQueryParams {
  account_id: string;
  status?: 'active' | 'inactive';
  search?: string;
  page?: number;
  limit?: number;
}

export interface BranchCreateParams {
  name: string;
  address: string;
  phone?: string;
  location?: string;
  account_id: string;
}

export interface BranchUpdateParams {
  name?: string;
  address?: string;
  phone?: string;
  location?: string;
  is_active?: boolean;
}
