import { z } from 'zod';

export interface Product {
  id: string;
  name: string;
  description?: string;
  type: 'physical' | 'digital' | 'service';
  price: number;
  compare_at_price: number;
  status: 'active' | 'inactive' | 'draft';
  category_id: string;
  category_name?: string;
  sku?: string;
  barcode?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  tax_rate?: number;
  cost_per_item?: number;
  image_url?: string;
  image_urls?: string[];
  track_inventory: boolean;
  inventory: Array<{
    branch_id: string;
    stock: number;
    reserved_stock: number;
    attribute_id?: string;
  }>;
  branch_products: Array<{
    branch_id: string;
    is_active: boolean;
  }>;
  product_attributes: Array<{
    id: string;
    name: string;
    value: string;
    price_modifier: number;
  }>;
  account_id: string;
  created_at: string;
  updated_at: string;

  // Flash Sale information
  flash_sale?: {
    id: string;
    name: string;
    discount_percentage: number;
    start_time: string;
    end_time: string;
    quantity_limit?: number;
    quantity_sold?: number;
    original_price: number;
    discounted_price: number;
  };

  // Final price after all discounts
  final_price?: number;
}

export interface ProductQueryParams {
  account_id: string;
  search?: string;
  category_id?: string;
  status?: 'active' | 'inactive' | 'draft';
  page?: number;
  limit?: number;
}

export interface Voucher {
  id: string;
  code: string;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  min_order_value?: number;
  max_discount_value?: number;
  max_uses?: number;
  uses_count: number;
  start_date: string;
  end_date: string;
  status: 'active' | 'expired' | 'disabled';
  is_customer_specific: boolean;
  account_id: string;
  created_at: string;
  updated_at: string;
}

export interface VoucherRedemption {
  id: string;
  voucher_id: string;
  order_id: string;
  customer_id?: string;
  discount_amount: number;
  redeemed_at: string;
}

export interface CheckoutCalculation {
  subtotal: number;
  discount: number;
  total: number;
  items: CheckoutItem[];
  voucher?: {
    id: string;
    code: string;
    discount_amount: number;
    discount_type: 'percentage' | 'fixed';
    discount_value: number;
  };
}

export interface CheckoutItem {
  product_id: string;
  name: string;
  quantity: number;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  flash_sale_id?: string;
  attributes?: Array<{
    id: string;
    name: string;
    value: string;
    price_modifier: number;
  }>;
}
