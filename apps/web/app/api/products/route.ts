import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';

import { createCorsResponse } from '~/lib/cors';

import type { Product } from './_lib/types';

const OptimizedProductQuerySchema = z.object({
  search: z.string().optional().nullable(), // Chấp nhận null
  category_id: z.string().uuid('Invalid category ID').optional().nullable(), // Chấp nhận null
  page: z.coerce.number().min(1).default(1).optional(), // Đảm bảo default 1
  limit: z.coerce.number().min(1).max(50).default(10).optional(), // Đảm bảo default 10, max 50
  branch_id: z.string().uuid('Invalid branch ID').optional().nullable(), // Chấp nhận null
});

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: Get products with pagination and filtering
 *     description: Retrieves a list of products with optional filtering by search term, category, and branch
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter products by name
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Category ID to filter products
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Branch ID to filter products
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: Number of items per page
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: List of products
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     total:
 *                       type: integer
 *                       example: 100
 *                     totalPages:
 *                       type: integer
 *                       example: 10
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       500:
 *         description: Internal server error
 */
export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      const {
        search,
        category_id,
        page = 1,
        limit = 10,
        branch_id,
      } = OptimizedProductQuerySchema.parse({
        search: searchParams.get('search'),
        category_id: searchParams.get('category_id'),
        page: searchParams.get('page') ?? '1', // Default nếu null
        limit: searchParams.get('limit') ?? '10', // Default nếu null
        branch_id: searchParams.get('branch_id'),
      });

      // Get account_id from user metadata or from the token claims
      let account_id = user.user_metadata?.account_id;

      // If not found in user_metadata, try to get from app_metadata
      if (!account_id && user.app_metadata?.account_id) {
        account_id = user.app_metadata.account_id;
      }

      // If still not found, check if there's a team_id in the token
      if (!account_id && user.user_metadata?.team_id) {
        account_id = user.user_metadata.team_id;
      }

      // For test purposes, if we're in a test environment and still don't have an account_id,
      // try to get the first account the user has access to
      if (!account_id && process.env.NODE_ENV === 'test') {
        const { data: memberships } = await supabase
          .from('accounts_memberships')
          .select('account_id')
          .eq('user_id', user.sub)
          .limit(1);

        if (memberships && memberships.length > 0) {
          account_id = memberships[0].account_id;
        }
      }

      if (!account_id) {
        return createCorsResponse(
          request,
          { success: false, error: 'No account_id found in token or user memberships' },
          401,
        );
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      const now = new Date().toISOString();

      let query = supabase
        .from('products')
        .select(
          `
          id,
          name,
          price,
          compare_at_price,
          image_url,
          image_urls,
          categories (
            id,
            name
          ),
          inventory (
            branch_id,
            stock,
            reserved_stock,
            attribute_id
          ),
          branch_products!inner (
            branch_id,
            is_active
          ),
          product_attributes (
            id,
            name,
            value,
            price_modifier
          ),
          flash_sale:flash_sale_products(
            flash_sale_id,
            discount_percentage,
            quantity_limit,
            quantity_sold,
            flash_sales!inner(
              id,
              name,
              start_time,
              end_time,
              status
            )
          )
          `,
          { count: 'exact' },
        )
        .eq('account_id', account_id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .range(from, to);

      if (search) {
        query = query.ilike('name', `%${search}%`); // Chỉ tìm kiếm trên name
      }
      if (category_id) {
        query = query.eq('category_id', category_id);
      }
      if (branch_id) {
        query = query
          .eq('branch_products.branch_id', branch_id)
          .eq('branch_products.is_active', true); // Đảm bảo sản phẩm active tại chi nhánh
      }

      const { data, error, count } = await query;

      if (error) {
        throw new Error(error.message || 'Database query failed');
      }

      const products = (data || []).map((product) => {
        // Process flash sale data
        let flashSale = null;
        let finalPrice = product.price;

        if (product.flash_sale && product.flash_sale.length > 0) {
          const fsProduct = product.flash_sale[0];
          const fs = fsProduct.flash_sales;

          // Check if flash sale is active
          const currentTime = new Date();
          const startTime = new Date(fs.start_time);
          const endTime = new Date(fs.end_time);

          if (fs.status === 'active' && currentTime >= startTime && currentTime <= endTime) {
            const discountedPrice = product.price * (1 - fsProduct.discount_percentage / 100);

            flashSale = {
              id: fs.id,
              name: fs.name,
              discount_percentage: fsProduct.discount_percentage,
              start_time: fs.start_time,
              end_time: fs.end_time,
              original_price: product.price,
              discounted_price: discountedPrice
            };

            finalPrice = discountedPrice;
          }
        }

        return {
          id: product.id,
          name: product.name,
          price: product.price,
          final_price: finalPrice, // Add final price after discounts
          compare_at_price: product.compare_at_price,
          image_url: product.image_url,
          image_urls: product.image_urls,
          category: {
            id: product.categories?.id,
            name: product.categories?.name,
          },
          flash_sale: flashSale, // Add flash sale information
          inventory_by_branch:
            product.inventory?.reduce((acc: any, inv: any) => {
              if (inv.stock > inv.reserved_stock) {
                acc[inv.branch_id] = {
                  stock: inv.stock,
                  reserved_stock: inv.reserved_stock,
                  attribute_id: inv.attribute_id,
                };
              }
              return acc;
            }, {}) || {},
          attributes:
            product.product_attributes?.map((attr: any) => ({
              id: attr.id,
              name: attr.name,
              value: attr.value,
              price_modifier: attr.price_modifier,
            })) || [],
        };
      }) as Product[];

      // // Lọc bỏ sản phẩm không có inventory_by_branch (không còn hàng)
      // const filteredProducts = products.filter(
      //   (product) => Object.keys(product.inventory_by_branch).length > 0,
      // );

      return createCorsResponse(
        request,
        {
          success: true,
          data: products,
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages: count ? Math.ceil(count / limit) : 0,
          },
        },
        200,
      );
    } catch (error: any) {
      const logger = await getLogger();

      if (error instanceof z.ZodError) {
        logger.warn({ error: error.errors }, 'Invalid request parameters in GET /api/products');
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      // Log the error for debugging
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          user_id: user?.sub,
          user_metadata: user?.user_metadata,
          app_metadata: user?.app_metadata
        },
        'Error in GET /api/products'
      );

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
          // For test environments, include more details to help with debugging
          debug: process.env.NODE_ENV === 'test' ? {
            user_id: user?.sub,
            has_user_metadata: !!user?.user_metadata,
            has_app_metadata: !!user?.app_metadata,
          } : undefined,
        },
        500,
      );
    }
  },
  { auth: true },
);
