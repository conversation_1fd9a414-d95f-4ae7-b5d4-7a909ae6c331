import { NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { Database } from '~/lib/database.types';
import { getLogger } from '@kit/shared/logger';

import { getIPOSCredentialsFromIntegration, handleIPOSWebhook } from '~/app/home/<USER>/integrations/_lib/server/ipos-connector-server';

/**
 * Xử lý webhook từ iPOS
 */
export async function POST(request: Request) {
  const logger = await getLogger();

  try {
    // Xác thực webhook
    const signature = request.headers.get('x-ipos-signature');

    if (!signature) {
      logger.warn('Missing iPOS webhook signature');
      return new Response('Missing signature', { status: 401 });
    }

    // Lấy dữ liệu webhook
    const payload = await request.json();

    if (!payload || !payload.event || !payload.data) {
      logger.warn('Invalid iPOS webhook payload');
      return new Response('Invalid payload', { status: 400 });
    }

    logger.info({ event: payload.event }, 'Received iPOS webhook');

    // Lấy integration ID từ payload
    const integrationId = payload.integration_id;

    if (!integrationId) {
      logger.warn('Missing integration_id in iPOS webhook payload');
      return new Response('Missing integration_id', { status: 400 });
    }

    // Lấy thông tin integration
    const supabase = getSupabaseServerAdminClient<Database>();

    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('type', 'ipos' as Database['public']['Enums']['integration_type'])
      .single();

    if (integrationError || !integration) {
      logger.error({ error: integrationError, integrationId }, 'iPOS integration not found');
      return new Response('Integration not found', { status: 404 });
    }

    // Kiểm tra trạng thái integration
    if (integration.status !== 'connected' as Database['public']['Enums']['integration_status'] || !integration.enabled) {
      logger.warn({ integrationId }, 'iPOS integration is not connected or disabled');
      return new Response('Integration is not connected or disabled', { status: 400 });
    }

    // Xác thực webhook signature
    const webhookSecret = integration.webhook_secret;

    if (webhookSecret) {
      // Tính toán signature từ payload và secret
      const crypto = require('crypto');
      const hmac = crypto.createHmac('sha256', webhookSecret);
      const calculatedSignature = hmac.update(JSON.stringify(payload)).digest('hex');

      // So sánh signature
      if (calculatedSignature !== signature) {
        logger.warn({ integrationId }, 'Invalid iPOS webhook signature');
        return new Response('Invalid signature', { status: 401 });
      }
    }

    // Lấy credentials từ integration
    const credentials = await getIPOSCredentialsFromIntegration(integrationId);

    // Xử lý webhook
    await handleIPOSWebhook(credentials, payload);

    // Cập nhật thời gian đồng bộ cuối cùng
    await supabase
      .from('integrations')
      .update({
        last_sync_at: new Date().toISOString(),
      })
      .eq('id', integrationId);

    logger.info({ event: payload.event, integrationId }, 'Successfully processed iPOS webhook');

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error({ error }, 'Error processing iPOS webhook');
    return new Response(`Error processing webhook: ${error.message}`, { status: 500 });
  }
}
