import { NextResponse } from 'next/server';

import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';

// Sử dụng enhanceRouteHandler để xử lý xác thực
export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const logger = await getLogger();

      // Lấy dữ liệu từ request
      const data = await request.json();
      const { eventType, themeId, visitorId, deviceType, ...eventData } = data;

      // Lấy account_id từ user metadata
      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return NextResponse.json(
          { error: 'No account_id found in token' },
          { status: 401 },
        );
      }

      // Lấy user_id từ user
      const user_id = user.id;

      if (!eventType || !themeId || !visitorId || !deviceType) {
        return NextResponse.json(
          { error: 'Missing required fields' },
          { status: 400 },
        );
      }

      // Xử lý sự kiện dựa trên loại
      // Tạo event_data tùy thuộc vào loại sự kiện
      let eventDataObj = {};

      switch (eventType) {
        case 'pageview':
          // Tạo dữ liệu cho sự kiện pageview
          eventDataObj = {
            pagePath: eventData.pagePath || '/',
            pageTitle: eventData.pageTitle || null,
            duration: eventData.duration || null,
            utmSource: eventData.utmSource || null,
            utmMedium: eventData.utmMedium || null,
            utmCampaign: eventData.utmCampaign || null,
            browser: eventData.browser || null,
            os: eventData.os || null,
            ipAddress: eventData.ipAddress || null,
            referrer: eventData.referrer || null,
          };
          break;

        case 'product_view':
        case 'add_to_cart':
        case 'purchase':
          // Tạo dữ liệu cho sự kiện sản phẩm
          eventDataObj = {
            productId: eventData.productId,
            quantity: eventData.quantity || 1,
          };

          // Thêm thông tin đơn hàng nếu là sự kiện mua hàng
          if (eventType === 'purchase' && eventData.orderId) {
            eventDataObj.orderId = eventData.orderId;
            eventDataObj.amount = eventData.amount || 0;
          }
          break;

        default:
          // Sự kiện không được hỗ trợ
          return NextResponse.json(
            { error: 'Unsupported event type' },
            { status: 400 },
          );
      }

      // Lưu sự kiện vào bảng analytics_events sử dụng service role
      try {
        const { data: insertData, error: eventError } = await supabase
          .from('analytics_events')
          .insert({
            account_id: account_id,
            theme_id: themeId,
            event_type: eventType,
            event_data: eventDataObj,
            visitor_id: visitorId,
            user_id: user_id,
            device_type: deviceType,
            source: 'zalo_miniapp',
          })
          .select();

        if (eventError) {
          logger.error(
            { error: eventError, eventType },
            'Error saving analytics event',
          );
          return NextResponse.json(
            {
              error: 'Error saving analytics event',
              details: eventError.message,
            },
            { status: 500 },
          );
        }

        logger.info(
          { eventId: insertData[0].id, eventType, account_id, user_id },
          'Analytics event saved successfully',
        );
      } catch (error) {
        logger.error(
          { error, eventType },
          'Unexpected error saving analytics event',
        );
        return NextResponse.json(
          {
            error: 'Unexpected error saving analytics event',
          },
          { status: 500 },
        );
      }

      return NextResponse.json({ success: true });
    } catch (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Error processing analytics event');
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 },
      );
    }
  },
  { auth: true },
);
