import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';
import { emitAppEvent } from '@kit/shared/events';

export async function POST(request: NextRequest) {
  try {
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();
    
    // Lấy token từ header
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid authorization header' }, { status: 401 });
    }
    
    const token = authHeader.substring(7); // Bỏ 'Bearer ' ở đầu
    
    // Xác thực token Zalo
    // Trong môi trường thực, bạn cần triển khai hàm verifyZaloToken
    // Ở đây chúng ta giả định token hợp lệ để đơn giản hóa
    const userId = 'zalo_user_id'; // Trong thực tế, lấy từ kết quả xác thực token
    
    // Lấy dữ liệu từ request
    const data = await request.json();
    const { 
      eventType, 
      accountId, 
      themeId, 
      visitorId, 
      customerId, 
      deviceType, 
      ...eventData 
    } = data;
    
    // Kiểm tra các trường bắt buộc
    if (!eventType || !accountId || !themeId || !visitorId || !deviceType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Lưu sự kiện trực tiếp vào bảng analytics_events
    const { error: insertError } = await supabase.from('analytics_events').insert({
      account_id: accountId,
      theme_id: themeId,
      event_type: eventType,
      event_data: eventData,
      visitor_id: visitorId,
      user_id: customerId || userId,
      device_type: deviceType,
      source: 'zalo_miniapp',
    });
    
    if (insertError) {
      logger.error({ error: insertError, eventType }, 'Error inserting analytics event');
      return NextResponse.json({ error: 'Error processing event' }, { status: 500 });
    }
    
    // Phát sự kiện để hệ thống App Events có thể xử lý
    emitAppEvent({
      type: `zalo.${eventType}`,
      payload: {
        accountId,
        themeId,
        visitorId,
        customerId: customerId || userId,
        deviceType,
        ...eventData
      }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error processing analytics event');
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
