import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';

import { createCorsResponse } from '~/lib/cors';

const FlashSaleQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1).optional(),
  limit: z.coerce.number().min(1).max(50).default(10).optional(),
  status: z.enum(['active', 'draft', 'ended', 'cancelled']).optional(),
});

/**
 * @swagger
 * /api/flash-sales:
 *   get:
 *     summary: Get flash sales with pagination and filtering
 *     description: Retrieves a list of flash sales with optional filtering by status
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, draft, ended, cancelled]
 *         description: Filter flash sales by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: Number of items per page
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: List of flash sales
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       500:
 *         description: Internal server error
 */
export const GET = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      const {
        page = 1,
        limit = 10,
        status,
      } = FlashSaleQuerySchema.parse({
        page: searchParams.get('page') ?? '1',
        limit: searchParams.get('limit') ?? '10',
        status: searchParams.get('status'),
      });

      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return createCorsResponse(
          request,
          { success: false, error: 'No account_id found in token' },
          401,
        );
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;
      const now = new Date().toISOString();

      let query = supabase
        .from('flash_sales')
        .select(
          `
          id,
          name,
          description,
          start_time,
          end_time,
          status,
          created_at,
          updated_at,
          products:flash_sale_products(
            id,
            product_id,
            discount_percentage,
            quantity_limit,
            quantity_sold,
            products(
              id,
              name,
              price,
              image_url
            )
          )
          `,
          { count: 'exact' },
        )
        .eq('account_id', account_id)
        .order('created_at', { ascending: false })
        .range(from, to);

      // Filter by status if provided
      if (status) {
        query = query.eq('status', status);
      }

      // If status is not provided, show active flash sales by default
      // Active means status='active' AND current time is between start_time and end_time
      if (!status) {
        query = query
          .eq('status', 'active')
          .lte('start_time', now)
          .gte('end_time', now);
      }

      const { data, error, count } = await query;

      if (error) {
        throw new Error(error.message || 'Database query failed');
      }

      // Process the data to format it nicely
      const flashSales = (data || []).map((flashSale) => {
        // Calculate total products and total discount
        const totalProducts = flashSale.products?.length || 0;
        
        // Format products data
        const products = flashSale.products?.map((product) => {
          const originalPrice = product.products.price;
          const discountedPrice = originalPrice * (1 - product.discount_percentage / 100);
          
          return {
            id: product.product_id,
            name: product.products.name,
            image_url: product.products.image_url,
            original_price: originalPrice,
            discounted_price: discountedPrice,
            discount_percentage: product.discount_percentage,
            quantity_limit: product.quantity_limit,
            quantity_sold: product.quantity_sold,
          };
        });
        
        return {
          id: flashSale.id,
          name: flashSale.name,
          description: flashSale.description,
          start_time: flashSale.start_time,
          end_time: flashSale.end_time,
          status: flashSale.status,
          created_at: flashSale.created_at,
          updated_at: flashSale.updated_at,
          total_products: totalProducts,
          products: products,
        };
      });

      return createCorsResponse(
        request,
        {
          success: true,
          data: flashSales,
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages: count ? Math.ceil(count / limit) : 0,
          },
        },
        200,
      );
    } catch (error: any) {
      if (error instanceof z.ZodError) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: true },
);
