{"name": "web", "version": "0.1.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"analyze": "ANALYZE=true pnpm run build", "build": "next build", "build:test": "NODE_ENV=test next build --turbopack", "clean": "git clean -xdf .next .turbo node_modules", "dev": "next dev --turbo | pino-pretty -c", "lint": "eslint .", "lint:fix": "next lint --fix", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "start": "next start", "start:test": "NODE_ENV=test next start", "test:api": "cd ../e2e && pnpm test:api", "api:refresh-token": "cd ../e2e && pnpm api:refresh-token", "generate:api-docs": "node scripts/generate-api-docs.js", "typecheck": "tsc --noEmit", "supabase": "supabase", "supabase:start": "supabase status || supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:test": "supabase db test", "supabase:db:lint": "supabase db lint", "supabase:db:diff": "supabase db diff", "supabase:deploy": "supabase link --project-ref $SUPABASE_PROJECT_REF && supabase db push", "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app", "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts", "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts", "supabase:db:dump:local": "supabase db dump --local --data-only", "supabase:db:migrate": "supabase db push --local --include-all", "supabase:db:migrate:fix-customer-stats": "supabase db push --local --db-only -f supabase/migrations/20250702000000_fix_customer_stats.sql"}, "dependencies": {"@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@kit/accounts": "workspace:*", "@kit/admin": "workspace:*", "@kit/analytics": "workspace:*", "@kit/auth": "workspace:*", "@kit/billing": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/cms": "workspace:*", "@kit/database-webhooks": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/i18n": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/next": "workspace:*", "@kit/notifications": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/team-accounts": "workspace:*", "@kit/ui": "workspace:*", "@kit/zns": "workspace:*", "@makerkit/data-loader-supabase-core": "^0.0.10", "@makerkit/data-loader-supabase-nextjs": "^1.2.5", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-icons": "^1.3.2", "@supabase/supabase-js": "2.49.4", "@tanstack/react-query": "5.72.2", "@tanstack/react-table": "^8.21.2", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-placeholder": "^2.1.0", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/react": "^2.1.0", "@tiptap/starter-kit": "^2.1.0", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "jose": "^6.0.10", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "nanoid": "^5.1.5", "next": "15.3.0", "next-sitemap": "^4.2.3", "next-themes": "0.4.6", "papaparse": "^5.5.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-joyride": "next", "recharts": "2.15.2", "sharp": "^0.33.5", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@next/bundle-analyzer": "15.3.0", "@tailwindcss/postcss": "^4.1.3", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@types/supertest": "^6.0.3", "axios": "^1.6.0", "babel-plugin-react-compiler": "19.0.0-beta-e993439-20250405", "cssnano": "^7.0.6", "dotenv": "^16.5.0", "form-data": "^4.0.2", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "next-swagger-doc": "^0.4.1", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "supabase": "^2.20.12", "supertest": "^7.1.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-react": "^5.20.8", "tailwindcss": "4.1.3", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.3.1", "typescript": "^5.8.3"}, "prettier": "@kit/prettier-config", "browserslist": ["last 1 versions", "> 0.7%", "not dead"]}