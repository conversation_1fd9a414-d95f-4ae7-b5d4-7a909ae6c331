/*
 * -------------------------------------------------------
 * Add CDP permissions to existing roles and create sample data
 * -------------------------------------------------------
 */

-- Insert CDP permissions for existing roles
-- Note: This assumes standard roles exist (owner, admin, member)

-- Add CDP permissions for 'owner' role (if exists)
INSERT INTO public.role_permissions (role, permission)
SELECT 'owner', 'cdp.manage'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'owner')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions 
    WHERE role = 'owner' AND permission = 'cdp.manage'::public.app_permissions
);

INSERT INTO public.role_permissions (role, permission)
SELECT 'owner', 'cdp.view'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'owner')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions 
    WHERE role = 'owner' AND permission = 'cdp.view'::public.app_permissions
);

-- Add CDP permissions for 'admin' role (if exists)
INSERT INTO public.role_permissions (role, permission)
SELECT 'admin', 'cdp.manage'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'admin')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions 
    WHERE role = 'admin' AND permission = 'cdp.manage'::public.app_permissions
);

INSERT INTO public.role_permissions (role, permission)
SELECT 'admin', 'cdp.view'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'admin')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions 
    WHERE role = 'admin' AND permission = 'cdp.view'::public.app_permissions
);

-- Add CDP view permission for 'member' role (if exists)
INSERT INTO public.role_permissions (role, permission)
SELECT 'member', 'cdp.view'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'member')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions 
    WHERE role = 'member' AND permission = 'cdp.view'::public.app_permissions
);

-- Add CDP permissions for any role with hierarchy_level = 1 (highest level)
INSERT INTO public.role_permissions (role, permission)
SELECT r.name, 'cdp.manage'::public.app_permissions
FROM public.roles r
WHERE r.hierarchy_level = 1
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions rp
    WHERE rp.role = r.name AND rp.permission = 'cdp.manage'::public.app_permissions
);

INSERT INTO public.role_permissions (role, permission)
SELECT r.name, 'cdp.view'::public.app_permissions
FROM public.roles r
WHERE r.hierarchy_level = 1
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions rp
    WHERE rp.role = r.name AND rp.permission = 'cdp.view'::public.app_permissions
);

-- Add CDP view permission for any role with hierarchy_level <= 3
INSERT INTO public.role_permissions (role, permission)
SELECT r.name, 'cdp.view'::public.app_permissions
FROM public.roles r
WHERE r.hierarchy_level <= 3
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions rp
    WHERE rp.role = r.name AND rp.permission = 'cdp.view'::public.app_permissions
);

/*
 * -------------------------------------------------------
 * Create sample data for testing (optional)
 * -------------------------------------------------------
 */

-- Function to create sample CDP data for an account
CREATE OR REPLACE FUNCTION public.create_sample_cdp_data(target_account_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
    -- Only allow service_role to execute this function
    IF current_user != 'service_role' THEN
        RAISE EXCEPTION 'Only service_role can create sample data';
    END IF;

    -- Insert sample customer profiles
    INSERT INTO public.customer_profiles (
        account_id, email, first_name, last_name, phone,
        total_orders, total_spent, avg_order_value,
        engagement_score, churn_risk_score, value_tier,
        last_active_at, last_purchase_at
    ) VALUES
    (target_account_id, '<EMAIL>', 'Nguyễn', 'Văn An', '+***********',
     15, ********, 833333, 0.85, 0.15, 'high',
     NOW() - INTERVAL '2 days', NOW() - INTERVAL '5 days'),
    (target_account_id, '<EMAIL>', 'Trần', 'Thị Bình', '+***********',
     8, 3200000, 400000, 0.72, 0.35, 'medium',
     NOW() - INTERVAL '1 week', NOW() - INTERVAL '2 weeks'),
    (target_account_id, '<EMAIL>', 'Lê', 'Minh Châu', '+***********',
     3, 850000, 283333, 0.45, 0.75, 'low',
     NOW() - INTERVAL '3 weeks', NOW() - INTERVAL '1 month'),
    (target_account_id, '<EMAIL>', 'Phạm', 'Thị Dung', '+***********',
     22, ********, 852273, 0.92, 0.08, 'high',
     NOW() - INTERVAL '1 day', NOW() - INTERVAL '3 days'),
    (target_account_id, '<EMAIL>', 'Hoàng', 'Văn Em', '+***********',
     5, 1250000, 250000, 0.58, 0.62, 'medium',
     NOW() - INTERVAL '2 weeks', NOW() - INTERVAL '3 weeks')
    ON CONFLICT (account_id, email) DO NOTHING;

    -- Insert sample customer segments
    INSERT INTO public.customer_segments (
        account_id, name, description, type, criteria,
        customer_count, growth_rate, is_auto_updating
    ) VALUES
    (target_account_id, 'Khách hàng VIP', 'Khách hàng có giá trị cao với chi tiêu trên 5 triệu VND', 'value_based',
     '{"total_spent": {"gte": 5000000}}'::jsonb, 1247, 0.15, true),
    (target_account_id, 'Khách hàng mới', 'Khách hàng đăng ký trong 30 ngày qua', 'behavioral',
     '{"created_at": {"gte": "30_days_ago"}}'::jsonb, 856, 0.23, true),
    (target_account_id, 'Có nguy cơ rời bỏ', 'Khách hàng có điểm rủi ro cao', 'predictive',
     '{"churn_risk_score": {"gte": 0.7}}'::jsonb, 342, -0.08, true),
    (target_account_id, 'Khách hàng trung thành', 'Khách hàng có hơn 10 đơn hàng và engagement cao', 'behavioral',
     '{"total_orders": {"gte": 10}, "engagement_score": {"gte": 0.8}}'::jsonb, 2156, 0.12, true),
    (target_account_id, 'Mobile-first Users', 'Khách hàng chủ yếu sử dụng mobile app', 'behavioral',
     '{"primary_device": "mobile", "app_usage": {"gte": 0.8}}'::jsonb, 3421, 0.25, true)
    ON CONFLICT (account_id, name) DO NOTHING;

    -- Insert sample analytics data
    INSERT INTO public.analytics_data (
        account_id, total_customers, active_customers, monthly_revenue,
        conversion_rate, churn_rate, avg_order_value, customer_lifetime_value,
        engagement_score, period
    ) VALUES
    (target_account_id, 12847, 8934, **********, 0.032, 0.025, 156750, 2340000, 0.742, 'current_month')
    ON CONFLICT DO NOTHING;

    -- Insert sample integration statuses
    INSERT INTO public.integration_statuses (
        account_id, name, provider, category, status,
        last_sync, records_synced, health_score
    ) VALUES
    (target_account_id, 'Mailchimp', 'mailchimp', 'email', 'connected',
     NOW() - INTERVAL '1 hour', 12847, 0.95),
    (target_account_id, 'Google Analytics', 'google', 'analytics', 'connected',
     NOW() - INTERVAL '30 minutes', 45678, 0.88),
    (target_account_id, 'Shopify', 'shopify', 'ecommerce', 'connected',
     NOW() - INTERVAL '2 hours', 8934, 0.92),
    (target_account_id, 'Facebook Ads', 'facebook', 'advertising', 'connected',
     NOW() - INTERVAL '45 minutes', 15623, 0.91),
    (target_account_id, 'HubSpot CRM', 'hubspot', 'crm', 'connected',
     NOW() - INTERVAL '3 hours', 7892, 0.89),
    (target_account_id, 'Zalo OA', 'zalo', 'social', 'connected',
     NOW() - INTERVAL '1.5 hours', 9876, 0.93)
    ON CONFLICT (account_id, provider, name) DO NOTHING;

    -- Insert sample AI insights
    INSERT INTO public.ai_insights (
        account_id, type, title, description, confidence, impact, category,
        data, recommendations, status
    ) VALUES
    (target_account_id, 'opportunity', 'High-Value Customer Upsell Opportunity',
     'Customers who view product pages 3+ times have 45% higher conversion rate',
     0.87, 'high', 'revenue',
     '{"potential_revenue": 125000, "affected_customers": 1247, "conversion_lift": 0.45}'::jsonb,
     ARRAY['Create targeted email campaign for high-intent users', 'Implement dynamic pricing for repeat viewers'],
     'active'),
    (target_account_id, 'risk', 'High Churn Risk Detected',
     '156 customers at high churn risk. Send re-engagement campaign within 48 hours',
     0.92, 'critical', 'churn',
     '{"at_risk_customers": 156, "potential_revenue_loss": 89000, "churn_reduction_potential": 0.23}'::jsonb,
     ARRAY['Send personalized re-engagement emails', 'Offer exclusive discounts to at-risk customers'],
     'active'),
    (target_account_id, 'optimization', 'Email Campaign Performance Optimization',
     'Email campaigns sent on Tuesday have 23% higher open rates',
     0.78, 'medium', 'engagement',
     '{"best_send_day": "Tuesday", "open_rate_improvement": 0.23, "affected_campaigns": 45}'::jsonb,
     ARRAY['Schedule future campaigns for Tuesday mornings', 'A/B test different send times'],
     'active'),
    (target_account_id, 'prediction', 'Customer Lifetime Value Prediction',
     'New customers from organic search have 34% higher predicted LTV',
     0.81, 'high', 'revenue',
     '{"organic_ltv_lift": 0.34, "predicted_ltv": 2890000, "traffic_source": "organic"}'::jsonb,
     ARRAY['Invest more in SEO and content marketing', 'Create organic-specific onboarding flow'],
     'active'),
    (target_account_id, 'anomaly', 'Unusual Purchase Pattern Detected',
     'Mobile app purchases increased 67% this week - investigate cause',
     0.94, 'medium', 'performance',
     '{"mobile_purchase_increase": 0.67, "week_over_week": true, "platform": "mobile_app"}'::jsonb,
     ARRAY['Analyze mobile app changes or promotions', 'Monitor for sustained growth'],
     'active')
    ON CONFLICT DO NOTHING;

END;
$$;

-- Grant execute permission to service_role
GRANT EXECUTE ON FUNCTION public.create_sample_cdp_data(UUID) TO service_role;
