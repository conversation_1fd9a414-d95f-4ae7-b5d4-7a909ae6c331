/*
 * -------------------------------------------------------
 * Add CDP permissions to existing roles
 * -------------------------------------------------------
 */

-- First, add CDP permissions to the app_permissions enum if they don't exist
DO $$
BEGIN
    -- Add cdp.view permission if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'cdp.view' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'app_permissions')) THEN
        ALTER TYPE public.app_permissions ADD VALUE 'cdp.view';
    END IF;

    -- Add cdp.manage permission if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'cdp.manage' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'app_permissions')) THEN
        ALTER TYPE public.app_permissions ADD VALUE 'cdp.manage';
    END IF;
END $$;

-- Insert CDP permissions for existing roles
-- Note: This assumes standard roles exist (owner, admin, member)

-- Add CDP permissions for 'owner' role (if exists)
INSERT INTO public.role_permissions (role, permission)
SELECT 'owner', 'cdp.manage'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'owner')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions
    WHERE role = 'owner' AND permission = 'cdp.manage'::public.app_permissions
);

INSERT INTO public.role_permissions (role, permission)
SELECT 'owner', 'cdp.view'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'owner')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions
    WHERE role = 'owner' AND permission = 'cdp.view'::public.app_permissions
);

-- Add CDP permissions for 'admin' role (if exists)
INSERT INTO public.role_permissions (role, permission)
SELECT 'admin', 'cdp.manage'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'admin')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions
    WHERE role = 'admin' AND permission = 'cdp.manage'::public.app_permissions
);

INSERT INTO public.role_permissions (role, permission)
SELECT 'admin', 'cdp.view'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'admin')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions
    WHERE role = 'admin' AND permission = 'cdp.view'::public.app_permissions
);

-- Add CDP view permission for 'member' role (if exists)
INSERT INTO public.role_permissions (role, permission)
SELECT 'member', 'cdp.view'::public.app_permissions
WHERE EXISTS (SELECT 1 FROM public.roles WHERE name = 'member')
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions
    WHERE role = 'member' AND permission = 'cdp.view'::public.app_permissions
);

-- Add CDP permissions for any role with hierarchy_level = 1 (highest level)
INSERT INTO public.role_permissions (role, permission)
SELECT r.name, 'cdp.manage'::public.app_permissions
FROM public.roles r
WHERE r.hierarchy_level = 1
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions rp
    WHERE rp.role = r.name AND rp.permission = 'cdp.manage'::public.app_permissions
);

INSERT INTO public.role_permissions (role, permission)
SELECT r.name, 'cdp.view'::public.app_permissions
FROM public.roles r
WHERE r.hierarchy_level = 1
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions rp
    WHERE rp.role = r.name AND rp.permission = 'cdp.view'::public.app_permissions
);

-- Add CDP view permission for any role with hierarchy_level <= 3
INSERT INTO public.role_permissions (role, permission)
SELECT r.name, 'cdp.view'::public.app_permissions
FROM public.roles r
WHERE r.hierarchy_level <= 3
AND NOT EXISTS (
    SELECT 1 FROM public.role_permissions rp
    WHERE rp.role = r.name AND rp.permission = 'cdp.view'::public.app_permissions
);

/*
 * -------------------------------------------------------
 * Create sample data for testing (optional)
 * -------------------------------------------------------
 */

-- Function to create sample CDP data for an account
CREATE OR REPLACE FUNCTION public.create_sample_cdp_data(target_account_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
    -- Only allow service_role to execute this function
    IF current_user != 'service_role' THEN
        RAISE EXCEPTION 'Only service_role can create sample data';
    END IF;

    -- Insert sample customer profiles
    INSERT INTO public.customer_profiles (
        account_id, email, first_name, last_name, phone, location,
        total_orders, total_spent, avg_order_value,
        engagement_score, churn_risk_score, value_tier,
        tags, notes, metadata
    ) VALUES
    (target_account_id, '<EMAIL>', 'Nguyễn', 'Văn An', '+***********', 'Hà Nội',
     15, ********, 833333, 0.85, 0.15, 'high',
     ARRAY['vip', 'loyal'], 'Khách hàng VIP từ chương trình giới thiệu', '{}'),
    (target_account_id, '<EMAIL>', 'Trần', 'Thị Bình', '+***********', 'TP.HCM',
     8, 3200000, 400000, 0.72, 0.35, 'medium',
     ARRAY['regular'], 'Khách hàng thường xuyên', '{}'),
    (target_account_id, '<EMAIL>', 'Lê', 'Minh Châu', '+***********', 'Đà Nẵng',
     3, 850000, 283333, 0.45, 0.75, 'low',
     ARRAY['new'], 'Khách hàng mới', '{}')
    ON CONFLICT (email) DO NOTHING;

    -- Insert sample customer segments
    INSERT INTO public.customer_segments (
        account_id, name, description, type, criteria,
        customer_count, growth_rate, is_auto_updating, is_active
    ) VALUES
    (target_account_id, 'Khách hàng VIP', 'Khách hàng có giá trị cao với chi tiêu trên 5 triệu VND', 'value_based',
     '{"total_spent": {"operator": "gte", "value": 5000000}}'::jsonb, 3, 15.5, true, true),
    (target_account_id, 'Khách hàng có tương tác cao', 'Khách hàng có điểm engagement cao', 'behavioral',
     '{"engagement_score": {"operator": "gte", "value": 0.7}}'::jsonb, 2, 8.2, true, true)
    ON CONFLICT DO NOTHING;

    -- Insert sample analytics data
    INSERT INTO public.analytics_data (
        account_id, metric_name, metric_value, metric_type, period, metadata
    ) VALUES
    (target_account_id, 'total_customers', 3, 'count', 'current_month', '{"source": "customer_profiles"}'),
    (target_account_id, 'total_revenue', ********, 'currency', 'current_month', '{"source": "customer_profiles", "currency": "VND"}')
    ON CONFLICT DO NOTHING;

    -- Insert sample integration statuses
    INSERT INTO public.integration_statuses (
        account_id, name, provider, category, status,
        last_sync, records_synced, health_score, config
    ) VALUES
    (target_account_id, 'Email Marketing Platform', 'mailchimp', 'marketing', 'connected',
     NOW() - INTERVAL '1 hour', 1250, 0.95, '{"api_key": "***", "list_id": "abc123"}'),
    (target_account_id, 'SMS Gateway', 'twilio', 'communication', 'connected',
     NOW() - INTERVAL '30 minutes', 456, 0.88, '{"account_sid": "***", "auth_token": "***"}')
    ON CONFLICT DO NOTHING;

    -- Insert sample AI insights
    INSERT INTO public.ai_insights (
        account_id, type, title, description, confidence, impact, category,
        data, recommendations, status
    ) VALUES
    (target_account_id, 'trend', 'Tăng trưởng tương tác khách hàng',
     'Điểm tương tác khách hàng đã tăng 15% trong tháng qua',
     0.85, 'high', 'engagement',
     '{"engagement_increase": 0.15, "timeframe": "30_days"}'::jsonb,
     ARRAY['Tiếp tục chiến lược tương tác hiện tại', 'Mở rộng các chiến dịch thành công'],
     'active')
    ON CONFLICT DO NOTHING;

END;
$$;

-- Grant execute permission to service_role
GRANT EXECUTE ON FUNCTION public.create_sample_cdp_data(UUID) TO service_role;
