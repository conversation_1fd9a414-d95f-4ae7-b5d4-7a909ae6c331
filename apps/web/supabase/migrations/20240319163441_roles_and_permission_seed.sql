-- Seed the roles table with default roles: 'owner' (level 1), 'member' (level 2), and 'customer' (level 3)
insert into public.roles(name, hierarchy_level)
values
    ('owner', 1),
    ('member', 2),
    ('customer', 3)
on conflict (name) do nothing;

-- Seed the role_permissions table with default permissions
insert into public.role_permissions(role, permission)
values
    -- Owner permissions (full access)
    ('owner', 'teams.manage'),
    ('owner', 'roles.manage'),
    ('owner', 'billing.manage'),
    ('owner', 'settings.manage'),
    ('owner', 'members.manage'),
    ('owner', 'invites.manage'),
    ('owner', 'products.manage'),
    ('owner', 'categories.manage'),
    ('owner', 'orders.manage'),
    ('owner', 'points.manage'),
    ('owner', 'branches.manage'),
    ('owner', 'inventory.manage'),
    ('owner', 'notifications.manage'),
    -- MiniApp permissions
    ('owner', 'miniapps.manage'),
    ('owner', 'miniapps.view'),
    ('owner', 'miniapps.themes.manage'),
    -- Customer permissions
    ('owner', 'customers.manage'),
    ('owner', 'customers.view'),
     -- ZNS permissions
    ('owner', 'zns.manage'),
    ('owner', 'zns.view'),

    -- Integration permissions
    ('owner', 'integrations.manage'),
    ('owner', 'integrations.view'),
    ('owner', 'integrations.connect'),

    -- Data management permissions
    ('owner', 'data.manage'),

    -- CDP permissions
    ('owner', 'cdp.manage'),
    ('owner', 'cdp.view'),
    ('owner', 'cdp.profiles.manage'),
    ('owner', 'cdp.profiles.view'),
    ('owner', 'cdp.segments.manage'),
    ('owner', 'cdp.segments.view'),
    ('owner', 'cdp.journeys.manage'),
    ('owner', 'cdp.journeys.view'),
    ('owner', 'cdp.analytics.view'),
    ('owner', 'cdp.insights.view'),

    -- Member permissions (limited management access)
    ('member', 'customers.manage'),
    ('member', 'branches.manage'),
    ('member', 'miniapps.manage'),
    ('member', 'products.manage'),
    ('member', 'categories.manage'),
    ('member', 'orders.manage'),
    ('member', 'inventory.manage'),
    ('member', 'notifications.manage'),

    -- CDP permissions for members (view only)
    ('member', 'cdp.view'),
    ('member', 'cdp.profiles.view'),
    ('member', 'cdp.segments.view'),
    ('member', 'cdp.journeys.view'),
    ('member', 'cdp.analytics.view'),
    ('member', 'cdp.insights.view'),

    -- Customer permissions (view only)
    ('customer', 'orders.view'),
    ('customer', 'points.view')
on conflict (role, permission) do nothing;
