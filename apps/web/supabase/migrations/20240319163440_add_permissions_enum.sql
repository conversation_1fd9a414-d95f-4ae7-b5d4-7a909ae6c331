-- Add new permissions to app_permissions enum
DO $$
DECLARE
    new_value text;
    new_values text[] := ARRAY[
      -- Basic permissions
      'teams.manage',
      'roles.manage',
      'billing.manage',
      'settings.manage',
      'members.manage',
      'invites.manage',

      -- Store management permissions
      'products.manage',
      'categories.manage',
      'orders.manage',
      'points.manage',
      'branches.manage',
      'inventory.manage',
      'notifications.manage',

      -- MiniApp permissions
      'miniapps.manage',
      'miniapps.view',
      'miniapps.themes.manage',

       -- Zns
      'zns.manage',
      'zns.view',

      -- Customer permissions
      'customers.manage',
      'customers.view',
      'orders.view',
      'points.view',

      -- Integration permissions
      'integrations.manage',
      'integrations.view',
      'integrations.connect',

      -- Data management permissions
      'data.manage'
    ];
BEGIN
    FOREACH new_value IN ARRAY new_values
    LOOP
        EXECUTE 'ALTER TYPE public.app_permissions ADD VALUE IF NOT EXISTS ''' || new_value || '''';
    END LOOP;
END
$$;
