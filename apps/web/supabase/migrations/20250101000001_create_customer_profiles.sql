-- Migration: Create Customer Profiles and related tables
-- Description: Core CDP tables for customer profiles, identities, and behavioral data

-- Customer Profiles Table
CREATE TABLE IF NOT EXISTS public.customer_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

  -- Identity fields
  primary_email TEXT,
  primary_phone TEXT,
  primary_user_id UUID,

  -- Personal information
  first_name TEXT,
  last_name TEXT,
  full_name TEXT GENERATED ALWAYS AS (
    CASE
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL
      THEN first_name || ' ' || last_name
      WHEN first_name IS NOT NULL
      THEN first_name
      WHEN last_name IS NOT NULL
      THEN last_name
      ELSE NULL
    END
  ) STORED,

  -- Demographics (JSONB for flexibility)
  demographics JSONB DEFAULT '{}',

  -- Behavioral metrics
  total_sessions INTEGER DEFAULT 0,
  total_pageviews INTEGER DEFAULT 0,
  total_events INTEGER DEFAULT 0,
  total_purchases INTEGER DEFAULT 0,
  total_revenue DECIMAL(15,2) DEFAULT 0,
  average_order_value DECIMAL(15,2) DEFAULT 0,
  avg_session_duration INTEGER DEFAULT 0, -- in seconds

  -- Engagement scores (0-1 scale)
  email_engagement_score DECIMAL(5,4) DEFAULT 0 CHECK (email_engagement_score >= 0 AND email_engagement_score <= 1),
  website_engagement_score DECIMAL(5,4) DEFAULT 0 CHECK (website_engagement_score >= 0 AND website_engagement_score <= 1),
  social_engagement_score DECIMAL(5,4) DEFAULT 0 CHECK (social_engagement_score >= 0 AND social_engagement_score <= 1),
  overall_engagement_score DECIMAL(5,4) DEFAULT 0 CHECK (overall_engagement_score >= 0 AND overall_engagement_score <= 1),

  -- Predictive scores (0-1 scale)
  churn_risk_score DECIMAL(5,4) DEFAULT 0 CHECK (churn_risk_score >= 0 AND churn_risk_score <= 1),
  purchase_propensity_score DECIMAL(5,4) DEFAULT 0 CHECK (purchase_propensity_score >= 0 AND purchase_propensity_score <= 1),
  engagement_propensity_score DECIMAL(5,4) DEFAULT 0 CHECK (engagement_propensity_score >= 0 AND engagement_propensity_score <= 1),
  upsell_propensity_score DECIMAL(5,4) DEFAULT 0 CHECK (upsell_propensity_score >= 0 AND upsell_propensity_score <= 1),
  lifetime_value_score DECIMAL(15,2) DEFAULT 0,

  -- Lifecycle and segmentation
  lifecycle_stage TEXT DEFAULT 'prospect' CHECK (lifecycle_stage IN ('prospect', 'lead', 'customer', 'advocate', 'churned')),
  customer_value_tier TEXT DEFAULT 'low' CHECK (customer_value_tier IN ('low', 'medium', 'high', 'vip')),

  -- Preferences
  preferences JSONB DEFAULT '{}',

  -- Custom attributes and tags
  custom_attributes JSONB DEFAULT '{}',
  tags TEXT[] DEFAULT '{}',

  -- Timestamps
  first_seen_at TIMESTAMPTZ,
  last_seen_at TIMESTAMPTZ,
  last_activity_at TIMESTAMPTZ,
  last_order_at TIMESTAMPTZ,
  computed_at TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Customer Identities Table (for identity resolution)
CREATE TABLE IF NOT EXISTS public.customer_identities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_profile_id UUID NOT NULL REFERENCES public.customer_profiles(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

  identity_type TEXT NOT NULL CHECK (identity_type IN ('email', 'phone', 'user_id', 'visitor_id', 'social_id', 'external_id')),
  identity_value TEXT NOT NULL,
  verified BOOLEAN DEFAULT false,
  source TEXT NOT NULL,

  first_seen_at TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),

  -- Ensure unique identity per account
  UNIQUE(account_id, identity_type, identity_value)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_customer_profiles_account_id ON public.customer_profiles(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_primary_email ON public.customer_profiles(primary_email) WHERE primary_email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_customer_profiles_primary_phone ON public.customer_profiles(primary_phone) WHERE primary_phone IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_customer_profiles_primary_user_id ON public.customer_profiles(primary_user_id) WHERE primary_user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_customer_profiles_lifecycle_stage ON public.customer_profiles(account_id, lifecycle_stage);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_value_tier ON public.customer_profiles(account_id, customer_value_tier);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_churn_risk ON public.customer_profiles(account_id, churn_risk_score DESC);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_ltv ON public.customer_profiles(account_id, lifetime_value_score DESC);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_last_activity ON public.customer_profiles(account_id, last_activity_at DESC);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_tags ON public.customer_profiles USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_demographics ON public.customer_profiles USING GIN(demographics);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_preferences ON public.customer_profiles USING GIN(preferences);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_custom_attributes ON public.customer_profiles USING GIN(custom_attributes);

-- Indexes for customer identities
CREATE INDEX IF NOT EXISTS idx_customer_identities_profile_id ON public.customer_identities(customer_profile_id);
CREATE INDEX IF NOT EXISTS idx_customer_identities_account_id ON public.customer_identities(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_identities_type_value ON public.customer_identities(identity_type, identity_value);
CREATE INDEX IF NOT EXISTS idx_customer_identities_value ON public.customer_identities(identity_value);
CREATE INDEX IF NOT EXISTS idx_customer_identities_source ON public.customer_identities(source);

-- RLS Policies
ALTER TABLE public.customer_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_identities ENABLE ROW LEVEL SECURITY;

-- Customer Profiles RLS Policies
CREATE POLICY "customer_profiles_read" ON public.customer_profiles FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "customer_profiles_insert" ON public.customer_profiles FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.profiles.create')
  );

CREATE POLICY "customer_profiles_update" ON public.customer_profiles FOR UPDATE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.profiles.update')
  );

CREATE POLICY "customer_profiles_delete" ON public.customer_profiles FOR DELETE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.profiles.delete')
  );

-- Customer Identities RLS Policies
CREATE POLICY "customer_identities_read" ON public.customer_identities FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "customer_identities_insert" ON public.customer_identities FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.profiles.create')
  );

CREATE POLICY "customer_identities_update" ON public.customer_identities FOR UPDATE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.profiles.update')
  );

CREATE POLICY "customer_identities_delete" ON public.customer_identities FOR DELETE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.profiles.delete')
  );

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_customer_profile_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updated_at
CREATE TRIGGER customer_profiles_updated_at
  BEFORE UPDATE ON public.customer_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_customer_profile_updated_at();

-- Function to automatically update computed scores
CREATE OR REPLACE FUNCTION update_customer_computed_scores()
RETURNS TRIGGER AS $$
BEGIN
  -- Update overall engagement score as average of individual scores
  NEW.overall_engagement_score = (
    COALESCE(NEW.email_engagement_score, 0) +
    COALESCE(NEW.website_engagement_score, 0) +
    COALESCE(NEW.social_engagement_score, 0)
  ) / 3.0;

  -- Update average order value
  IF NEW.total_purchases > 0 THEN
    NEW.average_order_value = NEW.total_revenue / NEW.total_purchases;
  ELSE
    NEW.average_order_value = 0;
  END IF;

  -- Update computed_at timestamp
  NEW.computed_at = now();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for computed scores
CREATE TRIGGER customer_profiles_computed_scores
  BEFORE INSERT OR UPDATE ON public.customer_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_customer_computed_scores();

-- Add foreign key constraint to link analytics events with customer profiles (if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'analytics_events') THEN
    ALTER TABLE public.analytics_events
    ADD COLUMN IF NOT EXISTS customer_profile_id UUID REFERENCES public.customer_profiles(id);

    -- Index for the new foreign key
    CREATE INDEX IF NOT EXISTS idx_analytics_events_customer_profile
    ON public.analytics_events(customer_profile_id) WHERE customer_profile_id IS NOT NULL;
  END IF;
END $$;
