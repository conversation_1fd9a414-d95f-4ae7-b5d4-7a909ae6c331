/*
 * -------------------------------------------------------
 * Add customer memberships to existing users
 * This creates customer accounts and memberships for testing CDP
 * -------------------------------------------------------
 */

-- Add customer memberships for existing users
DO $$
DECLARE
    team_account_id UUID;
    customer_user_id UUID;
    customer_account_id UUID;
BEGIN
    -- Get the makerkit team account
    SELECT id INTO team_account_id
    FROM public.accounts
    WHERE slug = 'makerkit' AND is_personal_account = false;
    
    -- If no makerkit account, exit
    IF team_account_id IS NULL THEN
        RAISE NOTICE 'No makerkit account found, skipping customer creation';
        RETURN;
    END IF;
    
    RAISE NOTICE 'Adding customer memberships for makerkit account: %', team_account_id;

    -- Create customer accounts for existing users and add them as customers
    -- User 1: <EMAIL>
    SELECT id INTO customer_user_id FROM auth.users WHERE email = '<EMAIL>';
    IF customer_user_id IS NOT NULL THEN
        -- Create personal account for this user
        INSERT INTO public.accounts (
            id,
            name,
            email,
            is_personal_account,
            primary_owner_user_id,
            public_data,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            'Test Customer',
            '<EMAIL>',
            true,
            customer_user_id,
            jsonb_build_object(
                'total_spent', 2500000,
                'total_orders', 8,
                'engagement_score', 0.72,
                'churn_risk_score', 0.25,
                'last_active_at', '2024-12-30T10:00:00Z',
                'tags', ARRAY['customer', 'test'],
                'metadata', '{}'::jsonb
            ),
            NOW() - INTERVAL '15 days',
            NOW()
        ) RETURNING id INTO customer_account_id;
        
        -- Add customer membership
        INSERT INTO public.accounts_memberships (
            id,
            account_id,
            user_id,
            account_role,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            team_account_id,
            customer_user_id,
            'customer',
            NOW() - INTERVAL '15 days',
            NOW()
        ) ON CONFLICT (account_id, user_id) DO NOTHING;
        
        RAISE NOTICE 'Added customer <NAME_EMAIL>';
    END IF;

    -- User 2: <EMAIL>
    SELECT id INTO customer_user_id FROM auth.users WHERE email = '<EMAIL>';
    IF customer_user_id IS NOT NULL THEN
        -- Create personal account for this user
        INSERT INTO public.accounts (
            id,
            name,
            email,
            is_personal_account,
            primary_owner_user_id,
            public_data,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            'Custom Customer',
            '<EMAIL>',
            true,
            customer_user_id,
            jsonb_build_object(
                'total_spent', 1800000,
                'total_orders', 5,
                'engagement_score', 0.65,
                'churn_risk_score', 0.45,
                'last_active_at', '2024-12-28T14:30:00Z',
                'tags', ARRAY['customer', 'custom'],
                'metadata', '{}'::jsonb
            ),
            NOW() - INTERVAL '20 days',
            NOW()
        ) RETURNING id INTO customer_account_id;
        
        -- Add customer membership
        INSERT INTO public.accounts_memberships (
            id,
            account_id,
            user_id,
            account_role,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            team_account_id,
            customer_user_id,
            'customer',
            NOW() - INTERVAL '20 days',
            NOW()
        ) ON CONFLICT (account_id, user_id) DO NOTHING;
        
        RAISE NOTICE 'Added customer <NAME_EMAIL>';
    END IF;

    -- Create additional sample customers
    -- Customer 3: New customer
    INSERT INTO auth.users (
        id,
        email,
        email_confirmed_at,
        created_at,
        updated_at,
        raw_app_meta_data,
        raw_user_meta_data,
        is_super_admin,
        role
    ) VALUES (
        gen_random_uuid(),
        '<EMAIL>',
        NOW(),
        NOW() - INTERVAL '10 days',
        NOW(),
        '{"provider": "email", "providers": ["email"]}'::jsonb,
        '{"name": "Nguyễn Văn An", "phone": "+***********"}'::jsonb,
        false,
        'authenticated'
    ) RETURNING id INTO customer_user_id;
    
    -- Create personal account
    INSERT INTO public.accounts (
        id,
        name,
        email,
        phone,
        is_personal_account,
        primary_owner_user_id,
        public_data,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        'Nguyễn Văn An',
        '<EMAIL>',
        '+***********',
        true,
        customer_user_id,
        jsonb_build_object(
            'total_spent', ********,
            'total_orders', 15,
            'engagement_score', 0.85,
            'churn_risk_score', 0.15,
            'last_active_at', '2024-12-31T16:45:00Z',
            'tags', ARRAY['customer', 'vip'],
            'metadata', '{}'::jsonb
        ),
        NOW() - INTERVAL '10 days',
        NOW()
    );
    
    -- Add customer membership
    INSERT INTO public.accounts_memberships (
        id,
        account_id,
        user_id,
        account_role,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        team_account_id,
        customer_user_id,
        'customer',
        NOW() - INTERVAL '10 days',
        NOW()
    );

    -- Customer 4: Another sample customer
    INSERT INTO auth.users (
        id,
        email,
        email_confirmed_at,
        created_at,
        updated_at,
        raw_app_meta_data,
        raw_user_meta_data,
        is_super_admin,
        role
    ) VALUES (
        gen_random_uuid(),
        '<EMAIL>',
        NOW(),
        NOW() - INTERVAL '25 days',
        NOW(),
        '{"provider": "email", "providers": ["email"]}'::jsonb,
        '{"name": "Trần Thị Bình", "phone": "+***********"}'::jsonb,
        false,
        'authenticated'
    ) RETURNING id INTO customer_user_id;
    
    -- Create personal account
    INSERT INTO public.accounts (
        id,
        name,
        email,
        phone,
        is_personal_account,
        primary_owner_user_id,
        public_data,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        'Trần Thị Bình',
        '<EMAIL>',
        '+***********',
        true,
        customer_user_id,
        jsonb_build_object(
            'total_spent', 3200000,
            'total_orders', 8,
            'engagement_score', 0.72,
            'churn_risk_score', 0.35,
            'last_active_at', '2024-12-25T14:30:00Z',
            'tags', ARRAY['customer', 'regular'],
            'metadata', '{}'::jsonb
        ),
        NOW() - INTERVAL '25 days',
        NOW()
    );
    
    -- Add customer membership
    INSERT INTO public.accounts_memberships (
        id,
        account_id,
        user_id,
        account_role,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        team_account_id,
        customer_user_id,
        'customer',
        NOW() - INTERVAL '25 days',
        NOW()
    );

    RAISE NOTICE 'Successfully created customer memberships for makerkit account';

END;
$$;
