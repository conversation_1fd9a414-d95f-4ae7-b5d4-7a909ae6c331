-- Migration: Create Customer Journeys and related tables
-- Description: Tables for customer journey orchestration and automation

-- Customer Journeys Table
CREATE TABLE IF NOT EXISTS public.customer_journeys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  
  -- Basic Information
  name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 255),
  description TEXT,
  
  -- Journey Configuration
  trigger_type TEXT NOT NULL CHECK (trigger_type IN ('segment_entry', 'event', 'date', 'manual')),
  trigger_config JSONB NOT NULL DEFAULT '{}',
  
  -- Journey Definition (workflow steps)
  steps JSONB NOT NULL DEFAULT '[]',
  
  -- Settings
  is_active BOOLEAN DEFAULT false,
  is_draft BOOLEAN DEFAULT true,
  
  -- Performance tracking
  total_entries INTEGER DEFAULT 0,
  total_completions INTEGER DEFAULT 0,
  total_exits INTEGER DEFAULT 0,
  conversion_rate DECIMAL(5,4) DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  published_at TIMESTAMPTZ,
  
  -- Metadata
  created_by UUID,
  tags TEXT[] DEFAULT '{}',
  custom_attributes JSONB DEFAULT '{}'
);

-- Journey Executions Table (tracks individual customer journey runs)
CREATE TABLE IF NOT EXISTS public.journey_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journey_id UUID NOT NULL REFERENCES public.customer_journeys(id) ON DELETE CASCADE,
  customer_profile_id UUID NOT NULL REFERENCES public.customer_profiles(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  
  -- Execution details
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed', 'paused', 'cancelled')),
  current_step_index INTEGER DEFAULT 0,
  current_step_id TEXT,
  
  -- Entry details
  entry_trigger TEXT NOT NULL,
  entry_data JSONB DEFAULT '{}',
  
  -- Progress tracking
  steps_completed INTEGER DEFAULT 0,
  steps_failed INTEGER DEFAULT 0,
  
  -- Timestamps
  started_at TIMESTAMPTZ DEFAULT now(),
  completed_at TIMESTAMPTZ,
  failed_at TIMESTAMPTZ,
  last_activity_at TIMESTAMPTZ DEFAULT now(),
  
  -- Metadata
  execution_data JSONB DEFAULT '{}',
  error_details JSONB DEFAULT '{}'
);

-- Journey Step Executions Table (tracks individual step executions)
CREATE TABLE IF NOT EXISTS public.journey_step_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journey_execution_id UUID NOT NULL REFERENCES public.journey_executions(id) ON DELETE CASCADE,
  journey_id UUID NOT NULL REFERENCES public.customer_journeys(id) ON DELETE CASCADE,
  customer_profile_id UUID NOT NULL REFERENCES public.customer_profiles(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  
  -- Step details
  step_id TEXT NOT NULL,
  step_type TEXT NOT NULL CHECK (step_type IN ('wait', 'email', 'sms', 'webhook', 'condition', 'segment_check', 'update_profile')),
  step_index INTEGER NOT NULL,
  
  -- Execution details
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'skipped')),
  
  -- Step configuration and data
  step_config JSONB DEFAULT '{}',
  input_data JSONB DEFAULT '{}',
  output_data JSONB DEFAULT '{}',
  
  -- Timestamps
  scheduled_at TIMESTAMPTZ,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  failed_at TIMESTAMPTZ,
  
  -- Error handling
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  error_details JSONB DEFAULT '{}',
  
  -- Performance
  execution_duration_ms INTEGER,
  
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Journey Performance History Table
CREATE TABLE IF NOT EXISTS public.journey_performance_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journey_id UUID NOT NULL REFERENCES public.customer_journeys(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  
  -- Performance metrics
  date DATE NOT NULL,
  entries INTEGER DEFAULT 0,
  completions INTEGER DEFAULT 0,
  exits INTEGER DEFAULT 0,
  conversion_rate DECIMAL(5,4) DEFAULT 0,
  avg_completion_time_hours DECIMAL(10,2) DEFAULT 0,
  
  -- Step performance
  step_performance JSONB DEFAULT '{}',
  
  computed_at TIMESTAMPTZ DEFAULT now(),
  
  -- Ensure unique entry per journey per date
  UNIQUE(journey_id, date)
);

-- Journey Templates Table
CREATE TABLE IF NOT EXISTS public.journey_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Template info
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('onboarding', 'nurturing', 'retention', 'winback', 'upsell', 'support')),
  
  -- Template definition
  trigger_type TEXT NOT NULL,
  trigger_config JSONB NOT NULL DEFAULT '{}',
  steps JSONB NOT NULL DEFAULT '[]',
  
  -- Metadata
  tags TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_customer_journeys_account_id ON public.customer_journeys(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_journeys_active ON public.customer_journeys(account_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_customer_journeys_trigger_type ON public.customer_journeys(account_id, trigger_type);
CREATE INDEX IF NOT EXISTS idx_customer_journeys_tags ON public.customer_journeys USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_journey_executions_journey ON public.journey_executions(journey_id);
CREATE INDEX IF NOT EXISTS idx_journey_executions_customer ON public.journey_executions(customer_profile_id);
CREATE INDEX IF NOT EXISTS idx_journey_executions_status ON public.journey_executions(journey_id, status);
CREATE INDEX IF NOT EXISTS idx_journey_executions_active ON public.journey_executions(journey_id, status) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_journey_executions_account ON public.journey_executions(account_id);

CREATE INDEX IF NOT EXISTS idx_journey_step_executions_execution ON public.journey_step_executions(journey_execution_id);
CREATE INDEX IF NOT EXISTS idx_journey_step_executions_journey ON public.journey_step_executions(journey_id);
CREATE INDEX IF NOT EXISTS idx_journey_step_executions_customer ON public.journey_step_executions(customer_profile_id);
CREATE INDEX IF NOT EXISTS idx_journey_step_executions_status ON public.journey_step_executions(journey_id, status);
CREATE INDEX IF NOT EXISTS idx_journey_step_executions_scheduled ON public.journey_step_executions(scheduled_at) WHERE status = 'pending' AND scheduled_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_journey_performance_journey_date ON public.journey_performance_history(journey_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_journey_performance_account ON public.journey_performance_history(account_id);

CREATE INDEX IF NOT EXISTS idx_journey_templates_category ON public.journey_templates(category);
CREATE INDEX IF NOT EXISTS idx_journey_templates_active ON public.journey_templates(is_active) WHERE is_active = true;

-- RLS Policies
ALTER TABLE public.customer_journeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journey_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journey_step_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journey_performance_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journey_templates ENABLE ROW LEVEL SECURITY;

-- Customer Journeys RLS Policies
CREATE POLICY "customer_journeys_read" ON public.customer_journeys FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "customer_journeys_insert" ON public.customer_journeys FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.journeys.create')
  );

CREATE POLICY "customer_journeys_update" ON public.customer_journeys FOR UPDATE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.journeys.update')
  );

CREATE POLICY "customer_journeys_delete" ON public.customer_journeys FOR DELETE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.journeys.delete')
  );

-- Journey Executions RLS Policies
CREATE POLICY "journey_executions_read" ON public.journey_executions FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "journey_executions_insert" ON public.journey_executions FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.journeys.read')
  );

CREATE POLICY "journey_executions_update" ON public.journey_executions FOR UPDATE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.journeys.read')
  );

-- Journey Step Executions RLS Policies
CREATE POLICY "journey_step_executions_read" ON public.journey_step_executions FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "journey_step_executions_insert" ON public.journey_step_executions FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.journeys.read')
  );

CREATE POLICY "journey_step_executions_update" ON public.journey_step_executions FOR UPDATE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.journeys.read')
  );

-- Journey Performance History RLS Policies
CREATE POLICY "journey_performance_read" ON public.journey_performance_history FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "journey_performance_insert" ON public.journey_performance_history FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.analytics.read')
  );

-- Journey Templates RLS Policies (public read, admin write)
CREATE POLICY "journey_templates_read" ON public.journey_templates FOR SELECT
  TO authenticated USING (true);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_customer_journeys_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updated_at
CREATE TRIGGER customer_journeys_updated_at
  BEFORE UPDATE ON public.customer_journeys
  FOR EACH ROW
  EXECUTE FUNCTION update_customer_journeys_updated_at();

-- Function to update journey performance metrics
CREATE OR REPLACE FUNCTION update_journey_performance()
RETURNS TRIGGER AS $$
BEGIN
  -- Update journey totals when execution status changes
  IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
    UPDATE public.customer_journeys
    SET
      total_entries = (
        SELECT COUNT(*) FROM public.journey_executions 
        WHERE journey_id = NEW.journey_id
      ),
      total_completions = (
        SELECT COUNT(*) FROM public.journey_executions 
        WHERE journey_id = NEW.journey_id AND status = 'completed'
      ),
      total_exits = (
        SELECT COUNT(*) FROM public.journey_executions 
        WHERE journey_id = NEW.journey_id AND status IN ('failed', 'cancelled')
      )
    WHERE id = NEW.journey_id;
    
    -- Update conversion rate
    UPDATE public.customer_journeys
    SET conversion_rate = CASE 
      WHEN total_entries > 0 THEN total_completions::DECIMAL / total_entries::DECIMAL
      ELSE 0
    END
    WHERE id = NEW.journey_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update journey performance
CREATE TRIGGER journey_execution_performance_update
  AFTER INSERT OR UPDATE ON public.journey_executions
  FOR EACH ROW
  EXECUTE FUNCTION update_journey_performance();

-- Insert default journey templates
INSERT INTO public.journey_templates (name, description, category, trigger_type, trigger_config, steps) VALUES
(
  'Welcome Onboarding',
  'Welcome new customers with a series of helpful emails',
  'onboarding',
  'segment_entry',
  '{"segment_name": "New Customers"}',
  '[
    {
      "id": "welcome_email",
      "type": "email",
      "name": "Welcome Email",
      "config": {
        "template": "welcome",
        "subject": "Welcome to our platform!",
        "delay_hours": 0
      }
    },
    {
      "id": "wait_1_day",
      "type": "wait",
      "name": "Wait 1 Day",
      "config": {
        "duration_hours": 24
      }
    },
    {
      "id": "getting_started",
      "type": "email",
      "name": "Getting Started Guide",
      "config": {
        "template": "getting_started",
        "subject": "Get the most out of your account",
        "delay_hours": 0
      }
    }
  ]'
),
(
  'Churn Prevention',
  'Re-engage customers at risk of churning',
  'retention',
  'segment_entry',
  '{"segment_name": "Churn Risk Customers"}',
  '[
    {
      "id": "check_activity",
      "type": "condition",
      "name": "Check Recent Activity",
      "config": {
        "condition": "last_activity_days < 30",
        "true_path": "send_reengagement",
        "false_path": "end"
      }
    },
    {
      "id": "send_reengagement",
      "type": "email",
      "name": "Re-engagement Email",
      "config": {
        "template": "reengagement",
        "subject": "We miss you! Come back for exclusive offers",
        "delay_hours": 0
      }
    },
    {
      "id": "wait_3_days",
      "type": "wait",
      "name": "Wait 3 Days",
      "config": {
        "duration_hours": 72
      }
    },
    {
      "id": "special_offer",
      "type": "email",
      "name": "Special Offer",
      "config": {
        "template": "special_offer",
        "subject": "Exclusive 20% discount just for you",
        "delay_hours": 0
      }
    }
  ]'
),
(
  'High Value Upsell',
  'Upsell premium features to high-value customers',
  'upsell',
  'segment_entry',
  '{"segment_name": "High Value Customers"}',
  '[
    {
      "id": "check_subscription",
      "type": "condition",
      "name": "Check Subscription Tier",
      "config": {
        "condition": "subscription_tier != \"premium\"",
        "true_path": "send_upsell",
        "false_path": "end"
      }
    },
    {
      "id": "send_upsell",
      "type": "email",
      "name": "Premium Features Email",
      "config": {
        "template": "premium_upsell",
        "subject": "Unlock premium features for your business",
        "delay_hours": 0
      }
    },
    {
      "id": "wait_1_week",
      "type": "wait",
      "name": "Wait 1 Week",
      "config": {
        "duration_hours": 168
      }
    },
    {
      "id": "follow_up",
      "type": "email",
      "name": "Follow-up Email",
      "config": {
        "template": "upsell_followup",
        "subject": "Questions about premium features?",
        "delay_hours": 0
      }
    }
  ]'
);
