/*
 * -------------------------------------------------------
 * Manually create sample customers for testing CDP
 * This creates sample customer accounts and memberships for 'makerkit' account
 * -------------------------------------------------------
 */

-- Create sample customers for any existing team account
DO $$
DECLARE
    team_account_id UUID;
    customer_user_id UUID;
    customer_account_id UUID;
    sample_customers JSONB[];
    customer_data JSONB;
BEGIN
    -- Get any existing team account
    SELECT id INTO team_account_id
    FROM public.accounts
    WHERE is_personal_account = false
    ORDER BY created_at
    LIMIT 1;

    -- If no team account exists, skip creating customers
    IF team_account_id IS NULL THEN
        RAISE NOTICE 'No team account found, skipping customer creation';
        RETURN;
    END IF;

    RAISE NOTICE 'Using team account: %', team_account_id;

    -- Sample customer data
    sample_customers := ARRAY[
        '{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>", "phone": "+***********", "total_spent": ********, "total_orders": 15, "engagement_score": 0.85, "churn_risk_score": 0.15, "last_active_at": "2024-12-30T10:00:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Trần Thị Bình", "phone": "+***********", "total_spent": 3200000, "total_orders": 8, "engagement_score": 0.72, "churn_risk_score": 0.35, "last_active_at": "2024-12-25T14:30:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Lê Minh Châu", "phone": "+***********", "total_spent": 850000, "total_orders": 3, "engagement_score": 0.45, "churn_risk_score": 0.75, "last_active_at": "2024-12-10T09:15:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Phạm Thị Dung", "phone": "+***********", "total_spent": ********, "total_orders": 22, "engagement_score": 0.92, "churn_risk_score": 0.08, "last_active_at": "2024-12-31T16:45:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Hoàng Văn Em", "phone": "+84901234571", "total_spent": 1250000, "total_orders": 5, "engagement_score": 0.58, "churn_risk_score": 0.62, "last_active_at": "2024-12-18T11:20:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Vũ Thị Phương", "phone": "+84901234572", "total_spent": 6750000, "total_orders": 12, "engagement_score": 0.78, "churn_risk_score": 0.25, "last_active_at": "2024-12-29T13:10:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Đỗ Minh Giang", "phone": "+84901234573", "total_spent": 2100000, "total_orders": 6, "engagement_score": 0.65, "churn_risk_score": 0.45, "last_active_at": "2024-12-22T08:30:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Bùi Thị Hoa", "phone": "+84901234574", "total_spent": 9800000, "total_orders": 18, "engagement_score": 0.88, "churn_risk_score": 0.12, "last_active_at": "2024-12-30T15:25:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Ngô Văn Đức", "phone": "+84901234575", "total_spent": 450000, "total_orders": 2, "engagement_score": 0.35, "churn_risk_score": 0.85, "last_active_at": "2024-12-05T12:00:00Z"}'::jsonb,
        '{"email": "<EMAIL>", "name": "Lý Thị Mai", "phone": "+84901234576", "total_spent": 15200000, "total_orders": 25, "engagement_score": 0.95, "churn_risk_score": 0.05, "last_active_at": "2024-12-31T18:00:00Z"}'::jsonb
    ];

    -- Loop through sample customers and create them
    FOR i IN 1..array_length(sample_customers, 1) LOOP
        customer_data := sample_customers[i];

        -- Check if user already exists
        SELECT id INTO customer_user_id
        FROM auth.users
        WHERE email = customer_data->>'email';

        -- Create user if doesn't exist
        IF customer_user_id IS NULL THEN
            INSERT INTO auth.users (
                id,
                email,
                email_confirmed_at,
                created_at,
                updated_at,
                raw_app_meta_data,
                raw_user_meta_data,
                is_super_admin,
                role
            ) VALUES (
                gen_random_uuid(),
                customer_data->>'email',
                NOW(),
                NOW() - INTERVAL '30 days' + (random() * INTERVAL '25 days'), -- Random creation time in last 30 days
                NOW(),
                '{"provider": "email", "providers": ["email"]}'::jsonb,
                jsonb_build_object(
                    'name', customer_data->>'name',
                    'phone', customer_data->>'phone'
                ),
                false,
                'authenticated'
            ) RETURNING id INTO customer_user_id;

            RAISE NOTICE 'Created user: % (%)', customer_data->>'name', customer_data->>'email';
        END IF;

        -- Check if customer account already exists
        SELECT id INTO customer_account_id
        FROM public.accounts
        WHERE email = customer_data->>'email' AND is_personal_account = true;

        -- Create customer account if doesn't exist
        IF customer_account_id IS NULL THEN
            INSERT INTO public.accounts (
                id,
                name,
                email,
                phone,
                is_personal_account,
                primary_owner_user_id,
                public_data,
                created_at,
                updated_at
            ) VALUES (
                gen_random_uuid(),
                customer_data->>'name',
                customer_data->>'email',
                customer_data->>'phone',
                true,
                customer_user_id,
                jsonb_build_object(
                    'total_spent', (customer_data->>'total_spent')::numeric,
                    'total_orders', (customer_data->>'total_orders')::integer,
                    'engagement_score', (customer_data->>'engagement_score')::numeric,
                    'churn_risk_score', (customer_data->>'churn_risk_score')::numeric,
                    'last_active_at', customer_data->>'last_active_at',
                    'tags', ARRAY['customer', 'sample'],
                    'metadata', '{}'::jsonb
                ),
                NOW() - INTERVAL '30 days' + (random() * INTERVAL '25 days'), -- Random creation time
                NOW()
            ) RETURNING id INTO customer_account_id;

            RAISE NOTICE 'Created customer account: % (%)', customer_data->>'name', customer_account_id;
        END IF;

        -- Create account membership if doesn't exist
        INSERT INTO public.accounts_memberships (
            id,
            account_id,
            user_id,
            account_role,
            invited_by,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            team_account_id,
            customer_user_id,
            'customer',
            NULL,
            NOW() - INTERVAL '30 days' + (random() * INTERVAL '25 days'), -- Random creation time
            NOW()
        ) ON CONFLICT (account_id, user_id) DO NOTHING;

    END LOOP;

    RAISE NOTICE 'Created % sample customers for team account %', array_length(sample_customers, 1), team_account_id;

END;
$$;
