-- Migration: Create Customer Segments and related tables
-- Description: Tables for dynamic customer segmentation and segment management

-- Customer Segments Table
CREATE TABLE IF NOT EXISTS public.customer_segments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  
  -- Basic Information
  name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 255),
  description TEXT,
  color TEXT DEFAULT '#3B82F6', -- Hex color for UI
  icon TEXT DEFAULT 'users', -- Icon name for UI
  
  -- Segment Definition
  criteria JSONB NOT NULL DEFAULT '{}',
  type TEXT NOT NULL DEFAULT 'dynamic' CHECK (type IN ('static', 'dynamic')),
  
  -- Metadata
  customer_count INTEGER DEFAULT 0,
  estimated_size INTEGER, -- For preview before creation
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  is_computing BOOLEAN DEFAULT false,
  
  -- Performance tracking
  performance_data JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  last_computed_at TIMESTAMPTZ,
  
  -- Metadata
  created_by UUID,
  tags TEXT[] DEFAULT '{}',
  custom_attributes JSONB DEFAULT '{}'
);

-- Segment Membership Table (for tracking which customers belong to which segments)
CREATE TABLE IF NOT EXISTS public.segment_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_profile_id UUID NOT NULL REFERENCES public.customer_profiles(id) ON DELETE CASCADE,
  segment_id UUID NOT NULL REFERENCES public.customer_segments(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  
  -- Membership details
  added_at TIMESTAMPTZ DEFAULT now(),
  removed_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true,
  entry_reason TEXT, -- Which condition caused entry
  exit_reason TEXT, -- Which condition caused exit
  
  -- Ensure unique membership per customer per segment
  UNIQUE(customer_profile_id, segment_id)
);

-- Segment Performance History Table
CREATE TABLE IF NOT EXISTS public.segment_performance_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  segment_id UUID NOT NULL REFERENCES public.customer_segments(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  
  -- Performance metrics
  date DATE NOT NULL,
  customer_count INTEGER DEFAULT 0,
  new_customers INTEGER DEFAULT 0,
  churned_customers INTEGER DEFAULT 0,
  growth_rate DECIMAL(5,4) DEFAULT 0,
  engagement_rate DECIMAL(5,4) DEFAULT 0,
  conversion_rate DECIMAL(5,4) DEFAULT 0,
  revenue_generated DECIMAL(15,2) DEFAULT 0,
  
  computed_at TIMESTAMPTZ DEFAULT now(),
  
  -- Ensure unique entry per segment per date
  UNIQUE(segment_id, date)
);

-- Segment Templates Table (pre-built segment definitions)
CREATE TABLE IF NOT EXISTS public.segment_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Template info
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('behavioral', 'demographic', 'transactional', 'engagement', 'lifecycle')),
  
  -- Template definition
  criteria JSONB NOT NULL,
  variables JSONB DEFAULT '[]', -- Configurable variables
  
  -- Metadata
  tags TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_customer_segments_account_id ON public.customer_segments(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_segments_active ON public.customer_segments(account_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_customer_segments_type ON public.customer_segments(account_id, type);
CREATE INDEX IF NOT EXISTS idx_customer_segments_tags ON public.customer_segments USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_customer_segments_criteria ON public.customer_segments USING GIN(criteria);

CREATE INDEX IF NOT EXISTS idx_segment_memberships_customer ON public.segment_memberships(customer_profile_id);
CREATE INDEX IF NOT EXISTS idx_segment_memberships_segment ON public.segment_memberships(segment_id);
CREATE INDEX IF NOT EXISTS idx_segment_memberships_active ON public.segment_memberships(segment_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_segment_memberships_account ON public.segment_memberships(account_id);

CREATE INDEX IF NOT EXISTS idx_segment_performance_segment_date ON public.segment_performance_history(segment_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_segment_performance_account ON public.segment_performance_history(account_id);

CREATE INDEX IF NOT EXISTS idx_segment_templates_category ON public.segment_templates(category);
CREATE INDEX IF NOT EXISTS idx_segment_templates_active ON public.segment_templates(is_active) WHERE is_active = true;

-- RLS Policies
ALTER TABLE public.customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.segment_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.segment_performance_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.segment_templates ENABLE ROW LEVEL SECURITY;

-- Customer Segments RLS Policies
CREATE POLICY "customer_segments_read" ON public.customer_segments FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "customer_segments_insert" ON public.customer_segments FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.segments.create')
  );

CREATE POLICY "customer_segments_update" ON public.customer_segments FOR UPDATE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.segments.update')
  );

CREATE POLICY "customer_segments_delete" ON public.customer_segments FOR DELETE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.segments.delete')
  );

-- Segment Memberships RLS Policies
CREATE POLICY "segment_memberships_read" ON public.segment_memberships FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "segment_memberships_insert" ON public.segment_memberships FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.segments.create')
  );

CREATE POLICY "segment_memberships_update" ON public.segment_memberships FOR UPDATE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.segments.update')
  );

CREATE POLICY "segment_memberships_delete" ON public.segment_memberships FOR DELETE
  TO authenticated USING (
    public.has_permission(auth.uid(), account_id, 'cdp.segments.delete')
  );

-- Segment Performance History RLS Policies
CREATE POLICY "segment_performance_read" ON public.segment_performance_history FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "segment_performance_insert" ON public.segment_performance_history FOR INSERT
  TO authenticated WITH CHECK (
    public.has_permission(auth.uid(), account_id, 'cdp.analytics.read')
  );

-- Segment Templates RLS Policies (public read, admin write)
CREATE POLICY "segment_templates_read" ON public.segment_templates FOR SELECT
  TO authenticated USING (true);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_customer_segments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updated_at
CREATE TRIGGER customer_segments_updated_at
  BEFORE UPDATE ON public.customer_segments
  FOR EACH ROW
  EXECUTE FUNCTION update_customer_segments_updated_at();

-- Function to update segment customer count
CREATE OR REPLACE FUNCTION update_segment_customer_count()
RETURNS TRIGGER AS $$
BEGIN
  -- Update customer count for the affected segment
  UPDATE public.customer_segments
  SET customer_count = (
    SELECT COUNT(*)
    FROM public.segment_memberships
    WHERE segment_id = COALESCE(NEW.segment_id, OLD.segment_id)
      AND is_active = true
  )
  WHERE id = COALESCE(NEW.segment_id, OLD.segment_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update segment count on membership changes
CREATE TRIGGER segment_membership_count_update
  AFTER INSERT OR UPDATE OR DELETE ON public.segment_memberships
  FOR EACH ROW
  EXECUTE FUNCTION update_segment_customer_count();

-- Insert default segment templates
INSERT INTO public.segment_templates (name, description, category, criteria, variables) VALUES
(
  'High Value Customers',
  'Customers with high lifetime value and recent activity',
  'transactional',
  '{
    "operator": "AND",
    "conditions": [
      {"field": "lifetime_value_score", "operator": "greater_than", "value": 5000000},
      {"field": "total_orders", "operator": "greater_than", "value": 3},
      {"field": "last_order_at", "operator": "within_days", "value": 90}
    ]
  }',
  '[
    {"name": "min_lifetime_value", "type": "number", "default_value": 5000000, "description": "Minimum lifetime value"},
    {"name": "min_orders", "type": "number", "default_value": 3, "description": "Minimum number of orders"},
    {"name": "activity_days", "type": "number", "default_value": 90, "description": "Days since last order"}
  ]'
),
(
  'Churn Risk Customers',
  'Customers at risk of churning based on behavior patterns',
  'behavioral',
  '{
    "operator": "AND",
    "conditions": [
      {"field": "churn_risk_score", "operator": "greater_than", "value": 0.7},
      {"field": "last_activity_at", "operator": "more_than_days_ago", "value": 30},
      {"field": "total_purchases", "operator": "greater_than", "value": 0}
    ]
  }',
  '[
    {"name": "churn_threshold", "type": "number", "default_value": 0.7, "description": "Churn risk threshold"},
    {"name": "inactivity_days", "type": "number", "default_value": 30, "description": "Days of inactivity"}
  ]'
),
(
  'New Customers',
  'Recently acquired customers within specified timeframe',
  'lifecycle',
  '{
    "operator": "AND",
    "conditions": [
      {"field": "first_seen_at", "operator": "within_days", "value": 30},
      {"field": "lifecycle_stage", "operator": "in", "value": ["prospect", "lead", "customer"]}
    ]
  }',
  '[
    {"name": "new_customer_days", "type": "number", "default_value": 30, "description": "Days since first seen"}
  ]'
),
(
  'Highly Engaged Users',
  'Users with high engagement across multiple channels',
  'engagement',
  '{
    "operator": "AND",
    "conditions": [
      {"field": "overall_engagement_score", "operator": "greater_than", "value": 0.7},
      {"field": "total_sessions", "operator": "greater_than", "value": 10},
      {"field": "last_activity_at", "operator": "within_days", "value": 7}
    ]
  }',
  '[
    {"name": "engagement_threshold", "type": "number", "default_value": 0.7, "description": "Minimum engagement score"},
    {"name": "min_sessions", "type": "number", "default_value": 10, "description": "Minimum number of sessions"}
  ]'
),
(
  'VIP Customers',
  'Top tier customers with highest value and loyalty',
  'transactional',
  '{
    "operator": "AND",
    "conditions": [
      {"field": "customer_value_tier", "operator": "equals", "value": "vip"},
      {"field": "total_revenue", "operator": "greater_than", "value": 10000000},
      {"field": "churn_risk_score", "operator": "less_than", "value": 0.3}
    ]
  }',
  '[
    {"name": "min_revenue", "type": "number", "default_value": 10000000, "description": "Minimum total revenue"},
    {"name": "max_churn_risk", "type": "number", "default_value": 0.3, "description": "Maximum churn risk"}
  ]'
);
