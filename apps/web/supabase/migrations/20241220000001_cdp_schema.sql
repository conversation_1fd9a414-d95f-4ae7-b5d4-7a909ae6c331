-- CDP (Customer Data Platform) Schema
-- This migration creates all necessary tables for the CDP module

-- Customer Profiles Table
CREATE TABLE IF NOT EXISTS customer_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  email <PERSON><PERSON><PERSON><PERSON> UNIQUE NOT NULL,
  first_name <PERSON><PERSON><PERSON><PERSON>,
  last_name <PERSON><PERSON><PERSON><PERSON>,
  phone VARCHAR,
  avatar_url VARCHAR,
  location VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE,
  total_orders INTEGER DEFAULT 0,
  total_spent DECIMAL DEFAULT 0,
  avg_order_value DECIMAL DEFAULT 0,
  engagement_score DECIMAL DEFAULT 0 CHECK (engagement_score >= 0 AND engagement_score <= 1),
  churn_risk_score DECIMAL DEFAULT 0 CHECK (churn_risk_score >= 0 AND churn_risk_score <= 1),
  value_tier VARCHAR DEFAULT 'low' CHECK (value_tier IN ('high', 'medium', 'low')),
  tags TEXT[] DEFAULT '{}',
  notes TEXT,
  metadata JSONB DEFAULT '{}'
);

-- Customer Segments Table
CREATE TABLE IF NOT EXISTS customer_segments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  description TEXT,
  type VARCHAR NOT NULL CHECK (type IN ('behavioral', 'demographic', 'value_based', 'predictive')),
  criteria JSONB DEFAULT '{}',
  customer_count INTEGER DEFAULT 0,
  growth_rate DECIMAL DEFAULT 0,
  is_auto_updating BOOLEAN DEFAULT true,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer Journeys Table
CREATE TABLE IF NOT EXISTS customer_journeys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  description TEXT,
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('active', 'paused', 'draft', 'completed')),
  trigger_type VARCHAR NOT NULL CHECK (trigger_type IN ('segment_entry', 'event', 'date', 'manual')),
  trigger_config JSONB DEFAULT '{}',
  steps JSONB DEFAULT '[]',
  participants INTEGER DEFAULT 0,
  completion_rate DECIMAL DEFAULT 0 CHECK (completion_rate >= 0 AND completion_rate <= 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE,
  tags TEXT[] DEFAULT '{}'
);

-- Analytics Data Table
CREATE TABLE IF NOT EXISTS analytics_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  metric_name VARCHAR NOT NULL,
  metric_value DECIMAL NOT NULL,
  metric_type VARCHAR NOT NULL CHECK (metric_type IN ('count', 'percentage', 'currency', 'duration')),
  period VARCHAR DEFAULT 'current_month' CHECK (period IN ('current_day', 'current_week', 'current_month', 'current_year')),
  metadata JSONB DEFAULT '{}',
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Integration Statuses Table
CREATE TABLE IF NOT EXISTS integration_statuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  provider VARCHAR NOT NULL,
  category VARCHAR NOT NULL,
  status VARCHAR DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'pending')),
  last_sync TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  records_synced INTEGER DEFAULT 0,
  health_score DECIMAL DEFAULT 0 CHECK (health_score >= 0 AND health_score <= 1),
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Insights Table
CREATE TABLE IF NOT EXISTS ai_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  type VARCHAR NOT NULL CHECK (type IN ('trend', 'anomaly', 'prediction', 'recommendation')),
  title VARCHAR NOT NULL,
  description TEXT,
  confidence DECIMAL DEFAULT 0 CHECK (confidence >= 0 AND confidence <= 1),
  impact VARCHAR DEFAULT 'low' CHECK (impact IN ('high', 'medium', 'low')),
  category VARCHAR NOT NULL,
  status VARCHAR DEFAULT 'active' CHECK (status IN ('active', 'dismissed', 'resolved')),
  data JSONB DEFAULT '{}',
  recommendations TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Journey Participants Table
CREATE TABLE IF NOT EXISTS journey_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journey_id UUID NOT NULL REFERENCES customer_journeys(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customer_profiles(id) ON DELETE CASCADE,
  current_step INTEGER DEFAULT 0,
  status VARCHAR DEFAULT 'active' CHECK (status IN ('active', 'completed', 'exited')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}'
);

-- Segment Members Table
CREATE TABLE IF NOT EXISTS segment_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  segment_id UUID NOT NULL REFERENCES customer_segments(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customer_profiles(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(segment_id, customer_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customer_profiles_account_id ON customer_profiles(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_email ON customer_profiles(email);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_value_tier ON customer_profiles(value_tier);

CREATE INDEX IF NOT EXISTS idx_customer_segments_account_id ON customer_segments(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_segments_type ON customer_segments(type);

CREATE INDEX IF NOT EXISTS idx_customer_journeys_account_id ON customer_journeys(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_journeys_status ON customer_journeys(status);

CREATE INDEX IF NOT EXISTS idx_analytics_data_account_id ON analytics_data(account_id);
CREATE INDEX IF NOT EXISTS idx_analytics_data_metric_name ON analytics_data(metric_name);

CREATE INDEX IF NOT EXISTS idx_integration_statuses_account_id ON integration_statuses(account_id);
CREATE INDEX IF NOT EXISTS idx_integration_statuses_status ON integration_statuses(status);

CREATE INDEX IF NOT EXISTS idx_ai_insights_account_id ON ai_insights(account_id);
CREATE INDEX IF NOT EXISTS idx_ai_insights_type ON ai_insights(type);

-- Enable Row Level Security (RLS) - Simple version
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_journeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_statuses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE journey_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE segment_members ENABLE ROW LEVEL SECURITY;

-- Simple RLS policies - Allow service_role to access everything
CREATE POLICY "Allow service_role full access" ON customer_profiles FOR ALL TO service_role USING (true);
CREATE POLICY "Allow service_role full access" ON customer_segments FOR ALL TO service_role USING (true);
CREATE POLICY "Allow service_role full access" ON customer_journeys FOR ALL TO service_role USING (true);
CREATE POLICY "Allow service_role full access" ON analytics_data FOR ALL TO service_role USING (true);
CREATE POLICY "Allow service_role full access" ON integration_statuses FOR ALL TO service_role USING (true);
CREATE POLICY "Allow service_role full access" ON ai_insights FOR ALL TO service_role USING (true);
CREATE POLICY "Allow service_role full access" ON journey_participants FOR ALL TO service_role USING (true);
CREATE POLICY "Allow service_role full access" ON segment_members FOR ALL TO service_role USING (true);

-- Grant permissions to service_role
GRANT ALL ON customer_profiles TO service_role;
GRANT ALL ON customer_segments TO service_role;
GRANT ALL ON customer_journeys TO service_role;
GRANT ALL ON analytics_data TO service_role;
GRANT ALL ON integration_statuses TO service_role;
GRANT ALL ON ai_insights TO service_role;
GRANT ALL ON journey_participants TO service_role;
GRANT ALL ON segment_members TO service_role;
