/*
 * -------------------------------------------------------
 * CDP Module - Customer Data Platform Tables
 * Compatible with existing accounts and memberships system
 * -------------------------------------------------------
 */

/*
 * -------------------------------------------------------
 * Customer Profiles Table
 * Stores customer data linked to accounts
 * -------------------------------------------------------
 */
CREATE TABLE IF NOT EXISTS public.customer_profiles (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

    -- Basic customer information
    email VARCHAR(320) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON>R(255),
    phone VARCHAR(50),
    avatar_url VARCHAR(1000),

    -- Customer metrics
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(15,2) DEFAULT 0,
    avg_order_value DECIMAL(15,2) DEFAULT 0,
    engagement_score DECIMAL(3,2) DEFAULT 0 CHECK (engagement_score >= 0 AND engagement_score <= 1),
    churn_risk_score DECIMAL(3,2) DEFAULT 0 CHECK (churn_risk_score >= 0 AND churn_risk_score <= 1),

    -- Customer classification
    value_tier VARCHAR(20) DEFAULT 'low' CHECK (value_tier IN ('high', 'medium', 'low')),
    tags TEXT[] DEFAULT '{}',

    -- Activity tracking
    last_active_at TIMESTAMPTZ,
    last_purchase_at TIMESTAMPTZ,

    -- Metadata
    metadata JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users,
    updated_by UUID REFERENCES auth.users,

    -- Constraints
    UNIQUE(account_id, email)
);

-- Indexes for customer_profiles
CREATE INDEX IF NOT EXISTS idx_customer_profiles_account_id ON public.customer_profiles(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_email ON public.customer_profiles(email);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_value_tier ON public.customer_profiles(value_tier);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_churn_risk ON public.customer_profiles(churn_risk_score);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_last_active ON public.customer_profiles(last_active_at);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_created_at ON public.customer_profiles(created_at);

-- Enable RLS
ALTER TABLE public.customer_profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for customer_profiles
CREATE POLICY "customer_profiles_select" ON public.customer_profiles
    FOR SELECT TO authenticated
    USING (
        public.has_role_on_account(account_id) OR
        public.has_permission(auth.uid(), account_id, 'cdp.view'::public.app_permissions)
    );

CREATE POLICY "customer_profiles_insert" ON public.customer_profiles
    FOR INSERT TO authenticated
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

CREATE POLICY "customer_profiles_update" ON public.customer_profiles
    FOR UPDATE TO authenticated
    USING (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    )
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

CREATE POLICY "customer_profiles_delete" ON public.customer_profiles
    FOR DELETE TO authenticated
    USING (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

/*
 * -------------------------------------------------------
 * Customer Segments Table
 * Stores customer segmentation data
 * -------------------------------------------------------
 */
CREATE TABLE IF NOT EXISTS public.customer_segments (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

    -- Segment information
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL CHECK (type IN ('behavioral', 'demographic', 'value_based', 'predictive')),

    -- Segment criteria and metrics
    criteria JSONB DEFAULT '{}',
    customer_count INTEGER DEFAULT 0,
    growth_rate DECIMAL(5,4) DEFAULT 0,

    -- Configuration
    is_auto_updating BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users,
    updated_by UUID REFERENCES auth.users,

    -- Constraints
    UNIQUE(account_id, name)
);

-- Indexes for customer_segments
CREATE INDEX IF NOT EXISTS idx_customer_segments_account_id ON public.customer_segments(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_segments_type ON public.customer_segments(type);
CREATE INDEX IF NOT EXISTS idx_customer_segments_active ON public.customer_segments(is_active);

-- Enable RLS
ALTER TABLE public.customer_segments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for customer_segments
CREATE POLICY "customer_segments_select" ON public.customer_segments
    FOR SELECT TO authenticated
    USING (
        public.has_role_on_account(account_id) OR
        public.has_permission(auth.uid(), account_id, 'cdp.view'::public.app_permissions)
    );

CREATE POLICY "customer_segments_insert" ON public.customer_segments
    FOR INSERT TO authenticated
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

CREATE POLICY "customer_segments_update" ON public.customer_segments
    FOR UPDATE TO authenticated
    USING (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    )
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

CREATE POLICY "customer_segments_delete" ON public.customer_segments
    FOR DELETE TO authenticated
    USING (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

/*
 * -------------------------------------------------------
 * Analytics Data Table
 * Stores aggregated analytics metrics
 * -------------------------------------------------------
 */
CREATE TABLE IF NOT EXISTS public.analytics_data (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

    -- Metrics
    total_customers INTEGER DEFAULT 0,
    active_customers INTEGER DEFAULT 0,
    monthly_revenue DECIMAL(15,2) DEFAULT 0,
    conversion_rate DECIMAL(5,4) DEFAULT 0,
    churn_rate DECIMAL(5,4) DEFAULT 0,
    avg_order_value DECIMAL(15,2) DEFAULT 0,
    customer_lifetime_value DECIMAL(15,2) DEFAULT 0,
    engagement_score DECIMAL(3,2) DEFAULT 0,

    -- Period information
    period VARCHAR(50) DEFAULT 'current_month',
    period_start TIMESTAMPTZ,
    period_end TIMESTAMPTZ,

    -- Timestamps
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for analytics_data
CREATE INDEX IF NOT EXISTS idx_analytics_data_account_id ON public.analytics_data(account_id);
CREATE INDEX IF NOT EXISTS idx_analytics_data_timestamp ON public.analytics_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_data_period ON public.analytics_data(period);

-- Enable RLS
ALTER TABLE public.analytics_data ENABLE ROW LEVEL SECURITY;

-- RLS Policies for analytics_data
CREATE POLICY "analytics_data_select" ON public.analytics_data
    FOR SELECT TO authenticated
    USING (
        public.has_role_on_account(account_id) OR
        public.has_permission(auth.uid(), account_id, 'cdp.view'::public.app_permissions)
    );

CREATE POLICY "analytics_data_insert" ON public.analytics_data
    FOR INSERT TO authenticated
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

/*
 * -------------------------------------------------------
 * Integration Statuses Table
 * Tracks third-party integration status
 * -------------------------------------------------------
 */
CREATE TABLE IF NOT EXISTS public.integration_statuses (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

    -- Integration information
    name VARCHAR(255) NOT NULL,
    provider VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('email', 'analytics', 'ecommerce', 'social', 'advertising', 'crm')),

    -- Status and metrics
    status VARCHAR(50) DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'syncing')),
    last_sync TIMESTAMPTZ,
    records_synced INTEGER DEFAULT 0,
    health_score DECIMAL(3,2) DEFAULT 0 CHECK (health_score >= 0 AND health_score <= 1),

    -- Configuration
    config JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users,
    updated_by UUID REFERENCES auth.users,

    -- Constraints
    UNIQUE(account_id, provider, name)
);

-- Indexes for integration_statuses
CREATE INDEX IF NOT EXISTS idx_integration_statuses_account_id ON public.integration_statuses(account_id);
CREATE INDEX IF NOT EXISTS idx_integration_statuses_status ON public.integration_statuses(status);
CREATE INDEX IF NOT EXISTS idx_integration_statuses_category ON public.integration_statuses(category);

-- Enable RLS
ALTER TABLE public.integration_statuses ENABLE ROW LEVEL SECURITY;

-- RLS Policies for integration_statuses
CREATE POLICY "integration_statuses_select" ON public.integration_statuses
    FOR SELECT TO authenticated
    USING (
        public.has_role_on_account(account_id) OR
        public.has_permission(auth.uid(), account_id, 'cdp.view'::public.app_permissions)
    );

CREATE POLICY "integration_statuses_insert" ON public.integration_statuses
    FOR INSERT TO authenticated
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

CREATE POLICY "integration_statuses_update" ON public.integration_statuses
    FOR UPDATE TO authenticated
    USING (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    )
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

/*
 * -------------------------------------------------------
 * AI Insights Table
 * Stores AI-generated insights and recommendations
 * -------------------------------------------------------
 */
CREATE TABLE IF NOT EXISTS public.ai_insights (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

    -- Insight information
    type VARCHAR(50) NOT NULL CHECK (type IN ('opportunity', 'risk', 'optimization', 'prediction', 'anomaly')),
    title VARCHAR(500) NOT NULL,
    description TEXT,

    -- AI metrics
    confidence DECIMAL(3,2) DEFAULT 0 CHECK (confidence >= 0 AND confidence <= 1),
    impact VARCHAR(20) DEFAULT 'low' CHECK (impact IN ('critical', 'high', 'medium', 'low')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('revenue', 'churn', 'engagement', 'conversion', 'performance')),

    -- Data and recommendations
    data JSONB DEFAULT '{}',
    recommendations TEXT[],

    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'dismissed', 'implemented')),

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users,
    updated_by UUID REFERENCES auth.users
);

-- Indexes for ai_insights
CREATE INDEX IF NOT EXISTS idx_ai_insights_account_id ON public.ai_insights(account_id);
CREATE INDEX IF NOT EXISTS idx_ai_insights_status ON public.ai_insights(status);
CREATE INDEX IF NOT EXISTS idx_ai_insights_type ON public.ai_insights(type);
CREATE INDEX IF NOT EXISTS idx_ai_insights_category ON public.ai_insights(category);

-- Enable RLS
ALTER TABLE public.ai_insights ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_insights
CREATE POLICY "ai_insights_select" ON public.ai_insights
    FOR SELECT TO authenticated
    USING (
        public.has_role_on_account(account_id) OR
        public.has_permission(auth.uid(), account_id, 'cdp.view'::public.app_permissions)
    );

CREATE POLICY "ai_insights_insert" ON public.ai_insights
    FOR INSERT TO authenticated
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

CREATE POLICY "ai_insights_update" ON public.ai_insights
    FOR UPDATE TO authenticated
    USING (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    )
    WITH CHECK (
        public.has_permission(auth.uid(), account_id, 'cdp.manage'::public.app_permissions)
    );

-- Grant table permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.customer_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.customer_segments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.analytics_data TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.integration_statuses TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.ai_insights TO authenticated;

-- Grant table permissions to service_role
GRANT ALL ON public.customer_profiles TO service_role;
GRANT ALL ON public.customer_segments TO service_role;
GRANT ALL ON public.analytics_data TO service_role;
GRANT ALL ON public.integration_statuses TO service_role;
GRANT ALL ON public.ai_insights TO service_role;
