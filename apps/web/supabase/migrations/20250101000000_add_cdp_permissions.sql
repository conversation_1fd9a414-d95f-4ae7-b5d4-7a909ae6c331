-- Migration: Add CDP permissions to app_permissions enum
-- Description: Add permissions for Customer Data Platform features

-- Add CDP permissions to the enum
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.profiles.create';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.profiles.read';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.profiles.update';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.profiles.delete';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.segments.create';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.segments.read';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.segments.update';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.segments.delete';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.journeys.create';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.journeys.read';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.journeys.update';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.journeys.delete';
ALTER TYPE app_permissions ADD VALUE IF NOT EXISTS 'cdp.analytics.read';
