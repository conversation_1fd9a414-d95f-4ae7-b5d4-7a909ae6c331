-- CDP Sample Data Migration
-- This migration creates sample data for testing CDP functionality

-- Insert sample customer profiles for makerkit account
INSERT INTO customer_profiles (
  account_id,
  email,
  first_name,
  last_name,
  phone,
  location,
  total_orders,
  total_spent,
  avg_order_value,
  engagement_score,
  churn_risk_score,
  value_tier,
  tags,
  notes
) 
SELECT 
  accounts.id,
  '<EMAIL>',
  '<PERSON>',
  'Doe',
  '+***********',
  '<PERSON>à Nộ<PERSON>',
  25,
  ********,
  600000,
  0.85,
  0.15,
  'high',
  ARRAY['vip', 'loyal'],
  'High-value customer from referral program'
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

INSERT INTO customer_profiles (
  account_id,
  email,
  first_name,
  last_name,
  phone,
  location,
  total_orders,
  total_spent,
  avg_order_value,
  engagement_score,
  churn_risk_score,
  value_tier,
  tags,
  notes
) 
SELECT 
  accounts.id,
  '<EMAIL>',
  '<PERSON>',
  '<PERSON>',
  '+***********',
  'TP.HCM',
  18,
  ********,
  666667,
  0.78,
  0.22,
  'high',
  ARRAY['premium', 'early-adopter'],
  'Premium customer with high engagement'
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

INSERT INTO customer_profiles (
  account_id,
  email,
  first_name,
  last_name,
  phone,
  location,
  total_orders,
  total_spent,
  avg_order_value,
  engagement_score,
  churn_risk_score,
  value_tier,
  tags,
  notes
) 
SELECT 
  accounts.id,
  '<EMAIL>',
  '<PERSON>',
  'Johnson',
  '+***********',
  'Đà Nẵng',
  32,
  ********,
  578125,
  0.92,
  0.08,
  'high',
  ARRAY['vip', 'frequent-buyer'],
  'Most active customer with highest engagement'
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

-- Insert sample customer segments
INSERT INTO customer_segments (
  account_id,
  name,
  description,
  type,
  criteria,
  customer_count,
  growth_rate,
  is_auto_updating,
  is_active
) 
SELECT 
  accounts.id,
  'High Value Customers',
  'Customers with high lifetime value and low churn risk',
  'value_based',
  '{"total_spent": {"operator": "gte", "value": ********}, "value_tier": "high"}',
  3,
  15.5,
  true,
  true
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

INSERT INTO customer_segments (
  account_id,
  name,
  description,
  type,
  criteria,
  customer_count,
  growth_rate,
  is_auto_updating,
  is_active
) 
SELECT 
  accounts.id,
  'Highly Engaged Users',
  'Users with high engagement scores and frequent interactions',
  'behavioral',
  '{"engagement_score": {"operator": "gte", "value": 0.7}}',
  4,
  8.2,
  true,
  true
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

-- Insert sample customer journeys
INSERT INTO customer_journeys (
  account_id,
  name,
  description,
  status,
  trigger_type,
  trigger_config,
  steps,
  participants,
  completion_rate,
  tags
) 
SELECT 
  accounts.id,
  'Welcome New Customers',
  'Onboarding journey for new customer registration',
  'active',
  'segment_entry',
  '{"segment_id": "new_customers"}',
  '[
    {"type": "email", "name": "Welcome Email", "config": {"subject": "Welcome to our platform!", "template": "welcome_email"}, "delay_hours": 0},
    {"type": "wait", "name": "Wait 24 Hours", "config": {}, "delay_hours": 24},
    {"type": "email", "name": "Getting Started Guide", "config": {"subject": "Get started with these tips", "template": "getting_started"}, "delay_hours": 0}
  ]',
  1247,
  0.78,
  ARRAY['onboarding', 'email']
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

INSERT INTO customer_journeys (
  account_id,
  name,
  description,
  status,
  trigger_type,
  trigger_config,
  steps,
  participants,
  completion_rate,
  tags
) 
SELECT 
  accounts.id,
  'Cart Abandonment Recovery',
  'Re-engage customers who abandoned their cart',
  'active',
  'event',
  '{"event_name": "cart_abandoned"}',
  '[
    {"type": "email", "name": "Cart Reminder", "config": {"subject": "You left something in your cart", "template": "cart_reminder"}, "delay_hours": 1},
    {"type": "wait", "name": "Wait 24 Hours", "config": {}, "delay_hours": 24},
    {"type": "email", "name": "Special Discount", "config": {"subject": "10% off your cart items", "template": "discount_offer"}, "delay_hours": 0}
  ]',
  856,
  0.45,
  ARRAY['recovery', 'email', 'discount']
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

-- Insert sample analytics data
INSERT INTO analytics_data (
  account_id,
  metric_name,
  metric_value,
  metric_type,
  period,
  metadata
) 
SELECT 
  accounts.id,
  'total_customers',
  3,
  'count',
  'current_month',
  '{"source": "customer_profiles"}'
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

INSERT INTO analytics_data (
  account_id,
  metric_name,
  metric_value,
  metric_type,
  period,
  metadata
) 
SELECT 
  accounts.id,
  'total_revenue',
  ********,
  'currency',
  'current_month',
  '{"source": "customer_profiles", "currency": "VND"}'
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

-- Insert sample integration statuses
INSERT INTO integration_statuses (
  account_id,
  name,
  provider,
  category,
  status,
  last_sync,
  records_synced,
  health_score,
  config
) 
SELECT 
  accounts.id,
  'Email Marketing Platform',
  'mailchimp',
  'marketing',
  'connected',
  NOW() - INTERVAL '2 hours',
  1250,
  0.95,
  '{"api_key": "***", "list_id": "abc123", "sync_frequency": "hourly"}'
FROM accounts WHERE slug = 'makerkit' LIMIT 1;

-- Insert sample AI insights
INSERT INTO ai_insights (
  account_id,
  type,
  title,
  description,
  confidence,
  impact,
  category,
  status,
  data,
  recommendations
) 
SELECT 
  accounts.id,
  'trend',
  'Increasing Customer Engagement',
  'Customer engagement scores have increased by 15% over the past month',
  0.85,
  'high',
  'engagement',
  'active',
  '{"engagement_increase": 0.15, "timeframe": "30_days"}',
  ARRAY['Continue current engagement strategies', 'Expand successful campaigns', 'Monitor engagement metrics closely']
FROM accounts WHERE slug = 'makerkit' LIMIT 1;
