/*
 * -------------------------------------------------------
 * CDP Module - Add CDP permissions to enum
 * This must be in a separate migration from table creation
 * -------------------------------------------------------
 */

-- Add CDP permissions to existing app_permissions enum
-- Note: We need to check if values exist before adding them
DO $$
BEGIN
    -- Add cdp.manage if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'cdp.manage' AND enumtypid = 'public.app_permissions'::regtype) THEN
        ALTER TYPE public.app_permissions ADD VALUE 'cdp.manage';
    END IF;

    -- Add cdp.view if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'cdp.view' AND enumtypid = 'public.app_permissions'::regtype) THEN
        ALTER TYPE public.app_permissions ADD VALUE 'cdp.view';
    END IF;
END
$$;

