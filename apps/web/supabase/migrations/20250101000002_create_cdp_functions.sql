-- Migration: Create CDP stored procedures and functions
-- Description: Functions for customer profile management, identity resolution, and data operations

-- Function to create customer profile with identities
CREATE OR REPLACE FUNCTION create_customer_profile_with_identities(
  profile_data JSONB,
  identities_data JSONB[]
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile_id UUID;
  identity_record JSONB;
BEGIN
  -- Insert customer profile
  INSERT INTO public.customer_profiles (
    id,
    account_id,
    primary_email,
    primary_phone,
    primary_user_id,
    first_name,
    last_name,
    demographics,
    total_sessions,
    total_pageviews,
    total_events,
    total_purchases,
    total_revenue,
    average_order_value,
    avg_session_duration,
    email_engagement_score,
    website_engagement_score,
    social_engagement_score,
    churn_risk_score,
    lifetime_value_score,
    purchase_propensity_score,
    engagement_propensity_score,
    upsell_propensity_score,
    lifecycle_stage,
    customer_value_tier,
    preferences,
    custom_attributes,
    tags,
    first_seen_at,
    last_seen_at,
    last_activity_at,
    last_order_at,
    created_at,
    updated_at,
    computed_at
  )
  VALUES (
    COALESCE((profile_data->>'id')::UUID, gen_random_uuid()),
    (profile_data->>'account_id')::UUID,
    profile_data->>'primary_email',
    profile_data->>'primary_phone',
    (profile_data->>'primary_user_id')::UUID,
    profile_data->>'first_name',
    profile_data->>'last_name',
    COALESCE(profile_data->'demographics', '{}'),
    COALESCE((profile_data->>'total_sessions')::INTEGER, 0),
    COALESCE((profile_data->>'total_pageviews')::INTEGER, 0),
    COALESCE((profile_data->>'total_events')::INTEGER, 0),
    COALESCE((profile_data->>'total_purchases')::INTEGER, 0),
    COALESCE((profile_data->>'total_revenue')::DECIMAL, 0),
    COALESCE((profile_data->>'average_order_value')::DECIMAL, 0),
    COALESCE((profile_data->>'avg_session_duration')::INTEGER, 0),
    COALESCE((profile_data->>'email_engagement_score')::DECIMAL, 0),
    COALESCE((profile_data->>'website_engagement_score')::DECIMAL, 0),
    COALESCE((profile_data->>'social_engagement_score')::DECIMAL, 0),
    COALESCE((profile_data->>'churn_risk_score')::DECIMAL, 0),
    COALESCE((profile_data->>'lifetime_value_score')::DECIMAL, 0),
    COALESCE((profile_data->>'purchase_propensity_score')::DECIMAL, 0),
    COALESCE((profile_data->>'engagement_propensity_score')::DECIMAL, 0),
    COALESCE((profile_data->>'upsell_propensity_score')::DECIMAL, 0),
    COALESCE(profile_data->>'lifecycle_stage', 'prospect'),
    COALESCE(profile_data->>'customer_value_tier', 'low'),
    COALESCE(profile_data->'preferences', '{}'),
    COALESCE(profile_data->'custom_attributes', '{}'),
    COALESCE(
      ARRAY(SELECT jsonb_array_elements_text(profile_data->'tags')),
      '{}'::TEXT[]
    ),
    COALESCE((profile_data->>'first_seen_at')::TIMESTAMPTZ, now()),
    (profile_data->>'last_seen_at')::TIMESTAMPTZ,
    (profile_data->>'last_activity_at')::TIMESTAMPTZ,
    (profile_data->>'last_order_at')::TIMESTAMPTZ,
    COALESCE((profile_data->>'created_at')::TIMESTAMPTZ, now()),
    COALESCE((profile_data->>'updated_at')::TIMESTAMPTZ, now()),
    COALESCE((profile_data->>'computed_at')::TIMESTAMPTZ, now())
  )
  RETURNING id INTO profile_id;

  -- Insert customer identities
  FOREACH identity_record IN ARRAY identities_data
  LOOP
    INSERT INTO public.customer_identities (
      customer_profile_id,
      account_id,
      identity_type,
      identity_value,
      verified,
      source,
      first_seen_at,
      created_at
    )
    VALUES (
      profile_id,
      (identity_record->>'account_id')::UUID,
      identity_record->>'type',
      identity_record->>'value',
      COALESCE((identity_record->>'verified')::BOOLEAN, false),
      identity_record->>'source',
      COALESCE((identity_record->>'first_seen_at')::TIMESTAMPTZ, now()),
      COALESCE((identity_record->>'created_at')::TIMESTAMPTZ, now())
    )
    ON CONFLICT (account_id, identity_type, identity_value) 
    DO UPDATE SET
      customer_profile_id = profile_id,
      verified = EXCLUDED.verified,
      source = EXCLUDED.source;
  END LOOP;

  RETURN profile_id;
END;
$$;

-- Function to update customer profile with identities
CREATE OR REPLACE FUNCTION update_customer_profile_with_identities(
  profile_id UUID,
  profile_data JSONB,
  new_identities JSONB[] DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  identity_record JSONB;
  account_id_val UUID;
BEGIN
  -- Get account_id for the profile
  SELECT account_id INTO account_id_val 
  FROM public.customer_profiles 
  WHERE id = profile_id;

  IF account_id_val IS NULL THEN
    RAISE EXCEPTION 'Profile not found: %', profile_id;
  END IF;

  -- Update customer profile
  UPDATE public.customer_profiles
  SET
    primary_email = COALESCE(profile_data->>'primary_email', primary_email),
    primary_phone = COALESCE(profile_data->>'primary_phone', primary_phone),
    primary_user_id = COALESCE((profile_data->>'primary_user_id')::UUID, primary_user_id),
    first_name = COALESCE(profile_data->>'first_name', first_name),
    last_name = COALESCE(profile_data->>'last_name', last_name),
    demographics = COALESCE(profile_data->'demographics', demographics),
    total_sessions = COALESCE((profile_data->>'total_sessions')::INTEGER, total_sessions),
    total_pageviews = COALESCE((profile_data->>'total_pageviews')::INTEGER, total_pageviews),
    total_events = COALESCE((profile_data->>'total_events')::INTEGER, total_events),
    total_purchases = COALESCE((profile_data->>'total_purchases')::INTEGER, total_purchases),
    total_revenue = COALESCE((profile_data->>'total_revenue')::DECIMAL, total_revenue),
    avg_session_duration = COALESCE((profile_data->>'avg_session_duration')::INTEGER, avg_session_duration),
    email_engagement_score = COALESCE((profile_data->>'email_engagement_score')::DECIMAL, email_engagement_score),
    website_engagement_score = COALESCE((profile_data->>'website_engagement_score')::DECIMAL, website_engagement_score),
    social_engagement_score = COALESCE((profile_data->>'social_engagement_score')::DECIMAL, social_engagement_score),
    churn_risk_score = COALESCE((profile_data->>'churn_risk_score')::DECIMAL, churn_risk_score),
    lifetime_value_score = COALESCE((profile_data->>'lifetime_value_score')::DECIMAL, lifetime_value_score),
    purchase_propensity_score = COALESCE((profile_data->>'purchase_propensity_score')::DECIMAL, purchase_propensity_score),
    engagement_propensity_score = COALESCE((profile_data->>'engagement_propensity_score')::DECIMAL, engagement_propensity_score),
    upsell_propensity_score = COALESCE((profile_data->>'upsell_propensity_score')::DECIMAL, upsell_propensity_score),
    lifecycle_stage = COALESCE(profile_data->>'lifecycle_stage', lifecycle_stage),
    customer_value_tier = COALESCE(profile_data->>'customer_value_tier', customer_value_tier),
    preferences = COALESCE(profile_data->'preferences', preferences),
    custom_attributes = COALESCE(profile_data->'custom_attributes', custom_attributes),
    tags = COALESCE(
      ARRAY(SELECT jsonb_array_elements_text(profile_data->'tags')),
      tags
    ),
    last_seen_at = COALESCE((profile_data->>'last_seen_at')::TIMESTAMPTZ, last_seen_at),
    last_activity_at = COALESCE((profile_data->>'last_activity_at')::TIMESTAMPTZ, last_activity_at),
    last_order_at = COALESCE((profile_data->>'last_order_at')::TIMESTAMPTZ, last_order_at),
    updated_at = now()
  WHERE id = profile_id;

  -- Insert new identities
  FOREACH identity_record IN ARRAY new_identities
  LOOP
    INSERT INTO public.customer_identities (
      customer_profile_id,
      account_id,
      identity_type,
      identity_value,
      verified,
      source,
      first_seen_at,
      created_at
    )
    VALUES (
      profile_id,
      account_id_val,
      identity_record->>'type',
      identity_record->>'value',
      COALESCE((identity_record->>'verified')::BOOLEAN, false),
      identity_record->>'source',
      COALESCE((identity_record->>'first_seen_at')::TIMESTAMPTZ, now()),
      COALESCE((identity_record->>'created_at')::TIMESTAMPTZ, now())
    )
    ON CONFLICT (account_id, identity_type, identity_value) 
    DO UPDATE SET
      customer_profile_id = profile_id,
      verified = EXCLUDED.verified,
      source = EXCLUDED.source;
  END LOOP;

  RETURN profile_id;
END;
$$;

-- Function to merge customer profiles
CREATE OR REPLACE FUNCTION merge_customer_profiles(
  primary_profile_id UUID,
  duplicate_profile_id UUID
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  primary_profile RECORD;
  duplicate_profile RECORD;
BEGIN
  -- Get both profiles
  SELECT * INTO primary_profile FROM public.customer_profiles WHERE id = primary_profile_id;
  SELECT * INTO duplicate_profile FROM public.customer_profiles WHERE id = duplicate_profile_id;

  IF primary_profile IS NULL THEN
    RAISE EXCEPTION 'Primary profile not found: %', primary_profile_id;
  END IF;

  IF duplicate_profile IS NULL THEN
    RAISE EXCEPTION 'Duplicate profile not found: %', duplicate_profile_id;
  END IF;

  -- Merge behavioral data (sum numeric values, take latest dates)
  UPDATE public.customer_profiles
  SET
    total_sessions = primary_profile.total_sessions + duplicate_profile.total_sessions,
    total_pageviews = primary_profile.total_pageviews + duplicate_profile.total_pageviews,
    total_events = primary_profile.total_events + duplicate_profile.total_events,
    total_purchases = primary_profile.total_purchases + duplicate_profile.total_purchases,
    total_revenue = primary_profile.total_revenue + duplicate_profile.total_revenue,
    avg_session_duration = (primary_profile.avg_session_duration + duplicate_profile.avg_session_duration) / 2,
    
    -- Take the better engagement scores
    email_engagement_score = GREATEST(primary_profile.email_engagement_score, duplicate_profile.email_engagement_score),
    website_engagement_score = GREATEST(primary_profile.website_engagement_score, duplicate_profile.website_engagement_score),
    social_engagement_score = GREATEST(primary_profile.social_engagement_score, duplicate_profile.social_engagement_score),
    
    -- Take the latest activity dates
    last_activity_at = GREATEST(
      COALESCE(primary_profile.last_activity_at, '1970-01-01'::TIMESTAMPTZ),
      COALESCE(duplicate_profile.last_activity_at, '1970-01-01'::TIMESTAMPTZ)
    ),
    last_order_at = GREATEST(
      COALESCE(primary_profile.last_order_at, '1970-01-01'::TIMESTAMPTZ),
      COALESCE(duplicate_profile.last_order_at, '1970-01-01'::TIMESTAMPTZ)
    ),
    last_seen_at = GREATEST(
      COALESCE(primary_profile.last_seen_at, '1970-01-01'::TIMESTAMPTZ),
      COALESCE(duplicate_profile.last_seen_at, '1970-01-01'::TIMESTAMPTZ)
    ),
    
    -- Take the earliest first seen date
    first_seen_at = LEAST(
      COALESCE(primary_profile.first_seen_at, now()),
      COALESCE(duplicate_profile.first_seen_at, now())
    ),
    
    -- Fill in missing personal data
    first_name = COALESCE(primary_profile.first_name, duplicate_profile.first_name),
    last_name = COALESCE(primary_profile.last_name, duplicate_profile.last_name),
    primary_email = COALESCE(primary_profile.primary_email, duplicate_profile.primary_email),
    primary_phone = COALESCE(primary_profile.primary_phone, duplicate_profile.primary_phone),
    
    updated_at = now()
  WHERE id = primary_profile_id;

  -- Move all identities from duplicate to primary
  UPDATE public.customer_identities
  SET customer_profile_id = primary_profile_id
  WHERE customer_profile_id = duplicate_profile_id;

  -- Update analytics events to point to primary profile
  UPDATE public.analytics_events
  SET customer_profile_id = primary_profile_id
  WHERE customer_profile_id = duplicate_profile_id;

  -- Delete the duplicate profile
  DELETE FROM public.customer_profiles WHERE id = duplicate_profile_id;

  RETURN primary_profile_id;
END;
$$;

-- Function to calculate customer scores
CREATE OR REPLACE FUNCTION calculate_customer_scores(profile_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile RECORD;
  engagement_score DECIMAL;
  churn_risk DECIMAL;
  ltv_score DECIMAL;
  days_since_last_activity INTEGER;
  result JSONB;
BEGIN
  -- Get profile data
  SELECT * INTO profile FROM public.customer_profiles WHERE id = profile_id;
  
  IF profile IS NULL THEN
    RAISE EXCEPTION 'Profile not found: %', profile_id;
  END IF;

  -- Calculate days since last activity
  days_since_last_activity := COALESCE(
    EXTRACT(DAY FROM (now() - profile.last_activity_at))::INTEGER,
    999
  );

  -- Calculate engagement score (0-1)
  engagement_score := LEAST(1.0, (
    COALESCE(profile.email_engagement_score, 0) * 0.3 +
    COALESCE(profile.website_engagement_score, 0) * 0.4 +
    COALESCE(profile.social_engagement_score, 0) * 0.3
  ));

  -- Calculate churn risk (0-1)
  churn_risk := LEAST(1.0, (
    CASE 
      WHEN days_since_last_activity > 90 THEN 0.8
      WHEN days_since_last_activity > 60 THEN 0.6
      WHEN days_since_last_activity > 30 THEN 0.4
      WHEN days_since_last_activity > 14 THEN 0.2
      ELSE 0.1
    END +
    CASE 
      WHEN engagement_score < 0.2 THEN 0.2
      WHEN engagement_score < 0.4 THEN 0.1
      ELSE 0
    END
  ));

  -- Calculate LTV score (simple: total revenue + predicted future value)
  ltv_score := profile.total_revenue + (
    CASE 
      WHEN profile.total_purchases > 0 THEN 
        (profile.total_revenue / profile.total_purchases) * 
        GREATEST(1, 12 - days_since_last_activity / 30) * 
        (1 - churn_risk)
      ELSE 0
    END
  );

  -- Update the profile with calculated scores
  UPDATE public.customer_profiles
  SET
    overall_engagement_score = engagement_score,
    churn_risk_score = churn_risk,
    lifetime_value_score = ltv_score,
    computed_at = now()
  WHERE id = profile_id;

  -- Return the calculated scores
  result := jsonb_build_object(
    'engagement_score', engagement_score,
    'churn_risk_score', churn_risk,
    'lifetime_value_score', ltv_score,
    'computed_at', now()
  );

  RETURN result;
END;
$$;
