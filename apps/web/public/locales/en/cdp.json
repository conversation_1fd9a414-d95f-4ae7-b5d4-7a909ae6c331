{"title": "Customer Data Platform", "description": "Unified customer data management and analytics", "dashboard": {"title": "CDP Dashboard", "description": "Overview of customer performance and insights", "welcome": "Welcome to CDP Dashboard", "subtitle": "Unified customer data platform with AI-powered insights"}, "metrics": {"totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "monthlyRevenue": "Monthly Revenue", "conversionRate": "Conversion Rate", "churnRate": "Churn Rate", "avgOrderValue": "Avg Order Value", "customerLifetimeValue": "Customer Lifetime Value", "engagementScore": "Engagement Score"}, "tabs": {"overview": "Overview", "realtime": "Real-time", "analytics": "Interactive Analytics", "insights": "AI Insights"}, "quickActions": {"title": "Quick Actions", "viewAll": "View All", "createSegment": "Create Segment", "runCampaign": "Run Campaign", "analyzeJourney": "Analyze Journey", "exportData": "Export Data", "manageIntegrations": "Manage Integrations", "viewReports": "View Reports", "customerGrowth": "Customer Growth", "revenueGrowth": "Revenue Growth", "engagementTrend": "Engagement Trend", "conversionRate": "Conversion Rate"}, "recentActivity": {"title": "Recent Activity", "newCustomer": "New customer registered", "segmentUpdated": "Segment updated", "campaignLaunched": "Campaign launched", "integrationConnected": "Integration connected", "reportGenerated": "Report generated", "workflowExecuted": "Workflow executed"}, "systemHealth": {"title": "System Health", "allSystemsOperational": "All Systems Operational", "dataProcessing": "Data Processing", "apiPerformance": "API Performance", "integrationStatus": "Integration Status", "mlModels": "ML Models", "healthy": "Healthy", "warning": "Warning", "critical": "Critical", "operational": "Operational", "maintenance": "Maintenance"}, "cards": {"customerGrowth": "Customer Growth", "revenueGrowth": "Revenue Growth", "engagementTrend": "Engagement Trend", "conversionRate": "Conversion Rate", "churnAnalysis": "Churn Analysis", "segmentPerformance": "Segment Performance", "viewChart": "View Chart", "viewDetails": "View Details", "configure": "Configure", "refresh": "Refresh", "export": "Export Data", "fullscreen": "Fullscreen"}, "profiles": {"title": "Customer Profiles", "description": "Manage and analyze customer profiles", "totalProfiles": "Total Profiles", "activeProfiles": "Active Profiles", "newThisMonth": "New This Month", "avgEngagement": "Avg Engagement", "searchPlaceholder": "Search customers by name, email, or ID...", "filters": {"all": "All", "highValue": "High Value", "mediumValue": "Medium Value", "lowValue": "Low Value", "atRisk": "At Risk", "newCustomers": "New Customers"}, "valueTiers": {"high": "High", "medium": "Medium", "low": "Low"}, "churnRisk": {"high": "High", "medium": "Medium", "low": "Low"}, "viewProfile": "View Profile", "sendMessage": "Send Message", "addToSegment": "Add to Segment", "emptyState": {"title": "No customers found", "description": "Try adjusting your search filters", "createProfile": "Create New Profile"}}, "segments": {"title": "Smart Segments", "description": "AI-powered customer segmentation and targeting", "totalSegments": "Total Segments", "activeSegments": "Active Segments", "totalCustomers": "Total Customers", "avgGrowth": "Avg Growth", "autoUpdating": "Auto-updating", "searchPlaceholder": "Search segments by name or description...", "filters": {"all": "All", "behavioral": "Behavioral", "demographic": "Demographic", "valueBased": "Value-based", "predictive": "Predictive", "advanced": "Advanced"}, "types": {"behavioral": "Behavioral", "demographic": "Demographic", "valueBased": "Value-based", "predictive": "Predictive"}, "createSegment": "Create Segment", "analyze": "Analyze", "campaign": "Campaign", "viewDetails": "View Details", "editSegment": "Edit Segment", "refreshData": "Refresh Data", "emptyState": {"title": "No segments found", "description": "Create your first customer segment to get started", "createSegment": "Create Segment"}, "performance": {"title": "Segment Performance Overview", "description": "Customer distribution and engagement across segments"}}, "analytics": {"title": "Advanced Analytics", "description": "Interactive charts and deep customer insights", "timeRanges": {"24h": "24H", "7d": "7D", "30d": "30D", "90d": "90D"}, "charts": {"customerGrowth": "Customer Growth", "revenueGrowth": "Revenue Growth", "engagementTrend": "Engagement Trend", "conversionRate": "Conversion Rate", "churnRate": "Churn Rate", "segmentPerformance": "Segment Performance", "monthlyRevenue": "Monthly Revenue", "customerAcquisition": "Customer Acquisition", "retentionRate": "Retention Rate", "averageOrderValue": "Average Order Value"}, "tabs": {"journey": "Customer Journey", "cohort": "Cohort Analysis", "attribution": "Attribution", "funnel": "Conversion Funnel", "revenue": "Revenue Analytics"}, "journey": {"title": "Customer Journey Flow", "description": "Step-by-step customer journey analysis", "conversionRates": "Journey Conversion Rates", "insights": "Journey Insights", "biggestDropoff": "Biggest Drop-off", "bestConverter": "Best Converter", "avgJourneyTime": "Avg Journey Time"}, "cohort": {"title": "Cohort Retention Analysis", "description": "Customer retention over time", "performance": "Cohort Performance", "retention30": "30-day Retention", "retention90": "90-day Retention", "retention1year": "1-year Retention", "churnRisk": "Churn Risk"}, "attribution": {"title": "Marketing Channel Attribution", "description": "Revenue attribution by marketing channel", "comparison": "Attribution Model Comparison", "insights": "Attribution Insights", "topChannel": "Top Channel", "bestROI": "Best ROI", "avgTouchpoints": "Avg Touchpoints"}, "funnel": {"title": "Conversion Funnel", "description": "Step-by-step conversion analysis", "rates": "Funnel Conversion Rates", "optimization": "Funnel Optimization", "overallConversion": "Overall Conversion", "potentialImprovement": "Potential Improvement"}, "revenue": {"title": "Revenue Trend Analysis", "description": "Monthly revenue growth and trends", "metrics": "Revenue Metrics", "insights": "Revenue Insights", "monthlyRecurring": "Monthly Recurring Revenue", "avgOrderValue": "Average Order Value", "customerLifetimeValue": "Customer Lifetime Value", "growthRate": "Revenue Growth Rate", "bestMonth": "Best Month", "growthTrend": "Growth Trend", "forecast": "Forecast"}}, "integrations": {"title": "Integration Hub", "description": "Connect and manage third-party integrations for your CDP", "activeIntegrations": "Active Integrations", "connected": "Connected", "recordsSynced": "Records Synced", "healthScore": "Health Score", "exportConfig": "Export Config", "addIntegration": "Add Integration", "configure": "Configure", "viewDetails": "View Details", "syncNow": "Sync Now", "status": {"connected": "Connected", "disconnected": "Disconnected", "error": "Error", "syncing": "Syncing"}, "categories": {"email": "Email Marketing", "analytics": "Analytics", "ecommerce": "E-commerce", "social": "Social Media", "advertising": "Advertising", "crm": "CRM"}}, "realtime": {"title": "Real-time Dashboard", "description": "Live customer activity and system metrics", "activeUsers": "Active Users", "pageViews": "Page Views", "conversions": "Conversions", "bounceRate": "Bounce Rate", "emailOpens": "Email Opens", "responseTime": "Avg Response Time", "connected": "Connected", "disconnected": "Disconnected", "pause": "Pause", "resume": "Resume", "lastUpdate": "Last update", "activityFeed": {"title": "Live Activity Feed", "description": "Real-time user actions and system events", "waiting": "Waiting for real-time events..."}, "systemPerformance": {"title": "System Performance", "description": "Real-time system health and performance metrics", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "databaseLoad": "Database Load", "apiResponseTime": "API Response Time", "activeConnections": "Active Connections", "systemStatus": "System Status", "allSystemsOperational": "All Systems Operational"}}, "aiInsights": {"title": "AI-Powered Insights", "description": "Machine learning insights and predictive analytics", "activeInsights": "Active Insights", "criticalIssues": "Critical Issues", "highImpact": "High Impact", "mlModels": "ML Models", "newThisWeek": "new this week", "requiresAttention": "Requires immediate attention", "revenueOpportunities": "Revenue opportunities", "allModelsActive": "All models active", "tabs": {"insights": "AI Insights", "predictions": "Predictions", "models": "ML Models", "recommendations": "Recommendations"}, "confidence": "Confidence Score", "impact": {"critical": "Critical", "high": "High", "medium": "Medium", "low": "Low"}, "categories": {"revenue": "Revenue", "churn": "Churn", "engagement": "Engagement", "conversion": "Conversion", "performance": "Performance"}, "actions": {"implement": "Implement", "dismiss": "<PERSON><PERSON><PERSON>", "configure": "Configure"}, "recommendations": {"title": "Today's AI Recommendations", "description": "Prioritized actions based on AI analysis", "highPriority": "High Priority", "mediumPriority": "Medium Priority", "lowPriority": "Low Priority"}, "models": {"accuracy": "Accuracy", "precision": "Precision", "recall": "Recall", "f1Score": "F1 Score", "aucRoc": "AUC-ROC", "lastTrained": "Last trained", "retrain": "Retrain"}, "predictions": {"title": "Customer Predictions", "description": "AI-powered predictions for individual customers", "selectCustomer": "Select a customer to view AI-powered predictions and risk assessments", "browseCustomers": "Browse Customers"}}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "update": "Update", "refresh": "Refresh", "export": "Export", "import": "Import", "search": "Search", "filter": "Filter", "sort": "Sort", "actions": "Actions", "settings": "Settings", "help": "Help", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "yes": "Yes", "no": "No", "all": "All", "none": "None", "select": "Select", "clear": "Clear", "reset": "Reset", "apply": "Apply", "download": "Download", "upload": "Upload", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo"}, "errors": {"generic": "An error occurred. Please try again.", "network": "Network connection error. Please check your internet connection.", "unauthorized": "You don't have permission to access this resource.", "notFound": "The requested resource was not found.", "validation": "Invalid input data. Please check and try again.", "server": "Internal server error. Please try again later.", "timeout": "Request timed out. Please try again."}, "success": {"saved": "Successfully saved", "updated": "Successfully updated", "deleted": "Successfully deleted", "created": "Successfully created", "imported": "Successfully imported", "exported": "Successfully exported", "synced": "Successfully synced"}}