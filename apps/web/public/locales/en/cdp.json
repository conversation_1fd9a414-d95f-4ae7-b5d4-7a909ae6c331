{"title": "Customer Data Platform", "description": "Unified customer profiles, segmentation, and marketing automation", "navigation": {"dashboard": "CDP Dashboard", "profiles": "Customer Profiles", "segments": "Segments", "journeys": "Journeys", "analytics": "Analytics"}, "dashboard": {"title": "Customer Data Platform", "description": "Unified customer profiles and analytics", "stats": {"totalCustomers": "Total Customers", "totalRevenue": "Total Revenue", "avgEngagement": "Avg Engagement", "churnRisk": "Churn Risk", "activeProfiles": "Active customer profiles", "lifetimeValue": "Lifetime customer value", "engagementScore": "Overall engagement score", "highRiskCustomers": "High-risk customers"}}, "profiles": {"title": "Customer Profiles", "description": "Unified view of all customer profiles in your CDP", "create": {"title": "Create Customer Profile", "description": "Add a new customer profile to your CDP", "button": "Create Profile"}, "search": {"placeholder": "Search customers...", "noResults": "No customer profiles found. Create your first profile to get started."}, "fields": {"email": "Email", "phone": "Phone", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "lifecycleStage": "Lifecycle Stage", "customerTier": "Customer Tier", "totalRevenue": "Total Revenue", "totalOrders": "Total Orders", "engagement": "Engagement", "churnRisk": "Churn Risk", "lastActivity": "Last Activity", "firstSeen": "First Seen"}, "lifecycle": {"prospect": "Prospect", "lead": "Lead", "customer": "Customer", "advocate": "Advocate", "churned": "Chu<PERSON>"}, "tiers": {"low": "Low", "medium": "Medium", "high": "High", "vip": "VIP"}}, "segments": {"title": "Customer Segments", "description": "Dynamic customer segmentation based on behavior and attributes", "create": {"title": "Create Segment", "description": "Define a new customer segment", "button": "Create Segment"}, "templates": {"highValue": "High Value Customers", "churnRisk": "Churn Risk Customers", "newCustomers": "New Customers", "inactiveCustomers": "Inactive Customers"}}, "journeys": {"title": "Customer Journeys", "description": "Automated marketing workflows and customer journey orchestration", "create": {"title": "Create Journey", "description": "Design a new customer journey", "button": "Create Journey"}}, "analytics": {"title": "CDP Analytics", "description": "Customer data platform analytics and insights", "metrics": {"profileGrowth": "Profile Growth", "engagementTrends": "Engagement Trends", "revenueAnalysis": "Revenue Analysis", "segmentPerformance": "Segment Performance"}}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "required": "Required", "optional": "Optional"}, "errors": {"profileNotFound": "Customer profile not found", "segmentNotFound": "Segment not found", "journeyNotFound": "Journey not found", "identityResolutionFailed": "Failed to resolve customer identity", "segmentEvaluationFailed": "Segment evaluation failed", "journeyExecutionFailed": "Journey execution failed", "networkError": "Network error occurred", "validationError": "Validation error", "permissionDenied": "Permission denied", "emailRequired": "Email is required", "invalidEmail": "Invalid email format", "invalidPhone": "Invalid phone format"}, "messages": {"profileCreated": "Customer profile created successfully", "profileUpdated": "Customer profile updated successfully", "profileDeleted": "Customer profile deleted successfully", "segmentCreated": "Segment created successfully", "segmentUpdated": "Segment updated successfully", "segmentDeleted": "Segment deleted successfully", "journeyCreated": "Journey created successfully", "journeyUpdated": "Journey updated successfully", "journeyDeleted": "Journey deleted successfully"}}