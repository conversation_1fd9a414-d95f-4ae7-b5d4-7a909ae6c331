import { Page, expect, test } from '@playwright/test';
import * as path from 'path';
import * as fs from 'fs';

import { ImportExportPageObject } from './import-export.po';

test.describe('Import/Export Feature', () => {
  let page: Page;
  let po: ImportExportPageObject;
  let slug: string;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    po = new ImportExportPageObject(page);
    const result = await po.setup();
    slug = result.slug;
  });

  test('should navigate to import-export page', async () => {
    await po.goToImportExport(slug);
    await expect(page.getByText('Import/Export Data')).toBeVisible();
  });

  test('should switch between export and import tabs', async () => {
    await po.switchToExportTab();
    await expect(page.getByText('Export Data')).toBeVisible();
    
    await po.switchToImportTab();
    await expect(page.getByText('Import Data')).toBeVisible();
    
    await po.switchToExportTab();
  });

  test('should select a resource and show available fields', async () => {
    await po.selectResource('Categories');
    await expect(page.getByTestId('field-name')).toBeVisible({ timeout: 5000 });
  });

  test('should drag fields to export', async () => {
    await po.dragFieldToExport('name');
    await po.dragFieldToExport('description');
    
    // Check if fields are in the selected area
    const selectedArea = page.locator('div[data-rbd-droppable-id="destination"]');
    await expect(selectedArea.getByTestId('field-name')).toBeVisible();
    await expect(selectedArea.getByTestId('field-description')).toBeVisible();
  });

  test('should enable export button when fields are selected', async () => {
    const exportButton = page.getByRole('button', { name: 'Export' });
    await expect(exportButton).toBeEnabled();
  });

  // Note: We can't fully test the actual file download in Playwright without additional setup
  // This test just verifies the button is clickable
  test('should click export button', async () => {
    // This will trigger a download which we can't easily verify in this test
    await po.clickExportButton();
  });

  test('should switch to import tab and upload a file', async () => {
    await po.switchToImportTab();
    
    // Create a temporary CSV file for testing
    const tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const csvContent = 'name,description\\nTest Category,Test Description';
    const csvPath = path.join(tempDir, 'test-categories.csv');
    fs.writeFileSync(csvPath, csvContent);
    
    await po.uploadFile(csvPath);
    
    // Clean up
    fs.unlinkSync(csvPath);
    fs.rmdirSync(tempDir);
    
    // Verify file was uploaded
    await expect(page.getByTestId('field-name')).toBeVisible({ timeout: 5000 });
  });

  // Note: The actual drag-and-drop mapping is difficult to test in Playwright
  // This is a simplified test that just verifies the UI elements are present
  test('should show mapping interface', async () => {
    await po.selectResource('Categories');
    
    // Check if source and destination areas are visible
    await expect(page.locator('div[data-rbd-droppable-id="source"]')).toBeVisible();
    await expect(page.locator('div[data-rbd-droppable-id="destination"]')).toBeVisible();
  });
});
