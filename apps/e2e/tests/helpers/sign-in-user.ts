import { Page } from '@playwright/test';

export async function signInUser(page: Page) {
  // Navigate to sign in page
  await page.goto('/auth/sign-in');
  
  // Fill in credentials (use test user credentials)
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'testpassword123');
  
  // Click sign in button
  await page.click('[data-testid="sign-in-submit"]');
  
  // Wait for redirect to dashboard
  await page.waitForURL('/home/<USER>');
  await page.waitForLoadState('networkidle');
}
