import { Page } from '@playwright/test';

import { TeamAccountsPageObject } from '../team-accounts/team-accounts.po';

export class CategoriesPageObject {
  private readonly page: Page;
  public teamAccounts: TeamAccountsPageObject;

  constructor(page: Page) {
    this.page = page;
    this.teamAccounts = new TeamAccountsPageObject(page);
  }

  async setup() {
    const { email, slug } = await this.teamAccounts.setup();
    await this.goToCategories(slug);
    return { email, slug };
  }

  async goToCategories(slug: string) {
    try {
      await this.page.goto(`/home/<USER>/categories`);
      await this.page.waitForURL(`**/home/<USER>/categories`, {
        timeout: 10000,
      });
    } catch (error) {
      console.error(`Failed to navigate to categories page: ${error}`);
      throw error;
    }
  }

  async createCategory(name: string, description?: string, parentId?: string) {
    try {
      // Navigate to the categories page first to ensure we're in the right place
      if (this.page.url().indexOf('/categories') === -1) {
        const slug = this.page.url().split('/home/')[1].split('/')[0];
        await this.goToCategories(slug);
      }

      // Wait for the add button to be visible and click it
      await this.page
        .getByTestId('categories-page-add-button')
        .waitFor({ state: 'visible', timeout: 10000 });
      await this.page.getByTestId('categories-page-add-button').click();

      // Wait for the modal to be fully visible
      await this.page
        .getByTestId('categories-page-create-name')
        .waitFor({ state: 'visible', timeout: 10000 });

      // Fill in the form fields
      await this.page.getByTestId('categories-page-create-name').fill(name);

      if (description) {
        await this.page
          .getByTestId('categories-page-create-description')
          .fill(description);
      }

      if (parentId) {
        try {
          // Try standard select element first
          await this.page
            .getByTestId('categories-page-create-parent')
            .selectOption(parentId);
        } catch (err) {
          console.log(
            'Parent selector is not a standard select element, trying alternative approach',
          );
          // If it's not a standard select, try clicking it and then selecting the option
          await this.page.getByTestId('categories-page-create-parent').click();
          await this.page.waitForTimeout(500);
          // Look for the option in the dropdown
          await this.page.getByRole('option', { name: parentId }).click();
        }
      }
      // Submit the form
      await this.page.getByTestId('categories-page-create-submit').click();
      await this.page.waitForTimeout(500);
      await this.page.reload();
      // Wait for the page to be fully loaded
      await this.page.waitForLoadState('domcontentloaded');
      await this.page.waitForTimeout(1000);

      // Take a screenshot after refresh

      // Try multiple times to find the category in the list
      let retries = 0;
      const maxRetries = 3;
      let found = false;

      while (retries < maxRetries && !found) {
        try {
          // Try to find by row containing the text
          await this.page.waitForSelector(`tr:has-text("${name}")`, {
            timeout: 10000,
          });
          found = true;
        } catch (err) {
          console.log(
            `Retry ${retries + 1}/${maxRetries} finding category ${name} by row selector`,
          );
          try {
            // Then try to find by text directly
            await this.page
              .getByText(name, { exact: true })
              .waitFor({ state: 'visible', timeout: 10000 });
            found = true;
          } catch (err2) {
            console.log(
              `Retry ${retries + 1}/${maxRetries} finding category ${name} by test ID`,
            );
            retries++;
            if (retries < maxRetries) {
              // Refresh the page and try again
              await this.page.reload();
              await this.page.waitForLoadState('networkidle');
              await this.page.waitForTimeout(2000);
              // Take a screenshot after each retry
            } else {
              throw err2;
            }
          }
        }
      }
    } catch (error) {
      console.error(`Failed to create category: ${error}`);
      // Take a screenshot on error (for debugging)
      throw error;
    }
  }

  async editCategory(
    oldName: string,
    newName: string,
    newDescription?: string,
    newParentId?: string,
  ) {
    try {
      // Find the row containing the category name
      const row = this.page.locator(`tr:has-text("${oldName}")`);
      // Click the edit button in that row
      await row.getByTestId('categories-page-edit-button').click();

      // Wait for the modal to be fully visible
      await this.page
        .getByTestId('categories-page-edit-name')
        .waitFor({ state: 'visible', timeout: 10000 });

      await this.page.getByTestId('categories-page-edit-name').fill(newName);

      if (newDescription !== undefined) {
        await this.page
          .getByTestId('categories-page-edit-description')
          .fill(newDescription);
      }

      if (newParentId !== undefined) {
        try {
          // Try standard select element first
          await this.page
            .getByTestId('categories-page-edit-parent')
            .selectOption(newParentId);
        } catch (err) {
          console.log(
            'Parent selector is not a standard select element, trying alternative approach',
          );
          // If it's not a standard select, try clicking it and then selecting the option
          await this.page.getByTestId('categories-page-edit-parent').click();
          await this.page.waitForTimeout(500);
          // Look for the option in the dropdown
          await this.page.getByRole('option', { name: newParentId }).click();
        }
      }

      // Take a screenshot before submitting (for debugging)

      await this.page.getByTestId('categories-page-edit-submit').click();

      // Wait for the form submission to complete and the toast to appear
      await this.page.waitForTimeout(3000);

      // Wait for the page to be fully loaded
      await this.page.waitForLoadState('domcontentloaded');

      // Take a screenshot after submitting (for debugging)

      // Try multiple times to find the category in the list
      let retries = 0;
      const maxRetries = 3;
      let found = false;

      while (retries < maxRetries && !found) {
        try {
          // First check if the element exists in the page
          await this.page.waitForSelector(`text=${newName}`, {
            timeout: 10000,
          });

          // Then check if it's inside an element with the correct data-testid
          const elements = await this.page
            .locator(`tr:has-text("${newName}")`)
            .all();
          if (elements.length === 0) {
            throw new Error(
              `Could not find element containing text: ${newName}`,
            );
          }
          found = true;
        } catch (err) {
          console.log(
            `Retry ${retries + 1}/${maxRetries} finding edited category ${newName}`,
          );
          retries++;
          if (retries < maxRetries) {
            // Refresh the page and try again
            await this.page.reload();
            await this.page.waitForLoadState('domcontentloaded');
            await this.page.waitForTimeout(2000);
          } else {
            throw err;
          }
        }
      }
    } catch (error) {
      console.error(`Failed to edit category: ${error}`);

      throw error;
    }
  }

  async deleteCategory(name: string) {
    try {
      // Find the row containing the category name
      const row = this.page.locator(`tr:has-text("${name}")`);
      // Click the delete button in that row
      await row.getByTestId('categories-page-delete-button').click();

      // Wait for the confirmation dialog to be visible
      await this.page
        .getByTestId('categories-page-delete-confirm')
        .waitFor({ state: 'visible', timeout: 10000 });

      await this.page.getByTestId('categories-page-delete-confirm').click();

      // Wait for the deletion to complete and the toast to appear
      await this.page.waitForTimeout(3000);

      // Wait for the page to be fully loaded
      await this.page.waitForLoadState('domcontentloaded');

      // Try multiple times to confirm the category is gone
      let retries = 0;
      const maxRetries = 3;
      let confirmed = false;

      while (retries < maxRetries && !confirmed) {
        try {
          // Check if the row with the category name is gone
          const rowExists = await this.page.isVisible(`tr:has-text("${name}")`);
          if (!rowExists) {
            confirmed = true;
          } else {
            throw new Error('Category still visible');
          }
        } catch (err) {
          console.log(
            `Retry ${retries + 1}/${maxRetries} confirming category ${name} is deleted`,
          );
          retries++;
          if (retries < maxRetries) {
            // Refresh the page and try again
            await this.page.reload();
            await this.page.waitForLoadState('domcontentloaded');
            await this.page.waitForTimeout(2000);
          } else {
            throw err;
          }
        }
      }
    } catch (error) {
      console.error(`Failed to delete category: ${error}`);
      throw error;
    }
  }

  async searchCategory(query: string) {
    try {
      // Try to find the search input by test ID first
      try {
        await this.page.getByTestId('categories-page-search').fill(query);
      } catch (err) {
        // If that fails, try to find it by placeholder text
        console.log('Search input not found by test ID, trying placeholder...');
        await this.page.locator('input[placeholder*="Search"]').fill(query);
      }

      // Wait for the search results to update
      await this.page.waitForTimeout(2000);
    } catch (error) {
      console.error(`Failed to search categories: ${error}`);
      throw error;
    }
  }
}
