import { Page, expect, test } from '@playwright/test';
import { IPOSPageObject } from './ipos.po';

test.describe('IPOS Integration Dashboard', () => {
  let page: Page;
  let po: IPOSPageObject;
  let slug: string;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    po = new IPOSPageObject(page);
    const result = await po.setup();
    slug = result.slug;
  });

  test.beforeEach(async () => {
    // Navigate to the IPOS dashboard before each test
    await po.goToDashboard(slug);
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("iPOS Dashboard")', { timeout: 10000 });
  });

  test('should display the dashboard with all sections', async () => {
    // Check if the page title is visible
    await expect(page.locator('h1:has-text("iPOS Dashboard")')).toBeVisible();

    // Check if the connection status card is visible
    await expect(page.getByText('Connection Status')).toBeVisible();

    // Check if the mapping status card is visible
    await expect(page.getByText('Field Mapping Status')).toBeVisible();

    // Check if the sync history card is visible
    await expect(page.getByText('Sync History')).toBeVisible();

    // Check if the quick actions card is visible
    await expect(page.getByText('Quick Actions')).toBeVisible();
  });

  test('should navigate to connection page when configure button is clicked', async () => {
    // Find and click the Configure button in the connection status card
    const configureButtons = page.getByRole('button', { name: 'Configure' });

    // Make sure we have at least one Configure button
    const count = await configureButtons.count();
    expect(count).toBeGreaterThan(0);

    // Click the first Configure button
    await configureButtons.first().click();

    // Check if we've navigated to the connection page
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/connect/);
  });

  test('should navigate to mapping page when setup mapping button is clicked', async () => {
    // Find and click the Setup Mapping or Edit Mapping button for Products
    const mappingButtons = page.getByRole('button', { name: /Setup Mapping|Edit Mapping/ });

    // Make sure we have at least one mapping button
    const count = await mappingButtons.count();
    expect(count).toBeGreaterThan(0);

    // Click the first mapping button
    await mappingButtons.first().click();

    // Check if we've navigated to the mapping page with the correct resource
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/mapping\?resource=products/);
  });

  test('should navigate to sync page when sync now button is clicked', async () => {
    // Find and click the Sync Now button
    const syncButtons = page.getByRole('button', { name: 'Sync Now' });

    // Make sure we have at least one Sync Now button
    const count = await syncButtons.count();
    expect(count).toBeGreaterThan(0);

    // Click the first Sync Now button
    await syncButtons.first().click();

    // Check if we've navigated to the sync page
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/sync/);
  });

  test('should navigate to sync history page when view all button is clicked', async () => {
    // Find and click the View All button in the sync history card
    const viewAllButtons = page.getByRole('button', { name: 'View All' });

    // Make sure we have at least one View All button
    const count = await viewAllButtons.count();
    expect(count).toBeGreaterThan(0);

    // Click the first View All button
    await viewAllButtons.first().click();

    // Check if we've navigated to the sync history page
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/sync-history/);
  });

  test('should test connection when test button is clicked', async () => {
    // Use the page object method to test the connection
    const result = await po.testConnection();

    // If the test was performed, we should have a result
    if (result) {
      // Wait for the toast notification
      await page.waitForTimeout(5000); // Give time for any toast to appear
    }
  });

  test('should display correct resource cards in mapping status', async () => {
    // Check if all three resource cards are visible
    await expect(page.getByText('Products', { exact: true })).toBeVisible();
    await expect(page.getByText('Orders', { exact: true })).toBeVisible();
    await expect(page.getByText('Customers', { exact: true })).toBeVisible();
  });

  test('should display all quick action buttons', async () => {
    // Check if all quick action buttons are visible
    await expect(page.getByText('Sync Data')).toBeVisible();
    await expect(page.getByText('Configure Mapping')).toBeVisible();
    await expect(page.getByText('View Sync History')).toBeVisible();
    await expect(page.getByText('Configure Connection')).toBeVisible();
  });
});
