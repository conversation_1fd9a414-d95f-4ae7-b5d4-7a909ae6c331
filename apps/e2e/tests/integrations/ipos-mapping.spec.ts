import { Page, expect, test } from '@playwright/test';
import { IPOSPageObject } from './ipos.po';

test.describe('IPOS Integration Mapping', () => {
  let page: Page;
  let po: IPOSPageObject;
  let slug: string;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    po = new IPOSPageObject(page);
    const result = await po.setup();
    slug = result.slug;
  });

  test.beforeEach(async () => {
    // Navigate to the IPOS mapping page before each test
    await po.goToMapping(slug);
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("iPOS Data Mapping")', { timeout: 10000 });
  });

  test('should display the mapping page with resource selector', async () => {
    // Check if the page title is visible
    await expect(page.locator('h1:has-text("iPOS Data Mapping")')).toBeVisible();

    // Check if the resource selector is visible
    await expect(page.getByLabel('Resource Type')).toBeVisible();

    // Check if the source fields section is visible
    await expect(page.getByText('Source Fields')).toBeVisible();

    // Check if the target fields section is visible
    await expect(page.getByText('Target Fields')).toBeVisible();
  });

  test('should change resource type when selector is changed', async () => {
    // Select the resource type dropdown
    const resourceTypeSelect = page.getByLabel('Resource Type');
    await expect(resourceTypeSelect).toBeVisible();
    await resourceTypeSelect.click();

    // Check if the dropdown options are visible
    const ordersOption = page.getByRole('option', { name: 'Orders' });
    await expect(ordersOption).toBeVisible();

    // Select Orders from the dropdown
    await ordersOption.click();

    // Check if the URL has been updated with the resource parameter
    await expect(page).toHaveURL(/resource=orders/);

    // Wait for the fields to load
    await page.waitForTimeout(1000);

    // Select the resource type dropdown again
    await resourceTypeSelect.click();

    // Select Customers from the dropdown
    const customersOption = page.getByRole('option', { name: 'Customers' });
    await expect(customersOption).toBeVisible();
    await customersOption.click();

    // Check if the URL has been updated with the resource parameter
    await expect(page).toHaveURL(/resource=customers/);
  });

  test('should be able to map fields between source and target', async () => {
    // Configure mapping for products
    const result = await po.configureMapping('products');

    // If mapping was successful, we should see a success message
    if (result) {
      await expect(page.getByText('Mappings saved successfully')).toBeVisible({ timeout: 5000 });
    }
  });

  test('should be able to save mappings', async () => {
    // Click the Save button
    const saveButton = page.getByRole('button', { name: 'Save Mappings' });
    await expect(saveButton).toBeVisible();

    // Check if the button is enabled
    if (await saveButton.isEnabled()) {
      await saveButton.click();

      // Check if the success message is displayed
      await expect(page.getByText('Mappings saved successfully')).toBeVisible({ timeout: 5000 });
    }
  });

  test('should be able to test mappings', async () => {
    // Click the Test Mapping button
    const testButton = page.getByRole('button', { name: 'Test Mapping' });
    await expect(testButton).toBeVisible();

    // Check if the button is enabled
    if (await testButton.isEnabled()) {
      await testButton.click();

      // Check if the preview section is displayed
      await expect(page.getByText('Mapping Preview')).toBeVisible({ timeout: 5000 });
    }
  });

  test('should be able to sync data after mapping', async () => {
    // Click the Sync Data button
    const syncButton = page.getByRole('button', { name: 'Sync Data' });
    await expect(syncButton).toBeVisible();

    // Check if the button is enabled
    if (await syncButton.isEnabled()) {
      await syncButton.click();

      // Check if the sync confirmation dialog is displayed
      await expect(page.getByText('Start Synchronization')).toBeVisible({ timeout: 5000 });

      // Click the Confirm button
      await page.getByRole('button', { name: 'Confirm' }).click();

      // Check if the sync started message is displayed
      await expect(page.getByText('Synchronization started')).toBeVisible({ timeout: 5000 });
    }
  });

  test('should navigate back to dashboard', async () => {
    // Click the Back to Dashboard button
    const backButton = page.getByRole('button', { name: 'Back to Dashboard' });
    await expect(backButton).toBeVisible();
    await backButton.click();

    // Check if we've navigated to the dashboard page
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/dashboard/);
  });
});
