import { expect, Page } from '@playwright/test';
import { AuthPageObject } from '../authentication/auth.po';
import { TeamAccountsPageObject } from '../team-accounts/team-accounts.po';

export class CustomersPageObject {
  private auth: AuthPageObject;
  private teamAccounts: TeamAccountsPageObject;

  constructor(public page: Page) {
    this.auth = new AuthPageObject(page);
    this.teamAccounts = new TeamAccountsPageObject(page);
  }

  /**
   * Setup a test account and navigate to the customers page
   */
  async setup() {
    const { email, slug } = await this.teamAccounts.setup();
    await this.auth.login(email);
    await this.goToCustomers(slug);
    return { email, slug };
  }

  /**
   * Navigate to the customers page
   */
  async goToCustomers(slug: string) {
    await this.page.goto(`/home/<USER>/customers`);
    await expect(this.page.locator('[data-testid="customers-page"]')).toBeVisible();
  }

  /**
   * Get the number of customers in the table
   */
  async getCustomersCount() {
    const rows = await this.page.locator('table tbody tr').count();
    return rows;
  }

  /**
   * Search for a customer
   */
  async searchCustomer(searchTerm: string) {
    await this.page.locator('[data-testid="customer-search-input"]').fill(searchTerm);
    await this.page.keyboard.press('Enter');
    // Wait for the search results to load
    await this.page.waitForTimeout(500);
  }

  /**
   * Create a new customer
   */
  async createCustomer(name: string, email: string, phone?: string, isVip = false) {
    await this.page.locator('[data-testid="create-customer-button"]').click();
    await this.page.locator('[data-testid="customer-name-input"]').fill(name);
    await this.page.locator('[data-testid="customer-email-input"]').fill(email);

    if (phone) {
      await this.page.locator('[data-testid="customer-phone-input"]').fill(phone);
    }

    if (isVip) {
      await this.page.locator('[data-testid="customer-vip-switch"]').click();
    }

    await this.page.locator('[data-testid="save-customer-button"]').click();

    // Wait for the customer to be created and redirected back to the list
    await this.page.waitForURL(/\/customers$/);
  }

  /**
   * View a customer's details
   */
  async viewCustomerDetails(rowIndex = 0) {
    await this.page.locator('table tbody tr').nth(rowIndex).locator('a').first().click();
    // Wait for the customer details page to load
    await this.page.waitForURL(/\/customers\/[^/]+$/);
  }

  /**
   * Update a customer
   */
  async updateCustomer(name: string, email: string, phone?: string, isVip = false) {
    await this.page.locator('[data-testid="customer-name-input"]').fill(name);
    await this.page.locator('[data-testid="customer-email-input"]').fill(email);

    if (phone) {
      await this.page.locator('[data-testid="customer-phone-input"]').fill(phone);
    }

    // Toggle VIP status if needed
    const currentVipStatus = await this.page.locator('[data-testid="customer-vip-switch"]').isChecked();
    if (currentVipStatus !== isVip) {
      await this.page.locator('[data-testid="customer-vip-switch"]').click();
    }

    await this.page.locator('[data-testid="save-customer-button"]').click();

    // Wait for the update to complete and redirect back to the list
    await this.page.waitForURL(/\/customers$/);
  }

  /**
   * Delete a customer
   */
  async deleteCustomer(rowIndex = 0) {
    const initialCount = await this.getCustomersCount();

    await this.page.locator('table tbody tr').nth(rowIndex).locator('button').nth(1).click();
    await this.page.locator('[data-testid="confirm-delete-button"]').click();

    // Wait for the customer to be deleted
    await this.page.waitForTimeout(500);

    const newCount = await this.getCustomersCount();
    expect(newCount).toBe(initialCount - 1);
  }

  /**
   * Check if pagination is visible
   */
  async hasPagination() {
    return await this.page.locator('[data-testid="customers-pagination"]').isVisible();
  }

  /**
   * Navigate to a specific page
   */
  async goToPage(pageNumber: number) {
    await this.page.locator(`[data-testid="pagination-page-${pageNumber}"]`).click();
    // Wait for the page to load
    await this.page.waitForTimeout(500);
  }

  /**
   * Check if the export button is visible
   */
  async hasExportButton() {
    return await this.page.locator('[data-testid="export-customers-button"]').isVisible();
  }
}
