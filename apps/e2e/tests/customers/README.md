# Customers Page E2E Tests

This directory contains E2E tests for the Customers page.

## Test Files

- `customers.po.ts`: Page Object for the Customers page
- `customers.spec.ts`: Full E2E tests for the Customers page (skipped because they require authentication)
- `customers-ui.spec.ts`: UI tests for the Customers page (skipped because they require authentication)
- `customers-auth.spec.ts`: Tests for the Customers page assuming the user is already authenticated (skipped because they require manual authentication)
- `customers-unauthenticated.spec.ts`: Tests for the Customers page when not authenticated (redirects to login page)

## Running the Tests

### Unauthenticated Tests

```bash
cd apps/e2e
npx playwright test tests/customers/customers-unauthenticated.spec.ts
```

These tests verify that unauthenticated users are redirected to the login page.

### Manual Tests (Requires Pre-authentication)

1. Start the application
2. Log in manually
3. Navigate to the customers page
4. Run the tests:

```bash
cd apps/e2e
npx playwright test tests/customers/customers-auth.spec.ts
```

## Test Coverage

- Basic page structure
- Search functionality
- Export button
- Table headers
- Pagination (if available)
- Customer details view
- Customer creation (commented out)
- Customer update (commented out)
- Customer deletion (commented out)

## Notes

- The full E2E tests require authentication, which is handled automatically by the test framework
- The UI tests are skipped because they require authentication
- The manual tests assume the user is already authenticated and on the customers page
