import { test, expect } from '@playwright/test';

// This test requires manual setup:
// 1. Start the application
// 2. Log in manually
// 3. Navigate to the order detail page
// 4. Run this test

// Skip all tests since they require manual authentication
test.describe.skip('Order Detail Page (Authenticated)', () => {
  test.beforeEach(async ({ page }) => {
    // Assume the user is already logged in and on the order detail page
    await page.goto('http://localhost:3000/home/<USER>/orders/fe497b47-cbf5-42fe-8373-e6bddd508760');
    
    // Wait for the page to load
    await page.waitForTimeout(2000);
  });

  test('should have correct page structure', async ({ page }) => {
    // Check if the page has the correct structure
    await expect(page.locator('[data-testid="order-detail-page"]')).toBeVisible();
    await expect(page.locator('[data-testid="invoice-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="invoice-items"]')).toBeVisible();
  });

  test('should display invoice header information', async ({ page }) => {
    // Check if the invoice header contains the correct information
    const header = page.locator('[data-testid="invoice-header"]');
    await expect(header.locator('h1')).toContainText('INVOICE');
    await expect(header.locator('h2:first-of-type')).toBeVisible(); // Company name
    await expect(header.locator('h2:nth-of-type(2)')).toContainText('Invoice To:');
  });

  test('should display invoice items', async ({ page }) => {
    // Check if the invoice items table is visible
    const items = page.locator('[data-testid="invoice-items"]');
    await expect(items.locator('table')).toBeVisible();
    
    // Check if the table headers are correct
    const headers = items.locator('table thead th');
    await expect(headers.nth(0)).toContainText('Description');
    await expect(headers.nth(1)).toContainText('Qty');
    await expect(headers.nth(2)).toContainText('Price');
    await expect(headers.nth(3)).toContainText('Total');
  });

  test('should have functional buttons', async ({ page }) => {
    // Check if the buttons are visible
    await expect(page.locator('[data-testid="print-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="approve-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="cancel-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="pdf-button"]')).toBeVisible();
  });
});
