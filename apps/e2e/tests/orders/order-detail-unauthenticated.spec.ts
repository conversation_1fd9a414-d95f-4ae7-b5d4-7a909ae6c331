import { test, expect } from '@playwright/test';

test.describe('Order Detail Page (Unauthenticated)', () => {
  test('should redirect to login page when not authenticated', async ({ page }) => {
    // Navigate to the order detail page
    await page.goto('http://localhost:3000/home/<USER>/orders/fe497b47-cbf5-42fe-8373-e6bddd508760');
    
    // Wait for the redirect
    await page.waitForTimeout(2000);
    
    // Check if redirected to login page
    expect(page.url()).toContain('/auth/sign-in');
    
    // Check if login form is visible
    await expect(page.locator('form')).toBeVisible();
    
    // Check if email input is visible
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    // Check if password input is visible
    await expect(page.locator('input[type="password"]')).toBeVisible();
    
    // Check if sign in button is visible
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });
});
