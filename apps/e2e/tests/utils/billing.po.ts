import { Page, expect } from '@playwright/test';

import { LemonSqueezyPageObject } from './lemon-squeezy.po';
import { StripePageObject } from './stripe.po';

export class BillingPageObject {
  public readonly lemonSqueezy: LemonSqueezyPageObject;
  public readonly stripe: StripePageObject;

  constructor(private readonly page: Page) {
    this.lemonSqueezy = new LemonSqueezyPageObject(page);
    this.stripe = new StripePageObject(page);
  }

  /**
   * Xử lý toàn bộ quá trình đăng ký gói và kiểm tra trạng thái thanh toán
   * @param planIndex Chỉ số của gói cần chọn (mặc định là 0 - gói đầu tiên)
   * @returns true nếu đăng ký thành công, false nếu có lỗi
   */
  async completeSubscription(planIndex = 0): Promise<boolean> {
    try {
      // Chọn gói
      await this.selectPlan(planIndex);
      console.log(`Selected plan at index ${planIndex}`);

      // Tiến hành thanh toán
      await this.proceedToCheckout();
      console.log('Proceeded to checkout');

      // Xử lý thanh toán với cổng thanh toán phù hợp
      try {
        // Thử với LemonSqueezy trước
        try {
          await this.lemonSqueezy.waitForForm();
          await this.lemonSqueezy.fillForm();
          await this.lemonSqueezy.submitForm();
          console.log('Submitted LemonSqueezy payment form');

          // Quay lại trang billing
          try {
            await this.returnToBillingLemonsqueezy();
          } catch (returnError) {
            console.log(
              'Could not return to billing automatically:',
              returnError,
            );
          }
        } catch (lemonError) {
          console.log(
            'LemonSqueezy form not found, trying Stripe:',
            lemonError,
          );

          // Thử với Stripe
          await this.stripe.waitForForm();
          await this.stripe.fillForm();
          await this.stripe.submitForm();
          console.log('Submitted Stripe payment form');

          // Quay lại trang billing
          try {
            await this.returnToBilling();
          } catch (returnError) {
            console.log(
              'Could not return to billing automatically:',
              returnError,
            );
          }
        }
      } catch (error) {
        console.error('Failed to submit payment:', error);
        return false;
      }

      // Kiểm tra trạng thái đăng ký sau khi quay lại trang billing
      try {
        // Đợi thêm một chút để đảm bảo trang đã tải hoàn toàn
        await this.page.waitForLoadState('domcontentloaded');
        await this.page.waitForTimeout(2000);

        // Kiểm tra URL hiện tại có phải là trang billing không
        const currentUrl = this.page.url();
        if (!currentUrl.includes('/branch')) {
          const match = currentUrl.match(/\/home\/<USER>\/]+)/);
          const accountSlug = match ? match[1] : 'makerkit';
          await this.page.goto(`/home/<USER>/billing`, {
            timeout: 30000,
          });
          await this.page.waitForLoadState('domcontentloaded');
        }

        // Kiểm tra trạng thái đăng ký
        const status = this.getStatus();
        await expect(status).toBeVisible({ timeout: 15000 });

        const manageButton = this.manageBillingButton();
        await expect(manageButton).toBeVisible();

        console.log('Subscription confirmed successful');
        return true;
      } catch (error) {
        console.log('Could not verify subscription status:', error);
        return false;
      }
    } catch (error) {
      console.error('Error in completeSubscription:', error);
      return false;
    }
  }

  plans() {
    return this.page.locator('[data-test-plan]');
  }

  async selectPlan(index = 0) {
    await expect(async () => {
      const plans = this.plans();
      const plan = plans.nth(index);

      await expect(plan).toBeVisible();

      await this.page.waitForTimeout(500);

      await plan.click();
    }).toPass();
  }

  manageBillingButton() {
    return this.page.locator('[data-test="manage-billing-redirect-button"]');
  }

  successStatus() {
    return this.page.locator('[data-test="payment-return-success"]');
  }

  async returnToBilling() {
    // Đợi webhook xử lý thanh toán và trang tải lại
    await this.page.waitForTimeout(5000);

    // Lấy URL hiện tại
    const currentUrl = this.page.url();
    console.log(`Current URL after payment: ${currentUrl}`);

    // Sử dụng expect.toPass() để đảm bảo thao tác hoàn thành
    return expect(async () => {
      // Kiểm tra xem đã ở trang thành công chưa
      if (currentUrl.includes('success') || currentUrl.includes('thank-you')) {
        console.log('Already on success page, looking for back button');

        // Thử tìm nút quay lại với nhiều selector khác nhau
        const backButtonSelectors = [
          '[data-test="checkout-success-back-link"]',
          'a:has-text("Back")',
          'a:has-text("Return")',
          'a:has-text("Continue")',
          'button:has-text("Back")',
          'button:has-text("Return")',
          'button:has-text("Continue")',
          'a.back-link',
          'a.return-link',
          'a.continue-link',
          'a[href*="billing"]',
        ];

        for (const selector of backButtonSelectors) {
          try {
            const backButton = this.page.locator(selector);
            if (await backButton.isVisible({ timeout: 2000 })) {
              console.log(`Found back button with selector: ${selector}`);
              await backButton.click();
              // Chấp nhận nhiều mẫu URL khác nhau
              await this.page.waitForURL(
                /\/(home\/[^\/]+(\/billing|\/billing\/return|))$/,
                { timeout: 10000 },
              );
              return;
            }
          } catch (error) {
            console.log(
              `Could not find back button with selector: ${selector}`,
            );
          }
        }
      }

      // Nếu không tìm thấy nút nào, thử quay lại trang billing bằng cách điều hướng trực tiếp
      console.log(
        'Could not find any back button, trying to navigate directly to billing page',
      );

      // Lấy slug từ URL hiện tại
      const urlParts = currentUrl.split('/');
      const homeIndex = urlParts.indexOf('home');

      if (homeIndex >= 0 && homeIndex + 1 < urlParts.length) {
        const slug = urlParts[homeIndex + 1];
        console.log(`Extracted slug from URL: ${slug}`);

        // Điều hướng trực tiếp đến trang billing
        await this.page.goto(`/home/<USER>/billing`, { timeout: 30000 });
        // Chấp nhận nhiều mẫu URL khác nhau
        await this.page.waitForURL(
          /\/(home\/[^\/]+(\/billing|\/billing\/return|))$/,
          { timeout: 10000 },
        );
      } else {
        console.log(
          'Could not extract slug from URL, trying to find Billing link',
        );
        // Nếu không lấy được slug, thử tìm link Billing trong menu
        await this.page
          .locator('a', {
            hasText: 'Billing',
          })
          .click();

        // Chấp nhận nhiều mẫu URL khác nhau
        await this.page.waitForURL(
          /\/(home\/[^\/]+(\/billing|\/billing\/return|))$/,
          { timeout: 10000 },
        );
      }
    }).toPass();
  }

  proceedToCheckout() {
    return this.page.click('[data-test="checkout-submit-button"]');
  }

  getStatus() {
    return this.page.locator('[data-test="current-plan-card-status-badge"]');
  }

  async returnToBillingLemonsqueezy() {
    // Đợi webhook xử lý thanh toán và trang tải lại
    await this.page.waitForTimeout(5000);

    // Lấy URL hiện tại
    const currentUrl = this.page.url();
    console.log(`Current URL after payment: ${currentUrl}`);

    // Sử dụng expect.toPass() để đảm bảo thao tác hoàn thành
    return expect(async () => {
      // Kiểm tra xem đã ở trang thành công chưa
      if (currentUrl.includes('success') || currentUrl.includes('thank-you')) {
        console.log('Already on success page, looking for back button');

        // Thử tìm nút quay lại với nhiều selector khác nhau
        const backButtonSelectors = [
          '[data-test="checkout-success-back-link"]',
          'a:has-text("Back")',
          'a:has-text("Return")',
          'a:has-text("Continue")',
          'button:has-text("Back")',
          'button:has-text("Return")',
          'button:has-text("Continue")',
          'a.back-link',
          'a.return-link',
          'a.continue-link',
          'a[href*="billing"]',
        ];

        for (const selector of backButtonSelectors) {
          try {
            const backButton = this.page.locator(selector);
            if (await backButton.isVisible({ timeout: 2000 })) {
              console.log(`Found back button with selector: ${selector}`);
              await backButton.click();
              await this.page.waitForURL('**/home/<USER>/billing', {
                timeout: 10000,
              });
              return;
            }
          } catch (error) {
            console.log(
              `Could not find back button with selector: ${selector}`,
            );
          }
        }
      }

      // Thử tìm nút trong iframe của LemonSqueezy
      try {
        // Kiểm tra xem iframe có tồn tại không trước khi tìm nút
        const iframeExists = (await this.page.locator('iframe').count()) > 0;

        if (iframeExists) {
          try {
            const duskButton = this.lemonSqueezy.checkoutSuccessButton();
            if (await duskButton.isVisible({ timeout: 2000 })) {
              console.log('Found checkout success button with dusk attribute');
              await duskButton.click();
              // Chấp nhận nhiều mẫu URL khác nhau
              await this.page.waitForURL(
                /\/(home\/[^\/]+(\/billing|\/billing\/return|))$/,
                { timeout: 10000 },
              );
              return;
            }
          } catch (error) {
            console.log('Could not find button with dusk attribute:', error);
          }
        } else {
          console.log(
            'No iframes found on the page, skipping iframe button search',
          );
        }
      } catch (error) {
        console.log('Error searching for button in iframe:', error);
      }

      // Nếu không tìm thấy nút nào, thử quay lại trang billing bằng cách điều hướng trực tiếp
      console.log(
        'Could not find any back button, trying to navigate directly to billing page',
      );

      // Lấy slug từ URL hiện tại
      const urlParts = currentUrl.split('/');
      const homeIndex = urlParts.indexOf('home');

      if (homeIndex >= 0 && homeIndex + 1 < urlParts.length) {
        const slug = urlParts[homeIndex + 1];
        console.log(`Extracted slug from URL: ${slug}`);

        // Điều hướng trực tiếp đến trang billing
        await this.page.goto(`/home/<USER>/billing`, { timeout: 30000 });
        // Chấp nhận nhiều mẫu URL khác nhau
        await this.page.waitForURL(
          /\/(home\/[^\/]+(\/billing|\/billing\/return|))$/,
          { timeout: 10000 },
        );
      } else {
        console.log(
          'Could not extract slug from URL, trying to find Billing link',
        );
        // Nếu không lấy được slug, thử tìm link Billing trong menu
        await this.page
          .locator('a', {
            hasText: 'Billing',
          })
          .click();

        // Chấp nhận nhiều mẫu URL khác nhau
        await this.page.waitForURL(
          /\/(home\/[^\/]+(\/billing|\/billing\/return|))$/,
          { timeout: 10000 },
        );
      }
    }).toPass();
  }
}
