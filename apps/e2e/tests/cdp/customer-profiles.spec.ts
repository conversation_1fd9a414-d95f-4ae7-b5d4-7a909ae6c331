import { test, expect } from '@playwright/test';
import { signInUser } from '../helpers/sign-in-user';

test.describe('CDP Customer Profiles', () => {
  test.beforeEach(async ({ page }) => {
    await signInUser(page);
    await page.goto('/home/<USER>/cdp');
    await page.waitForLoadState('networkidle');
  });

  test('should display customer profiles dashboard', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Check if the page loads correctly
    await expect(page.locator('h1')).toContainText('Hồ sơ khách hàng');
    
    // Check stats cards are visible
    await expect(page.locator('[data-testid="total-profiles-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="active-profiles-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="avg-value-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="growth-rate-stat"]')).toBeVisible();
  });

  test('should create a new customer profile', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Click create profile button
    await page.click('[data-testid="create-profile-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-profile-modal"]')).toBeVisible();

    // Fill in the form
    await page.fill('[data-testid="first-name-input"]', 'John');
    await page.fill('[data-testid="last-name-input"]', 'Doe');
    await page.fill('[data-testid="email-input"]', `john.doe.${Date.now()}@example.com`);
    await page.fill('[data-testid="phone-input"]', '+84901234567');
    await page.fill('[data-testid="location-input"]', 'Hà Nội');
    
    // Select value tier
    await page.click('[data-testid="value-tier-select"]');
    await page.click('[data-testid="value-tier-high"]');
    
    // Add tags
    await page.fill('[data-testid="tags-input"]', 'vip');
    await page.click('[data-testid="add-tag-btn"]');
    
    // Add notes
    await page.fill('[data-testid="notes-textarea"]', 'High-value customer from referral program');
    
    // Submit form
    await page.click('[data-testid="submit-profile-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify profile was created (should see it in the list)
    await expect(page.locator('text=John Doe')).toBeVisible();
  });

  test('should view customer profile details', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Click on first profile's view button
    await page.click('[data-testid="view-profile-btn"]:first-child');
    
    // Wait for detail modal to open
    await expect(page.locator('[data-testid="profile-detail-modal"]')).toBeVisible();
    
    // Check tabs are present
    await expect(page.locator('[data-testid="overview-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="activity-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="orders-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="insights-tab"]')).toBeVisible();
    
    // Check basic info is displayed
    await expect(page.locator('[data-testid="profile-email"]')).toBeVisible();
    await expect(page.locator('[data-testid="profile-name"]')).toBeVisible();
    
    // Check metrics are displayed
    await expect(page.locator('[data-testid="total-spent-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-orders-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="engagement-score-metric"]')).toBeVisible();
  });

  test('should delete a customer profile', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Get initial profile count
    const initialProfiles = await page.locator('[data-testid="profile-card"]').count();
    
    if (initialProfiles === 0) {
      // Create a profile first if none exist
      await page.click('[data-testid="create-profile-btn"]');
      await page.fill('[data-testid="first-name-input"]', 'Test');
      await page.fill('[data-testid="last-name-input"]', 'User');
      await page.fill('[data-testid="email-input"]', `test.${Date.now()}@example.com`);
      await page.click('[data-testid="submit-profile-btn"]');
      await page.waitForLoadState('networkidle');
    }

    // Click on first profile's delete button
    await page.click('[data-testid="delete-profile-btn"]:first-child');
    
    // Confirm deletion in dialog
    await page.click('[data-testid="confirm-delete-btn"]');
    
    // Wait for page reload
    await page.waitForLoadState('networkidle');
    
    // Verify profile count decreased
    const finalProfiles = await page.locator('[data-testid="profile-card"]').count();
    expect(finalProfiles).toBeLessThan(initialProfiles + 1); // +1 because we might have created one
  });

  test('should search customer profiles', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Use search functionality
    await page.fill('[data-testid="search-profiles-input"]', 'john');
    await page.waitForTimeout(1000); // Wait for debounce
    
    // Check that search results are filtered
    const profileCards = page.locator('[data-testid="profile-card"]');
    const count = await profileCards.count();
    
    if (count > 0) {
      // Verify search results contain the search term
      const firstProfile = profileCards.first();
      const profileText = await firstProfile.textContent();
      expect(profileText?.toLowerCase()).toContain('john');
    }
  });

  test('should filter customer profiles by value tier', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Click on high value filter
    await page.click('[data-testid="filter-high-value"]');
    await page.waitForLoadState('networkidle');
    
    // Verify URL contains filter parameter
    expect(page.url()).toContain('filter=high');
    
    // Check that only high-value profiles are shown
    const profileCards = page.locator('[data-testid="profile-card"]');
    const count = await profileCards.count();
    
    if (count > 0) {
      // Verify all visible profiles have high value tier
      for (let i = 0; i < count; i++) {
        const profile = profileCards.nth(i);
        await expect(profile.locator('[data-testid="value-tier-badge"]')).toContainText('Cao');
      }
    }
  });

  test('should handle empty state correctly', async ({ page }) => {
    // Navigate to customer profiles with a search that returns no results
    await page.goto('/home/<USER>/cdp?tab=profiles&search=nonexistentuser12345');
    await page.waitForLoadState('networkidle');

    // Check empty state is displayed
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
    await expect(page.locator('text=Không tìm thấy khách hàng')).toBeVisible();
    await expect(page.locator('[data-testid="create-profile-btn"]')).toBeVisible();
  });

  test('should export customer profiles data', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Set up download listener
    const downloadPromise = page.waitForEvent('download');
    
    // Click export button
    await page.click('[data-testid="export-profiles-btn"]');
    
    // Wait for download to start
    const download = await downloadPromise;
    
    // Verify download filename
    expect(download.suggestedFilename()).toContain('customer-profiles');
    expect(download.suggestedFilename()).toMatch(/\.(csv|xlsx)$/);
  });

  test('should refresh customer profiles data', async ({ page }) => {
    // Navigate to customer profiles
    await page.click('[data-testid="profiles-tab"]');
    await page.waitForLoadState('networkidle');

    // Click refresh button
    await page.click('[data-testid="refresh-profiles-btn"]');
    
    // Wait for page to reload
    await page.waitForLoadState('networkidle');
    
    // Verify page is still on profiles tab
    await expect(page.locator('h1')).toContainText('Hồ sơ khách hàng');
  });
});
