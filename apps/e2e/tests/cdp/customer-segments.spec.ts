import { test, expect } from '@playwright/test';
import { signInUser } from '../helpers/sign-in-user';

test.describe('CDP Customer Segments', () => {
  test.beforeEach(async ({ page }) => {
    await signInUser(page);
    await page.goto('/home/<USER>/cdp/segments');
    await page.waitForLoadState('networkidle');
  });

  test('should display customer segments dashboard', async ({ page }) => {
    // Check if the page loads correctly
    await expect(page.locator('h1')).toContainText('Phân khúc khách hàng');
    
    // Check stats cards are visible
    await expect(page.locator('[data-testid="total-segments-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-customers-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="avg-growth-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="auto-updating-stat"]')).toBeVisible();
  });

  test('should create a new customer segment', async ({ page }) => {
    // Click create segment button
    await page.click('[data-testid="create-segment-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-segment-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="segment-name-input"]', `High Value Customers ${Date.now()}`);
    await page.fill('[data-testid="segment-description-textarea"]', 'Customers with high lifetime value and engagement');
    
    // Select segment type - Value Based
    await page.click('[data-testid="segment-type-value_based"]');
    
    // Configure criteria for value-based segment
    await page.click('[data-testid="total-spent-operator"]');
    await page.click('[data-testid="operator-gte"]');
    await page.fill('[data-testid="total-spent-value"]', '5000000');
    
    await page.click('[data-testid="total-orders-operator"]');
    await page.click('[data-testid="operator-gte"]');
    await page.fill('[data-testid="total-orders-value"]', '10');
    
    // Enable auto-updating
    await page.check('[data-testid="auto-updating-toggle"]');
    
    // Submit form
    await page.click('[data-testid="submit-segment-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify segment was created
    await expect(page.locator('text=High Value Customers')).toBeVisible();
  });

  test('should create behavioral segment', async ({ page }) => {
    // Click create segment button
    await page.click('[data-testid="create-segment-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-segment-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="segment-name-input"]', `Highly Engaged Users ${Date.now()}`);
    await page.fill('[data-testid="segment-description-textarea"]', 'Users with high engagement scores');
    
    // Select segment type - Behavioral
    await page.click('[data-testid="segment-type-behavioral"]');
    
    // Configure behavioral criteria
    await page.click('[data-testid="engagement-score-operator"]');
    await page.click('[data-testid="operator-gte"]');
    await page.fill('[data-testid="engagement-score-value"]', '0.8');
    
    // Set recent activity filter
    await page.click('[data-testid="recent-activity-select"]');
    await page.click('[data-testid="activity-30-days"]');
    
    // Submit form
    await page.click('[data-testid="submit-segment-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify segment was created
    await expect(page.locator('text=Highly Engaged Users')).toBeVisible();
  });

  test('should create demographic segment', async ({ page }) => {
    // Click create segment button
    await page.click('[data-testid="create-segment-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-segment-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="segment-name-input"]', `Hanoi Customers ${Date.now()}`);
    await page.fill('[data-testid="segment-description-textarea"]', 'Customers located in Hanoi');
    
    // Select segment type - Demographic
    await page.click('[data-testid="segment-type-demographic"]');
    
    // Configure demographic criteria
    await page.click('[data-testid="location-select"]');
    await page.click('[data-testid="location-hanoi"]');
    
    await page.click('[data-testid="value-tier-select"]');
    await page.click('[data-testid="value-tier-high"]');
    
    // Submit form
    await page.click('[data-testid="submit-segment-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify segment was created
    await expect(page.locator('text=Hanoi Customers')).toBeVisible();
  });

  test('should create predictive segment', async ({ page }) => {
    // Click create segment button
    await page.click('[data-testid="create-segment-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-segment-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="segment-name-input"]', `Churn Risk Customers ${Date.now()}`);
    await page.fill('[data-testid="segment-description-textarea"]', 'Customers at high risk of churning');
    
    // Select segment type - Predictive
    await page.click('[data-testid="segment-type-predictive"]');
    
    // Configure predictive criteria
    await page.click('[data-testid="churn-risk-operator"]');
    await page.click('[data-testid="operator-gte"]');
    await page.fill('[data-testid="churn-risk-value"]', '0.7');
    
    // Submit form
    await page.click('[data-testid="submit-segment-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify segment was created
    await expect(page.locator('text=Churn Risk Customers')).toBeVisible();
  });

  test('should view segment details', async ({ page }) => {
    // Click on first segment's view button
    await page.click('[data-testid="view-segment-btn"]:first-child');
    
    // Wait for detail modal to open
    await expect(page.locator('[data-testid="segment-detail-modal"]')).toBeVisible();
    
    // Check tabs are present
    await expect(page.locator('[data-testid="overview-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="members-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="analytics-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="settings-tab"]')).toBeVisible();
    
    // Check segment info is displayed
    await expect(page.locator('[data-testid="segment-name"]')).toBeVisible();
    await expect(page.locator('[data-testid="segment-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="segment-type"]')).toBeVisible();
    
    // Check metrics are displayed
    await expect(page.locator('[data-testid="customer-count-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="growth-rate-metric"]')).toBeVisible();
  });

  test('should edit a segment', async ({ page }) => {
    // Click on first segment's edit button
    await page.click('[data-testid="edit-segment-btn"]:first-child');
    
    // Wait for edit modal to open
    await expect(page.locator('[data-testid="edit-segment-modal"]')).toBeVisible();
    
    // Update segment name
    await page.fill('[data-testid="segment-name-input"]', `Updated Segment ${Date.now()}`);
    
    // Update description
    await page.fill('[data-testid="segment-description-textarea"]', 'Updated segment description');
    
    // Submit changes
    await page.click('[data-testid="save-segment-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify segment was updated
    await expect(page.locator('text=Updated Segment')).toBeVisible();
  });

  test('should delete a segment', async ({ page }) => {
    // Get initial segment count
    const initialSegments = await page.locator('[data-testid="segment-card"]').count();
    
    if (initialSegments === 0) {
      // Create a segment first if none exist
      await page.click('[data-testid="create-segment-btn"]');
      await page.fill('[data-testid="segment-name-input"]', 'Test Segment');
      await page.fill('[data-testid="segment-description-textarea"]', 'Test description');
      await page.click('[data-testid="segment-type-behavioral"]');
      await page.click('[data-testid="submit-segment-btn"]');
      await page.waitForLoadState('networkidle');
    }

    // Click on first segment's delete button
    await page.click('[data-testid="delete-segment-btn"]:first-child');
    
    // Confirm deletion in dialog
    await page.click('[data-testid="confirm-delete-btn"]');
    
    // Wait for page reload
    await page.waitForLoadState('networkidle');
    
    // Verify segment count decreased
    const finalSegments = await page.locator('[data-testid="segment-card"]').count();
    expect(finalSegments).toBeLessThan(initialSegments + 1); // +1 because we might have created one
  });

  test('should filter segments by type', async ({ page }) => {
    // Click on behavioral filter
    await page.click('[data-testid="filter-behavioral"]');
    await page.waitForLoadState('networkidle');
    
    // Verify URL contains filter parameter
    expect(page.url()).toContain('filter=behavioral');
    
    // Check that only behavioral segments are shown
    const segmentCards = page.locator('[data-testid="segment-card"]');
    const count = await segmentCards.count();
    
    if (count > 0) {
      // Verify all visible segments have behavioral type
      for (let i = 0; i < count; i++) {
        const segment = segmentCards.nth(i);
        await expect(segment.locator('[data-testid="segment-type-badge"]')).toContainText('Hành vi');
      }
    }
  });

  test('should search segments', async ({ page }) => {
    // Use search functionality
    await page.fill('[data-testid="search-segments-input"]', 'high');
    await page.waitForTimeout(1000); // Wait for debounce
    
    // Check that search results are filtered
    const segmentCards = page.locator('[data-testid="segment-card"]');
    const count = await segmentCards.count();
    
    if (count > 0) {
      // Verify search results contain the search term
      const firstSegment = segmentCards.first();
      const segmentText = await firstSegment.textContent();
      expect(segmentText?.toLowerCase()).toContain('high');
    }
  });

  test('should analyze segment performance', async ({ page }) => {
    // Click on first segment's analyze button
    await page.click('[data-testid="analyze-segment-btn"]:first-child');
    
    // Wait for analytics modal or page to open
    await expect(page.locator('[data-testid="segment-analytics"]')).toBeVisible();
    
    // Check analytics components are present
    await expect(page.locator('[data-testid="performance-chart"]')).toBeVisible();
    await expect(page.locator('[data-testid="growth-metrics"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-distribution"]')).toBeVisible();
  });

  test('should handle empty state correctly', async ({ page }) => {
    // Navigate to segments with a search that returns no results
    await page.goto('/home/<USER>/cdp/segments?search=nonexistentsegment12345');
    await page.waitForLoadState('networkidle');

    // Check empty state is displayed
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
    await expect(page.locator('text=Không tìm thấy phân khúc')).toBeVisible();
    await expect(page.locator('[data-testid="create-segment-btn"]')).toBeVisible();
  });
});
