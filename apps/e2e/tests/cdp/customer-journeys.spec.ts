import { test, expect } from '@playwright/test';
import { signInUser } from '../helpers/sign-in-user';

test.describe('CDP Customer Journeys', () => {
  test.beforeEach(async ({ page }) => {
    await signInUser(page);
    await page.goto('/home/<USER>/cdp/journeys');
    await page.waitForLoadState('networkidle');
  });

  test('should display customer journeys dashboard', async ({ page }) => {
    // Check if the page loads correctly
    await expect(page.locator('h1')).toContainText('Điều phối hành trình');
    
    // Check stats cards are visible
    await expect(page.locator('[data-testid="total-journeys-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="active-journeys-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-participants-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="avg-completion-stat"]')).toBeVisible();
  });

  test('should create a new customer journey with segment trigger', async ({ page }) => {
    // Click create journey button
    await page.click('[data-testid="create-journey-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-journey-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="journey-name-input"]', `Welcome Journey ${Date.now()}`);
    await page.fill('[data-testid="journey-description-textarea"]', 'Onboarding journey for new customers');
    
    // Select trigger type - Segment Entry
    await page.click('[data-testid="trigger-type-segment_entry"]');
    
    // Configure segment trigger
    await page.click('[data-testid="segment-select"]');
    await page.click('[data-testid="segment-new-customers"]');
    
    // Verify steps preview is shown
    await expect(page.locator('[data-testid="steps-preview"]')).toBeVisible();
    await expect(page.locator('[data-testid="step-welcome-email"]')).toBeVisible();
    
    // Submit form
    await page.click('[data-testid="submit-journey-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify journey was created
    await expect(page.locator('text=Welcome Journey')).toBeVisible();
  });

  test('should create journey with event trigger', async ({ page }) => {
    // Click create journey button
    await page.click('[data-testid="create-journey-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-journey-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="journey-name-input"]', `Cart Abandonment ${Date.now()}`);
    await page.fill('[data-testid="journey-description-textarea"]', 'Re-engage customers who abandoned cart');
    
    // Select trigger type - Event
    await page.click('[data-testid="trigger-type-event"]');
    
    // Configure event trigger
    await page.fill('[data-testid="event-name-input"]', 'cart_abandoned');
    
    // Submit form
    await page.click('[data-testid="submit-journey-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify journey was created
    await expect(page.locator('text=Cart Abandonment')).toBeVisible();
  });

  test('should create journey with date trigger', async ({ page }) => {
    // Click create journey button
    await page.click('[data-testid="create-journey-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-journey-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="journey-name-input"]', `Scheduled Campaign ${Date.now()}`);
    await page.fill('[data-testid="journey-description-textarea"]', 'Scheduled marketing campaign');
    
    // Select trigger type - Date
    await page.click('[data-testid="trigger-type-date"]');
    
    // Configure date trigger
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dateString = tomorrow.toISOString().slice(0, 16); // Format for datetime-local
    await page.fill('[data-testid="schedule-date-input"]', dateString);
    
    // Submit form
    await page.click('[data-testid="submit-journey-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify journey was created
    await expect(page.locator('text=Scheduled Campaign')).toBeVisible();
  });

  test('should create manual journey', async ({ page }) => {
    // Click create journey button
    await page.click('[data-testid="create-journey-btn"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-journey-modal"]')).toBeVisible();

    // Fill in basic information
    await page.fill('[data-testid="journey-name-input"]', `Manual Campaign ${Date.now()}`);
    await page.fill('[data-testid="journey-description-textarea"]', 'Manually triggered campaign');
    
    // Select trigger type - Manual
    await page.click('[data-testid="trigger-type-manual"]');
    
    // Submit form
    await page.click('[data-testid="submit-journey-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify journey was created
    await expect(page.locator('text=Manual Campaign')).toBeVisible();
  });

  test('should view journey details', async ({ page }) => {
    // Click on first journey's view button
    await page.click('[data-testid="view-journey-btn"]:first-child');
    
    // Wait for detail modal to open
    await expect(page.locator('[data-testid="journey-detail-modal"]')).toBeVisible();
    
    // Check tabs are present
    await expect(page.locator('[data-testid="overview-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="steps-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="analytics-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="settings-tab"]')).toBeVisible();
    
    // Check journey info is displayed
    await expect(page.locator('[data-testid="journey-name"]')).toBeVisible();
    await expect(page.locator('[data-testid="journey-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="journey-status"]')).toBeVisible();
    
    // Check metrics are displayed
    await expect(page.locator('[data-testid="participants-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="completion-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="steps-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="duration-metric"]')).toBeVisible();
  });

  test('should view journey steps', async ({ page }) => {
    // Click on first journey's view button
    await page.click('[data-testid="view-journey-btn"]:first-child');
    
    // Wait for detail modal to open
    await expect(page.locator('[data-testid="journey-detail-modal"]')).toBeVisible();
    
    // Click on steps tab
    await page.click('[data-testid="steps-tab"]');
    
    // Check steps are displayed
    await expect(page.locator('[data-testid="journey-steps"]')).toBeVisible();
    await expect(page.locator('[data-testid="step-item"]').first()).toBeVisible();
    
    // Check step details
    const firstStep = page.locator('[data-testid="step-item"]').first();
    await expect(firstStep.locator('[data-testid="step-name"]')).toBeVisible();
    await expect(firstStep.locator('[data-testid="step-type"]')).toBeVisible();
    await expect(firstStep.locator('[data-testid="step-participants"]')).toBeVisible();
    await expect(firstStep.locator('[data-testid="step-completion"]')).toBeVisible();
  });

  test('should publish a journey', async ({ page }) => {
    // Find a draft journey and publish it
    const draftJourney = page.locator('[data-testid="journey-card"]').filter({ hasText: 'Nháp' }).first();
    
    if (await draftJourney.count() > 0) {
      // Click start/publish button
      await draftJourney.locator('[data-testid="start-journey-btn"]').click();
      
      // Wait for page reload
      await page.waitForLoadState('networkidle');
      
      // Verify journey status changed to active
      await expect(page.locator('text=Hoạt động')).toBeVisible();
    }
  });

  test('should pause an active journey', async ({ page }) => {
    // Find an active journey and pause it
    const activeJourney = page.locator('[data-testid="journey-card"]').filter({ hasText: 'Hoạt động' }).first();
    
    if (await activeJourney.count() > 0) {
      // Click pause button
      await activeJourney.locator('[data-testid="pause-journey-btn"]').click();
      
      // Wait for page reload
      await page.waitForLoadState('networkidle');
      
      // Verify journey status changed to paused
      await expect(page.locator('text=Tạm dừng')).toBeVisible();
    }
  });

  test('should edit a journey', async ({ page }) => {
    // Click on first journey's edit button
    await page.click('[data-testid="edit-journey-btn"]:first-child');
    
    // Wait for edit modal to open
    await expect(page.locator('[data-testid="edit-journey-modal"]')).toBeVisible();
    
    // Update journey name
    await page.fill('[data-testid="journey-name-input"]', `Updated Journey ${Date.now()}`);
    
    // Update description
    await page.fill('[data-testid="journey-description-textarea"]', 'Updated journey description');
    
    // Submit changes
    await page.click('[data-testid="save-journey-btn"]');
    
    // Wait for success and page reload
    await page.waitForLoadState('networkidle');
    
    // Verify journey was updated
    await expect(page.locator('text=Updated Journey')).toBeVisible();
  });

  test('should duplicate a journey', async ({ page }) => {
    // Get initial journey count
    const initialJourneys = await page.locator('[data-testid="journey-card"]').count();
    
    // Click on first journey's duplicate button
    await page.click('[data-testid="duplicate-journey-btn"]:first-child');
    
    // Wait for page reload
    await page.waitForLoadState('networkidle');
    
    // Verify journey count increased
    const finalJourneys = await page.locator('[data-testid="journey-card"]').count();
    expect(finalJourneys).toBeGreaterThan(initialJourneys);
    
    // Verify duplicate has "Copy" in the name
    await expect(page.locator('text=Copy')).toBeVisible();
  });

  test('should delete a journey', async ({ page }) => {
    // Get initial journey count
    const initialJourneys = await page.locator('[data-testid="journey-card"]').count();
    
    if (initialJourneys === 0) {
      // Create a journey first if none exist
      await page.click('[data-testid="create-journey-btn"]');
      await page.fill('[data-testid="journey-name-input"]', 'Test Journey');
      await page.fill('[data-testid="journey-description-textarea"]', 'Test description');
      await page.click('[data-testid="trigger-type-manual"]');
      await page.click('[data-testid="submit-journey-btn"]');
      await page.waitForLoadState('networkidle');
    }

    // Click on first journey's delete button
    await page.click('[data-testid="delete-journey-btn"]:first-child');
    
    // Confirm deletion in dialog
    await page.click('[data-testid="confirm-delete-btn"]');
    
    // Wait for page reload
    await page.waitForLoadState('networkidle');
    
    // Verify journey count decreased
    const finalJourneys = await page.locator('[data-testid="journey-card"]').count();
    expect(finalJourneys).toBeLessThan(initialJourneys + 1); // +1 because we might have created one
  });

  test('should filter journeys by status', async ({ page }) => {
    // Click on active filter
    await page.click('[data-testid="filter-active"]');
    await page.waitForLoadState('networkidle');
    
    // Verify URL contains filter parameter
    expect(page.url()).toContain('tab=active');
    
    // Check that only active journeys are shown
    const journeyCards = page.locator('[data-testid="journey-card"]');
    const count = await journeyCards.count();
    
    if (count > 0) {
      // Verify all visible journeys have active status
      for (let i = 0; i < count; i++) {
        const journey = journeyCards.nth(i);
        await expect(journey.locator('[data-testid="journey-status-badge"]')).toContainText('Hoạt động');
      }
    }
  });

  test('should search journeys', async ({ page }) => {
    // Use search functionality
    await page.fill('[data-testid="search-journeys-input"]', 'welcome');
    await page.waitForTimeout(1000); // Wait for debounce
    
    // Check that search results are filtered
    const journeyCards = page.locator('[data-testid="journey-card"]');
    const count = await journeyCards.count();
    
    if (count > 0) {
      // Verify search results contain the search term
      const firstJourney = journeyCards.first();
      const journeyText = await firstJourney.textContent();
      expect(journeyText?.toLowerCase()).toContain('welcome');
    }
  });

  test('should analyze journey performance', async ({ page }) => {
    // Click on first journey's analyze button
    await page.click('[data-testid="analyze-journey-btn"]:first-child');
    
    // Wait for analytics modal or page to open
    await expect(page.locator('[data-testid="journey-analytics"]')).toBeVisible();
    
    // Check analytics components are present
    await expect(page.locator('[data-testid="performance-chart"]')).toBeVisible();
    await expect(page.locator('[data-testid="conversion-funnel"]')).toBeVisible();
    await expect(page.locator('[data-testid="step-analytics"]')).toBeVisible();
  });

  test('should handle empty state correctly', async ({ page }) => {
    // Navigate to journeys with a search that returns no results
    await page.goto('/home/<USER>/cdp/journeys?search=nonexistentjourney12345');
    await page.waitForLoadState('networkidle');

    // Check empty state is displayed
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
    await expect(page.locator('text=Không tìm thấy hành trình')).toBeVisible();
    await expect(page.locator('[data-testid="create-journey-btn"]')).toBeVisible();
  });
});
