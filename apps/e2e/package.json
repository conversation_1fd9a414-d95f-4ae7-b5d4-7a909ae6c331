{"name": "web-e2e", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"report": "playwright show-report", "test": "playwright test --max-failures=1", "test:ui": "playwright test --ui", "test:debug": "playwright test --max-failures=1 --timeout 60000 --debug", "test:api:run": "NODE_ENV=test jest --config test-api/jest.config.js", "test:api": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/auth/zalo.spec.ts && NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/products/**/*.spec.ts test-api/__tests__/api/orders/**/*.spec.ts test-api/__tests__/api/categories/**/*.spec.ts test-api/__tests__/api/branches/**/*.spec.ts test-api/__tests__/api/analytics/**/*.spec.ts", "test:api:auth:zalo": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/auth/zalo.spec.ts", "test:api:auth": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/auth/**/*.spec.ts", "test:api:products": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/products/**/*.spec.ts", "test:api:orders": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/orders/**/*.spec.ts", "test:api:categories": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/categories/**/*.spec.ts", "test:api:branches": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/branches/**/*.spec.ts", "test:api:analytics": "NODE_ENV=test jest --config test-api/jest.config.js test-api/__tests__/api/analytics/**/*.spec.ts", "test:sample-data": "playwright test tests/team-accounts/team-sample-data.spec.ts", "test:full-flow": "pnpm test:sample-data && pnpm api:get-team-info && pnpm test:api:run", "test:full": "bash scripts/run-full-test.sh", "test:ipos": "bash scripts/run-ipos-tests.sh", "test:ipos:dashboard": "playwright test tests/integrations/ipos-dashboard.spec.ts --headed --workers=1", "test:ipos:mapping": "playwright test tests/integrations/ipos-mapping.spec.ts --headed --workers=1", "test:ipos:sync": "playwright test tests/integrations/ipos-sync.spec.ts --headed --workers=1", "test:ipos:sync-history": "playwright test tests/integrations/ipos-sync-history.spec.ts --headed --workers=1", "test:ipos:connect": "playwright test tests/integrations/ipos-connect.spec.ts --headed --workers=1", "api:refresh-token": "node test-api/scripts/auth/get-zalo-token.js", "api:get-zalo-token": "node test-api/scripts/auth/get-zalo-token.js", "api:get-account-theme": "node test-api/scripts/auth/get-account-theme.js", "api:get-team-info": "node test-api/scripts/auth/get-team-info.js", "test:cdp": "playwright test tests/cdp/", "test:cdp:headed": "playwright test tests/cdp/ --headed", "test:cdp:profiles": "playwright test tests/cdp/customer-profiles.spec.ts --headed", "test:cdp:segments": "playwright test tests/cdp/customer-segments.spec.ts --headed", "test:cdp:journeys": "playwright test tests/cdp/customer-journeys.spec.ts --headed"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@jest/globals": "^29.7.0", "@kit/supabase": "workspace:*", "@playwright/test": "^1.51.1", "@supabase/supabase-js": "2.49.4", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "axios": "^1.6.0", "dotenv": "^16.5.0", "jest": "^29.7.0", "node-html-parser": "^7.0.1", "totp-generator": "^1.0.0", "ts-jest": "^29.3.1", "typescript": "^5.8.3", "uuid": "^11.1.0"}}