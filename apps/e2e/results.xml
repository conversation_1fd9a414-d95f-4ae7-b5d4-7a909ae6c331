<testsuites id="" name="" tests="2" failures="0" skipped="2" errors="0" time="18.142563">
<testsuite name="resource-access/miniapp-limits.spec.ts" timestamp="2025-04-06T05:10:12.074Z" hostname="chromium" tests="2" failures="0" skipped="2" time="34.638" errors="0">
<testcase name="Mini App Resource Limits › Free Account › should show subscription required dialog for free account" classname="resource-access/miniapp-limits.spec.ts" time="17.319">
<skipped>
</skipped>
</testcase>
<testcase name="Mini App Resource Limits › Free Account › should not allow direct access to mini app setup page for free account" classname="resource-access/miniapp-limits.spec.ts" time="17.319">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>