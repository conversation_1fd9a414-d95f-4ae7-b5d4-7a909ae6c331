// Tệp này được thực thi sau khi Jest được thiết lập và trước khi các test được chạy
import { getAuthToken } from './utils/auth';

// Thiết lập thời gian chờ dài hơn cho các test
jest.setTimeout(30000);

// Đọc các biến môi trường từ file .env.test.local
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Đọc biến môi trường từ các file .env
function loadEnvVars() {
  // Thứ tự ưu tiên: .env.test.local > .env.test > .env.local > .env
  const envFiles = [
    path.join(process.cwd(), '.env.test.local'),
    path.join(process.cwd(), '.env.test'),
    path.join(process.cwd(), '../web/.env.test.local'),
    path.join(process.cwd(), '../web/.env.test'),
    path.join(process.cwd(), '.env.local'),
    path.join(process.cwd(), '.env'),
  ];

  // Đọc và gộp các biến môi trường từ các file
  for (const envFile of envFiles) {
    if (fs.existsSync(envFile)) {
      console.log(`Loading environment variables from ${envFile}`);
      const envConfig = dotenv.parse(fs.readFileSync(envFile));
      for (const key in envConfig) {
        if (!process.env[key]) {
          process.env[key] = envConfig[key];
        }
      }
    }
  }
}

// Thực hiện xác thực trước khi chạy các test
beforeAll(async () => {
  // Đọc các biến môi trường
  loadEnvVars();

  // Lấy token Zalo Mini App từ biến môi trường
  let ZALO_MINI_APP_ACCESS_TOKEN = process.env.ZALO_MINI_APP_ACCESS_TOKEN;

  if (!ZALO_MINI_APP_ACCESS_TOKEN) {
    console.error('\n\n\x1b[31m==========================================================');
    console.error('\x1b[31m  NO ZALO TOKEN FOUND. PLEASE PROVIDE A VALID ZALO TOKEN');
    console.error('\x1b[31m==========================================================\x1b[0m\n');
    process.exit(1);
  }

  // Kiểm tra xem server đang chạy trên cổng nào
  const axios = require('axios');
  try {
    await axios.get('http://localhost:3000');
    process.env.API_BASE_URL = 'http://localhost:3000';
    console.log('Server is running on port 3000');
  } catch (error) {
    try {
      await axios.get('http://localhost:3001');
      process.env.API_BASE_URL = 'http://localhost:3001';
      console.log('Server is running on port 3001');
    } catch (error) {
      console.error('\n\n\x1b[31m==========================================================');
      console.error('\x1b[31m  SERVER IS NOT RUNNING. PLEASE START THE SERVER');
      console.error('\x1b[31m==========================================================\x1b[0m\n');
      process.exit(1);
    }
  }

  // Lấy token xác thực - hàm này sẽ yêu cầu nhập token mới nếu token hết hạn
  try {
    await getAuthToken(ZALO_MINI_APP_ACCESS_TOKEN);
    console.log('Authentication setup completed successfully');
  } catch (error) {
    console.error('Authentication setup failed:', error);
    process.exit(1);
  }
});
