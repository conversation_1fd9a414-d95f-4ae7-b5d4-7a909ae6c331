import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';





/**
 * API Client cho việc test API
 */
export class ApiClient {
  private client: AxiosInstance;
  private baseUrl: string;

  /**
   * Khởi tạo API Client
   * @param baseUrl URL cơ sở của API
   * @param token Token xác thực (nếu có)
   */
  constructor(baseUrl: string = process.env.API_BASE_URL || 'http://localhost:3000', token?: string) {
    this.baseUrl = baseUrl;

    const config: AxiosRequestConfig = {
      baseURL: baseUrl,
      validateStatus: () => true, // Không throw lỗi cho bất kỳ status code nào
    };

    if (token) {
      config.headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };
    }

    this.client = axios.create(config);
  }

  /**
   * Cập nhật token xác thực
   * @param token Token xác thực mới
   */
  setToken(token: string): void {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * <PERSON><PERSON><PERSON> request GET
   * @param url URL của API
   * @param params Tham số query
   */
  async get(url: string, params?: Record<string, any>) {
    return this.client.get(url, { params });
  }

  /**
   * Gửi request POST
   * @param url URL của API
   * @param data Dữ liệu gửi lên
   */
  async post(url: string, data?: any) {
    return this.client.post(url, data);
  }

  /**
   * Gửi request PUT
   * @param url URL của API
   * @param data Dữ liệu gửi lên
   */
  async put(url: string, data?: any) {
    return this.client.put(url, data);
  }

  /**
   * Gửi request DELETE
   * @param url URL của API
   */
  async delete(url: string) {
    return this.client.delete(url);
  }
}

/**
 * Tạo API Client mới
 * @param baseUrl URL cơ sở của API
 * @param token Token xác thực (nếu có)
 */
export function createApiClient(baseUrl?: string, token?: string): ApiClient {
  return new ApiClient(baseUrl, token);
}
