import { createClient } from '@supabase/supabase-js';
import { getAuthToken } from './auth';

// Create a mock admin client for testing purposes
export function getMockAdminClient() {
  // Use environment variables with fallbacks for testing
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';

  // IMPORTANT: We should use the SUPABASE_SERVICE_ROLE_KEY to bypass RLS policies
  // This is required for creating test data in tables with RLS enabled
  // For testing purposes, we'll use a dummy key if the environment variable is not set
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY ||
                      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
                      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.warn('SUPABASE_SERVICE_ROLE_KEY is not set. Using fallback key for testing. RLS policy violations may occur.');
  }

  // Create a client with the service role key to bypass RLS
  const getClient = async () => {
    try {
      // IMPORTANT: When using service role key, we should NOT include the Authorization header
      // with a user token, as this will cause the service role key to be ignored
      console.log('Creating admin client with service role key');

      return createClient(supabaseUrl, supabaseKey, {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
          detectSessionInUrl: false,
        },
        // Do NOT include Authorization header when using service role key
      });
    } catch (error) {
      console.error('Error creating mock admin client:', error);

      // Return a minimal mock client for testing
      return {
        from: () => ({
          select: () => ({
            eq: () => ({
              single: () => ({ data: null, error: null }),
              limit: () => ({ data: [], error: null }),
              in: () => ({ data: [], error: null }),
            }),
            limit: () => ({ data: [], error: null }),
          }),
          insert: () => ({ data: { id: 'mock-id' }, error: null }),
          update: () => ({ data: null, error: null }),
          delete: () => ({ data: null, error: null }),
        }),
        rpc: () => ({ data: { success: true }, error: null }),
      };
    }
  };

  return getClient();
}
