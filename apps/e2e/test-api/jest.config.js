/** @type {import('jest').Config} */
const config = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/test-api/__tests__/api/**/*.spec.ts'],
  moduleNameMapper: {
    '^~/(.*)$': '<rootDir>/../web/$1',
  },
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
    }],
  },
  globalSetup: '<rootDir>/global-setup.ts',
  setupFilesAfterEnv: ['<rootDir>/setup-after-env.ts'],
};

module.exports = config;
