/**
 * Test cho API /api/categories/tree
 */
import { getAuthenticatedClient, expectStandardResponse } from '../../../utils/test-utils';

describe('Categories API - Get Category Tree', () => {
  const api = getAuthenticatedClient();

  it('should return a category tree', async () => {
    const response = await api.get('/api/categories/tree');

    expectStandardResponse(response);

    const categories = response.data.data;
    expect(Array.isArray(categories)).toBe(true);

    console.log(`Retrieved category tree with ${categories.length} root categories`);

    // Kiểm tra xem có danh mục nào không
    // Nếu không có danh mục, có thể là do API sai hoặc dữ liệu mẫu chưa được tạo đúng cách
    if (categories.length === 0) {
      console.warn('Không tìm thấy danh mục nào. Kiểm tra xem dữ liệu mẫu đã được tạo chưa.');
      console.warn('Nếu đã chạy test:sample-data nhưng vẫn không có danh mục, có thể là do API sai hoặc quyền truy cập.');
    } else {
      console.log(`Danh mục đầu tiên: ${JSON.stringify(categories[0])}`);
    }
  });

  it('should return only root categories when root_only=true', async () => {
    const response = await api.get('/api/categories/tree?root_only=true');

    expectStandardResponse(response);

    const categories = response.data.data;
    expect(Array.isArray(categories)).toBe(true);

    // Kiểm tra các danh mục - kiểm tra xem có danh mục hay không
    // Lưu ý: Khi sử dụng API thật, kết quả phụ thuộc vào dữ liệu thực tế trong database
    expect(categories.length).toBeGreaterThanOrEqual(0);

    console.log(`Retrieved ${categories.length} root categories`);
  });
});
