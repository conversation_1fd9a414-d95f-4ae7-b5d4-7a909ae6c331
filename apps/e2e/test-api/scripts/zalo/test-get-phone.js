/**
 * Test script for the Zalo getPhoneNumber API
 * 
 * Usage:
 * node test-get-phone.js <accountId> <code> [themeId]
 */

const axios = require('axios');
require('dotenv').config();

const API_URL = process.env.API_URL || 'http://localhost:3000';

async function testGetPhoneNumber() {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.error('Usage: node test-get-phone.js <accountId> <code> [themeId]');
    process.exit(1);
  }
  
  const accountId = args[0];
  const code = args[1];
  const themeId = args[2] || undefined;
  
  console.log(`Testing getPhoneNumber API with accountId=${accountId}, code=${code}, themeId=${themeId || 'undefined'}`);
  
  try {
    const response = await axios.post(`${API_URL}/api/integrations/zalo/phone`, {
      accountId,
      code,
      themeId
    });
    
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      console.log(`Phone number: ${response.data.data.phoneNumber}`);
    } else {
      console.error(`Error: ${response.data.error}`);
    }
  } catch (error) {
    console.error('Error calling API:', error.response?.data || error.message);
  }
}

testGetPhoneNumber();
