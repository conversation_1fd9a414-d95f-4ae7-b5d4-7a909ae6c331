#!/usr/bin/env node

/**
 * Test script for CDP Customer Profiles API
 * This script tests all CRUD operations for customer profiles
 */

const BASE_URL = 'http://localhost:3003';
const ACCOUNT_SLUG = 'makerkit';

async function testCustomerProfilesAPI() {
  console.log('🧪 Testing CDP Customer Profiles API...\n');

  try {
    // Test 1: GET - Load customer profiles
    console.log('1️⃣ Testing GET /api/cdp/customer-profiles');
    const getResponse = await fetch(`${BASE_URL}/api/cdp/customer-profiles?account=${ACCOUNT_SLUG}&page=1&pageSize=10`);
    const getResult = await getResponse.json();
    
    console.log('Status:', getResponse.status);
    console.log('Response:', JSON.stringify(getResult, null, 2));
    
    if (getResult.success) {
      console.log('✅ GET request successful');
      console.log(`📊 Found ${getResult.data.total} customer profiles`);
    } else {
      console.log('❌ GET request failed:', getResult.error);
    }
    console.log('');

    // Test 2: POST - Create new customer profile
    console.log('2️⃣ Testing POST /api/cdp/customer-profiles');
    const newProfile = {
      accountSlug: ACCOUNT_SLUG,
      first_name: 'Test',
      last_name: 'User',
      email: `test.user.${Date.now()}@example.com`,
      phone: '+***********',
      location: 'Hà Nội',
      value_tier: 'medium',
      tags: ['test', 'api'],
      notes: 'Created via API test'
    };

    const postResponse = await fetch(`${BASE_URL}/api/cdp/customer-profiles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newProfile),
    });
    const postResult = await postResponse.json();
    
    console.log('Status:', postResponse.status);
    console.log('Response:', JSON.stringify(postResult, null, 2));
    
    let createdProfileId = null;
    if (postResult.success) {
      console.log('✅ POST request successful');
      createdProfileId = postResult.data.id;
      console.log(`👤 Created profile with ID: ${createdProfileId}`);
    } else {
      console.log('❌ POST request failed:', postResult.error);
    }
    console.log('');

    // Test 3: PUT - Update customer profile (if we created one)
    if (createdProfileId) {
      console.log('3️⃣ Testing PUT /api/cdp/customer-profiles');
      const updateData = {
        accountSlug: ACCOUNT_SLUG,
        profileId: createdProfileId,
        notes: 'Updated via API test',
        value_tier: 'high'
      };

      const putResponse = await fetch(`${BASE_URL}/api/cdp/customer-profiles`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });
      const putResult = await putResponse.json();
      
      console.log('Status:', putResponse.status);
      console.log('Response:', JSON.stringify(putResult, null, 2));
      
      if (putResult.success) {
        console.log('✅ PUT request successful');
        console.log(`📝 Updated profile: ${putResult.data.first_name} ${putResult.data.last_name}`);
      } else {
        console.log('❌ PUT request failed:', putResult.error);
      }
      console.log('');
    }

    // Test 4: DELETE - Delete customer profile (if we created one)
    if (createdProfileId) {
      console.log('4️⃣ Testing DELETE /api/cdp/customer-profiles');
      const deleteResponse = await fetch(`${BASE_URL}/api/cdp/customer-profiles?account=${ACCOUNT_SLUG}&profileId=${createdProfileId}`, {
        method: 'DELETE',
      });
      const deleteResult = await deleteResponse.json();
      
      console.log('Status:', deleteResponse.status);
      console.log('Response:', JSON.stringify(deleteResult, null, 2));
      
      if (deleteResult.success) {
        console.log('✅ DELETE request successful');
        console.log(`🗑️ Deleted profile with ID: ${createdProfileId}`);
      } else {
        console.log('❌ DELETE request failed:', deleteResult.error);
      }
      console.log('');
    }

    // Test 5: Search functionality
    console.log('5️⃣ Testing search functionality');
    const searchResponse = await fetch(`${BASE_URL}/api/cdp/customer-profiles?account=${ACCOUNT_SLUG}&search=john&page=1&pageSize=5`);
    const searchResult = await searchResponse.json();
    
    console.log('Status:', searchResponse.status);
    console.log('Search Results:', JSON.stringify(searchResult, null, 2));
    
    if (searchResult.success) {
      console.log('✅ Search request successful');
      console.log(`🔍 Found ${searchResult.data.profiles.length} profiles matching "john"`);
    } else {
      console.log('❌ Search request failed:', searchResult.error);
    }
    console.log('');

    // Test 6: Filter by value tier
    console.log('6️⃣ Testing filter by value tier');
    const filterResponse = await fetch(`${BASE_URL}/api/cdp/customer-profiles?account=${ACCOUNT_SLUG}&filter=high&page=1&pageSize=5`);
    const filterResult = await filterResponse.json();
    
    console.log('Status:', filterResponse.status);
    console.log('Filter Results:', JSON.stringify(filterResult, null, 2));
    
    if (filterResult.success) {
      console.log('✅ Filter request successful');
      console.log(`🎯 Found ${filterResult.data.profiles.length} high-value profiles`);
    } else {
      console.log('❌ Filter request failed:', filterResult.error);
    }

    console.log('\n🎉 Customer Profiles API testing completed!');

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testCustomerProfilesAPI();
