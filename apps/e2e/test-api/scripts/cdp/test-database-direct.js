#!/usr/bin/env node

/**
 * Test script for CDP Database Direct Access
 * This script tests database operations directly using Supabase client
 */

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'http://127.0.0.1:54321';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

async function testCDPDatabase() {
  console.log('🧪 Testing CDP Database Direct Access...\n');

  try {
    // Create Supabase client with service role key
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Test 1: Get makerkit account
    console.log('1️⃣ Testing account lookup');
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('*')
      .eq('slug', 'makerkit')
      .single();

    if (accountError) {
      console.log('❌ Account lookup failed:', accountError.message);
      return;
    }

    console.log('✅ Account found:', account.name);
    console.log('📊 Account ID:', account.id);
    console.log('');

    // Test 2: Get customer profiles
    console.log('2️⃣ Testing customer profiles');
    const { data: profiles, error: profilesError, count } = await supabase
      .from('customer_profiles')
      .select('*', { count: 'exact' })
      .eq('account_id', account.id);

    if (profilesError) {
      console.log('❌ Customer profiles query failed:', profilesError.message);
    } else {
      console.log('✅ Customer profiles query successful');
      console.log(`📊 Found ${count} customer profiles`);
      if (profiles && profiles.length > 0) {
        console.log('👤 Sample profile:', {
          name: `${profiles[0].first_name} ${profiles[0].last_name}`,
          email: profiles[0].email,
          value_tier: profiles[0].value_tier,
          total_spent: profiles[0].total_spent
        });
      }
    }
    console.log('');

    // Test 3: Get customer segments
    console.log('3️⃣ Testing customer segments');
    const { data: segments, error: segmentsError, count: segmentsCount } = await supabase
      .from('customer_segments')
      .select('*', { count: 'exact' })
      .eq('account_id', account.id);

    if (segmentsError) {
      console.log('❌ Customer segments query failed:', segmentsError.message);
    } else {
      console.log('✅ Customer segments query successful');
      console.log(`📊 Found ${segmentsCount} customer segments`);
      if (segments && segments.length > 0) {
        console.log('🎯 Sample segment:', {
          name: segments[0].name,
          type: segments[0].type,
          customer_count: segments[0].customer_count,
          is_active: segments[0].is_active
        });
      }
    }
    console.log('');

    // Test 4: Get customer journeys
    console.log('4️⃣ Testing customer journeys');
    const { data: journeys, error: journeysError, count: journeysCount } = await supabase
      .from('customer_journeys')
      .select('*', { count: 'exact' })
      .eq('account_id', account.id);

    if (journeysError) {
      console.log('❌ Customer journeys query failed:', journeysError.message);
    } else {
      console.log('✅ Customer journeys query successful');
      console.log(`📊 Found ${journeysCount} customer journeys`);
      if (journeys && journeys.length > 0) {
        console.log('🛤️ Sample journey:', {
          name: journeys[0].name,
          status: journeys[0].status,
          trigger_type: journeys[0].trigger_type,
          participants: journeys[0].participants
        });
      }
    }
    console.log('');

    // Test 5: Get analytics data
    console.log('5️⃣ Testing analytics data');
    const { data: analytics, error: analyticsError, count: analyticsCount } = await supabase
      .from('analytics_data')
      .select('*', { count: 'exact' })
      .eq('account_id', account.id);

    if (analyticsError) {
      console.log('❌ Analytics data query failed:', analyticsError.message);
    } else {
      console.log('✅ Analytics data query successful');
      console.log(`📊 Found ${analyticsCount} analytics records`);
      if (analytics && analytics.length > 0) {
        console.log('📈 Sample analytics:', {
          metric_name: analytics[0].metric_name,
          metric_value: analytics[0].metric_value,
          metric_type: analytics[0].metric_type,
          period: analytics[0].period
        });
      }
    }
    console.log('');

    // Test 6: Get integration statuses
    console.log('6️⃣ Testing integration statuses');
    const { data: integrations, error: integrationsError, count: integrationsCount } = await supabase
      .from('integration_statuses')
      .select('*', { count: 'exact' })
      .eq('account_id', account.id);

    if (integrationsError) {
      console.log('❌ Integration statuses query failed:', integrationsError.message);
    } else {
      console.log('✅ Integration statuses query successful');
      console.log(`📊 Found ${integrationsCount} integrations`);
      if (integrations && integrations.length > 0) {
        console.log('🔗 Sample integration:', {
          name: integrations[0].name,
          provider: integrations[0].provider,
          status: integrations[0].status,
          health_score: integrations[0].health_score
        });
      }
    }
    console.log('');

    // Test 7: Get AI insights
    console.log('7️⃣ Testing AI insights');
    const { data: insights, error: insightsError, count: insightsCount } = await supabase
      .from('ai_insights')
      .select('*', { count: 'exact' })
      .eq('account_id', account.id);

    if (insightsError) {
      console.log('❌ AI insights query failed:', insightsError.message);
    } else {
      console.log('✅ AI insights query successful');
      console.log(`📊 Found ${insightsCount} AI insights`);
      if (insights && insights.length > 0) {
        console.log('🤖 Sample insight:', {
          title: insights[0].title,
          type: insights[0].type,
          impact: insights[0].impact,
          confidence: insights[0].confidence
        });
      }
    }
    console.log('');

    // Test 8: Create a test customer profile
    console.log('8️⃣ Testing customer profile creation');
    const newProfile = {
      account_id: account.id,
      email: `test.user.${Date.now()}@example.com`,
      first_name: 'Test',
      last_name: 'User',
      phone: '+***********',
      location: 'Hà Nội',
      value_tier: 'medium',
      tags: ['test', 'api'],
      notes: 'Created via direct database test',
      engagement_score: 0.5,
      churn_risk_score: 0.3,
      total_orders: 0,
      total_spent: 0,
      avg_order_value: 0,
      metadata: {}
    };

    const { data: createdProfile, error: createError } = await supabase
      .from('customer_profiles')
      .insert(newProfile)
      .select()
      .single();

    if (createError) {
      console.log('❌ Customer profile creation failed:', createError.message);
    } else {
      console.log('✅ Customer profile creation successful');
      console.log('👤 Created profile:', {
        id: createdProfile.id,
        name: `${createdProfile.first_name} ${createdProfile.last_name}`,
        email: createdProfile.email
      });

      // Test 9: Delete the test profile
      console.log('');
      console.log('9️⃣ Testing customer profile deletion');
      const { error: deleteError } = await supabase
        .from('customer_profiles')
        .delete()
        .eq('id', createdProfile.id);

      if (deleteError) {
        console.log('❌ Customer profile deletion failed:', deleteError.message);
      } else {
        console.log('✅ Customer profile deletion successful');
        console.log(`🗑️ Deleted profile with ID: ${createdProfile.id}`);
      }
    }

    console.log('\n🎉 CDP Database testing completed successfully!');

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testCDPDatabase();
