#!/usr/bin/env node

/**
 * Test script for CDP Customer Journeys API
 * This script tests all CRUD operations for customer journeys
 */

const BASE_URL = 'http://localhost:3003';
const ACCOUNT_SLUG = 'makerkit';

async function testCustomerJourneysAPI() {
  console.log('🧪 Testing CDP Customer Journeys API...\n');

  try {
    // Test 1: GET - Load customer journeys
    console.log('1️⃣ Testing GET /api/cdp/customer-journeys');
    const getResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys?account=${ACCOUNT_SLUG}&page=1&pageSize=10`);
    const getResult = await getResponse.json();
    
    console.log('Status:', getResponse.status);
    console.log('Response:', JSON.stringify(getResult, null, 2));
    
    if (getResult.success) {
      console.log('✅ GET request successful');
      console.log(`📊 Found ${getResult.data.total} customer journeys`);
    } else {
      console.log('❌ GET request failed:', getResult.error);
    }
    console.log('');

    // Test 2: POST - Create new customer journey
    console.log('2️⃣ Testing POST /api/cdp/customer-journeys');
    const newJourney = {
      accountSlug: ACCOUNT_SLUG,
      name: `Test Journey ${Date.now()}`,
      description: 'Test journey created via API',
      trigger_type: 'manual',
      trigger_config: {},
      steps: [
        {
          type: 'email',
          name: 'Welcome Email',
          config: {
            subject: 'Welcome!',
            template: 'welcome'
          },
          delay_hours: 0
        },
        {
          type: 'wait',
          name: 'Wait 24 Hours',
          config: {},
          delay_hours: 24
        },
        {
          type: 'sms',
          name: 'Follow-up SMS',
          config: {
            message: 'How are you finding our service?'
          },
          delay_hours: 0
        }
      ]
    };

    const postResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newJourney),
    });
    const postResult = await postResponse.json();
    
    console.log('Status:', postResponse.status);
    console.log('Response:', JSON.stringify(postResult, null, 2));
    
    let createdJourneyId = null;
    if (postResult.success) {
      console.log('✅ POST request successful');
      createdJourneyId = postResult.data.id;
      console.log(`🛤️ Created journey with ID: ${createdJourneyId}`);
    } else {
      console.log('❌ POST request failed:', postResult.error);
    }
    console.log('');

    // Test 3: PUT - Update customer journey (if we created one)
    if (createdJourneyId) {
      console.log('3️⃣ Testing PUT /api/cdp/customer-journeys');
      const updateData = {
        accountSlug: ACCOUNT_SLUG,
        journeyId: createdJourneyId,
        description: 'Updated test journey via API'
      };

      const putResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });
      const putResult = await putResponse.json();
      
      console.log('Status:', putResponse.status);
      console.log('Response:', JSON.stringify(putResult, null, 2));
      
      if (putResult.success) {
        console.log('✅ PUT request successful');
        console.log(`📝 Updated journey: ${putResult.data.name}`);
      } else {
        console.log('❌ PUT request failed:', putResult.error);
      }
      console.log('');
    }

    // Test 4: PUT - Publish journey (if we created one)
    if (createdJourneyId) {
      console.log('4️⃣ Testing PUT /api/cdp/customer-journeys (publish)');
      const publishData = {
        accountSlug: ACCOUNT_SLUG,
        journeyId: createdJourneyId,
        action: 'publish'
      };

      const publishResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(publishData),
      });
      const publishResult = await publishResponse.json();
      
      console.log('Status:', publishResponse.status);
      console.log('Response:', JSON.stringify(publishResult, null, 2));
      
      if (publishResult.success) {
        console.log('✅ Publish request successful');
        console.log(`🚀 Published journey: ${publishResult.data.name}`);
        console.log(`📊 Status: ${publishResult.data.status}`);
      } else {
        console.log('❌ Publish request failed:', publishResult.error);
      }
      console.log('');
    }

    // Test 5: PUT - Pause journey (if we created one)
    if (createdJourneyId) {
      console.log('5️⃣ Testing PUT /api/cdp/customer-journeys (pause)');
      const pauseData = {
        accountSlug: ACCOUNT_SLUG,
        journeyId: createdJourneyId,
        action: 'pause'
      };

      const pauseResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pauseData),
      });
      const pauseResult = await pauseResponse.json();
      
      console.log('Status:', pauseResponse.status);
      console.log('Response:', JSON.stringify(pauseResult, null, 2));
      
      if (pauseResult.success) {
        console.log('✅ Pause request successful');
        console.log(`⏸️ Paused journey: ${pauseResult.data.name}`);
        console.log(`📊 Status: ${pauseResult.data.status}`);
      } else {
        console.log('❌ Pause request failed:', pauseResult.error);
      }
      console.log('');
    }

    // Test 6: Search functionality
    console.log('6️⃣ Testing search functionality');
    const searchResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys?account=${ACCOUNT_SLUG}&search=welcome&page=1&pageSize=5`);
    const searchResult = await searchResponse.json();
    
    console.log('Status:', searchResponse.status);
    console.log('Search Results:', JSON.stringify(searchResult, null, 2));
    
    if (searchResult.success) {
      console.log('✅ Search request successful');
      console.log(`🔍 Found ${searchResult.data.journeys.length} journeys matching "welcome"`);
    } else {
      console.log('❌ Search request failed:', searchResult.error);
    }
    console.log('');

    // Test 7: Filter by status
    console.log('7️⃣ Testing filter by status');
    const filterResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys?account=${ACCOUNT_SLUG}&filter=active&page=1&pageSize=5`);
    const filterResult = await filterResponse.json();
    
    console.log('Status:', filterResponse.status);
    console.log('Filter Results:', JSON.stringify(filterResult, null, 2));
    
    if (filterResult.success) {
      console.log('✅ Filter request successful');
      console.log(`🎯 Found ${filterResult.data.journeys.length} active journeys`);
    } else {
      console.log('❌ Filter request failed:', filterResult.error);
    }
    console.log('');

    // Test 8: DELETE - Delete customer journey (if we created one)
    if (createdJourneyId) {
      console.log('8️⃣ Testing DELETE /api/cdp/customer-journeys');
      const deleteResponse = await fetch(`${BASE_URL}/api/cdp/customer-journeys?account=${ACCOUNT_SLUG}&journeyId=${createdJourneyId}`, {
        method: 'DELETE',
      });
      const deleteResult = await deleteResponse.json();
      
      console.log('Status:', deleteResponse.status);
      console.log('Response:', JSON.stringify(deleteResult, null, 2));
      
      if (deleteResult.success) {
        console.log('✅ DELETE request successful');
        console.log(`🗑️ Deleted journey with ID: ${createdJourneyId}`);
      } else {
        console.log('❌ DELETE request failed:', deleteResult.error);
      }
      console.log('');
    }

    console.log('\n🎉 Customer Journeys API testing completed!');

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testCustomerJourneysAPI();
