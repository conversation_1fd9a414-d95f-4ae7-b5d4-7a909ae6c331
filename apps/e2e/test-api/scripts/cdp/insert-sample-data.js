#!/usr/bin/env node

/**
 * Insert sample data directly into CDP tables
 */

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'http://127.0.0.1:54321';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

async function insertSampleData() {
  console.log('🧪 Inserting CDP sample data...\n');

  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Get the first account (ShopOne)
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('*')
      .eq('slug', 'makerkit')
      .single();

    if (accountError) {
      console.log('❌ Account lookup failed:', accountError.message);
      return;
    }

    console.log('✅ Account found:', account.name);
    console.log('📊 Account ID:', account.id);
    console.log('');

    // Insert customer profiles
    console.log('1️⃣ Inserting customer profiles...');
    const profiles = [
      {
        account_id: account.id,
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        phone: '+***********',
        location: 'Hà Nội',
        total_orders: 25,
        total_spent: ********,
        avg_order_value: 600000,
        engagement_score: 0.85,
        churn_risk_score: 0.15,
        value_tier: 'high',
        tags: ['vip', 'loyal'],
        notes: 'High-value customer from referral program',
        metadata: {}
      },
      {
        account_id: account.id,
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        phone: '+***********',
        location: 'TP.HCM',
        total_orders: 18,
        total_spent: ********,
        avg_order_value: 666667,
        engagement_score: 0.78,
        churn_risk_score: 0.22,
        value_tier: 'high',
        tags: ['premium', 'early-adopter'],
        notes: 'Premium customer with high engagement',
        metadata: {}
      },
      {
        account_id: account.id,
        email: '<EMAIL>',
        first_name: 'Mike',
        last_name: 'Johnson',
        phone: '+***********',
        location: 'Đà Nẵng',
        total_orders: 32,
        total_spent: ********,
        avg_order_value: 578125,
        engagement_score: 0.92,
        churn_risk_score: 0.08,
        value_tier: 'high',
        tags: ['vip', 'frequent-buyer'],
        notes: 'Most active customer with highest engagement',
        metadata: {}
      }
    ];

    const { data: insertedProfiles, error: profilesError } = await supabase
      .from('customer_profiles')
      .insert(profiles)
      .select();

    if (profilesError) {
      console.log('❌ Failed to insert profiles:', profilesError.message);
    } else {
      console.log(`✅ Inserted ${insertedProfiles.length} customer profiles`);
    }

    // Insert customer segments
    console.log('2️⃣ Inserting customer segments...');
    const segments = [
      {
        account_id: account.id,
        name: 'High Value Customers',
        description: 'Customers with high lifetime value and low churn risk',
        type: 'value_based',
        criteria: { total_spent: { operator: 'gte', value: ******** }, value_tier: 'high' },
        customer_count: 3,
        growth_rate: 15.5,
        is_auto_updating: true,
        is_active: true
      },
      {
        account_id: account.id,
        name: 'Highly Engaged Users',
        description: 'Users with high engagement scores and frequent interactions',
        type: 'behavioral',
        criteria: { engagement_score: { operator: 'gte', value: 0.7 } },
        customer_count: 4,
        growth_rate: 8.2,
        is_auto_updating: true,
        is_active: true
      }
    ];

    const { data: insertedSegments, error: segmentsError } = await supabase
      .from('customer_segments')
      .insert(segments)
      .select();

    if (segmentsError) {
      console.log('❌ Failed to insert segments:', segmentsError.message);
    } else {
      console.log(`✅ Inserted ${insertedSegments.length} customer segments`);
    }

    // Insert customer journeys
    console.log('3️⃣ Inserting customer journeys...');
    const journeys = [
      {
        account_id: account.id,
        name: 'Welcome New Customers',
        description: 'Onboarding journey for new customer registration',
        status: 'active',
        trigger_type: 'segment_entry',
        trigger_config: { segment_id: 'new_customers' },
        steps: [
          { type: 'email', name: 'Welcome Email', config: { subject: 'Welcome to our platform!', template: 'welcome_email' }, delay_hours: 0 },
          { type: 'wait', name: 'Wait 24 Hours', config: {}, delay_hours: 24 },
          { type: 'email', name: 'Getting Started Guide', config: { subject: 'Get started with these tips', template: 'getting_started' }, delay_hours: 0 }
        ],
        participants: 1247,
        completion_rate: 0.78,
        tags: ['onboarding', 'email']
      },
      {
        account_id: account.id,
        name: 'Cart Abandonment Recovery',
        description: 'Re-engage customers who abandoned their cart',
        status: 'active',
        trigger_type: 'event',
        trigger_config: { event_name: 'cart_abandoned' },
        steps: [
          { type: 'email', name: 'Cart Reminder', config: { subject: 'You left something in your cart', template: 'cart_reminder' }, delay_hours: 1 },
          { type: 'wait', name: 'Wait 24 Hours', config: {}, delay_hours: 24 },
          { type: 'email', name: 'Special Discount', config: { subject: '10% off your cart items', template: 'discount_offer' }, delay_hours: 0 }
        ],
        participants: 856,
        completion_rate: 0.45,
        tags: ['recovery', 'email', 'discount']
      }
    ];

    const { data: insertedJourneys, error: journeysError } = await supabase
      .from('customer_journeys')
      .insert(journeys)
      .select();

    if (journeysError) {
      console.log('❌ Failed to insert journeys:', journeysError.message);
    } else {
      console.log(`✅ Inserted ${insertedJourneys.length} customer journeys`);
    }

    // Insert analytics data
    console.log('4️⃣ Inserting analytics data...');
    const analytics = [
      {
        account_id: account.id,
        metric_name: 'total_customers',
        metric_value: 3,
        metric_type: 'count',
        period: 'current_month',
        metadata: { source: 'customer_profiles' }
      },
      {
        account_id: account.id,
        metric_name: 'total_revenue',
        metric_value: ********,
        metric_type: 'currency',
        period: 'current_month',
        metadata: { source: 'customer_profiles', currency: 'VND' }
      }
    ];

    const { data: insertedAnalytics, error: analyticsError } = await supabase
      .from('analytics_data')
      .insert(analytics)
      .select();

    if (analyticsError) {
      console.log('❌ Failed to insert analytics:', analyticsError.message);
    } else {
      console.log(`✅ Inserted ${insertedAnalytics.length} analytics records`);
    }

    // Insert integration statuses
    console.log('5️⃣ Inserting integration statuses...');
    const integrations = [
      {
        account_id: account.id,
        name: 'Email Marketing Platform',
        provider: 'mailchimp',
        category: 'marketing',
        status: 'connected',
        last_sync: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        records_synced: 1250,
        health_score: 0.95,
        config: { api_key: '***', list_id: 'abc123', sync_frequency: 'hourly' }
      }
    ];

    const { data: insertedIntegrations, error: integrationsError } = await supabase
      .from('integration_statuses')
      .insert(integrations)
      .select();

    if (integrationsError) {
      console.log('❌ Failed to insert integrations:', integrationsError.message);
    } else {
      console.log(`✅ Inserted ${insertedIntegrations.length} integration statuses`);
    }

    // Insert AI insights
    console.log('6️⃣ Inserting AI insights...');
    const insights = [
      {
        account_id: account.id,
        type: 'trend',
        title: 'Increasing Customer Engagement',
        description: 'Customer engagement scores have increased by 15% over the past month',
        confidence: 0.85,
        impact: 'high',
        category: 'engagement',
        status: 'active',
        data: { engagement_increase: 0.15, timeframe: '30_days' },
        recommendations: ['Continue current engagement strategies', 'Expand successful campaigns', 'Monitor engagement metrics closely']
      }
    ];

    const { data: insertedInsights, error: insightsError } = await supabase
      .from('ai_insights')
      .insert(insights)
      .select();

    if (insightsError) {
      console.log('❌ Failed to insert insights:', insightsError.message);
    } else {
      console.log(`✅ Inserted ${insertedInsights.length} AI insights`);
    }

    console.log('\n🎉 Sample data insertion completed successfully!');

  } catch (error) {
    console.error('💥 Failed with error:', error.message);
  }
}

// Run the insertion
insertSampleData();
