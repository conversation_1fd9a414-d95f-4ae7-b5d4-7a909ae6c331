#!/usr/bin/env node

/**
 * Check available accounts in database
 */

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'http://127.0.0.1:54321';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

async function checkAccounts() {
  console.log('🔍 Checking available accounts...\n');

  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Get all accounts
    const { data: accounts, error } = await supabase
      .from('accounts')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.log('❌ Failed to get accounts:', error.message);
      return;
    }

    console.log(`📊 Found ${accounts.length} accounts:`);
    accounts.forEach((account, index) => {
      console.log(`${index + 1}. ${account.name} (slug: ${account.slug}, id: ${account.id})`);
    });

    // Try to insert sample data for the first account
    if (accounts.length > 0) {
      const firstAccount = accounts[0];
      console.log(`\n🧪 Testing sample data insertion for account: ${firstAccount.name}`);

      // Insert a test customer profile
      const { data: profile, error: profileError } = await supabase
        .from('customer_profiles')
        .insert({
          account_id: firstAccount.id,
          email: `test.${Date.now()}@example.com`,
          first_name: 'Test',
          last_name: 'Customer',
          phone: '+***********',
          location: 'Hà Nội',
          total_orders: 5,
          total_spent: 2500000,
          avg_order_value: 500000,
          engagement_score: 0.75,
          churn_risk_score: 0.25,
          value_tier: 'medium',
          tags: ['test', 'sample'],
          notes: 'Test customer for CDP',
          metadata: {}
        })
        .select()
        .single();

      if (profileError) {
        console.log('❌ Failed to insert test profile:', profileError.message);
      } else {
        console.log('✅ Successfully inserted test profile:', {
          id: profile.id,
          name: `${profile.first_name} ${profile.last_name}`,
          email: profile.email
        });

        // Insert a test segment
        const { data: segment, error: segmentError } = await supabase
          .from('customer_segments')
          .insert({
            account_id: firstAccount.id,
            name: 'Test Segment',
            description: 'Test segment for CDP',
            type: 'behavioral',
            criteria: { engagement_score: { operator: 'gte', value: 0.7 } },
            customer_count: 1,
            growth_rate: 10.5,
            is_auto_updating: true,
            is_active: true
          })
          .select()
          .single();

        if (segmentError) {
          console.log('❌ Failed to insert test segment:', segmentError.message);
        } else {
          console.log('✅ Successfully inserted test segment:', {
            id: segment.id,
            name: segment.name,
            type: segment.type
          });
        }

        // Insert a test journey
        const { data: journey, error: journeyError } = await supabase
          .from('customer_journeys')
          .insert({
            account_id: firstAccount.id,
            name: 'Test Journey',
            description: 'Test journey for CDP',
            status: 'active',
            trigger_type: 'manual',
            trigger_config: {},
            steps: [
              {
                type: 'email',
                name: 'Welcome Email',
                config: { subject: 'Welcome!', template: 'welcome' },
                delay_hours: 0
              }
            ],
            participants: 1,
            completion_rate: 0.8,
            tags: ['test', 'welcome']
          })
          .select()
          .single();

        if (journeyError) {
          console.log('❌ Failed to insert test journey:', journeyError.message);
        } else {
          console.log('✅ Successfully inserted test journey:', {
            id: journey.id,
            name: journey.name,
            status: journey.status
          });
        }

        console.log('\n🎉 Sample data insertion completed!');
      }
    }

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
  }
}

// Run the check
checkAccounts();
