#!/usr/bin/env node

/**
 * Script để lấy account_themes của team đư<PERSON><PERSON> tạo từ E2E test
 *
 * Sử dụng:
 * pnpm api:get-account-theme
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const axios = require('axios');
const dotenv = require('dotenv');

// Đường dẫn đến file .env.test.local trong thư mục web
const ENV_LOCAL_PATH = path.join(process.cwd(), '../web/.env.test.local');

// Hàm chính
async function main() {
  console.log('\n=== Getting Account Theme ID ===\n');

  // Đọc token từ file .env.test.local
  let NEXT_AUTH_TOKEN;
  if (fs.existsSync(ENV_LOCAL_PATH)) {
    const envConfig = dotenv.parse(fs.readFileSync(ENV_LOCAL_PATH));
    NEXT_AUTH_TOKEN = envConfig.NEXT_AUTH_TOKEN;
  }

  if (!NEXT_AUTH_TOKEN) {
    console.error('\n\x1b[31mNo authentication token found. Please run api:get-zalo-token first.\x1b[0m');
    process.exit(1);
  }

  try {
    // Lấy danh sách team từ API test
    console.log('Fetching teams from API...');
    const teamsResponse = await axios.get('http://localhost:3000/api/test/teams', {
      headers: {
        Authorization: `Bearer ${NEXT_AUTH_TOKEN}`
      }
    });

    if (!teamsResponse.data.success || !teamsResponse.data.data || teamsResponse.data.data.length === 0) {
      console.error('\n\x1b[31mNo teams found. Please run test:sample-data first.\x1b[0m');
      process.exit(1);
    }

    // Lấy team đầu tiên
    const team = teamsResponse.data.data[0];
    console.log(`\n\x1b[36mFound team: ${team.name} (${team.slug})\x1b[0m`);

    // Lấy account_themes của team
    console.log('Fetching account themes...');

    // Trong môi trường test, chúng ta sẽ giả định rằng team ID là account ID
    const accountId = team.id;

    let themeId;

    // Chỉ lấy theme ID từ API nếu có token
    if (NEXT_AUTH_TOKEN) {
      try {
        // Lấy theme ID thực tế từ bảng account_themes
        const themeResponse = await axios.get(`http://localhost:3000/api/test/account-themes?accountId=${accountId}`, {
          headers: {
            Authorization: `Bearer ${NEXT_AUTH_TOKEN}`
          }
        });

        if (themeResponse.data.success && themeResponse.data.data && themeResponse.data.data.length > 0) {
          // Lấy theme đầu tiên (hoặc theme đang active nếu có)
          const activeTheme = themeResponse.data.data.find(theme => theme.is_active);
          const theme = activeTheme || themeResponse.data.data[0];
          themeId = theme.id;
          console.log(`\x1b[32mFound account theme: ${theme.name}\x1b[0m`);
        } else {
          // Nếu không tìm thấy theme, sử dụng team ID làm theme ID
          themeId = accountId;
          console.log(`\x1b[33mNo account theme found, using account ID as theme ID\x1b[0m`);
        }
      } catch (error) {
        // Nếu có lỗi khi lấy account theme, sử dụng team ID
        themeId = accountId;
        console.log(`\x1b[31mError fetching account theme: ${error.message}\x1b[0m`);
        console.log(`\x1b[33mUsing account ID as theme ID\x1b[0m`);
      }
    } else {
      // Nếu không có token, sử dụng team ID làm theme ID
      themeId = accountId;
      console.log(`\x1b[33mNo authentication token, using account ID as theme ID\x1b[0m`);
    }

    console.log(`\x1b[32mUsing theme ID: ${themeId}\x1b[0m`);

    // Đọc nội dung hiện tại của file .env.test.local
    let envContent = '';
    if (fs.existsSync(ENV_LOCAL_PATH)) {
      envContent = fs.readFileSync(ENV_LOCAL_PATH, 'utf8');
    }

    // Kiểm tra xem đã có THEME_ID chưa
    if (envContent.includes('THEME_ID=')) {
      // Cập nhật THEME_ID
      envContent = envContent.replace(/THEME_ID=.*(\r?\n|$)/, `THEME_ID=${themeId}$1`);
    } else {
      // Thêm THEME_ID mới
      envContent += `THEME_ID=${themeId}\n`;
    }

    // Lưu lại file .env.test.local
    fs.writeFileSync(ENV_LOCAL_PATH, envContent);
    console.log('\n\x1b[32mSaved theme ID to .env.test.local\x1b[0m');

    return themeId;
  } catch (error) {
    console.error('\n\x1b[31mError fetching account theme:', error.message, '\x1b[0m');
    process.exit(1);
  }
}

// Chạy hàm chính
main().catch(error => {
  console.error('\x1b[31mUnexpected error:', error, '\x1b[0m');
  process.exit(1);
});
