#!/usr/bin/env node

/**
 * Script để lấy token Zalo Mini App và xác thực với API
 *
 * Sử dụng:
 * pnpm api:get-zalo-token
 *
 * Hoặc với token mới:
 * ZALO_MINI_APP_ACCESS_TOKEN=your_new_token pnpm api:get-zalo-token
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');
const dotenv = require('dotenv');

// Đường dẫn đến file .env.test trong thư mục web
const ENV_TEST_PATH = path.join(process.cwd(), '../web/.env.test');
// Đường dẫn đến file .env.test.local trong thư mục web
const ENV_LOCAL_PATH = path.join(process.cwd(), '../web/.env.test.local');

// Hàm chính
async function main() {
  console.log('\n=== Zalo Mini App Token Authentication ===\n');

  // Lấy token Zalo Mini App từ biến môi trường hoặc file .env.test
  let ZALO_MINI_APP_ACCESS_TOKEN = process.env.ZALO_MINI_APP_ACCESS_TOKEN;
  let THEME_ID = process.env.THEME_ID;

  // Kiểm tra xem đã có theme ID trong file .env.test.local chưa
  if (fs.existsSync(ENV_LOCAL_PATH)) {
    const envConfig = dotenv.parse(fs.readFileSync(ENV_LOCAL_PATH));
    THEME_ID = envConfig.THEME_ID || THEME_ID;
  }

  // Hỏi người dùng về theme ID trước khi nhập token
  if (THEME_ID) {
    console.log(`\n\x1b[32mĐã tìm thấy theme ID mặc định: ${THEME_ID}\x1b[0m`);
    console.log(`\x1b[36mBạn có muốn sử dụng theme ID này không? (Nhấn Enter để đồng ý hoặc nhập theme ID mới)\x1b[0m`);

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      rl.question('Theme ID: ', answer => {
        rl.close();
        return resolve(answer);
      });
    });

    // Nếu người dùng nhập theme ID mới, sử dụng nó
    if (answer.trim()) {
      THEME_ID = answer.trim();
      console.log(`\x1b[32mSử dụng theme ID mới: ${THEME_ID}\x1b[0m`);
    } else {
      console.log(`\x1b[32mSử dụng theme ID mặc định: ${THEME_ID}\x1b[0m`);
    }
  } else {
    console.log(`\n\x1b[33mKhông tìm thấy theme ID mặc định\x1b[0m`);
    console.log(`\x1b[36mBạn có muốn nhập theme ID không? (Nhấn Enter để bỏ qua hoặc nhập theme ID)\x1b[0m`);

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      rl.question('Theme ID: ', answer => {
        rl.close();
        return resolve(answer);
      });
    });

    // Nếu người dùng nhập theme ID, sử dụng nó
    if (answer.trim()) {
      THEME_ID = answer.trim();
      console.log(`\x1b[32mSử dụng theme ID: ${THEME_ID}\x1b[0m`);
    }
  }

  // Nếu không có trong biến môi trường, đọc từ file .env.test
  if (!ZALO_MINI_APP_ACCESS_TOKEN) {
    if (fs.existsSync(ENV_TEST_PATH)) {
      const envContent = fs.readFileSync(ENV_TEST_PATH, 'utf8');
      const match = envContent.match(/ZALO_MINI_APP_ACCESS_TOKEN=(.+)/);
      if (match && match[1]) {
        ZALO_MINI_APP_ACCESS_TOKEN = match[1];
      }
    }
  }

  // Nếu vẫn không có token, yêu cầu người dùng nhập
  if (!ZALO_MINI_APP_ACCESS_TOKEN) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    ZALO_MINI_APP_ACCESS_TOKEN = await new Promise(resolve => {
      rl.question('Nhập token Zalo Mini App: ', answer => {
        rl.close();
        return resolve(answer);
      });
    });
  }

  if (!ZALO_MINI_APP_ACCESS_TOKEN) {
    console.error('\n\x1b[31m==========================================================');
    console.error('\x1b[31m  NO ZALO TOKEN FOUND. PLEASE PROVIDE A VALID ZALO TOKEN');
    console.error('\x1b[31m==========================================================\x1b[0m\n');
    process.exit(1);
  }

  console.log(`\nUsing Zalo token: ${ZALO_MINI_APP_ACCESS_TOKEN.substring(0, 10)}...${ZALO_MINI_APP_ACCESS_TOKEN.substring(ZALO_MINI_APP_ACCESS_TOKEN.length - 10)}`);

  // Kiểm tra xem có theme ID không
  if (!THEME_ID) {
    // Kiểm tra xem có theme ID trong file .env.test.local không
    if (fs.existsSync(ENV_LOCAL_PATH)) {
      const envConfig = dotenv.parse(fs.readFileSync(ENV_LOCAL_PATH));
      THEME_ID = envConfig.THEME_ID;
    }

    // Nếu vẫn không có theme ID, thử lấy từ API test
    if (!THEME_ID) {
      try {
        // Lấy danh sách team từ API test
        const axios = require('axios');
        const response = await axios.get('http://localhost:3000/api/test/teams');

        if (response.status === 200 && response.data.success && Array.isArray(response.data.data) && response.data.data.length > 0) {
          // Lấy team đầu tiên
          const team = response.data.data[0];

          // Kiểm tra xem đã có theme ID trong file .env.test.local chưa
          let defaultThemeId = '';
          if (fs.existsSync(ENV_LOCAL_PATH)) {
            const envConfig = dotenv.parse(fs.readFileSync(ENV_LOCAL_PATH));
            defaultThemeId = envConfig.THEME_ID || '';
          }

          const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
          });

          console.log(`\n\x1b[36mĐã tìm thấy team: ${team.name} (${team.slug})\x1b[0m`);

          if (defaultThemeId) {
            console.log(`\x1b[32mĐã tìm thấy theme ID từ database: ${defaultThemeId}\x1b[0m`);
            console.log(`\x1b[36mBạn có muốn sử dụng theme ID này không? (Nhấn Enter để đồng ý hoặc nhập theme ID mới)\x1b[0m`);
          } else {
            console.log(`\x1b[33mKhông tìm thấy theme ID trong database\x1b[0m`);
            console.log(`\x1b[36mBạn có muốn nhập theme ID không? (Nhấn Enter để bỏ qua hoặc nhập theme ID)\x1b[0m`);
          }

          const answer = await new Promise(resolve => {
            rl.question('Theme ID: ', answer => {
              rl.close();
              return resolve(answer);
            });
          });

          // Nếu người dùng nhập theme ID, sử dụng nó
          // Nếu không và có theme ID mặc định, sử dụng theme ID mặc định
          // Nếu không có gì, để trống
          THEME_ID = answer.trim() || defaultThemeId;

          console.log(`\x1b[32mSử dụng theme ID: ${THEME_ID}\x1b[0m`);
        }
      } catch (error) {
        console.log('\x1b[33mKhông thể lấy thông tin team, bỏ qua theme ID\x1b[0m');
      }
    }
  }

  if (THEME_ID) {
    console.log(`\nUsing theme ID: ${THEME_ID}`);
  }

  // Tạo file script tạm thời để xác thực
  const tempScriptPath = path.join(__dirname, 'temp-auth-script.js');
  fs.writeFileSync(tempScriptPath, `
    const axios = require('axios');
    const fs = require('fs');
    const path = require('path');

    const ENV_LOCAL_PATH = '${ENV_LOCAL_PATH.replace(/\\/g, '\\\\')}';

    async function authenticate() {
      console.log('\\nAuthenticating with Zalo Mini App...\\n');

      try {
        const requestBody = {
          access_token: '${ZALO_MINI_APP_ACCESS_TOKEN}'
        };

        ${THEME_ID ? `requestBody.theme_id = '${THEME_ID}';` : ''}

        const response = await axios.post('http://localhost:3000/api/auth/zalo', requestBody);

        if (response.status === 200 && response.data.access_token) {
          const token = response.data.access_token;
          const userId = response.data.user.id;

          // Lưu token vào file .env.test.local
          let envContent = \`NEXT_AUTH_TOKEN=\${token}\\nUSER_ID=\${userId}\\n\`;
          ${THEME_ID ? `envContent += \`THEME_ID=${THEME_ID}\\n\`;` : ''}
          fs.writeFileSync(ENV_LOCAL_PATH, envContent);

          console.log('\\x1b[32mSuccess! Token refreshed and saved to .env.test.local\\x1b[0m');
          console.log(\`\\x1b[32mToken: \${token.substring(0, 10)}...\${token.substring(token.length - 10)}\\x1b[0m\\n\`);
        } else {
          console.error('\\x1b[31mFailed to authenticate with Zalo Mini App\\x1b[0m');
          console.error(\`\\x1b[31mResponse: \${JSON.stringify(response.data)}\\x1b[0m\\n\`);
          process.exit(1);
        }
      } catch (error) {
        console.error('\\x1b[31mError authenticating with Zalo Mini App\\x1b[0m');

        if (error.response) {
          console.error(\`\\x1b[31mResponse: \${JSON.stringify(error.response.data)}\\x1b[0m\\n\`);

          // Kiểm tra lỗi token hết hạn
          if (error.response.data?.error === 452 ||
              error.response.data?.message?.includes('session expired') ||
              error.response.data?.message?.includes('Invalid Zalo token')) {
            console.error('\\x1b[31m==========================================================');
            console.error('\\x1b[31m  ZALO SESSION EXPIRED. PLEASE UPDATE YOUR ZALO TOKEN');
            console.error('\\x1b[31m==========================================================\\x1b[0m\\n');
          }
        }

        process.exit(1);
      }
    }

    authenticate();
  `);

  // Chạy script xác thực
  try {
    execSync(`node ${tempScriptPath}`, { stdio: 'inherit' });
  } catch (error) {
    // Lỗi đã được xử lý trong script
    // Nếu token hết hạn, yêu cầu người dùng nhập token mới
    console.log('\n\x1b[33mToken không hợp lệ hoặc đã hết hạn. Vui lòng nhập token mới.\x1b[0m');

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const newToken = await new Promise(resolve => {
      rl.question('\nNhập token Zalo Mini App mới: ', answer => {
        rl.close();
        return resolve(answer);
      });
    });

    if (newToken && newToken.trim()) {
      // Cập nhật token trong file .env.test
      if (fs.existsSync(ENV_TEST_PATH)) {
        let envContent = fs.readFileSync(ENV_TEST_PATH, 'utf8');
        envContent = envContent.replace(/ZALO_MINI_APP_ACCESS_TOKEN=.+/, `ZALO_MINI_APP_ACCESS_TOKEN=${newToken.trim()}`);
        fs.writeFileSync(ENV_TEST_PATH, envContent);
        console.log('\n\x1b[32mĐã cập nhật token trong file .env.test\x1b[0m');

        // Chạy lại script với token mới
        console.log('\n\x1b[33mĐang thử lại với token mới...\x1b[0m');
        process.env.ZALO_MINI_APP_ACCESS_TOKEN = newToken.trim();
        await main();
        return;
      }
    } else {
      console.error('\n\x1b[31mKhông nhận được token mới. Kết thúc.\x1b[0m');
      process.exit(1);
    }
  } finally {
    // Xóa file tạm thời
    if (fs.existsSync(tempScriptPath)) {
      fs.unlinkSync(tempScriptPath);
    }
  }
}

// Chạy hàm chính
main().catch(error => {
  console.error('\x1b[31mUnexpected error:', error, '\x1b[0m');
  process.exit(1);
});
