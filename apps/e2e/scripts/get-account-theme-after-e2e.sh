#!/bin/bash

# Script để lấy account theme ID từ database sau khi chạy E2E test
# Sử dụng: ./scripts/get-account-theme-after-e2e.sh

# Đường dẫn đến thư mục gốc của dự án
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
WEB_DIR="$PROJECT_ROOT/apps/web"
E2E_DIR="$PROJECT_ROOT/apps/e2e"

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Hàm in log
function log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

# Hàm in thông báo thành công
function success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Hàm in thông báo lỗi
function error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Hàm in thông báo cảnh báo
function warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Lấy team ID và theme ID từ team slug
function get_team_info_from_slug() {
  log "Lấy thông tin team từ slug..."

  # Kiểm tra xem đã có file .env.test.local chưa
  if [ ! -f "$WEB_DIR/.env.test.local" ]; then
    error "Không tìm thấy file .env.test.local. Vui lòng chạy E2E test trước."
    return 1
  fi

  # Kiểm tra xem đã có TEAM_SLUG trong file .env.test.local chưa
  if ! grep -q "TEAM_SLUG" "$WEB_DIR/.env.test.local"; then
    error "Không tìm thấy TEAM_SLUG trong file .env.test.local. Vui lòng chạy E2E test trước."
    return 1
  fi

  # Lấy TEAM_SLUG từ file .env.test.local
  TEAM_SLUG=$(grep "TEAM_SLUG" "$WEB_DIR/.env.test.local" | cut -d'=' -f2)
  log "Team slug: $TEAM_SLUG"

  # Kiểm tra xem đã có token chưa
  if ! grep -q "NEXT_AUTH_TOKEN" "$WEB_DIR/.env.test.local"; then
    # Lấy token
    cd "$PROJECT_ROOT" && pnpm --filter web-e2e api:get-zalo-token > /tmp/get-zalo-token.log

    # Kiểm tra lại xem đã có token chưa
    if ! grep -q "NEXT_AUTH_TOKEN" "$WEB_DIR/.env.test.local"; then
      error "Không thể lấy token. Vui lòng chạy api:get-zalo-token trước."
      return 1
    fi
  fi

  # Lấy token từ file .env.test.local
  NEXT_AUTH_TOKEN=$(grep "NEXT_AUTH_TOKEN" "$WEB_DIR/.env.test.local" | cut -d'=' -f2)

  # Lấy thông tin team từ slug
  log "Lấy thông tin team từ API..."
  TEAM_RESPONSE=$(curl -s -H "Authorization: Bearer $NEXT_AUTH_TOKEN" "http://localhost:3000/api/test/team-by-slug?slug=$TEAM_SLUG")

  # Kiểm tra xem có team nào không
  if ! echo "$TEAM_RESPONSE" | grep -q '"success":true'; then
    error "Không thể lấy thông tin team từ API."
    return 1
  fi

  # Lấy team ID
  TEAM_ID=$(echo "$TEAM_RESPONSE" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

  if [ -z "$TEAM_ID" ]; then
    error "Không tìm thấy team ID."
    return 1
  fi

  success "Đã tìm thấy team ID: $TEAM_ID"

  # Lưu TEAM_ID vào file .env.test.local
  local ENV_CONTENT=$(cat "$WEB_DIR/.env.test.local")

  # Kiểm tra xem đã có TEAM_ID chưa
  if echo "$ENV_CONTENT" | grep -q "TEAM_ID="; then
    # Cập nhật TEAM_ID
    ENV_CONTENT=$(echo "$ENV_CONTENT" | sed "s/TEAM_ID=.*/TEAM_ID=$TEAM_ID/")
  else
    # Thêm TEAM_ID mới
    ENV_CONTENT="$ENV_CONTENT\nTEAM_ID=$TEAM_ID"
  fi

  # Lấy theme ID nếu có
  if echo "$TEAM_RESPONSE" | grep -q '"themes":\[{'; then
    # Lấy theme ID đầu tiên
    THEME_ID=$(echo "$TEAM_RESPONSE" | grep -o '"themes":\[{[^}]*"id":"[^"]*"' | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

    if [ -n "$THEME_ID" ]; then
      success "Đã tìm thấy theme ID: $THEME_ID"

      # Kiểm tra xem đã có THEME_ID chưa
      if echo "$ENV_CONTENT" | grep -q "THEME_ID="; then
        # Cập nhật THEME_ID
        ENV_CONTENT=$(echo "$ENV_CONTENT" | sed "s/THEME_ID=.*/THEME_ID=$THEME_ID/")
      else
        # Thêm THEME_ID mới
        ENV_CONTENT="$ENV_CONTENT\nTHEME_ID=$THEME_ID"
      fi
    else
      warning "Không tìm thấy theme ID, sử dụng team ID làm theme ID"
      THEME_ID=$TEAM_ID

      # Kiểm tra xem đã có THEME_ID chưa
      if echo "$ENV_CONTENT" | grep -q "THEME_ID="; then
        # Cập nhật THEME_ID
        ENV_CONTENT=$(echo "$ENV_CONTENT" | sed "s/THEME_ID=.*/THEME_ID=$THEME_ID/")
      else
        # Thêm THEME_ID mới
        ENV_CONTENT="$ENV_CONTENT\nTHEME_ID=$THEME_ID"
      fi
    fi
  else
    warning "Không tìm thấy themes, sử dụng team ID làm theme ID"
    THEME_ID=$TEAM_ID

    # Kiểm tra xem đã có THEME_ID chưa
    if echo "$ENV_CONTENT" | grep -q "THEME_ID="; then
      # Cập nhật THEME_ID
      ENV_CONTENT=$(echo "$ENV_CONTENT" | sed "s/THEME_ID=.*/THEME_ID=$THEME_ID/")
    else
      # Thêm THEME_ID mới
      ENV_CONTENT="$ENV_CONTENT\nTHEME_ID=$THEME_ID"
    fi
  fi

  # Lưu lại file .env.test.local
  echo "$ENV_CONTENT" > "$WEB_DIR/.env.test.local"
  success "Đã lưu team ID và theme ID vào file .env.test.local"

  return 0
}

# Lấy account theme ID từ database
function get_account_theme_id() {
  local team_id=$1

  if [ -z "$team_id" ]; then
    error "Không có team ID để lấy account theme."
    return 1
  fi

  log "Lấy account theme ID cho team $team_id..."

  # Lấy token từ file .env.test.local
  NEXT_AUTH_TOKEN=$(grep "NEXT_AUTH_TOKEN" "$WEB_DIR/.env.test.local" | cut -d'=' -f2)

  # Lấy account theme từ API
  THEME_RESPONSE=$(curl -s -H "Authorization: Bearer $NEXT_AUTH_TOKEN" "http://localhost:3000/api/test/account-themes?accountId=$team_id")

  # Kiểm tra xem có account theme nào không
  if ! echo "$THEME_RESPONSE" | grep -q '"success":true'; then
    error "Không thể lấy account theme từ API."
    return 1
  fi

  # Kiểm tra xem có data không
  if echo "$THEME_RESPONSE" | grep -q '"data":\[\]'; then
    warning "Không tìm thấy account theme cho team $team_id."
    return 1
  fi

  # Lấy theme ID đầu tiên
  THEME_ID=$(echo "$THEME_RESPONSE" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

  if [ -z "$THEME_ID" ]; then
    error "Không tìm thấy account theme ID."
    return 1
  fi

  # Lấy theme name
  THEME_NAME=$(echo "$THEME_RESPONSE" | grep -o '"name":"[^"]*"' | head -1 | cut -d'"' -f4)

  success "Đã tìm thấy account theme: $THEME_NAME"
  success "Account theme ID: $THEME_ID"

  # Lưu theme ID vào file .env.test.local
  if [ -f "$WEB_DIR/.env.test.local" ]; then
    # Kiểm tra xem đã có THEME_ID chưa
    if grep -q "THEME_ID=" "$WEB_DIR/.env.test.local"; then
      # Cập nhật THEME_ID
      sed -i.bak "s/THEME_ID=.*/THEME_ID=$THEME_ID/" "$WEB_DIR/.env.test.local"
      rm -f "$WEB_DIR/.env.test.local.bak"
    else
      # Thêm THEME_ID mới
      echo "THEME_ID=$THEME_ID" >> "$WEB_DIR/.env.test.local"
    fi
    success "Đã lưu theme ID vào file .env.test.local"
  else
    error "Không tìm thấy file .env.test.local"
    return 1
  fi

  echo "$THEME_ID"
  return 0
}

# Hàm chính
function main() {
  log "=== Lấy Account Theme ID sau khi chạy E2E Test ==="

  # Kiểm tra xem server có đang chạy không
  if ! curl -s "http://localhost:3000" > /dev/null; then
    error "Server không chạy. Vui lòng khởi động server trước."
    exit 1
  fi

  # Lấy thông tin team từ slug
  if ! get_team_info_from_slug; then
    # Nếu không lấy được thông tin team từ slug, thử cách cũ
    warning "Không thể lấy thông tin team từ slug, thử cách khác..."

    # Lấy team ID
    TEAM_ID=$(get_account_theme_id)
    if [ $? -ne 0 ]; then
      exit 1
    fi
  fi

  success "=== Hoàn thành ==="

  # Hiển thị thông tin từ file .env.test.local
  if [ -f "$WEB_DIR/.env.test.local" ]; then
    TEAM_SLUG=$(grep "TEAM_SLUG" "$WEB_DIR/.env.test.local" | cut -d'=' -f2 || echo "N/A")
    TEAM_ID=$(grep "TEAM_ID" "$WEB_DIR/.env.test.local" | cut -d'=' -f2 || echo "N/A")
    THEME_ID=$(grep "THEME_ID" "$WEB_DIR/.env.test.local" | cut -d'=' -f2 || echo "N/A")

    success "Team Slug: $TEAM_SLUG"
    success "Team ID: $TEAM_ID"
    success "Theme ID: $THEME_ID"
  fi

  return 0
}

# Chạy hàm chính
main
