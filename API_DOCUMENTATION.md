# Tài liệu API cho Zalo Mini App Mobile

## Tổng quan

Tài liệu này mô tả các API cần thiết để tích hợp với hệ thống backend của Mini App. Các API này cho phép ứng dụng mobile thực hiện các chức năng như xác thực, qu<PERSON><PERSON> lý <PERSON>ả<PERSON>h<PERSON>, da<PERSON> m<PERSON>, chi <PERSON>, đơn hàng và thanh toán.

Hệ thống hoạt động như một third-party server, kết nối giữa Zalo Mini App và dữ liệu của doanh nghiệp. Thông tin của Zalo Mini App được lưu trữ trong bảng `oa_configurations`, liên kết với `account_theme` tương ứng. Khi người dùng gửi token, hệ thống sẽ xác định họ đang sử dụng `account_theme` nào và từ đó lấy được thông tin Zalo App tương ứng từ bảng `oa_configurations`.

## Base URL

```
https://your-domain.com/api
```

Thay thế `your-domain.com` bằng domain thực tế của ứng dụng.

## Xác thực

Tất cả các API đều yêu cầu xác thực bằng token JWT. Token này cần được gửi trong header của mỗi request:

```
Authorization: Bearer {token}
```

### Làm mới token từ Zalo

**Endpoint:** `POST /auth/zalo`

**Mô tả:** Xác thực và làm mới token từ Zalo Mini App

**Request Body:**
```json
{
  "zalo_token": "string",
  "theme_id": "uuid"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOi...",
  "user": {
    "id": "uuid",
    "email": "string",
    "full_name": "string",
    "phone": "string"
  }
}
```

**Mã lỗi:**
- 400: Thiếu thông tin xác thực
- 401: Token Zalo không hợp lệ
- 500: Lỗi server

### Làm mới JWT token

**Endpoint:** `POST /auth/refresh`

**Mô tả:** Làm mới JWT token khi token hiện tại sắp hết hạn

**Request Headers:**
```
Authorization: Bearer {current_token}
```

**Request Body:**
```json
{
  "refresh_token": "string" // Tùy chọn, nếu hệ thống sử dụng refresh token
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOi...",
  "user": {
    "id": "uuid",
    "email": "string",
    "full_name": "string",
    "phone": "string"
  }
}
```

**Mã lỗi:**
- 401: Token không hợp lệ hoặc đã hết hạn
- 403: Refresh token không hợp lệ
- 500: Lỗi server

## Sản phẩm

### Danh sách sản phẩm

**Endpoint:** `GET /products`

**Mô tả:** Lấy danh sách sản phẩm với phân trang và tìm kiếm

**Query Parameters:**
- `query`: Từ khóa tìm kiếm (tùy chọn)
- `category_id`: Lọc theo danh mục (tùy chọn)
- `page`: Số trang (mặc định: 1)
- `limit`: Số sản phẩm mỗi trang (mặc định: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "price": number,
      "compare_at_price": number,
      "image_url": "string",
      "image_urls": ["string"],
      "category": {
        "id": "uuid",
        "name": "string"
      },
      "inventory_by_branch": {
        "branch_id": {
          "stock": number,
          "reserved_stock": number
        }
      },
      "attributes": [
        {
          "id": "uuid",
          "name": "string",
          "value": "string",
          "price_modifier": number
        }
      ]
    }
  ],
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "total_pages": number
  }
}
```

### Chi tiết sản phẩm

**Endpoint:** `GET /products/{product_id}`

**Mô tả:** Lấy thông tin chi tiết của một sản phẩm

**Path Parameters:**
- `product_id`: ID của sản phẩm

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "string",
    "description": "string",
    "price": number,
    "compare_at_price": number,
    "image_url": "string",
    "image_urls": ["string"],
    "category": {
      "id": "uuid",
      "name": "string"
    },
    "inventory_by_branch": {
      "branch_id": {
        "stock": number,
        "reserved_stock": number
      }
    },
    "attributes": [
      {
        "id": "uuid",
        "name": "string",
        "value": "string",
        "price_modifier": number
      }
    ],
    "created_at": "string",
    "updated_at": "string"
  }
}
```

**Mã lỗi:**
- 404: Không tìm thấy sản phẩm

## Danh mục

### Cây danh mục

**Endpoint:** `GET /categories/tree`

**Mô tả:** Lấy cấu trúc cây của tất cả danh mục

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "description": "string",
      "image_url": "string",
      "parent_id": null,
      "children": [
        {
          "id": "uuid",
          "name": "string",
          "description": "string",
          "image_url": "string",
          "parent_id": "uuid",
          "children": []
        }
      ],
      "created_at": "string",
      "updated_at": "string"
    }
  ]
}
```

### Danh sách danh mục

**Endpoint:** `GET /categories`

**Mô tả:** Lấy danh sách danh mục phẳng với phân trang

**Query Parameters:**
- `query`: Từ khóa tìm kiếm (tùy chọn)
- `page`: Số trang (mặc định: 1)
- `limit`: Số danh mục mỗi trang (mặc định: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "description": "string",
      "image_url": "string",
      "parent_id": "uuid",
      "created_at": "string",
      "updated_at": "string"
    }
  ],
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "total_pages": number
  }
}
```

## Chi nhánh

### Danh sách chi nhánh

**Endpoint:** `GET /branches`

**Mô tả:** Lấy danh sách chi nhánh với phân trang và tìm kiếm

**Query Parameters:**
- `query`: Từ khóa tìm kiếm (tùy chọn)
- `page`: Số trang (mặc định: 1)
- `limit`: Số chi nhánh mỗi trang (mặc định: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "string",
      "address": "string",
      "phone": "string",
      "is_active": boolean,
      "account_id": "uuid",
      "created_at": "string",
      "updated_at": "string"
    }
  ],
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "total_pages": number
  }
}
```

### Chi tiết chi nhánh

**Endpoint:** `GET /branches/{branch_id}`

**Mô tả:** Lấy thông tin chi tiết của một chi nhánh

**Path Parameters:**
- `branch_id`: ID của chi nhánh

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "string",
    "address": "string",
    "phone": "string",
    "is_active": boolean,
    "account_id": "uuid",
    "created_at": "string",
    "updated_at": "string"
  }
}
```

**Mã lỗi:**
- 404: Không tìm thấy chi nhánh

## Đơn hàng

### Danh sách đơn hàng

**Endpoint:** `GET /orders`

**Mô tả:** Lấy danh sách đơn hàng của người dùng hiện tại

**Query Parameters:**
- `query`: Từ khóa tìm kiếm (tùy chọn)
- `status`: Lọc theo trạng thái đơn hàng (tùy chọn)
- `startDate`: Lọc từ ngày (tùy chọn, định dạng ISO)
- `endDate`: Lọc đến ngày (tùy chọn, định dạng ISO)
- `page`: Số trang (mặc định: 1)
- `limit`: Số đơn hàng mỗi trang (mặc định: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "branch_id": "uuid",
      "items": [
        {
          "product_id": "uuid",
          "product_name": "string",
          "quantity": number,
          "price": number,
          "attributes": [
            {
              "name": "string",
              "value": "string"
            }
          ]
        }
      ],
      "total_amount": number,
      "status": "string",
      "payment_method": "string",
      "created_at": "string"
    }
  ],
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "total_pages": number
  }
}
```

### Chi tiết đơn hàng

**Endpoint:** `GET /orders/{order_id}`

**Mô tả:** Lấy thông tin chi tiết của một đơn hàng

**Path Parameters:**
- `order_id`: ID của đơn hàng

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "customer": {
      "id": "uuid",
      "email": "string",
      "full_name": "string",
      "phone": "string"
    },
    "branch": {
      "id": "uuid",
      "name": "string",
      "address": "string",
      "phone": "string"
    },
    "items": [
      {
        "id": "uuid",
        "product": {
          "id": "uuid",
          "name": "string",
          "image_url": "string"
        },
        "quantity": number,
        "price": number,
        "attribute": {
          "id": "uuid",
          "name": "string",
          "value": "string"
        }
      }
    ],
    "total_amount": number,
    "status": "string",
    "payment_method": "string",
    "webhook": {
      "processed": boolean,
      "data": object
    },
    "created_at": "string",
    "updated_at": "string",
    "created_by": "uuid",
    "updated_by": "uuid"
  }
}
```

**Mã lỗi:**
- 404: Không tìm thấy đơn hàng hoặc không có quyền truy cập

### Tạo đơn hàng

**Endpoint:** `POST /orders`

**Mô tả:** Tạo một đơn hàng mới

**Request Body:**
```json
{
  "branch_id": "uuid",
  "items": [
    {
      "product_id": "uuid",
      "attribute_id": "uuid",
      "quantity": number
    }
  ],
  "payment_method": "string",
  "metadata": {
    "shipping_address": "string",
    "note": "string"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Order created successfully",
  "order_id": "uuid"
}
```

**Mã lỗi:**
- 400: Dữ liệu không hợp lệ
- 404: Không tìm thấy sản phẩm hoặc chi nhánh
- 409: Sản phẩm không có sẵn tại chi nhánh
- 500: Lỗi server

## Analytics

### Theo dõi sự kiện

**Endpoint:** `POST /analytics/track`

**Mô tả:** Ghi lại sự kiện người dùng cho phân tích

**Request Body:**
```json
{
  "event_type": "string", // "pageview", "product_view", "add_to_cart", "purchase"
  "theme_id": "uuid",
  "data": {
    // Dữ liệu tùy thuộc vào loại sự kiện
    "page_url": "string",
    "product_id": "uuid",
    "order_id": "uuid",
    "amount": number
  }
}
```

**Response:**
```json
{
  "success": true
}
```

**Mã lỗi:**
- 400: Loại sự kiện không được hỗ trợ hoặc thiếu trường bắt buộc
- 500: Lỗi server

## Zalo Mini App Integration

### Lấy số điện thoại người dùng

**Endpoint:** `POST /integrations/zalo/phone`

**Mô tả:** Lấy số điện thoại của người dùng Zalo sau khi họ đồng ý cung cấp thông qua form ủy quyền và cập nhật vào hồ sơ người dùng

**Request Body:**
```json
{
  "code": "string", // OAuth code từ Zalo Mini App
  "theme_id": "uuid"  // ID của theme để lấy cấu hình OA
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "name": "string",
      "phone": "string",
      "picture": "string"
    }
  }
}
```

**Mã lỗi:**
- 400: Thiếu code hoặc code không hợp lệ
- 401: Không có quyền truy cập hoặc không tìm thấy account_id trong token
- 404: Không tìm thấy cấu hình OA
- 500: Lỗi server

**Lưu ý:** Để sử dụng API này, cần thực hiện các bước sau trong ứng dụng Zalo Mini App:

1. Sử dụng `zmp-sdk` để gọi `requestPhoneNumber()` và hiển thị form ủy quyền
2. Khi người dùng đồng ý, Zalo sẽ trả về `code`
3. Gửi `code` và `theme_id` đến API `/integrations/zalo/phone` để lấy số điện thoại và cập nhật hồ sơ người dùng

**Ví dụ sử dụng trong Zalo Mini App:**

```javascript
import { requestPhoneNumber } from 'zmp-sdk';
import { getPhoneNumber } from '@/lib/zalo/mini-app';

const handleGetPhoneNumber = async (themeId) => {
  try {
    // Yêu cầu người dùng cấp quyền lấy số điện thoại
    const { code, error } = await requestPhoneNumber();

    if (error) {
      console.error('User denied permission:', error);
      return;
    }

    // Gửi code đến API
    const user = await getPhoneNumber(code, themeId);
    console.log('User updated with phone number:', user.phone);
    return user;
  } catch (error) {
    console.error('Failed to get phone number:', error);
  }
};

// Hoặc sử dụng trực tiếp fetch API
const getPhoneNumberWithFetch = async (code, themeId) => {
  const response = await fetch('/api/integrations/zalo/phone', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${yourAuthToken}`
    },
    body: JSON.stringify({
      code,
      theme_id: themeId
    }),
  });

  const data = await response.json();
  if (data.success) {
    console.log('User updated with phone number:', data.data.user.phone);
    return data.data.user;
  } else {
    throw new Error(data.error || 'Failed to get phone number');
  }
};
```

## Mã trạng thái đơn hàng

- `pending`: Đang chờ xử lý
- `processing`: Đang xử lý
- `completed`: Đã hoàn thành
- `cancelled`: Đã hủy
- `refunded`: Đã hoàn tiền

## Phương thức thanh toán

- `cash`: Tiền mặt
- `bank_transfer`: Chuyển khoản ngân hàng
- `zalopay`: ZaloPay
- `momo`: MoMo
- `vnpay`: VNPay

## Cấu trúc hệ thống và mối quan hệ dữ liệu

### Mối quan hệ giữa Zalo Mini App và hệ thống

Hệ thống hoạt động như một third-party server, kết nối giữa Zalo Mini App và dữ liệu của doanh nghiệp. Cấu trúc dữ liệu chính bao gồm:

1. **Bảng `oa_configurations`**: Lưu trữ thông tin của Zalo Mini App
   - `id`: ID duy nhất của cấu hình
   - `app_id`: ID của Zalo Mini App
   - `app_secret`: Secret key của Zalo Mini App
   - `oa_id`: ID của Official Account
   - `theme_id`: Liên kết với account_theme

2. **Bảng `account_themes`**: Lưu trữ thông tin giao diện và cấu hình của từng doanh nghiệp
   - `id`: ID duy nhất của theme
   - `account_id`: ID của tài khoản doanh nghiệp
   - `name`: Tên theme
   - `configuration`: Cấu hình giao diện

3. **Luồng xử lý**:
   - Khi người dùng gửi token Zalo, hệ thống sẽ xác thực và xác định `theme_id`
   - Từ `theme_id`, hệ thống tìm kiếm thông tin Zalo App tương ứng trong bảng `oa_configurations`
   - Sử dụng thông tin này để kết nối với Zalo API và thực hiện các tác vụ như lấy số điện thoại, gửi thông báo, v.v.

## Lưu ý quan trọng

1. Tất cả các API đều yêu cầu xác thực bằng token JWT.
2. Token có thời hạn sử dụng, cần làm mới khi hết hạn.
3. Tất cả các ID đều là UUID v4.
4. Các timestamp được trả về theo định dạng ISO 8601.
5. Khi tạo đơn hàng, hệ thống sẽ tự động tính toán giá tiền dựa trên giá sản phẩm và số lượng.
6. Nếu không cung cấp `branch_id` khi tạo đơn hàng, hệ thống sẽ sử dụng chi nhánh mặc định.
7. Tham số `theme_id` là bắt buộc trong hầu hết các API để xác định cấu hình Zalo Mini App cần sử dụng.

## Ví dụ tích hợp

### Luồng xác thực và lấy dữ liệu

1. Lấy token Zalo từ Mini App SDK
2. Gọi API `/auth/zalo` để xác thực và nhận JWT token
3. Lưu JWT token để sử dụng cho các request tiếp theo
4. Gọi API `/products` để lấy danh sách sản phẩm
5. Gọi API `/categories/tree` để lấy cấu trúc danh mục
6. Gọi API `/branches` để lấy danh sách chi nhánh

### Luồng tạo đơn hàng

1. Người dùng chọn sản phẩm và thêm vào giỏ hàng
2. Gọi API `/analytics/track` với `event_type: "add_to_cart"` để theo dõi hành vi
3. Người dùng tiến hành thanh toán
4. Gọi API `/orders` để tạo đơn hàng mới
5. Nhận `order_id` từ response
6. Gọi API `/orders/{order_id}` để lấy chi tiết đơn hàng vừa tạo
7. Gọi API `/analytics/track` với `event_type: "purchase"` để theo dõi việc mua hàng

### Luồng làm mới token

1. Ứng dụng client kiểm tra thời gian hết hạn của token hiện tại
2. Nếu token sắp hết hạn (ví dụ: còn dưới 5 phút), gọi API `/auth/refresh`
3. Lưu token mới nhận được để sử dụng cho các request tiếp theo
4. Nếu việc làm mới token thất bại, chuyển hướng người dùng đến trang đăng nhập

### Ví dụ kiểm tra và làm mới token

```javascript
// Kiểm tra xem token có sắp hết hạn không
function isTokenExpiringSoon(token) {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expiryTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiry = expiryTime - currentTime;

    // Trả về true nếu token sẽ hết hạn trong 5 phút
    return timeUntilExpiry < 5 * 60 * 1000;
  } catch (error) {
    // Nếu không thể giải mã token, coi như nó đã hết hạn
    return true;
  }
}

// Làm mới token nếu cần
async function refreshTokenIfNeeded() {
  const currentToken = localStorage.getItem('auth_token');

  if (currentToken && isTokenExpiringSoon(currentToken)) {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('auth_token', data.token);
        return data.token;
      } else {
        // Nếu làm mới thất bại, xóa token hiện tại
        localStorage.removeItem('auth_token');
        // Chuyển hướng đến trang đăng nhập
        window.location.href = '/auth/signin';
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
      localStorage.removeItem('auth_token');
      window.location.href = '/auth/signin';
    }
  }

  return currentToken;
}
```
