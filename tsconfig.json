{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noUncheckedIndexedAccess": true}, "exclude": ["node_modules"]}