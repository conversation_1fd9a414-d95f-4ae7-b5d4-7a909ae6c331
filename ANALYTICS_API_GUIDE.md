# Hướng dẫn sử dụng API Analytics

Tài liệu này mô tả cách sử dụng API Analytics để ghi nhận các sự kiện từ Zalo Mini App.

## Tổng quan

API Analytics cho phép bạn ghi nhận các sự kiện người dùng như xem trang, xem sản phẩm, thêm vào giỏ hàng và mua hàng. Dữ liệu này được lưu trữ trong cơ sở dữ liệu và có thể được sử dụng để phân tích hành vi người dùng.

## Endpoint

```
POST /api/analytics
```

## Xác thực

API yêu cầu xác thực bằng JWT token. Token này cần được gửi trong header `Authorization`.

```
Authorization: Bearer <your_jwt_token>
```

## Tham số

| Tham số | Kiểu dữ liệu | <PERSON><PERSON><PERSON> buộ<PERSON> | Mô tả |
|---------|--------------|----------|-------|
| eventType | string | Có | Loại sự kiện (`pageview`, `product_view`, `add_to_cart`, `purchase`) |
| themeId | string | Có | ID của theme (cửa hàng) |
| visitorId | string | Có | ID của người truy cập |
| deviceType | string | Có | Loại thiết bị (`mobile`, `desktop`, `tablet`) |

### Tham số bổ sung cho từng loại sự kiện

#### Sự kiện `pageview`

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| pagePath | string | Không | Đường dẫn trang (mặc định: `/`) |
| pageTitle | string | Không | Tiêu đề trang |
| duration | number | Không | Thời gian xem trang (giây) |
| utmSource | string | Không | Nguồn UTM |
| utmMedium | string | Không | Kênh UTM |
| utmCampaign | string | Không | Chiến dịch UTM |
| browser | string | Không | Trình duyệt |
| os | string | Không | Hệ điều hành |
| ipAddress | string | Không | Địa chỉ IP |
| referrer | string | Không | Trang giới thiệu |

#### Sự kiện `product_view` và `add_to_cart`

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| productId | string | Có | ID của sản phẩm |
| quantity | number | Không | Số lượng (mặc định: 1) |

#### Sự kiện `purchase`

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| productId | string | Có | ID của sản phẩm |
| quantity | number | Không | Số lượng (mặc định: 1) |
| orderId | string | Có | ID của đơn hàng |
| amount | number | Không | Tổng giá trị đơn hàng (mặc định: 0) |

## Phản hồi

### Thành công

```json
{
  "success": true
}
```

### Lỗi

```json
{
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Thiếu tham số bắt buộc hoặc loại sự kiện không được hỗ trợ |
| 401 | Không tìm thấy account_id trong token |
| 500 | Lỗi server khi lưu sự kiện |

## Ví dụ

### Ghi nhận sự kiện xem trang

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "eventType": "pageview",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "pagePath": "/products",
    "pageTitle": "Danh sách sản phẩm",
    "utmSource": "zalo",
    "utmMedium": "social",
    "utmCampaign": "summer_sale"
  }'
```

### Ghi nhận sự kiện xem sản phẩm

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "eventType": "product_view",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "productId": "product-456"
  }'
```

### Ghi nhận sự kiện thêm vào giỏ hàng

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "eventType": "add_to_cart",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "productId": "product-456",
    "quantity": 2
  }'
```

### Ghi nhận sự kiện mua hàng

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "eventType": "purchase",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "productId": "product-456",
    "quantity": 2,
    "orderId": "order-789",
    "amount": 1000000
  }'
```

## Lưu ý

1. API này yêu cầu xác thực JWT token có chứa thông tin account_id trong user_metadata.
2. Tất cả các sự kiện đều được ghi nhận với nguồn là `zalo_miniapp`.
3. Dữ liệu sự kiện được lưu trữ trong bảng `analytics_events` trong cơ sở dữ liệu.
4. Các loại sự kiện không được hỗ trợ sẽ bị từ chối với mã lỗi 400.

## Tích hợp với Zalo Mini App

Để tích hợp API Analytics vào Zalo Mini App, bạn cần:

1. Lấy JWT token từ quá trình xác thực
2. Tạo một ID duy nhất cho mỗi người truy cập (visitorId)
3. Gửi sự kiện đến API mỗi khi người dùng thực hiện một hành động cần theo dõi

### Ví dụ tích hợp (JavaScript)

```javascript
// Hàm gửi sự kiện analytics
async function sendAnalyticsEvent(eventData) {
  try {
    const response = await fetch('https://your-domain.com/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      },
      body: JSON.stringify({
        ...eventData,
        visitorId: localStorage.getItem('visitor_id') || generateVisitorId(),
        deviceType: detectDeviceType()
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error sending analytics event:', error);
    return { error: error.message };
  }
}

// Tạo ID người truy cập
function generateVisitorId() {
  const visitorId = 'visitor-' + Math.random().toString(36).substring(2, 15);
  localStorage.setItem('visitor_id', visitorId);
  return visitorId;
}

// Phát hiện loại thiết bị
function detectDeviceType() {
  const userAgent = navigator.userAgent;
  if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
    return 'mobile';
  } else if (/iPad|Tablet|PlayBook/i.test(userAgent)) {
    return 'tablet';
  }
  return 'desktop';
}

// Ví dụ sử dụng
// Ghi nhận sự kiện xem trang
sendAnalyticsEvent({
  eventType: 'pageview',
  themeId: 'your-theme-id',
  pagePath: window.location.pathname,
  pageTitle: document.title
});

// Ghi nhận sự kiện xem sản phẩm
function trackProductView(productId) {
  sendAnalyticsEvent({
    eventType: 'product_view',
    themeId: 'your-theme-id',
    productId
  });
}

// Ghi nhận sự kiện thêm vào giỏ hàng
function trackAddToCart(productId, quantity) {
  sendAnalyticsEvent({
    eventType: 'add_to_cart',
    themeId: 'your-theme-id',
    productId,
    quantity
  });
}

// Ghi nhận sự kiện mua hàng
function trackPurchase(productId, quantity, orderId, amount) {
  sendAnalyticsEvent({
    eventType: 'purchase',
    themeId: 'your-theme-id',
    productId,
    quantity,
    orderId,
    amount
  });
}
```
