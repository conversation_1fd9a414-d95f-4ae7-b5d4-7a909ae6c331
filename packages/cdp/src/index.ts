// Core exports
export { CDPManager, createCDPManager, defaultCDPConfig } from './core/cdp-manager';

// Service exports
export { ProfileService } from './services/profile-service';
export { SegmentationService } from './services/segmentation-service';
export { JourneyService } from './services/journey-service';

// Type exports
export * from './types';

// Utility exports
export * from './utils';

// Re-export commonly used types for convenience
export type {
  CustomerProfile,
  CustomerIdentity,
  CustomerSegment,
  CustomerJourney,
  CDPConfig,
  CDPService,
  CDPEvent
} from './types';
