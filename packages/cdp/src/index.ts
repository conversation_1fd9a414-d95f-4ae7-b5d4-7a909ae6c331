// Core exports
export { CDP<PERSON>anager, createCDPManager, defaultCDPConfig } from './core/cdp-manager';

// Service exports
export { ProfileService } from './services/profile-service';
export { SegmentationService } from './services/segmentation-service';
export { JourneyService } from './services/journey-service';
export { SimpleEventProcessor } from './services/simple-event-processor';
export { SimpleAnalytics } from './services/simple-analytics';
export { SimpleMonitor } from './services/simple-monitor';
export { SimpleQueue } from './services/simple-queue';
// Note: Some Phase 4 services temporarily disabled due to TypeScript issues

// Type exports
export * from './types';

// Utility exports
export * from './utils';

// Re-export commonly used types for convenience
export type {
  CustomerProfile,
  CustomerIdentity,
  CustomerSegment,
  CustomerJourney,
  CDPConfig,
  CDPService,
  CDPEvent
} from './types';
