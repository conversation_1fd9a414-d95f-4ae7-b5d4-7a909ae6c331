// Core exports
export { CDPManager, createCDPManager, defaultCDPConfig } from './core/cdp-manager';

// Service exports
export { ProfileService } from './services/profile-service';
export { SegmentationService } from './services/segmentation-service';
export { JourneyService } from './services/journey-service';
export { SimpleEventProcessor } from './services/simple-event-processor';
export { SimpleAnalytics } from './services/simple-analytics';
export { SimpleMonitor } from './services/simple-monitor';
export { SimpleQueue } from './services/simple-queue';

// Phase 4B: AI/ML Engine exports
export { PredictiveAnalytics } from './services/predictive-analytics';
export { RecommendationEngine } from './services/recommendation-engine';
export { AutoSegmentation } from './services/auto-segmentation';
export { ContentPersonalization } from './services/content-personalization';

// Type exports
export * from './types';

// Utility exports
export * from './utils';

// Re-export commonly used types for convenience
export type {
  CustomerProfile,
  CustomerIdentity,
  CustomerSegment,
  CustomerJourney,
  CDPConfig,
  CDPService,
  CDPEvent
} from './types';
