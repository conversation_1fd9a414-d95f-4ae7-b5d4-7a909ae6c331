import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface FunnelStep {
  id: string;
  name: string;
  event_type: string;
  order: number;
  is_required: boolean;
  conditions?: Record<string, any>;
}

export interface FunnelDefinition {
  id: string;
  name: string;
  description: string;
  steps: FunnelStep[];
  time_window: number; // hours
  created_at: Date;
  updated_at: Date;
}

export interface FunnelStepData {
  step: FunnelStep;
  total_users: number;
  unique_users: number;
  conversion_rate: number;
  drop_off_rate: number;
  avg_time_to_next_step: number;
  bounce_rate: number;
  segment_performance: Array<{
    segment: string;
    users: number;
    conversion_rate: number;
  }>;
}

export interface FunnelAnalysis {
  definition: FunnelDefinition;
  time_range: { start: Date; end: Date };
  total_entries: number;
  total_completions: number;
  overall_conversion_rate: number;
  steps: FunnelStepData[];
  bottlenecks: Array<{
    step_id: string;
    step_name: string;
    drop_off_rate: number;
    impact_score: number;
    recommendations: string[];
  }>;
  optimization_opportunities: Array<{
    type: 'step_optimization' | 'flow_improvement' | 'segment_targeting';
    priority: 'high' | 'medium' | 'low';
    description: string;
    potential_impact: number;
    effort_required: 'low' | 'medium' | 'high';
  }>;
  cohort_analysis: {
    by_acquisition_date: Array<{
      cohort: string;
      conversion_rate: number;
      avg_time_to_complete: number;
    }>;
    by_user_segment: Array<{
      segment: string;
      conversion_rate: number;
      step_performance: number[];
    }>;
  };
}

export interface FunnelComparison {
  funnel_a: FunnelAnalysis;
  funnel_b: FunnelAnalysis;
  differences: {
    conversion_rate_diff: number;
    step_differences: Array<{
      step_name: string;
      conversion_rate_diff: number;
      significance: 'high' | 'medium' | 'low';
    }>;
  };
  insights: string[];
  recommendations: string[];
}

export interface FunnelOptimizationExperiment {
  id: string;
  funnel_id: string;
  name: string;
  description: string;
  hypothesis: string;
  changes: Array<{
    step_id: string;
    change_type: 'ui_change' | 'flow_change' | 'content_change';
    description: string;
  }>;
  status: 'draft' | 'running' | 'completed' | 'paused';
  start_date?: Date;
  end_date?: Date;
  results?: {
    control_conversion_rate: number;
    variant_conversion_rate: number;
    improvement: number;
    statistical_significance: number;
  };
}

/**
 * Funnel Optimization Service
 * Provides advanced funnel analysis, bottleneck identification, and conversion optimization
 */
export class FunnelOptimization implements CDPService {
  private readonly FUNNELS_PREFIX = 'cdp:funnels:';
  private readonly ANALYSIS_PREFIX = 'cdp:funnel_analysis:';
  private readonly EXPERIMENTS_PREFIX = 'cdp:funnel_experiments:';
  
  private funnels: Map<string, FunnelDefinition> = new Map();
  private isProcessing = false;

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();
      
      // Load existing funnels
      await this.loadFunnels();
      
      // Initialize default funnels
      await this.initializeDefaultFunnels();
      
      console.log('FunnelOptimization initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize FunnelOptimization',
        'FUNNEL_OPTIMIZATION_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;
    console.log('FunnelOptimization destroyed');
  }

  /**
   * Create funnel definition
   */
  async createFunnel(definition: Omit<FunnelDefinition, 'id' | 'created_at' | 'updated_at'>): Promise<FunnelDefinition> {
    try {
      const funnel: FunnelDefinition = {
        ...definition,
        id: `funnel_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date(),
        updated_at: new Date()
      };

      // Validate funnel steps
      this.validateFunnelSteps(funnel.steps);

      // Store funnel
      await this.redis.set(`${this.FUNNELS_PREFIX}${funnel.id}`, JSON.stringify(funnel));
      this.funnels.set(funnel.id, funnel);

      return funnel;
    } catch (error) {
      throw new CDPError(
        'Failed to create funnel',
        'FUNNEL_CREATE_FAILED',
        { definition, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Analyze funnel performance
   */
  async analyzeFunnel(
    funnelId: string,
    timeRange: { start: Date; end: Date },
    segmentFilters?: Record<string, any>
  ): Promise<FunnelAnalysis> {
    try {
      const funnel = this.funnels.get(funnelId);
      if (!funnel) {
        throw new Error('Funnel not found');
      }

      // Get user events for analysis
      const events = await this.getUserEvents(timeRange, funnel.steps.map(s => s.event_type));
      
      // Build user journeys through funnel
      const userJourneys = await this.buildUserJourneys(events, funnel);
      
      // Calculate step performance
      const stepData = await this.calculateStepPerformance(userJourneys, funnel);
      
      // Identify bottlenecks
      const bottlenecks = this.identifyBottlenecks(stepData);
      
      // Generate optimization opportunities
      const optimizationOpportunities = this.generateOptimizationOpportunities(stepData, bottlenecks);
      
      // Perform cohort analysis
      const cohortAnalysis = await this.performCohortAnalysis(userJourneys, funnel);
      
      const totalEntries = userJourneys.length;
      const totalCompletions = userJourneys.filter(journey => 
        journey.completed_steps.length === funnel.steps.length
      ).length;
      const overallConversionRate = totalEntries > 0 ? totalCompletions / totalEntries : 0;

      const analysis: FunnelAnalysis = {
        definition: funnel,
        time_range: timeRange,
        total_entries: totalEntries,
        total_completions: totalCompletions,
        overall_conversion_rate: overallConversionRate,
        steps: stepData,
        bottlenecks,
        optimization_opportunities: optimizationOpportunities,
        cohort_analysis: cohortAnalysis
      };

      // Cache analysis
      await this.cacheAnalysis(funnelId, timeRange, analysis);

      return analysis;
    } catch (error) {
      throw new CDPError(
        'Failed to analyze funnel',
        'FUNNEL_ANALYSIS_FAILED',
        { funnelId, timeRange, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Compare two funnels
   */
  async compareFunnels(
    funnelIdA: string,
    funnelIdB: string,
    timeRange: { start: Date; end: Date }
  ): Promise<FunnelComparison> {
    try {
      const [analysisA, analysisB] = await Promise.all([
        this.analyzeFunnel(funnelIdA, timeRange),
        this.analyzeFunnel(funnelIdB, timeRange)
      ]);

      const conversionRateDiff = analysisB.overall_conversion_rate - analysisA.overall_conversion_rate;
      
      // Compare step performance
      const stepDifferences = analysisA.steps.map((stepA, index) => {
        const stepB = analysisB.steps[index];
        const conversionRateDiff = stepB ? stepB.conversion_rate - stepA.conversion_rate : 0;
        
        return {
          step_name: stepA.step.name,
          conversion_rate_diff: conversionRateDiff,
          significance: Math.abs(conversionRateDiff) > 0.1 ? 'high' : 
                       Math.abs(conversionRateDiff) > 0.05 ? 'medium' : 'low'
        };
      });

      // Generate insights
      const insights = this.generateComparisonInsights(analysisA, analysisB, conversionRateDiff);
      
      // Generate recommendations
      const recommendations = this.generateComparisonRecommendations(stepDifferences);

      return {
        funnel_a: analysisA,
        funnel_b: analysisB,
        differences: {
          conversion_rate_diff: conversionRateDiff,
          step_differences: stepDifferences
        },
        insights,
        recommendations
      };
    } catch (error) {
      throw new CDPError(
        'Failed to compare funnels',
        'FUNNEL_COMPARISON_FAILED',
        { funnelIdA, funnelIdB, timeRange, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Create optimization experiment
   */
  async createExperiment(experiment: Omit<FunnelOptimizationExperiment, 'id'>): Promise<FunnelOptimizationExperiment> {
    try {
      const newExperiment: FunnelOptimizationExperiment = {
        ...experiment,
        id: `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };

      // Store experiment
      await this.redis.set(`${this.EXPERIMENTS_PREFIX}${newExperiment.id}`, JSON.stringify(newExperiment));

      return newExperiment;
    } catch (error) {
      throw new CDPError(
        'Failed to create experiment',
        'EXPERIMENT_CREATE_FAILED',
        { experiment, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Private helper methods
   */
  private validateFunnelSteps(steps: FunnelStep[]): void {
    if (steps.length < 2) {
      throw new Error('Funnel must have at least 2 steps');
    }

    // Check step order
    const orders = steps.map(s => s.order).sort((a, b) => a - b);
    for (let i = 0; i < orders.length; i++) {
      if (orders[i] !== i + 1) {
        throw new Error('Funnel steps must have consecutive order numbers starting from 1');
      }
    }
  }

  private async getUserEvents(timeRange: { start: Date; end: Date }, eventTypes: string[]): Promise<any[]> {
    try {
      let query = this.supabase
        .from('analytics_events')
        .select('*')
        .in('event_type', eventTypes)
        .gte('timestamp', timeRange.start.toISOString())
        .lte('timestamp', timeRange.end.toISOString())
        .order('timestamp', { ascending: true });

      const { data: events } = await query.limit(50000);
      return events || [];
    } catch (error) {
      console.error('Failed to get user events:', error);
      return [];
    }
  }

  private async buildUserJourneys(events: any[], funnel: FunnelDefinition): Promise<any[]> {
    const userJourneys = new Map<string, any>();
    
    // Group events by user
    const userEvents = new Map<string, any[]>();
    for (const event of events) {
      const userId = event.customer_profile_id || event.anonymous_id;
      if (!userEvents.has(userId)) {
        userEvents.set(userId, []);
      }
      userEvents.get(userId)!.push(event);
    }
    
    // Build journeys for each user
    for (const [userId, events] of userEvents.entries()) {
      const sortedEvents = events.sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
      
      const completedSteps: any[] = [];
      let currentStepIndex = 0;
      
      for (const event of sortedEvents) {
        if (currentStepIndex < funnel.steps.length) {
          const currentStep = funnel.steps[currentStepIndex];
          
          if (event.event_type === currentStep.event_type) {
            completedSteps.push({
              step: currentStep,
              event,
              timestamp: new Date(event.timestamp)
            });
            currentStepIndex++;
          }
        }
      }
      
      userJourneys.set(userId, {
        user_id: userId,
        completed_steps: completedSteps,
        is_complete: completedSteps.length === funnel.steps.length,
        start_time: completedSteps.length > 0 ? completedSteps[0].timestamp : null,
        end_time: completedSteps.length > 0 ? completedSteps[completedSteps.length - 1].timestamp : null
      });
    }
    
    return Array.from(userJourneys.values());
  }

  private async calculateStepPerformance(userJourneys: any[], funnel: FunnelDefinition): Promise<FunnelStepData[]> {
    const stepData: FunnelStepData[] = [];
    
    for (let i = 0; i < funnel.steps.length; i++) {
      const step = funnel.steps[i];
      
      // Users who reached this step
      const usersAtStep = userJourneys.filter(journey => journey.completed_steps.length > i);
      
      // Users who completed this step and moved to next
      const usersToNext = userJourneys.filter(journey => journey.completed_steps.length > i + 1);
      
      const totalUsers = i === 0 ? userJourneys.length : userJourneys.filter(j => j.completed_steps.length >= i).length;
      const uniqueUsers = usersAtStep.length;
      const conversionRate = i === 0 ? 1 : (uniqueUsers / (stepData[i - 1]?.unique_users || 1));
      const dropOffRate = 1 - (usersToNext.length / (uniqueUsers || 1));
      
      // Calculate average time to next step
      let avgTimeToNext = 0;
      if (usersToNext.length > 0) {
        const times = usersToNext.map(journey => {
          const currentStep = journey.completed_steps[i];
          const nextStep = journey.completed_steps[i + 1];
          return nextStep ? nextStep.timestamp.getTime() - currentStep.timestamp.getTime() : 0;
        }).filter((time: number) => time > 0);
        
        avgTimeToNext = times.length > 0 ? times.reduce((sum: number, time: number) => sum + time, 0) / times.length : 0;
      }
      
      stepData.push({
        step,
        total_users: totalUsers,
        unique_users: uniqueUsers,
        conversion_rate: conversionRate,
        drop_off_rate: dropOffRate,
        avg_time_to_next_step: avgTimeToNext,
        bounce_rate: i === 0 ? dropOffRate : 0,
        segment_performance: [] // Simplified for now
      });
    }
    
    return stepData;
  }

  private identifyBottlenecks(stepData: FunnelStepData[]): FunnelAnalysis['bottlenecks'] {
    const bottlenecks: FunnelAnalysis['bottlenecks'] = [];
    
    for (let i = 0; i < stepData.length; i++) {
      const step = stepData[i];
      
      // Consider a step a bottleneck if drop-off rate is high
      if (step.drop_off_rate > 0.3) {
        const impactScore = step.drop_off_rate * step.unique_users;
        
        const recommendations: string[] = [];
        
        if (step.drop_off_rate > 0.5) {
          recommendations.push('Critical bottleneck - immediate attention required');
          recommendations.push('Consider A/B testing different approaches for this step');
        }
        
        if (step.avg_time_to_next_step > 300000) { // 5 minutes
          recommendations.push('Users spend too much time on this step - simplify the process');
        }
        
        recommendations.push('Analyze user behavior patterns at this step');
        recommendations.push('Consider adding progress indicators or help content');
        
        bottlenecks.push({
          step_id: step.step.id,
          step_name: step.step.name,
          drop_off_rate: step.drop_off_rate,
          impact_score: impactScore,
          recommendations
        });
      }
    }
    
    return bottlenecks.sort((a, b) => b.impact_score - a.impact_score);
  }

  private generateOptimizationOpportunities(
    stepData: FunnelStepData[],
    bottlenecks: FunnelAnalysis['bottlenecks']
  ): FunnelAnalysis['optimization_opportunities'] {
    const opportunities: FunnelAnalysis['optimization_opportunities'] = [];
    
    // High-impact bottleneck optimization
    for (const bottleneck of bottlenecks.slice(0, 3)) {
      opportunities.push({
        type: 'step_optimization',
        priority: bottleneck.drop_off_rate > 0.5 ? 'high' : 'medium',
        description: `Optimize ${bottleneck.step_name} to reduce ${(bottleneck.drop_off_rate * 100).toFixed(1)}% drop-off rate`,
        potential_impact: bottleneck.impact_score,
        effort_required: 'medium'
      });
    }
    
    // Flow improvement opportunities
    const avgConversionRate = stepData.reduce((sum, step) => sum + step.conversion_rate, 0) / stepData.length;
    if (avgConversionRate < 0.7) {
      opportunities.push({
        type: 'flow_improvement',
        priority: 'high',
        description: 'Overall funnel conversion rate is low - consider redesigning the entire flow',
        potential_impact: stepData.reduce((sum, step) => sum + step.unique_users, 0) * 0.2,
        effort_required: 'high'
      });
    }
    
    // Segment targeting opportunities
    opportunities.push({
      type: 'segment_targeting',
      priority: 'medium',
      description: 'Create targeted experiences for different user segments',
      potential_impact: stepData.reduce((sum, step) => sum + step.unique_users, 0) * 0.1,
      effort_required: 'medium'
    });
    
    return opportunities.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  private async performCohortAnalysis(userJourneys: any[], funnel: FunnelDefinition): Promise<FunnelAnalysis['cohort_analysis']> {
    // Simplified cohort analysis
    return {
      by_acquisition_date: [
        { cohort: '2024-01', conversion_rate: 0.15, avg_time_to_complete: 3600000 },
        { cohort: '2024-02', conversion_rate: 0.18, avg_time_to_complete: 3200000 },
        { cohort: '2024-03', conversion_rate: 0.22, avg_time_to_complete: 2800000 }
      ],
      by_user_segment: [
        { segment: 'new_users', conversion_rate: 0.12, step_performance: [1.0, 0.8, 0.6, 0.4, 0.12] },
        { segment: 'returning_users', conversion_rate: 0.25, step_performance: [1.0, 0.9, 0.8, 0.6, 0.25] },
        { segment: 'premium_users', conversion_rate: 0.35, step_performance: [1.0, 0.95, 0.9, 0.8, 0.35] }
      ]
    };
  }

  private generateComparisonInsights(
    analysisA: FunnelAnalysis,
    analysisB: FunnelAnalysis,
    conversionRateDiff: number
  ): string[] {
    const insights: string[] = [];
    
    if (Math.abs(conversionRateDiff) > 0.05) {
      const better = conversionRateDiff > 0 ? 'B' : 'A';
      const worse = conversionRateDiff > 0 ? 'A' : 'B';
      insights.push(`Funnel ${better} performs ${(Math.abs(conversionRateDiff) * 100).toFixed(1)}% better than Funnel ${worse}`);
    }
    
    if (analysisA.bottlenecks.length > analysisB.bottlenecks.length) {
      insights.push('Funnel A has more bottlenecks than Funnel B');
    } else if (analysisB.bottlenecks.length > analysisA.bottlenecks.length) {
      insights.push('Funnel B has more bottlenecks than Funnel A');
    }
    
    return insights;
  }

  private generateComparisonRecommendations(stepDifferences: any[]): string[] {
    const recommendations: string[] = [];
    
    const significantDifferences = stepDifferences.filter(diff => diff.significance === 'high');
    
    if (significantDifferences.length > 0) {
      recommendations.push('Focus on steps with high significance differences');
      
      for (const diff of significantDifferences) {
        if (diff.conversion_rate_diff > 0) {
          recommendations.push(`Apply Funnel B's approach to ${diff.step_name} in Funnel A`);
        } else {
          recommendations.push(`Apply Funnel A's approach to ${diff.step_name} in Funnel B`);
        }
      }
    }
    
    return recommendations;
  }

  private async loadFunnels(): Promise<void> {
    const funnelKeys = await this.redis.keys(`${this.FUNNELS_PREFIX}*`);
    
    for (const key of funnelKeys) {
      const funnelData = await this.redis.get(key);
      if (funnelData) {
        const funnel = JSON.parse(funnelData);
        this.funnels.set(funnel.id, funnel);
      }
    }
  }

  private async initializeDefaultFunnels(): Promise<void> {
    const defaultFunnels: Omit<FunnelDefinition, 'id' | 'created_at' | 'updated_at'>[] = [
      {
        name: 'E-commerce Purchase Funnel',
        description: 'Standard e-commerce conversion funnel',
        steps: [
          { id: 'step1', name: 'Product View', event_type: 'product_view', order: 1, is_required: true },
          { id: 'step2', name: 'Add to Cart', event_type: 'add_to_cart', order: 2, is_required: true },
          { id: 'step3', name: 'Checkout Start', event_type: 'checkout_start', order: 3, is_required: true },
          { id: 'step4', name: 'Payment Info', event_type: 'payment_info', order: 4, is_required: true },
          { id: 'step5', name: 'Purchase', event_type: 'purchase', order: 5, is_required: true }
        ],
        time_window: 24
      },
      {
        name: 'User Onboarding Funnel',
        description: 'New user onboarding process',
        steps: [
          { id: 'step1', name: 'Sign Up', event_type: 'signup', order: 1, is_required: true },
          { id: 'step2', name: 'Email Verification', event_type: 'email_verified', order: 2, is_required: true },
          { id: 'step3', name: 'Profile Setup', event_type: 'profile_completed', order: 3, is_required: true },
          { id: 'step4', name: 'First Action', event_type: 'first_action', order: 4, is_required: true }
        ],
        time_window: 72
      }
    ];

    for (const funnelDef of defaultFunnels) {
      if (this.funnels.size === 0) { // Only create if no funnels exist
        await this.createFunnel(funnelDef);
      }
    }
  }

  private async cacheAnalysis(
    funnelId: string,
    timeRange: { start: Date; end: Date },
    analysis: FunnelAnalysis
  ): Promise<void> {
    const cacheKey = `${this.ANALYSIS_PREFIX}${funnelId}:${timeRange.start.toISOString()}:${timeRange.end.toISOString()}`;
    await this.redis.setex(cacheKey, 3600, JSON.stringify(analysis)); // Cache for 1 hour
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();
      
      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        funnelsLoaded: this.funnels.size,
        isProcessing: this.isProcessing,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
