import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { 
  CDPConfig, 
  CDPService, 
  CDPEvent,
  CDPError 
} from '../types';

/**
 * Central CDP Manager that orchestrates all CDP services
 * Provides a unified interface for customer data platform operations
 */
export class CDPManager {
  private services: Map<string, CDPService> = new Map();
  private eventHandlers: Map<string, Array<(event: CDPEvent) => Promise<void>>> = new Map();
  private isInitialized = false;

  constructor(
    private config: CDPConfig,
    private supabase: SupabaseClient,
    private redis?: Redis
  ) {}

  /**
   * Initialize the CDP Manager and all services
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize core services
      await this.initializeCoreServices();
      
      // Initialize integration services
      await this.initializeIntegrationServices();
      
      // Setup event listeners
      this.setupEventListeners();
      
      this.isInitialized = true;
      
      await this.emit('cdp.initialized', {
        services: Array.from(this.services.keys()),
        config: this.config
      });
      
    } catch (error) {
      throw new CDPError(
        'Failed to initialize CDP Manager',
        'CDP_INITIALIZATION_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Destroy the CDP Manager and cleanup resources
   */
  async destroy(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      // Destroy all services
      for (const [name, service] of this.services) {
        try {
          await service.destroy();
        } catch (error) {
          console.warn(`Failed to destroy service ${name}:`, error);
        }
      }

      // Clear event handlers
      this.eventHandlers.clear();
      this.services.clear();
      
      this.isInitialized = false;
      
    } catch (error) {
      throw new CDPError(
        'Failed to destroy CDP Manager',
        'CDP_DESTRUCTION_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get a specific service by name
   */
  getService<T extends CDPService>(name: string): T {
    const service = this.services.get(name);
    if (!service) {
      throw new CDPError(
        `Service '${name}' not found`,
        'SERVICE_NOT_FOUND',
        { serviceName: name, availableServices: Array.from(this.services.keys()) }
      );
    }
    return service as T;
  }

  /**
   * Check if a service is available
   */
  hasService(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get list of all available services
   */
  getAvailableServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Register a custom service
   */
  async registerService(name: string, service: CDPService): Promise<void> {
    if (this.services.has(name)) {
      throw new CDPError(
        `Service '${name}' already exists`,
        'SERVICE_ALREADY_EXISTS',
        { serviceName: name }
      );
    }

    try {
      await service.initialize();
      this.services.set(name, service);
      
      await this.emit('cdp.service.registered', {
        serviceName: name,
        serviceType: service.constructor.name
      });
      
    } catch (error) {
      throw new CDPError(
        `Failed to register service '${name}'`,
        'SERVICE_REGISTRATION_FAILED',
        { 
          serviceName: name, 
          error: error instanceof Error ? error.message : String(error) 
        }
      );
    }
  }

  /**
   * Unregister a service
   */
  async unregisterService(name: string): Promise<void> {
    const service = this.services.get(name);
    if (!service) {
      return; // Service doesn't exist, nothing to do
    }

    try {
      await service.destroy();
      this.services.delete(name);
      
      await this.emit('cdp.service.unregistered', {
        serviceName: name
      });
      
    } catch (error) {
      throw new CDPError(
        `Failed to unregister service '${name}'`,
        'SERVICE_UNREGISTRATION_FAILED',
        { 
          serviceName: name, 
          error: error instanceof Error ? error.message : String(error) 
        }
      );
    }
  }

  /**
   * Emit an event to all registered handlers
   */
  async emit(eventType: string, payload: any): Promise<void> {
    const handlers = this.eventHandlers.get(eventType) || [];
    
    const event: CDPEvent = {
      type: eventType,
      payload,
      timestamp: new Date(),
      source: 'cdp-manager'
    };

    // Execute all handlers concurrently
    await Promise.allSettled(
      handlers.map(handler => handler(event))
    );
  }

  /**
   * Register an event handler
   */
  on(eventType: string, handler: (event: CDPEvent) => Promise<void>): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  /**
   * Unregister an event handler
   */
  off(eventType: string, handler: (event: CDPEvent) => Promise<void>): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): CDPConfig {
    return { ...this.config };
  }

  /**
   * Update configuration (requires restart for some settings)
   */
  updateConfig(newConfig: Partial<CDPConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get health status of all services
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    const status: Record<string, any> = {
      manager: {
        initialized: this.isInitialized,
        servicesCount: this.services.size,
        eventHandlersCount: this.eventHandlers.size
      },
      services: {}
    };

    // Check each service health
    for (const [name, service] of this.services) {
      try {
        // If service has a health check method, use it
        if ('getHealthStatus' in service && typeof service.getHealthStatus === 'function') {
          status.services[name] = await (service as any).getHealthStatus();
        } else {
          status.services[name] = { status: 'healthy' };
        }
      } catch (error) {
        status.services[name] = { 
          status: 'unhealthy', 
          error: error instanceof Error ? error.message : String(error) 
        };
      }
    }

    return status;
  }

  /**
   * Initialize core CDP services
   */
  private async initializeCoreServices(): Promise<void> {
    // Import services dynamically to avoid circular dependencies
    const { ProfileService } = await import('../services/profile-service');
    
    // Initialize Profile Service
    const profileService = new ProfileService(this.supabase, this.redis);
    await profileService.initialize();
    this.services.set('profile', profileService);

    // TODO: Initialize other core services as they are implemented
    // - SegmentationService
    // - JourneyService  
    // - PredictiveService
  }

  /**
   * Initialize integration services based on configuration
   */
  private async initializeIntegrationServices(): Promise<void> {
    // Initialize enabled integrations
    for (const integration of this.config.enabledIntegrations) {
      try {
        switch (integration) {
          case 'analytics':
            // TODO: Initialize Analytics Integration
            break;
          case 'email':
            // TODO: Initialize Email Integration
            break;
          case 'crm':
            // TODO: Initialize CRM Integration
            break;
          default:
            console.warn(`Unknown integration: ${integration}`);
        }
      } catch (error) {
        console.error(`Failed to initialize integration ${integration}:`, error);
      }
    }
  }

  /**
   * Setup internal event listeners
   */
  private setupEventListeners(): void {
    // Listen for profile updates to trigger downstream processes
    this.on('profile.updated', async (event) => {
      // TODO: Trigger segment re-evaluation
      // TODO: Update predictive scores
      // TODO: Check journey triggers
    });

    // Listen for segment membership changes
    this.on('segment.membership.changed', async (event) => {
      // TODO: Trigger journey events
      // TODO: Update activations
    });

    // Listen for journey events
    this.on('journey.step.completed', async (event) => {
      // TODO: Update journey analytics
      // TODO: Trigger next steps
    });
  }
}

/**
 * Factory function to create a CDP Manager instance
 */
export function createCDPManager(
  config: CDPConfig,
  supabase: SupabaseClient,
  redis?: Redis
): CDPManager {
  return new CDPManager(config, supabase, redis);
}

/**
 * Default CDP configuration
 */
export const defaultCDPConfig: CDPConfig = {
  realTimeProcessing: true,
  batchProcessingInterval: 3600, // 1 hour
  profileRetentionDays: 2555, // 7 years
  eventRetentionDays: 365, // 1 year
  cacheEnabled: true,
  cacheTTL: 3600, // 1 hour
  maxConcurrentProcessing: 10,
  enablePredictiveAnalytics: false,
  modelUpdateFrequency: 86400, // 24 hours
  enabledIntegrations: ['analytics'],
  integrationConfigs: {}
};
