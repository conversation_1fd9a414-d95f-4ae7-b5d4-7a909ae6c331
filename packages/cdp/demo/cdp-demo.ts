#!/usr/bin/env tsx

/**
 * CDP Module Demo Script
 * 
 * This script demonstrates the core functionality of the CDP module:
 * - Customer profile creation
 * - Identity resolution
 * - Profile updates
 * - Behavioral tracking
 * - Predictive scoring
 * 
 * Usage: tsx packages/cdp/demo/cdp-demo.ts
 */

import { createClient } from '@supabase/supabase-js';
import { createCDPManager, defaultCDPConfig } from '../src';
import { extractIdentities } from '../src/utils';

// Demo configuration
const DEMO_CONFIG = {
  supabaseUrl: process.env.SUPABASE_URL || 'http://localhost:54321',
  supabaseKey: process.env.SUPABASE_ANON_KEY || 'your-anon-key',
  accountId: 'demo-account-' + Date.now(),
};

async function runCDPDemo() {
  console.log('🚀 Starting CDP Module Demo...\n');

  try {
    // 1. Initialize CDP Manager
    console.log('1️⃣ Initializing CDP Manager...');
    const supabase = createClient(DEMO_CONFIG.supabaseUrl, DEMO_CONFIG.supabaseKey);
    const cdp = createCDPManager(defaultCDPConfig, supabase);
    await cdp.initialize();
    console.log('✅ CDP Manager initialized successfully\n');

    // 2. Get Profile Service
    const profileService = cdp.getService('profile');
    console.log('2️⃣ Profile Service ready\n');

    // 3. Create first customer profile
    console.log('3️⃣ Creating first customer profile...');
    const customer1Identities = extractIdentities({
      email: '<EMAIL>',
      phone: '+***********',
      user_id: 'user-123',
      source: 'demo'
    });

    const customer1 = await profileService.createOrUpdateProfile(
      DEMO_CONFIG.accountId,
      customer1Identities,
      {
        first_name: 'John',
        last_name: 'Doe',
        demographics: {
          age: 30,
          gender: 'male',
          location: {
            country: 'Vietnam',
            city: 'Ho Chi Minh City'
          }
        },
        behavior: {
          total_sessions: 15,
          total_pageviews: 75,
          total_events: 150,
          total_purchases: 3,
          total_revenue: 2500000,
          avg_session_duration: 420,
          first_seen_at: new Date('2024-01-01'),
          last_activity_at: new Date(),
        },
        preferences: {
          communication_channels: ['email', 'sms'],
          product_categories: ['electronics', 'books'],
        }
      }
    );
    console.log('✅ Customer 1 created:', {
      id: customer1.id,
      name: customer1.full_name,
      email: customer1.primary_email,
      lifecycle_stage: customer1.lifecycle_stage,
      value_tier: customer1.customer_value_tier
    });
    console.log('');

    // 4. Create second customer profile
    console.log('4️⃣ Creating second customer profile...');
    const customer2Identities = extractIdentities({
      email: '<EMAIL>',
      user_id: 'user-456',
      source: 'demo'
    });

    const customer2 = await profileService.createOrUpdateProfile(
      DEMO_CONFIG.accountId,
      customer2Identities,
      {
        first_name: 'Jane',
        last_name: 'Smith',
        demographics: {
          age: 25,
          gender: 'female',
          location: {
            country: 'Vietnam',
            city: 'Hanoi'
          }
        },
        behavior: {
          total_sessions: 5,
          total_pageviews: 20,
          total_events: 40,
          total_purchases: 0,
          total_revenue: 0,
          avg_session_duration: 180,
          first_seen_at: new Date('2024-12-01'),
          last_activity_at: new Date(),
        }
      }
    );
    console.log('✅ Customer 2 created:', {
      id: customer2.id,
      name: customer2.full_name,
      email: customer2.primary_email,
      lifecycle_stage: customer2.lifecycle_stage,
      value_tier: customer2.customer_value_tier
    });
    console.log('');

    // 5. Test identity resolution
    console.log('5️⃣ Testing identity resolution...');
    const resolvedProfile = await profileService.resolveIdentity(
      DEMO_CONFIG.accountId,
      [{ type: 'email', value: '<EMAIL>', verified: true, source: 'demo', first_seen_at: new Date(), created_at: new Date() }]
    );
    console.log('✅ Identity resolved to existing profile:', resolvedProfile?.id === customer1.id);
    console.log('');

    // 6. Update customer behavior
    console.log('6️⃣ Updating customer behavior...');
    const updatedCustomer1 = await profileService.updateProfile(
      customer1.id,
      {
        behavior: {
          ...customer1.behavior,
          total_sessions: customer1.behavior.total_sessions + 1,
          total_pageviews: customer1.behavior.total_pageviews + 5,
          last_activity_at: new Date(),
        }
      }
    );
    console.log('✅ Customer behavior updated:', {
      sessions: updatedCustomer1.behavior.total_sessions,
      pageviews: updatedCustomer1.behavior.total_pageviews,
      engagement: updatedCustomer1.engagement_scores.overall_engagement_score
    });
    console.log('');

    // 7. Search profiles
    console.log('7️⃣ Searching customer profiles...');
    const searchResults = await profileService.searchProfiles(
      DEMO_CONFIG.accountId,
      { lifecycle_stage: 'customer' },
      10,
      0
    );
    console.log('✅ Search results:', {
      total: searchResults.total,
      customers: searchResults.profiles.map(p => ({
        name: p.full_name,
        stage: p.lifecycle_stage,
        revenue: p.behavior.total_revenue
      }))
    });
    console.log('');

    // 8. Test health status
    console.log('8️⃣ Checking service health...');
    const healthStatus = await profileService.getHealthStatus();
    console.log('✅ Service health:', healthStatus);
    console.log('');

    // 9. Display analytics summary
    console.log('9️⃣ Analytics Summary:');
    console.log('📊 Customer Profiles Created: 2');
    console.log('📊 Total Revenue:', new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(customer1.behavior.total_revenue + customer2.behavior.total_revenue));
    console.log('📊 Average Engagement:', 
      ((customer1.engagement_scores.overall_engagement_score + customer2.engagement_scores.overall_engagement_score) / 2 * 100).toFixed(1) + '%'
    );
    console.log('📊 Customer Lifecycle Distribution:');
    const lifecycleCount = searchResults.profiles.reduce((acc, p) => {
      acc[p.lifecycle_stage] = (acc[p.lifecycle_stage] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    Object.entries(lifecycleCount).forEach(([stage, count]) => {
      console.log(`   - ${stage}: ${count}`);
    });
    console.log('');

    // 10. Cleanup (optional)
    console.log('🔟 Demo completed successfully!');
    console.log('');
    console.log('🎉 CDP Module is working perfectly!');
    console.log('');
    console.log('Next steps:');
    console.log('- Visit the CDP Dashboard: http://localhost:3000/home/<USER>/cdp');
    console.log('- Test the API endpoints: /api/cdp/profiles');
    console.log('- Start Phase 2: Segmentation & Intelligence');

  } catch (error) {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  }
}

// Run the demo
if (require.main === module) {
  runCDPDemo().catch(console.error);
}

export { runCDPDemo };
