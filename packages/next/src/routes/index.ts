// /apps/web/lib/enhance-route-handler.ts
import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { User } from '@supabase/supabase-js';

import { z } from 'zod';

import { verifyCaptchaToken } from '@kit/auth/captcha/server';
import { setAuthToken } from '@kit/supabase/auth-token-context';
import { requireUser } from '@kit/supabase/require-user';
import {
  SupabaseClient,
  getSupabaseServerClient,
} from '@kit/supabase/server-client';
import { verifyToken } from '@kit/supabase/verify-token';

import { zodParseFactory } from '../utils';

interface Config<Schema> {
  auth?: boolean;
  captcha?: boolean;
  schema?: Schema;
}

interface HandlerParams<
  Schema extends z.ZodType | undefined,
  RequireAuth extends boolean | undefined,
> {
  request: NextRequest;
  user: RequireAuth extends false ? undefined : User;
  body: <PERSON>hema extends z.ZodType ? z.infer<Schema> : undefined;
  params: Record<string, string>;
  supabase: SupabaseClient; // Add Supabase client to params
}

/**
 * Enhanced route handler function.
 *
 * This function takes a request and parameters object as arguments and returns a route handler function.
 * The route handler function can be used to handle HTTP requests and apply additional enhancements
 * based on the provided parameters.
 *
 * Usage:
 * export const POST = enhanceRouteHandler(
 *   ({ request, body, user, supabase }) => {
 *     return new Response(`Hello, ${body.name}!`);
 *   },
 *   {
 *     schema: z.object({
 *       name: z.string(),
 *     }),
 *   },
 * );
 */
export const enhanceRouteHandler = <
  Body,
  Params extends Config<z.ZodType<Body, z.ZodTypeDef>>,
>(
  handler:
    | ((
        params: HandlerParams<Params['schema'], Params['auth']>,
      ) => NextResponse | Response)
    | ((
        params: HandlerParams<Params['schema'], Params['auth']>,
      ) => Promise<NextResponse | Response>),
  params?: Params,
) => {
  return async function routeHandler(
    request: NextRequest,
    routeParams: {
      params: Promise<Record<string, string>>;
    },
  ) {
    type UserParam = Params['auth'] extends false ? undefined : User;

    let user: UserParam = undefined as UserParam;

    // Check if the captcha token should be verified
    const shouldVerifyCaptcha = params?.captcha ?? false;

    if (shouldVerifyCaptcha) {
      const token = captchaTokenGetter(request);
      if (token) {
        await verifyCaptchaToken(token);
      } else {
        return new Response('Captcha token is required', { status: 400 });
      }
    }

    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ')
      ? authHeader.slice(7)
      : undefined;
    setAuthToken(token);
    const supabase = getSupabaseServerClient();

    const shouldVerifyAuth = params?.auth ?? true;

    if (shouldVerifyAuth) {
      try {
        if (token) {
          // Verify token locally for mobile app/Postman
          const verifiedUser = await verifyToken(token);
          if (!verifiedUser) {
            return NextResponse.json(
              { error: 'Authentication required' },
              { status: 401 },
            );
          }
          user = verifiedUser as UserParam;
        } else {
          // Fallback to cookie-based session for Makerkit/browser
          const auth = await requireUser(supabase); // Use the initialized supabase client
          if (auth.error) {
            return NextResponse.json(
              { error: 'Authentication required' },
              { status: 401 },
            );
          }
          user = auth.data as UserParam;
        }
      } catch (error) {
        console.error('Auth error:', error);
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
      }
    }

    let body: any;
    if (params?.schema) {
      const json = await request.clone().json();
      body = zodParseFactory(params.schema)(json);
    }

    return handler({
      request,
      body,
      user,
      params: await routeParams.params,
      supabase,
    });
  };
};

/**
 * Get the captcha token from the request headers.
 * @param request
 */
function captchaTokenGetter(request: NextRequest) {
  return request.headers.get('x-captcha-token');
}
