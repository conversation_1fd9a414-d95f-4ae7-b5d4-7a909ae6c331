export interface TeamAccountAuthData {
  id: string;
  name: string;
  settings: Record<string, any>;
}

export interface CreateZaloUserParams {
  zaloId: string;
  name: string;
  picture?: string | null;
  teamId: string;
}

export type ZaloAuthResult = {
  userId: string;
  name: string;
  picture: string | null;
  phone: string | null;
  accountRole: string;
};

export interface ZaloUserData {
  id: string;
  name: string;
  picture?: string | null;
}

export type AccountRole = 'customer' | 'admin';
