services:
  db:
    # We use a mariadb image which supports both amd64 & arm64 architecture
    image: mariadb:10.6.4-focal
    # If you really want to use MySQL, uncomment the following line
    #image: mysql:8.0.27
    command: '--default-authentication-plugin=mysql_native_password'
    volumes:
      - db_data:/var/lib/mysql
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=somewordpress
      - MYSQL_DATABASE=wordpress
      - MYSQL_USER=wordpress
      - MYSQL_PASSWORD=wordpress
    expose:
      - 3306
      - 33060
  wordpress:
    image: wordpress:latest
    ports:
      - 8080:80
    restart: always
    working_dir: /var/www/html
    volumes:
      - ./wp-content:/var/www/html/wp-content
    environment:
      - WORDPRESS_DB_HOST=db
      - WORDPRESS_DB_USER=wordpress
      - WORDPRESS_DB_PASSWORD=wordpress
      - WORDPRESS_DB_NAME=wordpress
      - WORDPRESS_DEBUG=1
      - WORDPRESS_CONFIG_EXTRA = |
            define('FS_METHOD', 'direct');        
            /** disable wp core auto update */
            define('WP_AUTO_UPDATE_CORE', false);
            
            /** local environment settings */
            define('WP_CACHE', false);
            define('ENVIRONMENT', 'local');
        
            /** force site home url */
            if(!defined('WP_HOME')) {
              define('WP_HOME', 'http://localhost');
              define('WP_SITEURL', WP_HOME);
            }
volumes:
  db_data: