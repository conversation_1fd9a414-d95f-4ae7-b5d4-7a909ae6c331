-- CDP Sample Data Migration
-- This migration creates sample data for testing CDP functionality

-- Insert sample customer profiles
INSERT INTO customer_profiles (
  account_id,
  email,
  first_name,
  last_name,
  phone,
  location,
  total_orders,
  total_spent,
  avg_order_value,
  engagement_score,
  churn_risk_score,
  value_tier,
  tags,
  notes
) VALUES 
-- High value customers
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  '<PERSON>',
  'Doe',
  '+***********',
  '<PERSON>à Nội',
  25,
  ********,
  600000,
  0.85,
  0.15,
  'high',
  ARRAY['vip', 'loyal'],
  'High-value customer from referral program'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'Jane',
  'Smith',
  '+***********',
  'TP.HCM',
  18,
  ********,
  666667,
  0.78,
  0.22,
  'high',
  ARRAY['premium', 'early-adopter'],
  'Premium customer with high engagement'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  '<PERSON>',
  '<PERSON>',
  '+***********',
  'Đà Nẵng',
  32,
  ********,
  578125,
  0.92,
  0.08,
  'high',
  ARRAY['vip', 'frequent-buyer'],
  'Most active customer with highest engagement'
),

-- Medium value customers
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'Sarah',
  'Wilson',
  '+***********',
  'Hà Nội',
  12,
  4500000,
  375000,
  0.65,
  0.35,
  'medium',
  ARRAY['regular'],
  'Regular customer with steady purchases'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'David',
  'Brown',
  '+***********',
  'TP.HCM',
  8,
  3200000,
  400000,
  0.58,
  0.42,
  'medium',
  ARRAY['seasonal'],
  'Seasonal buyer, active during holidays'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'Lisa',
  'Davis',
  '+***********',
  'Cần Thơ',
  15,
  5800000,
  386667,
  0.72,
  0.28,
  'medium',
  ARRAY['loyal', 'referrer'],
  'Loyal customer who refers others'
),

-- Low value customers
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'Tom',
  'Anderson',
  '+***********',
  'Hà Nội',
  3,
  850000,
  283333,
  0.35,
  0.65,
  'low',
  ARRAY['new'],
  'New customer, recently joined'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'Emma',
  'Taylor',
  '+***********',
  'TP.HCM',
  5,
  1200000,
  240000,
  0.42,
  0.58,
  'low',
  ARRAY['price-sensitive'],
  'Price-sensitive customer, waits for discounts'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'Alex',
  'Martinez',
  '+***********',
  'Đà Nẵng',
  2,
  450000,
  225000,
  0.28,
  0.72,
  'low',
  ARRAY['inactive'],
  'Inactive customer, needs re-engagement'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  '<EMAIL>',
  'Olivia',
  'Garcia',
  '+***********',
  'Hà Nội',
  1,
  180000,
  180000,
  0.15,
  0.85,
  'low',
  ARRAY['one-time'],
  'One-time buyer, high churn risk'
);

-- Insert sample customer segments
INSERT INTO customer_segments (
  account_id,
  name,
  description,
  type,
  criteria,
  customer_count,
  growth_rate,
  is_auto_updating,
  is_active
) VALUES 
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'High Value Customers',
  'Customers with high lifetime value and low churn risk',
  'value_based',
  '{"total_spent": {"operator": "gte", "value": ********}, "value_tier": "high"}',
  3,
  15.5,
  true,
  true
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Highly Engaged Users',
  'Users with high engagement scores and frequent interactions',
  'behavioral',
  '{"engagement_score": {"operator": "gte", "value": 0.7}}',
  4,
  8.2,
  true,
  true
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Churn Risk Customers',
  'Customers at high risk of churning',
  'predictive',
  '{"churn_risk_score": {"operator": "gte", "value": 0.6}}',
  4,
  -12.3,
  true,
  true
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Hanoi Customers',
  'All customers located in Hanoi',
  'demographic',
  '{"location": "Hà Nội"}',
  4,
  5.7,
  true,
  true
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'VIP Members',
  'Premium customers with VIP status',
  'value_based',
  '{"tags": ["vip"], "total_orders": {"operator": "gte", "value": 20}}',
  2,
  22.1,
  true,
  true
);

-- Insert sample customer journeys
INSERT INTO customer_journeys (
  account_id,
  name,
  description,
  status,
  trigger_type,
  trigger_config,
  steps,
  participants,
  completion_rate,
  tags
) VALUES 
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Welcome New Customers',
  'Onboarding journey for new customer registration',
  'active',
  'segment_entry',
  '{"segment_id": "new_customers"}',
  '[
    {"type": "email", "name": "Welcome Email", "config": {"subject": "Welcome to our platform!", "template": "welcome_email"}, "delay_hours": 0},
    {"type": "wait", "name": "Wait 24 Hours", "config": {}, "delay_hours": 24},
    {"type": "email", "name": "Getting Started Guide", "config": {"subject": "Get started with these tips", "template": "getting_started"}, "delay_hours": 0},
    {"type": "wait", "name": "Wait 3 Days", "config": {}, "delay_hours": 72},
    {"type": "sms", "name": "Check-in SMS", "config": {"message": "How are you finding our platform?"}, "delay_hours": 0}
  ]',
  1247,
  0.78,
  ARRAY['onboarding', 'email', 'sms']
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Cart Abandonment Recovery',
  'Re-engage customers who abandoned their cart',
  'active',
  'event',
  '{"event_name": "cart_abandoned"}',
  '[
    {"type": "email", "name": "Cart Reminder", "config": {"subject": "You left something in your cart", "template": "cart_reminder"}, "delay_hours": 1},
    {"type": "wait", "name": "Wait 24 Hours", "config": {}, "delay_hours": 24},
    {"type": "email", "name": "Special Discount", "config": {"subject": "10% off your cart items", "template": "discount_offer"}, "delay_hours": 0},
    {"type": "wait", "name": "Wait 48 Hours", "config": {}, "delay_hours": 48},
    {"type": "sms", "name": "Final Reminder", "config": {"message": "Last chance - your cart expires soon!"}, "delay_hours": 0}
  ]',
  856,
  0.45,
  ARRAY['recovery', 'sms', 'email', 'discount']
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'VIP Customer Rewards',
  'Special rewards program for high-value customers',
  'paused',
  'segment_entry',
  '{"segment_id": "vip_customers"}',
  '[
    {"type": "email", "name": "VIP Welcome", "config": {"subject": "Welcome to VIP status!", "template": "vip_welcome"}, "delay_hours": 0},
    {"type": "wait", "name": "Wait 1 Week", "config": {}, "delay_hours": 168},
    {"type": "email", "name": "Exclusive Offers", "config": {"subject": "Exclusive VIP offers just for you", "template": "vip_offers"}, "delay_hours": 0},
    {"type": "wait", "name": "Wait 1 Month", "config": {}, "delay_hours": 720},
    {"type": "email", "name": "VIP Survey", "config": {"subject": "Help us improve your VIP experience", "template": "vip_survey"}, "delay_hours": 0}
  ]',
  234,
  0.92,
  ARRAY['vip', 'rewards', 'exclusive']
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Re-engagement Campaign',
  'Win back inactive customers',
  'draft',
  'segment_entry',
  '{"segment_id": "inactive_customers"}',
  '[
    {"type": "email", "name": "We Miss You", "config": {"subject": "We miss you! Come back with 20% off", "template": "win_back"}, "delay_hours": 0},
    {"type": "wait", "name": "Wait 1 Week", "config": {}, "delay_hours": 168},
    {"type": "sms", "name": "Special Offer SMS", "config": {"message": "Exclusive 30% off just for you!"}, "delay_hours": 0}
  ]',
  0,
  0.0,
  ARRAY['re-engagement', 'win-back', 'discount']
);

-- Insert sample analytics data
INSERT INTO analytics_data (
  account_id,
  metric_name,
  metric_value,
  metric_type,
  period,
  metadata
) VALUES 
-- Current month metrics
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'total_customers',
  10,
  'count',
  'current_month',
  '{"source": "customer_profiles"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'new_customers',
  3,
  'count',
  'current_month',
  '{"source": "customer_profiles", "filter": "created_this_month"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'total_revenue',
  ********,
  'currency',
  'current_month',
  '{"source": "customer_profiles", "currency": "VND"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'avg_engagement_score',
  0.57,
  'percentage',
  'current_month',
  '{"source": "customer_profiles"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'churn_rate',
  0.15,
  'percentage',
  'current_month',
  '{"source": "customer_profiles"}'
),

-- Journey metrics
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'active_journeys',
  2,
  'count',
  'current_month',
  '{"source": "customer_journeys"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'journey_participants',
  2337,
  'count',
  'current_month',
  '{"source": "journey_participants"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'avg_journey_completion',
  0.615,
  'percentage',
  'current_month',
  '{"source": "journey_participants"}'
),

-- Segment metrics
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'total_segments',
  5,
  'count',
  'current_month',
  '{"source": "customer_segments"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'avg_segment_growth',
  7.84,
  'percentage',
  'current_month',
  '{"source": "customer_segments"}'
);

-- Insert sample AI insights
INSERT INTO ai_insights (
  account_id,
  type,
  title,
  description,
  confidence,
  impact,
  category,
  status,
  data,
  recommendations
) VALUES 
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'trend',
  'Increasing Churn Risk in Medium Value Segment',
  'The medium value customer segment is showing increased churn risk over the past 30 days',
  0.85,
  'high',
  'retention',
  'active',
  '{"segment": "medium_value", "churn_increase": 0.12, "affected_customers": 3}',
  ARRAY['Create targeted retention campaign', 'Offer personalized discounts', 'Increase engagement touchpoints']
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'recommendation',
  'Optimize Welcome Journey Timing',
  'The welcome journey could be more effective with adjusted timing between steps',
  0.78,
  'medium',
  'optimization',
  'active',
  '{"journey_id": "welcome_journey", "optimal_delays": [0, 48, 120]}',
  ARRAY['Increase delay between steps 2 and 3 to 48 hours', 'Add personalization based on signup source']
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'anomaly',
  'Unusual Spike in Cart Abandonment',
  'Cart abandonment rate has increased by 25% in the last week',
  0.92,
  'high',
  'conversion',
  'active',
  '{"abandonment_rate": 0.68, "increase": 0.25, "timeframe": "7_days"}',
  ARRAY['Review checkout process for issues', 'A/B test simplified checkout flow', 'Implement exit-intent popups']
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'prediction',
  'High Value Customer Growth Opportunity',
  'Based on current trends, 2 medium value customers are likely to upgrade to high value',
  0.73,
  'medium',
  'growth',
  'active',
  '{"potential_upgrades": 2, "probability": 0.73, "timeframe": "30_days"}',
  ARRAY['Create VIP upgrade campaign', 'Offer exclusive benefits', 'Personalize product recommendations']
);

-- Insert sample integration statuses
INSERT INTO integration_statuses (
  account_id,
  name,
  provider,
  category,
  status,
  last_sync,
  records_synced,
  health_score,
  config
) VALUES 
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Email Marketing Platform',
  'mailchimp',
  'marketing',
  'connected',
  NOW() - INTERVAL '2 hours',
  1250,
  0.95,
  '{"api_key": "***", "list_id": "abc123", "sync_frequency": "hourly"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'SMS Gateway',
  'twilio',
  'communication',
  'connected',
  NOW() - INTERVAL '30 minutes',
  456,
  0.88,
  '{"account_sid": "***", "auth_token": "***", "phone_number": "+**********"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'Analytics Platform',
  'google_analytics',
  'analytics',
  'error',
  NOW() - INTERVAL '1 day',
  0,
  0.25,
  '{"tracking_id": "GA-123456", "error": "Invalid credentials"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'CRM System',
  'salesforce',
  'sales',
  'pending',
  NULL,
  0,
  0.0,
  '{"instance_url": "https://company.salesforce.com", "status": "configuring"}'
),
(
  (SELECT id FROM accounts WHERE slug = 'makerkit' LIMIT 1),
  'E-commerce Platform',
  'shopify',
  'ecommerce',
  'connected',
  NOW() - INTERVAL '15 minutes',
  2340,
  0.92,
  '{"shop_domain": "company.myshopify.com", "access_token": "***", "webhook_verified": true}'
);
