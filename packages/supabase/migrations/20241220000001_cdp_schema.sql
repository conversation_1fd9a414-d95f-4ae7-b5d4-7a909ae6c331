-- CDP (Customer Data Platform) Schema
-- This migration creates all necessary tables for the CDP module

-- Customer Profiles Table
CREATE TABLE IF NOT EXISTS customer_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  email <PERSON><PERSON><PERSON><PERSON> UNIQUE NOT NULL,
  first_name <PERSON><PERSON><PERSON><PERSON>,
  last_name <PERSON><PERSON><PERSON><PERSON>,
  phone VARCHAR,
  avatar_url VARCHAR,
  location VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE,
  total_orders INTEGER DEFAULT 0,
  total_spent DECIMAL DEFAULT 0,
  avg_order_value DECIMAL DEFAULT 0,
  engagement_score DECIMAL DEFAULT 0 CHECK (engagement_score >= 0 AND engagement_score <= 1),
  churn_risk_score DECIMAL DEFAULT 0 CHECK (churn_risk_score >= 0 AND churn_risk_score <= 1),
  value_tier VARCHAR DEFAULT 'low' CHECK (value_tier IN ('high', 'medium', 'low')),
  tags TEXT[] DEFAULT '{}',
  notes TEXT,
  metadata JSONB DEFAULT '{}'
);

-- Customer Segments Table
CREATE TABLE IF NOT EXISTS customer_segments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  description TEXT,
  type VARCHAR NOT NULL CHECK (type IN ('behavioral', 'demographic', 'value_based', 'predictive')),
  criteria JSONB DEFAULT '{}',
  customer_count INTEGER DEFAULT 0,
  growth_rate DECIMAL DEFAULT 0,
  is_auto_updating BOOLEAN DEFAULT true,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer Journeys Table
CREATE TABLE IF NOT EXISTS customer_journeys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  description TEXT,
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('active', 'paused', 'draft', 'completed')),
  trigger_type VARCHAR NOT NULL CHECK (trigger_type IN ('segment_entry', 'event', 'date', 'manual')),
  trigger_config JSONB DEFAULT '{}',
  steps JSONB DEFAULT '[]',
  participants INTEGER DEFAULT 0,
  completion_rate DECIMAL DEFAULT 0 CHECK (completion_rate >= 0 AND completion_rate <= 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE,
  tags TEXT[] DEFAULT '{}'
);

-- Analytics Data Table
CREATE TABLE IF NOT EXISTS analytics_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  metric_name VARCHAR NOT NULL,
  metric_value DECIMAL NOT NULL,
  metric_type VARCHAR NOT NULL CHECK (metric_type IN ('count', 'percentage', 'currency', 'duration')),
  period VARCHAR DEFAULT 'current_month' CHECK (period IN ('current_day', 'current_week', 'current_month', 'current_year')),
  metadata JSONB DEFAULT '{}',
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Integration Statuses Table
CREATE TABLE IF NOT EXISTS integration_statuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  provider VARCHAR NOT NULL,
  category VARCHAR NOT NULL,
  status VARCHAR DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'pending')),
  last_sync TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  records_synced INTEGER DEFAULT 0,
  health_score DECIMAL DEFAULT 0 CHECK (health_score >= 0 AND health_score <= 1),
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Insights Table
CREATE TABLE IF NOT EXISTS ai_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  type VARCHAR NOT NULL CHECK (type IN ('trend', 'anomaly', 'prediction', 'recommendation')),
  title VARCHAR NOT NULL,
  description TEXT,
  confidence DECIMAL DEFAULT 0 CHECK (confidence >= 0 AND confidence <= 1),
  impact VARCHAR DEFAULT 'low' CHECK (impact IN ('high', 'medium', 'low')),
  category VARCHAR NOT NULL,
  status VARCHAR DEFAULT 'active' CHECK (status IN ('active', 'dismissed', 'resolved')),
  data JSONB DEFAULT '{}',
  recommendations TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Journey Participants Table (for tracking customer journey progress)
CREATE TABLE IF NOT EXISTS journey_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journey_id UUID NOT NULL REFERENCES customer_journeys(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customer_profiles(id) ON DELETE CASCADE,
  current_step INTEGER DEFAULT 0,
  status VARCHAR DEFAULT 'active' CHECK (status IN ('active', 'completed', 'exited')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}'
);

-- Segment Members Table (for tracking which customers belong to which segments)
CREATE TABLE IF NOT EXISTS segment_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  segment_id UUID NOT NULL REFERENCES customer_segments(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customer_profiles(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(segment_id, customer_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customer_profiles_account_id ON customer_profiles(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_email ON customer_profiles(email);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_value_tier ON customer_profiles(value_tier);
CREATE INDEX IF NOT EXISTS idx_customer_profiles_churn_risk ON customer_profiles(churn_risk_score);

CREATE INDEX IF NOT EXISTS idx_customer_segments_account_id ON customer_segments(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_segments_type ON customer_segments(type);
CREATE INDEX IF NOT EXISTS idx_customer_segments_active ON customer_segments(is_active);

CREATE INDEX IF NOT EXISTS idx_customer_journeys_account_id ON customer_journeys(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_journeys_status ON customer_journeys(status);
CREATE INDEX IF NOT EXISTS idx_customer_journeys_trigger_type ON customer_journeys(trigger_type);

CREATE INDEX IF NOT EXISTS idx_analytics_data_account_id ON analytics_data(account_id);
CREATE INDEX IF NOT EXISTS idx_analytics_data_metric_name ON analytics_data(metric_name);
CREATE INDEX IF NOT EXISTS idx_analytics_data_period ON analytics_data(period);

CREATE INDEX IF NOT EXISTS idx_integration_statuses_account_id ON integration_statuses(account_id);
CREATE INDEX IF NOT EXISTS idx_integration_statuses_status ON integration_statuses(status);

CREATE INDEX IF NOT EXISTS idx_ai_insights_account_id ON ai_insights(account_id);
CREATE INDEX IF NOT EXISTS idx_ai_insights_type ON ai_insights(type);
CREATE INDEX IF NOT EXISTS idx_ai_insights_status ON ai_insights(status);

CREATE INDEX IF NOT EXISTS idx_journey_participants_journey_id ON journey_participants(journey_id);
CREATE INDEX IF NOT EXISTS idx_journey_participants_customer_id ON journey_participants(customer_id);
CREATE INDEX IF NOT EXISTS idx_journey_participants_status ON journey_participants(status);

CREATE INDEX IF NOT EXISTS idx_segment_members_segment_id ON segment_members(segment_id);
CREATE INDEX IF NOT EXISTS idx_segment_members_customer_id ON segment_members(customer_id);

-- Enable Row Level Security (RLS)
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_journeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_statuses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE journey_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE segment_members ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Customer Profiles policies
CREATE POLICY "Users can view customer profiles for their account" ON customer_profiles
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can insert customer profiles for their account" ON customer_profiles
  FOR INSERT WITH CHECK (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Users can update customer profiles for their account" ON customer_profiles
  FOR UPDATE USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Users can delete customer profiles for their account" ON customer_profiles
  FOR DELETE USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Customer Segments policies
CREATE POLICY "Users can view customer segments for their account" ON customer_segments
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage customer segments for their account" ON customer_segments
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Customer Journeys policies
CREATE POLICY "Users can view customer journeys for their account" ON customer_journeys
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage customer journeys for their account" ON customer_journeys
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Analytics Data policies
CREATE POLICY "Users can view analytics data for their account" ON analytics_data
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage analytics data for their account" ON analytics_data
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Integration Statuses policies
CREATE POLICY "Users can view integration statuses for their account" ON integration_statuses
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage integration statuses for their account" ON integration_statuses
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- AI Insights policies
CREATE POLICY "Users can view AI insights for their account" ON ai_insights
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage AI insights for their account" ON ai_insights
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM account_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Journey Participants policies
CREATE POLICY "Users can view journey participants for their account" ON journey_participants
  FOR SELECT USING (
    journey_id IN (
      SELECT id FROM customer_journeys 
      WHERE account_id IN (
        SELECT account_id FROM account_memberships 
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
      )
    )
  );

CREATE POLICY "Users can manage journey participants for their account" ON journey_participants
  FOR ALL USING (
    journey_id IN (
      SELECT id FROM customer_journeys 
      WHERE account_id IN (
        SELECT account_id FROM account_memberships 
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
      )
    )
  );

-- Segment Members policies
CREATE POLICY "Users can view segment members for their account" ON segment_members
  FOR SELECT USING (
    segment_id IN (
      SELECT id FROM customer_segments 
      WHERE account_id IN (
        SELECT account_id FROM account_memberships 
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'member')
      )
    )
  );

CREATE POLICY "Users can manage segment members for their account" ON segment_members
  FOR ALL USING (
    segment_id IN (
      SELECT id FROM customer_segments 
      WHERE account_id IN (
        SELECT account_id FROM account_memberships 
        WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
      )
    )
  );

-- Create functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_customer_profiles_updated_at BEFORE UPDATE ON customer_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_segments_updated_at BEFORE UPDATE ON customer_segments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_journeys_updated_at BEFORE UPDATE ON customer_journeys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_integration_statuses_updated_at BEFORE UPDATE ON integration_statuses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_insights_updated_at BEFORE UPDATE ON ai_insights FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
