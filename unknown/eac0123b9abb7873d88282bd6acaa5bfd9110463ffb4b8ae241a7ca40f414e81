import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/logger';
import { prisma } from '@kit/db';
import { IPOSConnector } from '@/lib/integrations/connectors/ipos/ipos-connector';
import { IPOSMapper } from '@/lib/integrations/connectors/ipos/ipos-mapper';
import { IPOSSync } from '@/lib/integrations/connectors/ipos/ipos-sync';

const logger = getLogger({ service: 'api-ipos-orders-status' });

/**
 * API bắc cầu để lấy trạng thái đơn hàng từ iPOS
 * @param request Request
 */
export async function GET(request: NextRequest) {
  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const foodbookCode = searchParams.get('foodbookCode');

    // Kiểm tra tham số bắt buộc
    if (!accountId || !foodbookCode) {
      return NextResponse.json(
        { success: false, message: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Lấy thông tin tích hợp iPOS
    const integration = await prisma.integrations.findFirst({
      where: {
        account_id: accountId,
        platform: 'ipos',
        status: 'active'
      }
    });

    if (!integration) {
      return NextResponse.json(
        { success: false, message: 'No active iPOS integration found' },
        { status: 404 }
      );
    }

    // Lấy cấu hình từ integration
    const config = integration.config as any;
    const credentials = {
      access_token: config.access_token,
      pos_parent: config.pos_parent,
      pos_id: config.pos_id,
      baseUrl: config.baseUrl || 'https://api.foodbook.vn'
    };

    // Khởi tạo connector, mapper và sync
    const connector = new IPOSConnector(credentials, logger);
    const mapper = new IPOSMapper(logger);
    const sync = new IPOSSync(connector, mapper, logger, accountId, integration.id);

    // Đồng bộ trạng thái đơn hàng
    const result = await sync.syncOrderStatus(foodbookCode);

    // Trả về kết quả
    return NextResponse.json({
      success: result.success,
      data: result.data,
      message: result.message
    });
  } catch (error) {
    logger.error({ error }, 'Error getting order status from iPOS');
    return NextResponse.json(
      { success: false, message: (error as Error).message },
      { status: 500 }
    );
  }
}
